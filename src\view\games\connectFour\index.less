@charset "UTF-8";
//*,
//*:before,
//*:after {
//    box-sizing: inherit;
//    font-family: inherit;
//    color: inherit;
//    position: inherit;
//}



//*:focus {
//    outline: 0.15rem dotted #ff6a13;
//    outline-offset: 0.15rem;
//}

[hidden] {
    display: none !important;
}


.red,
.yellow {
    text-transform: capitalize;
    margin-right: 0.1rem;
    //overflow: hidden;
}

.red {
    color: #e4002b;
}

.yellow {
    color: #ffd100;
}

button {
    cursor: pointer;
}
.connectFour {
    height: 100%;
    position: relative;
    .message {
        position: absolute;
        height: 4rem;
        left: 0.26rem;
        bottom: 0.18rem;
        width: 2.38rem;
        padding: 0.3rem;
        //border: 0.04rem solid #5FC5DC;
        background: url("../../../assets/connectFour_state.png") no-repeat;
        background-size: 100% 100%;
        border-radius: 0.16rem;
        h2 {
            color: #1EE8F8;
            margin-bottom: 0.2rem;
        }
        p {
            font-size: 0.236rem;
            display: flex;
            margin-bottom: 0.1rem;
            span {
                display: inline-block;
            }
            span:nth-child(2) {
                margin-top: -0.04rem;
                margin-left: 0.06rem;
            }
        }
    }
}
#game {
    width: 9.04rem;
    //height: 8.4rem;
    margin: auto;
    border-radius: 0.25rem;
    position: absolute;
    left: 49%;
    top: -1.5rem;
    transform: translateX(-50%);

}

#game .flex {
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    align-items: center;
}

#game .flex p {
    width: 3rem;
    font-size: 0.26rem !important;
    margin: 0;
}

#game .flex button {
    font-size: 0.26rem;
    //padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 2px solid #a7a8aa;
}

#game .flex button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#game .flex button:not(:disabled):hover {
    -webkit-filter: brightness(0.9);
    filter: brightness(0.9);
}

#button-board {
    width: 100%;
    height: 0.6rem;
    padding: 0.1rem 0.15rem;
    grid-template-columns: repeat(7, 1fr);
    grid-gap: 0rem;
    display: grid;
    margin-bottom: 0.1rem;
}

//@media (min-width: 40rem) {
//    #button-board {
//        display: grid;
//    }
//}

#button-board [data-column] {
    position: relative;
}

#button-board button {
    padding: 0.25rem 0;
    border: 0;
    text-align: center;
    width: 100%;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: end;
    align-items: flex-end;
    -webkit-box-pack: center;
    justify-content: center;
    background: transparent;
}

#button-board button span {
    height: 0;
    border-left: 1vmin solid transparent;
    border-right: 1vmin solid transparent;
    border-bottom: none;
    border-top: 1vmin solid #a7a8aa;
    text-align: center;
    pointer-events: none;
    display: block;
}

#button-board button span::after {
    content: "";
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 0.5rem;
    border: 3px solid #a7a8aa;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: block;
    position: absolute;
}

#button-board button:focus,
#button-board button:hover {
    outline: none;
}

#button-board button:focus+.slot,
#button-board button:hover+.slot {
    display: block;
    left: 50%;
    z-index: 2;
    pointer-events: none;
    position: absolute;
    bottom: 0.2rem;
    transform: translateX(-50%);
}

#button-board button:focus+.slot:before,
#button-board button:hover+.slot:before {
    content: "👇";
    position: absolute;
    font-size: 0.6rem;
    width: 90%;
    left: 0;
    top: -0.6rem;
    text-align: center;
    z-index: 2;
}
















#button-board .active {
    outline: none;
}

#button-board .active + .slot {
    display: block;
    left: 50%;
    z-index: 2;
    pointer-events: none;
    position: absolute;
    bottom: 0.2rem;
    transform: translateX(-50%);
}

#button-board .active + .slot:before {
    content: "";
    position: absolute;
    font-size: 0.6rem;
    width: 90%;
    left: 0;
    top: -0.6rem;
    text-align: center;
    z-index: 2;
}

.connectFour {
    .el-dialog {
        width: fit-content;
        margin-top: 0 !important;
        top: 50%;
        transform: translateY(-50%);
        border-radius: 0.16rem;
        .el-dialog__header {
            display: none;
        }
        .el-dialog__body {
            .callMyFriend {
                .myFriendsBtn {
                    width: 20vw;
                    height: 0.6rem;
                    line-height: 0.6rem;
                    background: #00CCFF;
                    color: #fff;
                    border-radius: 0.16rem;
                    margin-bottom: 0.2rem;
                    text-align: center;
                    font-weight: bold;
                    font-size: 0.24rem;
                }
                .myFriendsBtn:last-child {
                    margin-bottom: 0;
                }
            }
            .popupMessage {
                .popupMessage {
                    line-height: 0.4rem;
                    text-align: center;
                    font-size: 0.24rem;
                    margin-bottom: 0.2rem;
                }
                .popupMessageBtn {
                    width: 20vw;
                    height: 0.6rem;
                    line-height: 0.6rem;
                    background: #00CCFF;
                    font-weight: bold;
                    text-align: center;
                    font-size: 0.24rem;
                    color: #fff;
                    border-radius: 0.16rem;
                }
            }


        }
    }
}








#button-board button:focus+.slot div,
#button-board button:hover+.slot div {
    -webkit-animation: none;
    animation: none;
    z-index: 1;
    box-shadow: -0.1rem -0.1rem 1px rgba(255, 255, 255, 0.2) inset;
}

#button-board button:focus+.slot div:before,
#button-board button:hover+.slot div:before {
    box-shadow: -0.1rem -0.1rem 1px rgba(255, 255, 255, 0.2) inset, 0.2rem 0.2rem 0.3rem rgba(83, 86, 90, 0.2) inset;
}

#button-board .slot {
    position: absolute;
    top: 0;
    display: none;
}

#board {
    width: 100%;
    height: 7.76rem;
    margin: auto;
    display: grid;
    grid-gap: 0rem;
    align-content: space-evenly;
    grid-template-columns: repeat(7, 1fr);
    -webkit-box-pack: center;
    justify-content: center;
    overflow: hidden;
    //border-radius: 0.25rem;
    padding: 0.15rem;
    background: url("../../../assets/connectFour_page.png") no-repeat;
    background-size: 100% 100%;
    //mix-blend-mode: multiply;
    //background: #7ba7bc;
    //box-shadow: 0.2rem 0.3rem 0.4rem rgba(83, 86, 90, 0.3), -0.25rem -0.25rem 0.25rem rgba(89, 144, 171, 0.5) inset, 0.25rem 0.25rem 0.25rem rgba(157, 190, 205, 0.8) inset;
}



.slot {
    width: 1.1rem;
    height: 1.1rem;
    background: #293E6B;
    border-radius: 1.1rem;
    padding: 0;
    border: none;
    margin: auto;
}



.slot.shadow {
    //box-shadow: 0rem 0rem 0.01rem rgba(174, 201, 214, 0.5), 0rem 0rem 0.01rem rgba(79, 131, 156, 0.5), 0.2rem 0.2rem 0.2rem rgba(83, 86, 90, 0.3) inset;
}

.slot .red,
.slot .yellow {
    width: inherit;
    height: inherit;
    border-radius: inherit;
    border: none;
    pointer-events: none;
    //box-shadow: -0.1rem -0.1rem 0.01rem rgba(255, 255, 255, 0.2) inset, 0.1rem 0.1rem 1px rgba(83, 86, 90, 0.2) inset, 0.2rem 0.2rem 0.3rem rgba(83, 86, 90, 0.3) inset;
    position: relative;
    font-size: 0.4rem;
    line-height: 0.9rem;
    text-align: center;
    color: rgba(0, 0, 0, 0.4);
}

.slot .red.loser,
.slot .yellow.loser {
    -webkit-filter: opacity(0.7) grayscale(60%) contrast(0.4);
    filter: opacity(0.7) grayscale(60%) contrast(0.4);
}
//
//.slot .red:before,
//.slot .yellow:before {
//    content: "";
//    width: 70%;
//    height: 70%;
//    border-radius: inherit;
//    box-shadow: inherit;
//    left: 15%;
//    top: 15%;
//    position: absolute;
//}

.slot .red {
    background: #e4002b;
    box-shadow: inset -0.01rem -0.04rem 0.04rem 0.04rem rgba(0,0,0,0.3);
}

.slot .yellow {
    background: #ffd100;
    box-shadow: inset -0.01rem -0.04rem 0.04rem 0.04rem rgba(0,0,0,0.3);
}

#button-board .slot {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    background: transparent !important;
    top: 0;
    bottom: 0 !important;

}

#button-board .red {
    width: 0.73rem;
    height: 0.33rem;
    background: url("../../../assets/connectFour_arrow.png") no-repeat;
    background-size: 100% 100%;
    //background-position: 50% -100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-10%);
    box-shadow: initial !important;
}

#button-board .yellow {
    width: 0.73rem;
    height: 0.33rem;
    background: url("../../../assets/connectFour_arrow.png") no-repeat;
    background-size: 100% 100%;
    //background-position: 50% -100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-10%);
    box-shadow: initial !important;
}

#button-board .red:before,
#button-board .yellow:before {
    content: "";
    box-shadow: inherit;
    left: -40%;
    top: 0.64rem;
    position: absolute;
    border: 0.04rem solid #fff;
    border-radius: 0.16rem;
    width: 168%;
    height: 7.76rem;
}

#button-board .ai .red:before,
#button-board .ai .yellow:before {
    content: none !important;
}




//#button-board .red:before {
//    border-color: red;
//}
//#button-board .yellow:before {
//    border-color: yellow;
//}


.game_state {
    width: 3.02rem;
    height: 5rem;
    background: url("../../../assets/connectFour_state.png") no-repeat;
    background-size: 100% 100%;
    position: absolute;
    bottom: 0.18rem;
    left: 13.5rem;
    div {
        padding: 0.2rem 0.38rem;
        font-size: 0.26rem;
        font-weight: bold;
        span {
            display: inline-block;
        }
        span:nth-child(1) {
            letter-spacing: 0.05rem;
            width: 1.3rem;
        }
        span:nth-child(2) {
            font-size: 0.35rem;
        }
    }
    div:first-child {
        margin-top: 0.26rem;
    }

}







#winner-burst {
    -webkit-transition: all 0.7s ease;
    transition: all 0.7s ease;
    width: 150vmax;
    height: 150vmax;
    border-radius: 75vmax;
    position: absolute;
    left: calc(50% - 75vmax);
    z-index: 3;
    top: calc(50% - 75vmax);
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: scale(0);
    transform: scale(0);
    pointer-events: none;
}

#winner-burst.red {
    background-color: #e4002b;
}

#winner-burst.yellow {
    background-color: #ffd100;
}

#winner-burst.burst {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation: fadeOut 0.7s ease forwards;
    animation: fadeOut 0.7s ease forwards;
    -webkit-animation-delay: 0.7s;
    animation-delay: 0.7s;
}

@-webkit-keyframes drop {
    from {
        -webkit-transform: translateY(-54vmin);
        transform: translateY(-54vmin);
    }
    to {
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}

@keyframes drop {
    from {
        -webkit-transform: translateY(-54vmin);
        transform: translateY(-54vmin);
    }
    to {
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}

@-webkit-keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

