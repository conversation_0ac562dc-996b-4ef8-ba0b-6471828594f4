import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store/index";
import axios from "./api/resouce";

const CryptoJS = require("crypto-js");


import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';

// // 引入本地 jQuery 文件
// import './view/games/hextrix/hextrix_js/jquery.js';
//
// window.$ = window.jQuery = jQuery;
import $ from 'jquery';
// 将 jQuery 挂载到全局
window.$ = window.jQuery = $;




import {
    GetNowTime,
    GetUserInfo,
    GetCarLastOrder,
    sendCarOrder,
    cancelCarOrder,
    GetWeather,
    GetStatus,
    getRestPoint,
    getPointTask,
    insertPoint,
    Login
} from "@/api/index";




Vue.config.productionTip = false;
Vue.prototype.axios = axios;

import Bridge from './utils/bridge.js'
Vue.prototype.$bridge = Bridge

Vue.use(ElementUI);


import VueLazyload from 'vue-lazyload'  //引入这个懒加载插件
Vue.use(VueLazyload, {
  attempt: 1,
  error: require('./assets/font_default.jpg'),
  loading: require('./assets/loading.gif'),
})


window.addEventListener('resize', () => {
  // store.state.app.screen_multiple = document.body.clientWidth / 1920
  store.dispatch('app/setScreenMultiple', document.body.clientWidth / 1920)
})
store.dispatch('app/setScreenMultiple', document.body.clientWidth / 1920)


/***
 * App 携带 mac 调取 web 初始化程序方法
 */
Bridge.registerhandler('callMeReady', (data, responseCallback) => {
  responseCallback('已取到mac')
  if (data) {
    store.state.app.is_tv = true
    sessionStorage.setItem('MAC', data)
    initVueApp()
  }
})

/***
 * 非 pc 摄像头在线判断、网络判断
 */
function getDevicesStatus() {
  Bridge.callhandler('getDeviceStatus', null,(res) => {
    if (res) {
      let dataJson = JSON.parse(res)
      // 网络设备
      if (dataJson.network  == "true") {
        // 首页 网络图标
        store.state.app.onLineList[1].show = true
        // 设置  网络情况
        store.state.app.settingBtn[0].status = '已连接'
      } else {
        // 首页 网络图标
        store.state.app.onLineList[1].show = false
        // 设置  网络情况
        store.state.app.settingBtn[0].status = '未连接'

        if (store.state.app.media) {
          store.state.app.media.stop()
        }

      }

      // 视频设备
      if (dataJson.camera == "true") {
        store.state.app.onLineList[0].show = true
      } else if(dataJson.camera == "false"){
        store.state.app.onLineList[0].show = false
      } else {
        store.state.app.onLineList[0].show = -1
      }
    } else {
      store.state.app.onLineList[0].show = false
      store.state.app.onLineList[1].show = false
    }
  })
}

if (navigator.appVersion.indexOf('Windows') == -1) {
  getDevicesStatus()
  setInterval(()=>{
    getDevicesStatus()
  },1000)

}


/***
 * App 全局智能语音助手
 */
Bridge.registerhandler('StartVoiceAssistant', (data, responseCallback) => {
  // 不是在首页 才执行
  // if (window.location.href.indexOf('/index') == -1) {
  //   return
  // }
  if (!store.getters.getUserInfo || !store.getters.getUserInfo.is_agree) {
    return
  }
  if(!store.state.app.onLineList[1].show) {
    return
  }
  if (data) {
      data = JSON.parse(data)

      if (data.type == -1) {  // 初次创建
        store.state.app.aiShow = true
      } else {  // 后续对话
        clearInterval(store.state.app.storeTimer)
        store.state.app.storeTimer = null
        if (Number(data.type)) {
          let newArr = JSON.parse(JSON.stringify(store.getters.AISayList))
          let obj = {
            type: 1,
            message: data.message
          }
          newArr[0] = obj
          store.dispatch('app/setAiSay', newArr);
        } else {
          let newArr = JSON.parse(JSON.stringify(store.getters.AISayList))
          let obj = {
            type: 0,
            message: data.message
          }
          newArr[0] = obj
          store.dispatch('app/setAiSay', newArr);
        }
      }
      if (data.order) {
        let newArr = JSON.parse(JSON.stringify(store.getters.AISayList))
        switch (Number(data.order)) {
          case 2: // 叫车
            let obj = {
              type: 0,
              message: data.message
            }
            newArr[0] = obj
            store.dispatch('app/setAiSay', newArr);

            Vue.prototype.fuc.sendCallTaxi((res)=>{
              if (res.code == 200) {
                getLastOrder()
              }
              // store.state.app.aiShow = false
              // router.push({
              //   path: '/onLineCar'
              // })
            },(error)=>{
              getLastOrder((res)=>{
                if (res.code == 3) {
                  let newArr = JSON.parse(JSON.stringify(store.getters.AISayList))
                  let obj = {
                    type: 0,
                    message: '叫车失败，请稍后再试'
                  }
                  newArr[0] = obj
                  store.dispatch('app/setAiSay', newArr);
                }
              })
            })
            break;
        }
      }



  }
})


/**
 * 全局视频按键跳转视频通话 我的好友
 */

let lastExecutionTime = 0;
function executeIfIntervalPassed(fn) {
  const now = Date.now();
  if (now - lastExecutionTime >= 0) {
    fn();
    lastExecutionTime = now;
  }
}



function goToVideoChat() {
  // 视频通话页面不响应遥控器视频按键
  if (window.location.href.indexOf("zegoCall") == -1) {
    if (!store.getters.getUserInfo) {
      return
    }
    executeIfIntervalPassed(()=> {
      if(store.state.app.media) {
        if (!store.getters.getUserInfo.is_agree) {
          return
        }
        store.state.app.media.stop(()=>{
          router.push({
            path: './zegoCall?type=1'
          })
        })
        return
      }
      if (store.getters.getUserInfo.is_agree) {
        router.push({
          path: './zegoCall?type=1'
        })
      }
    })
  }
}
Bridge.registerhandler('goToVideoChat', (data, responseCallback) => {
  goToVideoChat()
})

/***
 * 全局首页按键
 */

// 清空sessionStorage，但保留特定键的项
function clearSessionStorageExceptKey(keyToKeep) {
  // 检查是否支持sessionStorage
  if (typeof(Storage) !== "undefined") {
    // 遍历sessionStorage中的所有项
    for (var i = 0; i < sessionStorage.length; i++) {
      var key = sessionStorage.key(i);
      // 检查每个项的键是否与指定的键相同
      if (key !== keyToKeep) {
        // 如果键不同，则移除该项
        sessionStorage.removeItem(key);
      }
    }
  } else {
    console.log("对不起，您的浏览器不支持sessionStorage。");
  }
}

const requiredClicks = 10;
const maxInterval = 1500; // 最大间隔时间（毫秒）
let clickTimes = []; // 存储点击时间
let clickTimer = null

function goToHome() {
  clearTimeout(clickTimer)
  clickTimer = null
  clickTimer = setTimeout(()=>{
    clickTimes = []
  },1000)
  if (window.location.href.indexOf("index") == -1) {
    clearSessionStorageExceptKey('MAC')
    executeIfIntervalPassed(()=> {
      if (store.state.app.media) {
        executeIfIntervalPassed(()=>{
          store.state.app.media.stop(()=>{
            clearSessionStorageExceptKey('MAC')
            router.push({
              path: './index'
            })
          })
        })
        return
      }
      router.push({
        path: './index'
      })
    })
  } else {
    const now = Date.now();

    // 添加当前点击时间
    clickTimes.push(now);

    // 过滤掉超出最大间隔的点击时间
    clickTimes = clickTimes.filter(time => now - time <= maxInterval);

    // 检查是否达到所需的点击次数
    if (clickTimes.length >= requiredClicks) {
      // 执行你想要的操作
      store.state.app.versionShow = !store.state.app.versionShow
      // 重置点击时间数组
      clickTimes = [];
    }
  }
}

Bridge.registerhandler('goToHome', (data, responseCallback) => {
  goToHome()
})

let keywordsGo = true

let keyWordsnNum = 200
let keydown_flag = true;
let keydown_time_control = null;

let callback = null
let focusEvent = null

/***
 * 时间格式化  new Date().format('yyyy-MM-dd hh:mm:ss')
 * @param fmt
 * @returns {*}
 */

Date.prototype.format = function (fmt){
  var weekArray = new Array("日","一", "二", "三", "四", "五", "六")
  let o = {
    "M+": this.getMonth() + 1,                 //月份
    "d+": this.getDate(),                    //日
    "h+": this.getHours(),                   //小时
    "m+": this.getMinutes(),                 //分
    "s+": this.getSeconds(),                 //秒
    "q+": Math.floor((this.getMonth() + 3) / 3), //季度
    "S": this.getMilliseconds(),             //毫秒
    "W": weekArray[this.getDay()]
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    }
  }
  return fmt;
}
/**
 * 时间段判断
 */

Date.prototype.isDuringDate = function (beginDateStr,endDateStr,nowDate){
  var curDate = nowDate ? new Date(nowDate) : new Date(),
      beginDate = new Date(beginDateStr),
      endDate = new Date(endDateStr);
  if (curDate >= beginDate && curDate <= endDate) {
    return true;
  }
  return false;
}

Vue.prototype.fuc = {
  getToken(callback,errorback) {
    Login({ mac: sessionStorage.getItem('MAC') })
      .then(loginRes => {
        if (loginRes.code == 200) {
          let thisToken = CryptoJS.AES.encrypt(loginRes.data.token, store.state.app.s).toString()
          store.state.app.token = thisToken;
          sessionStorage.setItem('token', store.state.app.token);
          if (callback) {
            callback();
          }
        }
      })
      .catch(loginErr => {
        if (errorback) {
          errorback();
        }
      })
  },
  setScroll() {
      let parentList = document.getElementsByClassName('scrollParent')
      let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
      // 不存在翻页标识时，但是存在滚动条 则删除该滚动条
      if (parentList.length == 0) {
        let scrollDom = document.querySelector('.scroll');
        if (scrollDom) {
          scrollDom.remove()
        }
        return
      }
      for (let i of parentList ){
        let styles = null;
        let styleMargin = 0
        if (i && i.childNodes[0] && i.childNodes[0].childNodes[0]) {
          styles = getComputedStyle(i.childNodes[0].childNodes[0])
          styleMargin = Number(styles.getPropertyValue('margin-bottom').split('px')[0])
        }

        if (i.childNodes.length == 0) {
          return
        }
        const windowHeight = i.clientHeight  // 可视区域高度
        let scrollElement = i.querySelector('.scroll');
        let scroll = document.createElement('div');
        let scrollBar = document.createElement('div')
        let scrollHeight = 0

        // 设置当前dom元素  父元素滚动条
        if (i.childNodes[0].clientHeight > windowHeight + styleMargin) {
          // 创建滚动条
          if (!scrollElement) {
            scroll.className = "scroll"
            scroll.style.height = windowHeight + 'px'
            scroll.style.left = i.offsetLeft + i.clientWidth + (0 * fontSize/100) + 'px'
            scroll.style.top = i.offsetTop + 'px'

            scrollBar.className = "scrollBar"
            // 计算滚动条高度 位置
            scrollBar.style.top = '0px'

            scrollHeight = (windowHeight * (windowHeight / i.childNodes[0].clientHeight))
            scrollBar.style.height = scrollHeight + 'px'
            scroll.appendChild(scrollBar)
            i.appendChild(scroll);
          } else {
            // 当  当前父元素 卷去高度为0时   滚动条恢复到顶部
            if (Number(i.childNodes[0].style.top.split('rem')[0]) == 0) {
              i.childNodes[1].childNodes[0].style.top = "0px"
            }

          }
        } else {
          if (scrollElement) {
            let scrollElement = i.querySelector('.scroll');
            i.removeChild(scrollElement)
          }
        }
      }
    // })
  },
  getUserInfo(callback) {
    getUserInfo(callback)
  },
  getPointTotals() {
    getRestPoint({
      fk_home_id: store.getters.getUserInfo.home_id
    })
    .then((res)=>{
      if (res.code == 200) {
        store.dispatch('app/setPoint',res.data.point)
      }
    })

  },
  getPointList(callback,errorback) {
    getPointTask({
      fk_home_id: store.getters.getUserInfo.home_id
    })
    .then((res)=>{
      if (callback) {
        callback(res)
      }
      if (res.code == 200) {
        store.state.app.pointList = res.data
      }
    })
    .catch((err)=>{
      if(errorback) {
        errorback(err)
      }
    })
  },
  insertPointNum(id) {
    if (!id) {
      return
    }
    insertPoint({
      fk_home_id: store.getters.getUserInfo.home_id,
      fk_point_type_id: id
    })
    .then(res=>{
      if (res.code == 200) {
        store.state.app.pointList.map(item=>{
          if (item.id == id) {
            item.harvest = 1
          }
        })
      }
    })
  },
  cancelOrderCar(success,error) {
    cancelCarOrder({
      id:  store.getters.getOrderInfo.data[0].id,   // 订单id
      phone: store.getters.getUserInfo.oldsters[store.state.app.selectUserIndex].phone
    })
    .then(res=>{
      if(success) {
        success(res)
      }
      this.callTaxi(store.getters.getUserInfo.oldsters[store.state.app.selectUserIndex].phone)
    })
    .catch(err=>{
      if(error) {
        error(err)
      }
    })
  },
  sendCallTaxi(success,error) {
    sendCarOrder({
      fr_user_id: store.getters.getUserInfo.oldsters[store.state.app.selectUserIndex].id,
      phone: store.getters.getUserInfo.oldsters[store.state.app.selectUserIndex].phone,
      src: store.getters.getUserInfo.fk_province_city_name + ',' + store.getters.getUserInfo.addr,
      name: store.getters.getUserInfo.oldsters[store.state.app.selectUserIndex].name,
      device_type: 1
    }).then(res=>{
        if (success) {
          success(res)
        }
        this.callTaxi(store.getters.getUserInfo.oldsters[store.state.app.selectUserIndex].phone)
    })
    .catch(err=>{
      if (error) {
        error(err)
      }
    })

  },
  callTaxi(phone,success,error) {
    clearInterval(store.state.app.storeTimer)
    store.state.app.storeTimer = null
    GetCarLastOrder({
      phone: phone
    })
    .then(res=>{
      if (res.code == 200) {
        // store.state.polyline = res.data.route
        // 状态为等待接单 时  只需要赋值一次，由组件内部进行每秒刷新
        if (res.data.data[0].status == 2) {
          if (!store.getters.getOrderInfo) {
            store.dispatch('app/setOrderInfo', res.data);
          } else if (store.getters.getOrderInfo.data[0].status != res.data.data[0].status) {
            store.dispatch('app/setOrderInfo', res.data);
          }
        }
        // 状态不为等待接单 2 时,每3秒刷新状态及位置信息
        else {
          store.dispatch('app/setOrderInfo', res.data);
        }

        if(success) {
          success(res)
        }
        if (res.data.data[0].status == 3 && store.state.app.storeTimer) {
          clearInterval(store.state.app.storeTimer)
          store.state.app.storeTimer = null
        }
      }

    })
    .catch(err=>{
      store.dispatch('app/setOrderInfo', null);
      clearInterval(store.state.app.storeTimer)
      store.state.app.storeTimer = null
      if(error) {
        error(err)
      }
    })

    store.state.app.storeTimer = setInterval(()=>{
      GetCarLastOrder({
        phone: phone
      })
      .then(res=>{
        if (res.code == 200) {
          // store.state.polyline = res.data.route
          if (res.data.data[0].status == 2) {
            if (!store.getters.getOrderInfo) {
              store.dispatch('app/setOrderInfo', res.data);
            } else if (store.getters.getOrderInfo.data[0].status != res.data.data[0].status) {
              store.dispatch('app/setOrderInfo', res.data);
            }
          } else {
            store.dispatch('app/setOrderInfo', res.data);
          }
          if (res.data.data[0].status == 3 || res.data.data[0].status == 1) {  // 行程结束/取消订单
            clearInterval(store.state.app.storeTimer)
            store.state.app.storeTimer = null
          }
        }
        if(success) {
          success(res)
        }
      })
      .catch(err=>{
        store.dispatch('app/setOrderInfo', null);
        if(error) {
          error(err)
        }
      })
    },3000)
  },
  dateTime(callback) {
    GetNowTime().then(res=>{
      if (res.code == 200) {
        if (callback) {
          callback(res.data)
        }
        var date = new Date(res.data.time * 1000);
        sessionStorage.setItem('time', res.data.time * 1000)

        var month = date.getMonth() + 1;
        if (month < 10) {
          month = '0' + month;
        }
        var day = date.getDate();
        if (day < 10) {
          day = '0' + day;
        }
        var hours = date.getHours();
        if (hours < 10) {
          hours = '0' + hours;
        }
        var minutes = date.getMinutes();
        if (minutes < 10) {
          minutes = '0' + minutes
        }
        var week = date.getDay();
        switch (week) {
          case 1:
            week = '一';
            break;
          case 2:
            week = '二';
            break;
          case 3:
            week = '三';
            break;
          case 4:
            week = '四';
            break;
          case 5:
            week = '五';
            break;
          case 6:
            week = '六';
            break;
          case 0:
            week = '日';
            break;
        }
        let obj = {
          time: {
            hours: hours,
            minutes: minutes
          },
          date: {
            month: month,
            day: day,
            week: week
          },
          timeDate: res.data.time * 1000
        }
        store.state.app.timeInfo = obj
        weatherTimer()
      }
    })
    .catch(err=>{

    })
  },
  callMessage(data) { // 需求留言
    let thisPath = router.app._route.fullPath
    sessionStorage.setItem('lastPath',thisPath)
    // store.dispatch('index/setMainTitle', '要求留言')
    router.push('/mediacall');
  },
  backTv(data) { // 回到电视
    Bridge.callhandler('exitApp', null,(res) => {
      alert(res)
    })
  },
  formatSeconds(value) { // 秒数转换时分秒
    let secondTime = parseInt(value); // 秒
    let minuteTime = 0; // 分
    let hourTime = 0; // 小时
    if (secondTime > 60) { //如果秒数大于60，将秒数转换成整数
      //获取分钟，除以60取整数，得到整数分钟
      minuteTime = parseInt(secondTime / 60) < 10 ? '0' + parseInt(secondTime / 60) : parseInt(secondTime / 60);
      //获取秒数，秒数取佘，得到整数秒数
      secondTime = parseInt(secondTime % 60) < 10 ? '0' + parseInt(secondTime % 60) : parseInt(secondTime % 60);
      //如果分钟大于60，将分钟转换成小时
      if (minuteTime > 60) {
        //获取小时，获取分钟除以60，得到整数小时
        hourTime = parseInt(minuteTime / 60) < 10 ? '0' + parseInt(minuteTime / 60) : parseInt(minuteTime / 60);
        //获取小时后取佘的分，获取分钟除以60取佘的分
        minuteTime = parseInt(minuteTime % 60) < 10 ? '0' + parseInt(minuteTime % 60) : parseInt(minuteTime % 60);
      }
    } else {
      minuteTime = '00'
      hourTime = '00'
      if (secondTime < 10) {
        secondTime = '0' + secondTime
      }
    }

    let result = hourTime + ":" + minuteTime + ":" + secondTime;
    return result;
},
  //  键盘事件
  // 同一页面相同的ref使用focusEvent
  KeyboardEvents(callbackFuc,focusEventFuc,keyType) {
    callback = null
    focusEvent = null
    if (callbackFuc) {
      callback = callbackFuc
    }
    if (focusEvent) {
      focusEvent = focusEventFuc
    }

    if (keyType) {
      document.onkeydown = this.funcKeyBoard
    } else {
      document.onkeyup = this.funcKeyBoard
    }


    if (JSON.stringify(navigator.userAgent).indexOf('EVM TVOS') > -1 || JSON.stringify(navigator.userAgent).indexOf('Hi3798')) { // 国科威厂商 || 网关
      Bridge.registerhandler('syncBackEvent',  (intCode)=> {  // 国科威返回键app通知
        if (intCode) {
            switch (Number(intCode)) {   // 888888  up    999999 down
              case 888888:
                var obj = {
                  keyCode: 888888
                }
                this.funcKeyBoard(obj,callback,focusEvent)
                break;
              // default:
              //   var obj = {
              //     keyCode: 999999
              //   }
              //   this.funcKeyBoard(obj,callback,focusEvent)
              //   break
            }
        }
      })
    }



  },
  funcKeyBoard(e,ecallback,focusEvent) {
    switch (e.keyCode) {
      case 888888:     // 国科威/网关盒子返回
        store.state.app.versionShow = false
        // 设置页面
        if (store.state.app.settingShow) {
          store.state.app.settingShow = false
          return
        }
        // 网络断掉情况
        if(!store.state.app.onLineList[1].show) {
          return
        }
        // 弹窗出现  返回 关闭弹窗
        if (store.state.app.aiShow) {
          store.state.app.aiShow = false
          return
        }
        if (callback && callback.esc) {
          if (keydown_flag) {
            clearTimeout(keydown_time_control);
            keydown_flag = false;
            keydown_time_control = setTimeout(()=> {
              keydown_flag = true;
            }, keyWordsnNum);
          } else {
            return false;
          }
          callback.esc()
          if (window.location.href.indexOf("index") == -1) {
            lastExecutionTime =  Date.now()
          }
          if(focusEvent){
            focusEvent()
            if (window.location.href.indexOf("index") == -1) {
              lastExecutionTime =  Date.now()
            }
          }
        }
        break;
      case 1:    //  上
      case 38:
      case 28:
        if (store.getters.loadingState) {
          return
        }
        event.preventDefault();
        // 设置页面
        if (store.state.app.settingShow) {
          if (store.state.app.settingBtnIndex > 3) {
            store.state.app.settingBtn[store.state.app.settingBtnIndex].ref = ""
            store.state.app.settingBtnIndex -= 4
            store.state.app.settingBtn[store.state.app.settingBtnIndex].ref = "setting_active"
          }
          return
        }
        // 网络断掉情况
        if(!store.state.app.onLineList[1].show) {
          return
        }
        // 弹窗出现  返回 关闭弹窗
        if (store.state.app.aiShow) {
          return
        }
        if (callback && callback.up && !store.state.app.fullscreen) {
          if (keydown_flag) {
            clearTimeout(keydown_time_control);
            keydown_flag = false;
            keydown_time_control = setTimeout(()=> {
              keydown_flag = true;
            }, keyWordsnNum);
          } else {
            return false;
          }
          callback.up()
          if(focusEvent){
            focusEvent()
          }
        }
        break;
      case 2:    //  下
      case 40:
      case 31:
        if (store.getters.loadingState) {
          return
        }
        event.preventDefault();
        // 设置页面
        if (store.state.app.settingShow) {
          if (store.state.app.settingBtnIndex < 4) {
            store.state.app.settingBtn[store.state.app.settingBtnIndex].ref = ""
            store.state.app.settingBtnIndex += 4
            store.state.app.settingBtn[store.state.app.settingBtnIndex].ref = "setting_active"
          }
          return
        }
        // 网络断掉情况
        if(!store.state.app.onLineList[1].show) {
          return
        }
        // 弹窗出现  返回 关闭弹窗
        if (store.state.app.aiShow) {
          return
        }
        if (callback && callback.down && !store.state.app.fullscreen) {
          if (keydown_flag) {
            clearTimeout(keydown_time_control);
            keydown_flag = false;
            keydown_time_control = setTimeout(()=> {
              keydown_flag = true;
            }, keyWordsnNum);
          } else {
            return false;
          }
          // 网络断掉情况
          if(!store.state.app.onLineList[1].show) {
            return
          }
          callback.down()
          if(focusEvent){
            focusEvent()
          }
        }
        break;
      case 37:    //  左
      case 29:
      case 3:
        if (store.getters.loadingState) {
          return
        }
        event.preventDefault();
        // 设置页面
        if (store.state.app.settingShow) {
          if (store.state.app.settingBtnIndex%4 != 0) {
            store.state.app.settingBtn[store.state.app.settingBtnIndex].ref = ""
            store.state.app.settingBtnIndex -= 1
            store.state.app.settingBtn[store.state.app.settingBtnIndex].ref = "setting_active"
          }
          return
        }
        // 网络断掉情况
        if(!store.state.app.onLineList[1].show) {
          return
        }
        // 弹窗出现  返回 关闭弹窗
        if (store.state.app.aiShow) {
          return
        }
        if (callback && callback.left && !store.state.app.fullscreen) {
          if (keydown_flag) {
            clearTimeout(keydown_time_control);
            keydown_flag = false;
            keydown_time_control = setTimeout(()=> {
              keydown_flag = true;
            }, keyWordsnNum);
          } else {
            return false;
          }
          // 网络断掉情况
          if(!store.state.app.onLineList[1].show) {
            return
          }
          callback.left()
          if(focusEvent){
            focusEvent()
          }
        }
        break;
      case 39:    //  右
      case 30:
      case 4:
        if (store.getters.loadingState) {
          return
        }
        event.preventDefault();
        // 设置页面
        if (store.state.app.settingShow) {
          if (store.state.app.settingBtnIndex%4 != 3) {
            store.state.app.settingBtn[store.state.app.settingBtnIndex].ref = ""
            store.state.app.settingBtnIndex += 1
            store.state.app.settingBtn[store.state.app.settingBtnIndex].ref = "setting_active"
          }
          return
        }
        // 网络断掉情况
        if(!store.state.app.onLineList[1].show) {
          return
        }
        // 弹窗出现  返回 关闭弹窗
        if (store.state.app.aiShow) {
          return
        }
        if (callback && callback.right && !store.state.app.fullscreen) {
          if (keydown_flag) {
            clearTimeout(keydown_time_control);
            keydown_flag = false;
            keydown_time_control = setTimeout(()=> {
              keydown_flag = true;
            }, keyWordsnNum);
          } else {
            return false;
          }
          // 网络断掉情况
          if(!store.state.app.onLineList[1].show) {
            return
          }
          callback.right()
          if(focusEvent){
            focusEvent()
          }
        }
        break;
      case 8:  //返回
      case 340:
      case 4096:
      case 27:
        event.preventDefault();
        store.state.app.versionShow = false
        // 设置页面
        if (store.state.app.settingShow) {
          store.state.app.settingShow = false
          return
        }
        // 网络断掉情况
        if(!store.state.app.onLineList[1].show) {
          return
        }
        // 弹窗出现  返回 关闭弹窗
        if (store.state.app.aiShow) {
          store.state.app.aiShow = false
          return
        }
        if (callback && callback.esc) {
          if (keydown_flag) {
            clearTimeout(keydown_time_control);
            keydown_flag = false;
            keydown_time_control = setTimeout(()=> {
              keydown_flag = true;
            }, keyWordsnNum);
          } else {
            return false;
          }
          callback.esc()
          if (window.location.href.indexOf("index") == -1) {
            lastExecutionTime =  Date.now()
          }
          if(focusEvent){
            focusEvent()
          }
        } else {
          history.go(-1)
          if (window.location.href.indexOf("index") == -1) {
            lastExecutionTime =  Date.now()
          }
        }
        break;
      case 13://确定
      case 4097:
        if (store.getters.loadingState) {
          return
        }
        event.preventDefault();
        // 设置页面
        if (store.state.app.settingShow) {
          return
        }
        // 网络断掉情况   跳转app设置  暂时去除跳转设置
        if(!store.state.app.onLineList[1].show) {
          Bridge.callhandler('setNetWork', (data, responseCallback) => {
          })
          // store.state.app.settingShow = true
          return
        }
        // 弹窗出现  返回 关闭弹窗
        if (store.state.app.aiShow) {
          return
        }
        if (callback && callback.enter && !store.state.app.fullscreen) {
          if (keydown_flag) {
            clearTimeout(keydown_time_control);
            keydown_flag = false;
            keydown_time_control = setTimeout(()=> {
              keydown_flag = true;
            }, keyWordsnNum);
          } else {
            return false;
          }
          callback.enter()
        }
        break;
      case 338: //刷新
      case 403:
      case 17:
      case 4231:
      case 101:
        // case 115:
        event.preventDefault();
        router.go(0);
        if (callback && callback.refresh && !store.state.app.fullscreen) {
          if (keydown_flag) {
            clearTimeout(keydown_time_control);
            keydown_flag = false;
            keydown_time_control = setTimeout(()=> {
              keydown_flag = true;
            }, keyWordsnNum);
          } else {
            return false;
          }
          callback.refresh()
        }
        break;
      case 2320: //#删除键
      case 32: //电脑空格键
        event.preventDefault();
        goToHome()
        // goToVideoChat()
        // store.state.app.settingShow = !store.state.app.settingShow
        // store.state.app.aiShow = !store.state.app.aiShow
        // store.state.app.settingShow = !store.state.app.settingShow
        if (callback && callback.delete && !store.state.app.fullscreen) {
          if (keydown_flag) {
            clearTimeout(keydown_time_control);
            keydown_flag = false;
            keydown_time_control = setTimeout(()=> {
              keydown_flag = true;
            }, keyWordsnNum);
          } else {
            return false;
          }
          callback.delete()
        }
        break;
      case 48: //数字输入tv键值
      case 49:
      case 50:
      case 51:
      case 52:
      case 53:
      case 54:
      case 55:
      case 56:
      case 57:
      case 96: //pc键值
      case 97:
      case 98:
      case 99:
      case 100:
      case 101:
      case 102:
      case 103:
      case 104:
      case 105:
        event.preventDefault();
        if (callback && callback.inputNum  && !store.state.app.fullscreen) {
          if (keydown_flag) {
            clearTimeout(keydown_time_control);
            keydown_flag = false;
            keydown_time_control = setTimeout(()=> {
              keydown_flag = true;
            }, keyWordsnNum);
          } else {
            return false;
          }
          if(e.keyCode<95){
            callback.inputNum(e.keyCode-48)
          }else{
            callback.inputNum(e.keyCode-96)
          }
        }
        break;
      default:
        break
    }
  }
}

if (localStorage.getItem('weatherList')) {
  let weatherData = JSON.parse(decodeURI(localStorage.getItem('weatherList')))
  store.state.app.weatherInfo = weatherData
}


// 天气更新规则  30分钟更新一次  1分钟检测一次
// 6*60*60*1000
function weatherTimer() {

  if (localStorage.getItem('weatherList')) {
    let weatherData = JSON.parse(decodeURI(localStorage.getItem('weatherList')))
    // 当前时间  - 存储天气时间
    if ((Number(sessionStorage.getItem('time')) - weatherData.time) > 30 * 60 * 1000) {
      getWeatherInfo()
    }
  } else {
    getWeatherInfo()
  }
}

// 获取天气
function getWeatherInfo() {
  GetWeather({
    // area_name: store.getters.getUserInfo.fk_province_city_name ? store.getters.getUserInfo.fk_province_city_name.split(',')[0].split('区')[0] : '上海市'
  }).then(res=>{
    if (res.code == 200 && res.data) {
      let obj = res.data
      obj.time = Number(sessionStorage.getItem('time'))
      localStorage.setItem('weatherList', encodeURI(JSON.stringify(obj)))
      store.state.app.weatherInfo = obj
    } else {
      localStorage.removeItem('weatherList')
      store.state.app.weatherInfo = null
    }
  })
  .catch(err=>{
    localStorage.removeItem('weatherList')
    store.state.app.weatherInfo = null
  })
}

function getLastOrder(callback) {
  Vue.prototype.fuc.callTaxi(store.getters.getUserInfo.oldsters[store.state.app.selectUserIndex].phone,(res)=>{
    if (res.code == 200) {
      if (callback) {
        callback(res.data.data[0])
      }
      let orderInfo = res.data.data[0]
      let newArr = JSON.parse(JSON.stringify(store.getters.AISayList))
      let obj = {
        type: 0,
        message: '正在叫车，请等待...'
      }
      switch (orderInfo.status) {
        case 2:  // 接单中
          obj = {
            type: 0,
            message: '正在叫车，请等待...'
          }
          break;
        case 4:  // 已接单
          obj = {
            type: 0,
            message: "司机已接单,距离你" + orderInfo.driver_distance + '公里，预估时间：' + orderInfo.driver_arrive + '分钟'
          }
          break;
        case 101:
          obj = {
            type: 0,
            message: "司机到达出发地址,请尽快上车"
          }
          break;
        case 102:
          obj = {
            type: 0,
            message: "行程中"
          }
          break;
        case 103:
          obj = {
            type: 0,
            message: "已到达目的地"
          }
          break;
      }
      newArr[0] = obj
      store.dispatch('app/setAiSay', newArr);
    }
  },(err=>{

  }))
}


let dataTimer= null
function setIntervalTimer() {
  clearInterval(dataTimer)
  dataTimer = null
  dataTimer = setInterval(()=>{
    Vue.prototype.fuc.dateTime()
  }, 60000);
}



function getUserInfo(callback,errorback) {
  if (store.state.app.sha256Button) {
    if (!sessionStorage.getItem('token')) {
      // if (document.getElementById('errorContent')) {
      //   document.getElementById('errorContent').innerHTML = '<div style="width:100vw;height:100vh;background:url(' + require('./assets/illegal.jpg') + ') no-repeat;background-size:100% 100%;position:absolute;top:0;left:0" >'
      // }
      Vue.prototype.fuc.getToken(()=>{
        getUserInfo(callback);
      },()=>{
        if (errorback) {
          errorback();
        }
      })

      return
    }
    store.state.app.token = sessionStorage.getItem('token');
  }

  if (!localStorage.getItem('statusList')) {
      GetStatus().then(res=>{
          if (res.code == 200) {
              localStorage.setItem('statusList', res.data)
          }
      })
  }

  GetUserInfo({ 'mac': sessionStorage.getItem('MAC') }).then((res) => {
    if (res.code == 200) {
      if (document.getElementById('errorContent')) {
        document.getElementById('errorContent').innerHTML = ""
      }
      Vue.prototype.fuc.dateTime((data)=>{
        // 获取当前时间与正分钟差多少秒，则在多少秒后再次请求 获取整点
        let dateTime = 60 - Number(new Date().format('s'))
        dataTimer = setTimeout(()=>{
          Vue.prototype.fuc.dateTime(()=>{
            setIntervalTimer()
          })
        },dateTime * 1000)
      })
      store.dispatch('app/setUserInfo', res.data)
      Vue.prototype.fuc.getPointTotals()
      Vue.prototype.fuc.getPointList()

      if (callback) {
        callback(res)
      }
    }
    else {
      if (document.getElementById('errorContent')) {
        document.getElementById('errorContent').innerHTML =  '<div style="width:100vw;height:100vh;background:url(' + require('./assets/illegal.jpg') + ') no-repeat;background-size:100% 100%;position:absolute;top:0;left:0" >'
      }
      if (store.state.app.media) {
        store.state.app.media.stop()
      }
      // if (res.code == -333333333) {
      //   clearTimeout(appTimer)
      //   appTimer = null
      //   return
      // }
      if (errorback) {
        errorback()
      }
    }
  })
  .catch((err) => {
    // document.body.innerHTML = '<div style="width:19.2rem;height:10.8rem;background:url(' + require('./assets/illegal.jpg') + ') no-repeat;background-size:100% 100%;position:absolute;top:0;left:0" >'
    if(document.getElementById('errorContent')) {
      document.getElementById('errorContent').innerHTML = '<div style="width:100vw;height:100vh;background:url(' + require('./assets/illegal.jpg') + ') no-repeat;background-size:100% 100%;position:absolute;top:0;left:0" >'
    }

    if (store.state.app.media) {
      store.state.app.media.stop()
    }
    if (errorback) {
      errorback()
    }
  })
}

// 网路状态检测
function setNetWork() {
  let domImg = document.createElement('img')
  domImg.src = process.env.VUE_APP_API + '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png?' + new Date().getTime()
  domImg.onerror = (err) =>{
    // 首页 网络图标
    store.state.app.onLineList[1].show = false
    // 设置  网络情况
    store.state.app.settingBtn[0].status = '未连接'
  }
  domImg.onload = ()=>{
    // 首页 网络图标
    store.state.app.onLineList[1].show = true
    // 设置  网络情况
    store.state.app.settingBtn[0].status = '已连接'
  }
}

let onlyOnce = true
if (navigator.appVersion.indexOf('Windows') > -1 || navigator.appVersion.indexOf('Version') == -1) {

  setNetWork()
  // setInterval(()=>{
  //   setNetWork()
  // },1000)
  // localhost 或者 HTTPS 才能调用摄像头检测相关
  if (window.location.href.indexOf('localhost') > -1 || window.location.href.indexOf('https') > -1) {
    // 视频设备检测
    async function  initVideo(){
      let obj = false
      if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
        await navigator.mediaDevices.enumerateDevices()
            .then((devices)=> {
              devices.forEach((device)=> {
                if (device.kind == 'videoinput') {
                  obj = true
                }
              });
            })
            .catch(function(err) {
              obj = false
              console.error("获取摄像头列表时出现错误:", err);
            });
      } else {
        obj = false
        console.error("浏览器不支持获取媒体设备列表");
      }
      return obj
    }

    function setCameraStatus() {
      initVideo().then(res=>{
        if (res) {
          store.state.app.onLineList[0].show = true
        } else {
          store.state.app.onLineList[0].show = false
        }
      })
    }
    setCameraStatus()
    //
    // setInterval(()=>{
    //   setCameraStatus()
    // },1000)
  }


  store.state.app.is_tv = false
  initVueApp()
}

let appTimer = null
function initVueApp() {
  if (!sessionStorage.getItem('MAC')) {
    if (document.getElementById('errorContent')) {
      document.getElementById('errorContent').innerHTML =  '<div style="width:100vw;height:100vh;background:url(' + require('./assets/illegal.jpg') + ') no-repeat;background-size:100% 100%;position:absolute;top:0;left:0" >'
    }
    return
  }
  if (onlyOnce) {
    getUserInfo(() => {
      setNetWork()
      new Vue({
        router,
        store,
        axios,
        render: h => h(App)
      }).$mount("#app");
      onlyOnce = false
    },()=>{
      clearTimeout(appTimer)
      appTimer = null
      appTimer = setTimeout(()=>{
        initVueApp()
      },10000)
    })
  }
}



