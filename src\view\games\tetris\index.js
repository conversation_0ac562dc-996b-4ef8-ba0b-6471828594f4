// import store from "@/store/index";
//
// import '@/unit/const';
// // import '@/control';
// import { subscribeRecord } from '@/unit';
// subscribeRecord(store); // 将更新的状态记录到localStorage


import '@/unit/const';
import control from '@/control';
import store from "@/store/index";
import { subscribeRecord } from '@/unit';

import Next from '@/components/next/index.vue'
import Music from '@/components/music/index.vue'
import Pause from '@/components/pause/index.vue'
import Number from '@/components/number/index.vue'
import Point from '@/components/point/index.vue'
import Logo from '@/components/logo/index.vue'
import Keyboard from '@/components/keyboard/index.vue'
import Matrix from '@/components/matrix/index.vue'
import { mapState } from 'vuex'
import { transform, lastRecord, speeds, i18n, lan } from '@/unit/const'
import { visibilityChangeEvent, isFocus } from '@/unit/'
import states from '@/control/states'


subscribeRecord(store); // 将更新的状态记录到localStorage

import { recordGamePoints, GetPointsHigh } from "@/api/index";

export default {
    name:'tetris',
    created() {
        //设置页面左上角标题
        this.$store.dispatch('index/setMainTitle','俄罗斯方块')

        subscribeRecord(store); // 将更新的状态记录到localStorage
        // 开始执行游戏按键监听
        control.start()
        this.getMaxPoints()
    },
    mounted() {
        states.start()
        this.render()
        window.addEventListener('resize', this.resize.bind(this), true)
        this.$store.state.lock = false
        this.$nextTick(()=>{
            this.$store.dispatch('index/setFocusDom', null);
        })


        this.fuc.KeyboardEvents({
            esc:()=>{
                this.$store.state.lock = true
                localStorage.removeItem('VUE_TETRIS')
                history.go(-1)
            }
        })



    },
    destroyed() {
        // 移除游戏按键监听
        control.end()

        states.overStart()
        this.$store.state.lock = true
        localStorage.removeItem('VUE_TETRIS')
    },
    watch:{
      '$store.state.points'() {  // 分数
          this.recordPoints(this.$store.state.points)
      }
    },
    data() {
        return {
            size: {},
            w: document.documentElement.clientWidth,
            h: document.documentElement.clientHeight,
            filling: '',
        }
    },
    components: {
        Next,
        Music,
        Pause,
        Number,
        Point,
        Logo,
        Keyboard,
        Matrix
    },
    computed: {
        pContent() {
            return this.cur ? i18n.cleans[lan] : i18n.startLine[lan]
        },
        level: () => i18n.level[lan],
        nextText: () => i18n.next[lan],
        ...mapState([
            'matrix',
            'keyboard',
            'music',
            'pause',
            'next',
            'cur',
            'speedStart',
            'speedRun',
            'startLines',
            'clearLines',
            'points',
            'max',
            'reset',
            'drop'
        ])
    },
    methods: {
        getMaxPoints() {
            GetPointsHigh({
                fr_user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
                game_type: 1
            }).then(res=>{
                if (res.code == 200) {
                    this.$store.state.max = res.data.data[0].high_score
                }
            })
            .catch(err=>{
                this.$store.state.max = 0
            })
        },
        recordPoints(points) {
            recordGamePoints({
                fr_user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
                game_type: 1,
                high_score: points
            }).then(res=>{

            })
        },
        render() {
            let filling = 0
            const size = (() => {
                const w = this.w
                const h = this.h
                const ratio = h / w
                let scale
                let css = {}
                if (ratio < 1.5) {
                    scale = h / 960
                } else {
                    scale = w / 640
                    filling = (h - 960 * scale) / scale / 3
                    css = {
                        'padding-top': Math.floor(filling) + 42 + 'px',
                        'padding-bottom': Math.floor(filling) + 'px',
                        'margin-top': Math.floor(-480 - filling * 1.5) + 'px'
                    }
                }
                css[transform] = `scale(${scale})`
                return css
            })()
            this.size = size
            this.start()
            this.filling = filling
        },
        resize() {
            this.w = document.documentElement.clientWidth
            this.h = document.documentElement.clientHeight
            this.render()
        },
        start() {
            if (visibilityChangeEvent) {
                // 将页面的焦点变换写入store
                document.addEventListener(
                    visibilityChangeEvent,
                    () => {
                        states.focus(isFocus())
                    },
                    false
                )
            }

            if (lastRecord) {
                // 读取记录
                if (lastRecord.cur && !lastRecord.pause) {
                    // 拿到上一次游戏的状态, 如果在游戏中且没有暂停, 游戏继续
                    const speedRun = this.$store.state.speedRun
                    let timeout = speeds[speedRun - 1] / 2 // 继续时, 给予当前下落速度一半的停留时间
                        // 停留时间不小于最快速的速度
                    timeout =
                        speedRun < speeds[speeds.length - 1] ?
                        speeds[speeds.length - 1] :
                        speedRun
                    states.auto(timeout)
                }

                if (!lastRecord.cur) {
                    states.overStart()
                }
            } else {
                states.overStart()
            }
        }
    }
}