<template>
  <div class="appointment">
    <div class="container">
      <div class="left">
        <div
          class="messageCenterList scrollParent"
          v-if="this.leftList.length > 0"
        >
          <div class="leftList" :style="{ top: '0rem' }">
            <div
              class="messageItem"
              :ref="item.ref"
              v-for="(item, index) in leftList"
              :key="index"
            >
              <div
                class="item_user"
                :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }"
              >
                <div class="content">
                  <div class="name">
                    <div class="doc_name">{{ item.date }}</div>
                    <div class="doc_level">{{ item.time_range }}</div>
                    <div class="doc_level">{{ item.yy_week }}</div>
                    <div class="doc_level">费用：{{ item.gh_fee }}元</div>
                  </div>
                </div>
              </div>
              <div class="choose_tip" v-if="item.yy_status === 2">
                <div>已预约</div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_content" v-else>{{ info }}</div>
      </div>
      <div class="right scrollParent">
        <div class="rightList" ref="ref" :style="{ top: '0rem' }">
          <div class="rightItem">
            <img :src="list.img" alt="" />
            <div class="info">
              <div class="name">{{ list.doctor_menzhen_name }}</div>
              <div class="tag">{{ list.doctor_menzhen_tag }}</div>
              <div class="area">{{ ks }}</div>
            </div>
          </div>
          <div class="introduce">
            <div class="title">医生介绍</div>
            <div class="content">
              <p>{{ list.doctor_menzhen_desc }}</p>
            </div>
          </div>
        </div>
      </div>
      <el-dialog
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        title="订单信息"
      >
        <!--发起预约-->
        <div class="message_box sendCall" v-if="popupModule == 1">
          <div class="message_content">
            <div class="title">请确认预约信息</div>
            <div class="time">
              预约时间：{{ leftList[leftNums] && leftList[leftNums].date }}
              {{ leftList[leftNums] && leftList[leftNums].morning_afternoon }}
              {{ leftList[leftNums] && leftList[leftNums].time_range }}
            </div>
            <div class="name">
              患者姓名：{{
                patient.slice(0, -1).replace(/./g, '*') + patient.slice(-1)
              }}
            </div>
            <div class="hospital">预约医院：{{ hospital_name }}</div>
            <div class="department">
              预约科室：{{ leftList[leftNums] && leftList[leftNums].yyks }}
            </div>
            <div class="doctor">
              预约医生：{{
                leftList[leftNums] && leftList[leftNums].doctor_name
              }}
            </div>
            <div class="fee">
              挂号费用：{{ leftList[leftNums] && leftList[leftNums].gh_fee }}元
            </div>
          </div>
        </div>

        <!--取消预约-->
        <div class="message_box cancel" v-if="popupModule == 2">
          <div>是否要取消预约?</div>
        </div>

        <div
          class="message_box"
          style="
            text-align: center;
            font-size: 0.37rem;
            justify-content: center;
          "
          v-html="popupMessage"
          v-if="popupModule == 3"
        ></div>

        <div class="popupBtnList">
          <div
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
          >
            {{ item.text }}
          </div>
        </div>
      </el-dialog>
    </div>
    <div class="address">
      <div class="name">{{ title }}-{{ ks }}({{ addr }})</div>
    </div>
    <div class="done"><div class="item">信息介绍</div></div>
  </div>
</template>
      
<script>
import { getAppointmentList, submitAppointment } from '@/api/index'

export default {
  name: 'appointment',
  components: {},
  data() {
    return {
      patient_name: '', //患者姓名
      hospital_name: '', // 预约医院
      list: {},
      addr: '',
      title: '',
      ks: '',
      info: '',
      patient: '',
      leftList: [],
      rightList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      firstShow: true,
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '预约挂号')
  },
  computed: {},
  watch: {},
  mounted() {
    this.list = JSON.parse(this.$route.query.list)
    this.title = this.$route.query.title
    this.addr = this.$route.query.addr
    this.ks = this.$route.query.ks
    this.patient = this.$route.query.patient

    this.getData()

    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          return
        }
        // 左侧
        if (this.nextNums == -1) {
          if (this.leftNums < this.leftList.length - 1) {
            if (this.leftList[this.leftNums + 3]) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums += 3
              this.leftList[this.leftNums].ref = 'active'
            } else {
              if (
                this.leftNums <
                  this.leftList.length - (this.leftList.length % 3) &&
                this.leftList.length % 3 != 0
              ) {
                this.leftList[this.leftNums].ref = ''
                this.leftNums = this.leftList.length - 1
                this.leftList[this.leftNums].ref = 'active'
              }
            }
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 右侧
        if (this.nextNums > -1) {
          const listEl = this.$refs.ref
          const scrollBar =
            this.$refs.ref.parentNode.querySelector('.scrollBar')
          // console.log('可视区域', listEl.parentNode.clientHeight)
          // console.log('元素距离顶部', listEl.offsetTop)
          // console.log('元素高度', listEl.clientHeight)
          // console.log('下拉高度', listEl.parentNode.offsetTop)
          if (listEl && scrollBar) {
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight
            const radio = listHeight / listVisHeight

            const potentialNewTop = currentTop - 100 // 预计的新top值
            const scrollNewTop = currentScrollTop + 100 / radio
            const maxScrollableHeight =
              listEl.clientHeight - listEl.parentNode.clientHeight // 最大可滚动高度
            const maxSrcollBarHeight =
              scrollBar.parentNode.clientHeight - scrollBar.clientHeight
            if (listVisHeight < listHeight) {
              // 将元素的top值与最大可滚动高度进行比较，以确定是否已经到达或超过了底部
              if (-potentialNewTop < maxScrollableHeight) {
                listEl.style.top = `${potentialNewTop}px`
              } else {
                // 已经到达或超过底部，调整top以确保内容底部和容器底部对齐
                listEl.style.top = `${-maxScrollableHeight - 50}px`
              }
            } else {
              return
            }

            if (scrollNewTop < maxSrcollBarHeight) {
              scrollBar.style.top = `${scrollNewTop}px`
            } else {
              scrollBar.style.top = `${maxSrcollBarHeight}px`
            }
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          return
        }
        // 左侧
        if (this.nextNums == -1) {
          if (this.leftNums > 0 && this.leftNums - 3 >= 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums -= 3
            this.leftList[this.leftNums].ref = 'active'
          }
        }
        // 右侧
        if (this.nextNums > -1) {
          const listEl = this.$refs.ref
          const scrollBar =
            this.$refs.ref.parentNode.querySelector('.scrollBar')
          if (listEl && scrollBar) {
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight
            const radio = listHeight / listVisHeight
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            const potentialNewTop = currentTop + 100 // 预计的新top值
            const scrollNewTop = currentScrollTop - 100 / radio

            // 检查是否已经到达最顶部
            if (potentialNewTop >= 0) {
              listEl.style.top = `0px` // 直接设置为0，确保不会超过顶部
              scrollBar.style.top = `0px`
            } else {
              listEl.style.top = `${potentialNewTop}px`
              scrollBar.style.top = `${scrollNewTop}px`
            }
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左侧
        if (this.nextNums == -1) {
          if (this.leftNums % 3 != 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
          }
        }
        // 在右侧
        else if (this.nextNums > -1) {
          if (this.leftList.length > 0) {
            const listEl = this.$refs.ref
            const scrollBar =
              this.$refs.ref.parentNode.querySelector('.scrollBar')
            if (listEl && scrollBar) {
              let currentTop = parseInt(listEl.style.top, 10)
              const potentialNewTop = currentTop + 100 // 预计的新top值

              // 检查是否已经到达最顶部
              if (potentialNewTop >= 0) {
                listEl.style.top = `0px` // 直接设置为0，确保不会超过顶部
                scrollBar.style.top = `0px`
              } 
            }
            this.$refs.active = []
            this.leftList[this.leftNums].ref = 'active'
            this.nextNums = -1
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左侧
        if (this.nextNums == -1) {
          if (this.leftNums < this.leftList.length - 1) {
            if (this.leftNums % 3 != 2) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums++
              this.leftList[this.leftNums].ref = 'active'
            } else {
              this.leftList[this.leftNums].ref = ''
              this.$refs.active.splice(0, 1, this.$refs.ref)
              this.nextNums = this.leftNums
            }
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        if (this.nextNums == -1) {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].fuc(
              this.leftList[this.leftNums]
            )
            return
          }
          if (this.leftList[this.leftNums]) {
            this.popupBtnList = [
              {
                text: '关闭',
                ref: '',
                fuc: () => {
                  this.dialogVisible = false
                },
              },
              {
                text: '确认预约',
                ref: '',
                fuc: this.senOrder,
              },
            ]
            this.dialogVisible = true
            this.popupModule = 1
            this.leftList[this.leftNums].ref = ''
            this.popupBtnList[this.popupBtnNums].ref = 'active'
          }
        }
        return
      },
      esc: () => {
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.leftList[this.leftNums].ref = 'active'
          this.dialogVisible = false
          return
        }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        this.popupBtnNums = 0
        this.popupModule = 1
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#b36371'
      })
    },
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState', true)
      // 获取可预约时间列表
      getAppointmentList({
        mac: sessionStorage.getItem('MAC'),
        card_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].card_id_md5,
        ksbh: this.$route.query.ksbh,
        hospital_id: this.$route.query.hospital_id,
        gh_type: this.list.gh_type,
        ysgh: this.list.ysgh,
      }).then(
        (res) => {
          this.$store.dispatch('app/setLoadingState', false)
          if (res.code == 200) {
            let list = JSON.parse(JSON.stringify(res.data))
            list.map((item) => {
              item.ref = ''
            })
            this.leftList = list

            this.hospital_name = this.$route.query.title
            if (this.leftList.length > 0) {
              this.nextNums = -1
              this.leftList[this.leftNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            } else {
              this.nextNums = this.leftNums
            }
          }
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          if (error.response) {
            this.info = error.response.data.msg
          }
        }
      )
    },
    senOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      submitAppointment({
        mac: sessionStorage.getItem('MAC'),
        card_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].card_id_md5,
        ksbh: this.$route.query.ksbh,
        hospital_id: this.$route.query.hospital_id,
        gh_type: this.list.gh_type,
        ysgh: this.list.ysgh,
        time_id: this.leftList[this.leftNums].time_id,
        morning_afternoon_flag:
          this.leftList[this.leftNums].morning_afternoon_flag,
        date: this.leftList[this.leftNums].date,
        time_range: this.leftList[this.leftNums].time_range,
        yyks: this.leftList[this.leftNums].yyks,
        doctor_name: this.leftList[this.leftNums].doctor_name,
      }).then(
        (res) => {
          if (res.code == 200) {
            this.popupModule = 3
            this.leftList[this.leftNums].ref = ''
            setTimeout(() => {
              this.getData()
            }, 300)
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                },
              },
            ]
            this.popupMessage = '预约成功！<br/>请及时赴约！'
            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.leftList[this.leftNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br/>预约失败!<br>'
          if (
            error.response &&
            error.response.data &&
            error.response.data.msg
          ) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
      <style lang='less' scoped>
.appointment {
  .address {
    display: flex;
    position: absolute;
    top: 1.6rem;
    left: 1.45rem;
    .name {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .name::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .done {
    position: absolute;
    top: 1.6rem;
    right: 3.6rem;
    .item {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .item::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .container {
    height: 7rem;
    overflow: hidden;
    display: flex;
    grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
    gap: 0.16rem;
    font-size: 0.2rem;
    color: #e7e7ef;
    .noData {
      div {
        font-size: 0.5rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -200%);
        letter-spacing: 0.05rem;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
      }
    }
    .aside {
      width: 2rem;
      height: 0.7rem;
      text-align: center;
      line-height: 0.7rem;
      margin-bottom: 0.2rem;
      background: #343d74;
      border-radius: 0.2rem;
      position: relative;
      font-weight: bold;
      transition: all 0.3s;
      letter-spacing: 0.05rem;
      text-indent: 0.05rem;
      font-size: 0.34rem;
      color: #e7e7ef;
      text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
    }
    .left {
      width: 12.2rem;
      height: 7rem;
      margin-left: 0;

      .messageCenterList {
        width: 12.2rem;
        height: 100%;
        position: relative;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          // width: 83.6%;
          border-radius: 0.3rem;
          position: absolute;
          // left: 0.8rem;
          transition: all 0.2s;
          .messageItem {
            width: 2.8rem;
            height: 2.31rem;
            // padding: 0.5rem;
            padding: 0.4rem 0.5rem 0.5rem 0.5rem;
            margin-bottom: 0.59rem;
            position: relative;
            margin-right: 0.28rem;
            background-size: 100% 100% !important;
            background: #262954;
            border-radius: 0.3rem;
            overflow: hidden;
            .item_user {
              display: flex;
              // height: 100%;
              justify-content: center;
              align-items: center;
              .content {
                display: flex;
                height: initial !important;
                width: initial !important;
                margin: initial !important;
                .name {
                  .doc_name,
                  .doc_level {
                    font-weight: bold;
                    color: #fff;
                    font-size: 0.36rem;
                    margin-bottom: 0.15rem;
                    text-align: center;
                  }
                }
              }
              .introduce {
                height: 0.9rem;
                margin-top: 0.1rem !important;
                margin-bottom: 0 !important;
                font-size: 0.22rem !important;
                font-weight: 500 !important;
                line-height: 0.3rem;
                letter-spacing: 0.03rem;
                -webkit-line-clamp: 3 !important;
                background-image: initial !important;
                -webkit-text-fill-color: #bddb6c !important;
              }
            }
            .choose_tip {
              width: 4rem;
              height: 0.37rem;
              position: relative;
              // top: 0.1rem;
              right: 0.6rem;
              div {
                width: 100%;
                height: 100%;
                font-size: 0.22rem;
                background: #3cc92d;
                border-radius: 0.05rem;
                position: absolute;
                text-align: center;
                line-height: 0.35rem;
                font-weight: bold;
                color: #fff;
                letter-spacing: 0.03rem;
              }
            }
          }
          .messageItem:nth-child(3n + 3) {
            margin-right: 0;
          }
        }
      }
      .no_content {
        position: absolute;
        line-height: 7rem;
        left: 6rem;
        // width: 4rem;
        // height: 7rem;
        text-align: center;
        font-size: 0.4rem;
      }
    }
    .right {
      width: 4.2rem;
      height: 7rem;
      overflow: hidden;
      .rightList {
        position: relative;
        overflow-y: auto;
        margin-left: 0.15rem;
        padding: 0.1rem 0.1rem 0 0.1rem;
        border-radius: 0.24rem;
        transition: all 0.3s;
        .rightItem {
          display: flex;

          img {
            width: 1.45rem;
            height: 1.75rem;
            border-radius: 0.14rem;
            object-fit: cover;
          }
          .info {
            margin-left: 0.3rem;
            .name {
              font-size: 0.3rem;
              font-weight: bold;
              color: #fff;
            }
            .tag,
            .area {
              font-size: 0.3rem;
              color: #fff;
            }
          }
        }
        .introduce {
          // height: 4.7rem;
          // overflow: hidden;
          margin-top: 0.2rem;
          .title,
          .content {
            font-size: 0.3rem;
            font-weight: bold;
            color: #fff;
          }
          .content {
            width: initial !important;
            height: initial !important;
            // overflow: hidden;
            text-overflow: ellipsis;
            white-space: wrap;
            // line-height: 4.5rem;
          }
        }
      }
    }

    .tab_list {
      width: 4.2rem;
      height: 6.65rem;
      position: absolute;
      top: 0.69rem;
      right: 0.3rem;
      overflow: hidden;
      .no_content {
        margin: auto;
        line-height: 6.2rem;
        width: 4rem;
        height: 0.8rem;
        text-align: center;
        font-size: 0.4rem;
      }
    }
  }
}
</style>
      <style lang="less">
.appointment {
  // position: relative;

  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    background: repeating-linear-gradient(to right, #c98693, #a95361);
    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        align-items: center;
        .message_content {
          margin-top: 0.3rem;
          .title,
          .time,
          .name,
          .hospital,
          .department,
          .doctor,
          .fee {
            font-size: 0.35rem;
            font-weight: bold;
            padding-bottom: 0.12rem !important;
          }
          .time {
            margin-top: 0.2rem;
          }
          .time,
          .fee {
            color: #00cc00;
          }
        }
      }
      .cancel {
        flex-direction: column;
        div {
          text-align: center;
        }
        div:nth-child(2) {
          margin-top: 0.1rem;
          color: red;
          line-height: 0.48rem;
        }
        //align-items: normal !important;
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          background: repeating-linear-gradient(to right, #c98693, #a95361);
        }
      }
    }
  }
  .container {
    .right {
      .scroll {
        top: 2.35rem !important;
        left: 18rem !important;
      }
    }
  }
}
</style>
      