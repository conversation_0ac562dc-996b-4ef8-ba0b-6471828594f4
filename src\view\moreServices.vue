<template>
  <div class='moreServices'>
    <div class="left_more scrollParent">
      <div class="leftList" :style="{top: '0rem'}">
        <div class="leftItem" :ref="item.ref" v-for="(item,index) in leftList" :key="index">
          <div>
            {{item.title}}
          </div>
          <div class="badge" v-if="item.badge"></div>
        </div>
      </div>
    </div>

    <div class="right_more">
      <div class="item_list scrollParent" :key="leftNums">
        <div class="rightList" :key="leftNums" :style="{top: '0rem'}">
          <div class="friendsItem" :ref="item.ref" v-for="(item,index) in rightList" :key="index">
              <img v-if="item.show" @error="defImg(index)" :src="item.icon_more" alt="">
              <div v-else class="item_user" :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }">
                <div>
                  {{item.title}}
                </div>
              </div>
            <div class="remark"  v-if="item.remark">
              {{item.remark}}
            </div>
          </div>
        </div>
      </div>

      <el-dialog
          :visible.sync="dialogVisible"
          :show-close="false"
          :close-on-click-modal="false"
          @opened="popupOpend"
          @close="popupClose"
      >
        <!--呼叫弹窗-->
        <div class="callMyFriend" v-if="popupModule == 1">
          <div class="myFriendsBtn" :ref="item.ref" v-for="(item,index) in popupBtnList" :key="index">
            {{ item.text}}
          </div>
        </div>

        <!--消息弹窗-->
        <div class="popupMessage" v-if="popupModule == 2">
          <div class="popupMessage" v-html="popupMessage"></div>
          <div class="popupMessageBtn" :ref="item.ref" v-for="(item,index) in popupBtnList" :key="index">
            {{ item.text}}
          </div>
        </div>


      </el-dialog>

    </div>
  </div>
</template>

<script>

import {GetIndexICon} from "@/api/index";

export default {
  name:'moreServices',
  components: {
  },
  data() {
    return {
      timer: null,
      leftList:[],
      leftNums: 0,
      nextNums: -1,   // -1  说明焦点在左侧   > -1  在右侧

      rightList:[],
      rightNums: 0,

      firstShow: true,

      dialogVisible: false,
      popupModule: 1,   // 1、好友弹窗  2、消息提示
      popupMessage:'',
      popupBtnNums: 0,
      popupBtnList:[
        {
          text: '呼叫好友',
          ref:'',
          fuc: this.callMyfriend
        },
        {
          text: '删除好友',
          ref:'',
          fuc: this.delectMyfriend
        },
      ],
      bgList:[
        {
          color:'rgba(89,167,216,1)',
          bg: require('@/assets/1_bg_pic.png')
        },{
          color:'rgba(210,126,126,1)',
          bg: require('@/assets/2_bg_pic.png')
        },{
          color:'rgba(90,184,148,1)',
          bg: require('@/assets/3_bg_pic.png')
        },{
          color:'rgba(141,133,218,1)',
          bg: require('@/assets/4_bg_pic.png')
        },{
          color:'rgba(108,151,206,1)',
          bg: require('@/assets/5_bg_pic.png')
        },{
          color:'rgba(198,164,100,1)',
          bg: require('@/assets/6_bg_pic.png')
        },
      ],

    };
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle','更多服务')
    if (sessionStorage.getItem('serviceFocus')) {
      this.leftNums=Number(sessionStorage.getItem('serviceFocus').split(',')[0])
      this.rightNums=Number(sessionStorage.getItem('serviceFocus').split(',')[1])
      this.nextNums = this.leftNums
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.getData()

    this.fuc.KeyboardEvents({
      down:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length-1) {
            this.popupBtnList[this.popupBtnNums].ref = ""
            this.popupBtnNums ++
            this.popupBtnList[this.popupBtnNums].ref = "active"
            this.$nextTick(()=>{
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
            })
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
            if (this.rightNums < this.rightList.length - 1) {
              if (this.rightList[this.rightNums + 3]) {
                this.rightList[this.rightNums].ref = ""
                this.rightNums += 3
                this.rightList[this.rightNums].ref = "active"
              } else {
                // console.log(this.rightNums)
                // console.log(this.rightList.length -(this.rightList.length % 3))
                if (this.rightList.length % 3 != 0 && this.rightNums  < (this.rightList.length - this.rightList.length % 3)) {
                  this.rightList[this.rightNums].ref = ""
                  this.rightNums = this.rightList.length - 1
                  this.rightList[this.rightNums].ref = "active"
                }
              }
          }
        }
        // 左侧
        else {
          if (this.leftNums < this.leftList.length -1) {
            this.rightNums = 0
            this.rightList = []
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ""
            this.leftNums ++
            this.leftList[this.leftNums].ref = "active"
            this.$nextTick(()=>{
              this.$refs.active[0].classList.add('select')
            })
            if (this.leftList[this.leftNums].fuc) {
              this.leftList[this.leftNums].fuc(this.leftList[this.leftNums])
            }
          }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      up:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ""
            this.popupBtnNums --
            this.popupBtnList[this.popupBtnNums].ref = "active"
            this.$nextTick(()=>{
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
            })
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
            if (this.rightList[this.rightNums - 3]) {
              this.rightList[this.rightNums].ref = ""
              this.rightNums -= 3
              this.rightList[this.rightNums].ref = "active"
            }
        }
        // 左侧
        else {
          if (this.leftNums > 0) {
            this.rightNums = 0
            this.rightList = []
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ""
            this.leftNums --
            this.leftList[this.leftNums].ref = "active"
            this.$nextTick(()=>{
              this.$refs.active[0].classList.add('select')
            })
            if (this.leftList[this.leftNums].fuc) {
              this.leftList[this.leftNums].fuc(this.leftList[this.leftNums])
            }
          }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      left:()=>{
        if (this.dialogVisible) {
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          if(this.rightNums % 3 == 0){
            this.rightList[this.rightNums].ref = ""
            this.leftList[this.nextNums].ref = "active"
            this.nextNums = -1
            // this.rightNums = 0
          } else {
            this.rightList[this.rightNums].ref = ""
            this.rightNums --
            this.rightList[this.rightNums].ref = "active"
          }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      right:()=>{
        if (this.dialogVisible) {
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1) {
            if (this.rightNums % 3 != 2) {
              this.rightList[this.rightNums].ref = ""
              this.rightNums ++
              this.rightList[this.rightNums].ref = "active"
            }
          }
        } else
            // 在左侧
        if (this.nextNums == -1 && this.rightList.length > 0) {
          this.leftList[this.leftNums].ref = ""
          this.nextNums = this.leftNums
          this.rightList[this.rightNums].ref = "active"
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      enter:()=>{
        if (this.dialogVisible) {  // 弹窗
          return
        }
        if (this.nextNums > -1 && this.rightList.length > 0) {
          // if (this.rightList[this.rightNums] && this.rightList[this.rightNums].redirect) {
            sessionStorage.setItem('serviceFocus',this.leftNums + ',' + this.rightNums)

            // if (this.rightList[this.rightNums].has_supplier) {
              sessionStorage.setItem('redirectInfo', JSON.stringify(this.rightList[this.rightNums]))
              GetIndexICon({
                fid: this.rightList[this.rightNums].id,
                user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
                home_id: this.$store.getters.getUserInfo.home_id
              })
              .then(res=>{
                if (res.code == 200 && res.data.length > 0) {
                  if (res.data.length > 1) {
                    sessionStorage.setItem('supplierList', JSON.stringify(res.data))
                    this.$nextTick(()=>{
                      this.$router.push({
                        path: './supplier'
                      })
                    })
                  } else {
                    sessionStorage.setItem('supplierInfo', JSON.stringify(res.data[0]))
                    this.$store.dispatch('index/setFocusDom', document.body);
                    this.$nextTick(()=>{
                      if (res.data[0].redirect) {
                        this.$router.push({
                          path: res.data[0].redirect
                        })
                      }
                    })
                  }
                }
              })
            // } else {
            //   this.$store.dispatch('index/setFocusDom', document.body);
            //   this.$nextTick(()=>{
            //     this.$router.push({
            //       path: this.rightList[this.rightNums].redirect
            //     })
            //   })
            // }
          // }
        }
      },
      esc:()=>{
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ""
          this.rightList[this.rightNums].ref = "active"
          this.dialogVisible = false
          return
        }
        this.$store.dispatch('index/setFocusDom', null);
        // history.go(-1)
        this.$router.push('/index')
      }
    })
  },
  methods: {
    defImg(index){
      this.rightList[index].show = false
      // let img = event.srcElement;
      // img.src = process.env.VUE_APP_API + '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png';
      // img.onerror = null; //防止闪图
    },
    popupClose() {
      if (this.leftNums == 0) {
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0]);
          document.getElementById('focus_border').style.borderColor = "#fff"
          this.popupBtnNums = 0
          this.popupModule = 1
        })
      }
    },
    popupOpend() {
      this.$nextTick(()=>{
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0]);
        document.getElementById('focus_border').style.borderColor = "#00CCFF"
      })
    },

    getData(){
      this.$store.dispatch('index/setFocusDom',this.$refs.active);
      GetIndexICon({
        // fid: JSON.parse(sessionStorage.getItem('indexIconList'))[Number(sessionStorage.getItem('indexFocus'))].id,
        fid: Number(sessionStorage.getItem('indexFocus')),
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        home_id: this.$store.getters.getUserInfo.home_id,
      })
      .then(res=>{
        if (res.code == 200) {
          let leftICon = JSON.parse(JSON.stringify(res.data))

          leftICon.map((item,index)=>{
            item.ref = ""
            item.fuc = this.getRightList
          })
          leftICon[this.leftNums].ref = "active"
          this.leftList = leftICon


          this.$nextTick(()=>{
            // if (!sessionStorage.getItem('serviceFocus')) {
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
            // }
            this.$refs.active[0].classList.add('select')
            this.leftList[this.leftNums].fuc(this.leftList[this.leftNums])
          })

        }
      })
    },
    // 右侧数据
    getRightList(item) {
      this.$store.dispatch('app/setLoadingState', true)
      this.rightList = []
      GetIndexICon({
        fid: item.id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        home_id: this.$store.getters.getUserInfo.home_id
      })
      .then(res=>{
        this.$store.dispatch('app/setLoadingState', false)
        if (res.code == 200) {
          let rightList =  JSON.parse(JSON.stringify(res.data))

          rightList.map(item=>{
            item.ref = ""
            item.show = false
            if (item.icon_more) {
              item.icon_more = item.icon_more.indexOf('http') > -1 ? item.icon_more : process.env.VUE_APP_API + item.icon
              item.show = true
            } else {
              item.show = false
            }
          })

          this.rightList = rightList




          // if (this.firstShow && this.rightList.length > 0) {
          if (sessionStorage.getItem('serviceFocus')) {
            this.nextNums = this.leftNums
            this.leftList[this.leftNums].ref = ""
            this.rightList[this.rightNums].ref = "active"
            this.$nextTick(()=>{
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
              this.fuc.setScroll()
            })
            this.firstShow = false
            sessionStorage.removeItem('serviceFocus')
          } else {
            this.rightNums = 0
            this.nextNums = -1
          }

          this.$nextTick(()=>{
            this.fuc.setScroll()
          })
        }
      })
      .catch(err=>{
        this.$store.dispatch('app/setLoadingState', false)
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      })


    }
  },
  destroyed() {

  },
  beforeDestory() {
  },
}
</script>
<style lang='less' scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
.moreServices {
  height: 7rem;
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
  gap: 0.16rem;
  font-size: 0.2rem;
  color: #E7E7EF;
  .left_more {
    width: 3.2rem;
    border-radius: 0.2rem;
    height: 7rem;
    position: relative;
    .leftList {
      width: 94%;
      text-align: center;
      position: absolute;
      transition: all 0.3s;
      .leftItem {
        //height: 0.8246rem;
        height: 0.826rem;
        //height: 2.195rem;
        line-height: 0.92rem;
        margin-bottom: 0.2rem;
        background: #343D74;
        border-radius: 0.2rem;
        position: relative;
        font-weight: bold;
        transition: all 0.3s;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        //font-size: 0.34rem;
        font-size: 0.38rem;
        color: #E7E7EF;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0,0,0,0.6);
        .badge {
          width: 0.15rem;
          height: 0.15rem;
          border-radius: 50%;
          background: #FF5F5F;
          position: absolute;
          top: 50%;
          right: 0.2rem;
          transform: translateY(-50%);
        }
      }
      .select {
        background: #89A7FF;
        transition: all 0.3s;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(102,129,218,0.6);
      }
    }
  }
  .right_more {
    width: 12.95rem;
    height: 7rem;
    margin-left: 0;
    //padding-left: 0.1rem;
    //margin-left: 0.05rem;
    .item_title {
      height: 0.6rem;
      line-height: 0.6rem;
      background: #ccc;
      font-size: 0.28rem;
      font-weight: bold;
      color: #000;
      padding-left: 0.2rem;
    }
    .item_list {
      //height: calc(100% - 0.76rem);
      height: 100%;
      //background: #ccc;
      border-radius: 0.52rem;
      overflow: hidden;
      position: relative;
      .rightList {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(3, 1fr); /* 每行显示两个元素 */
        //gap: 0.16rem;
        gap: 0;
        position: absolute;
        top: 0;
        transition: all 0.2s;
        .friendsItem {
          border-radius: 0.52rem;
          height: 3.3603rem;
          overflow: hidden;
          color: #000;
          font-weight: bold;
          position: relative;
          margin-bottom: 0.28rem;
          margin-right: 0.28rem;
          background-size: 100% 100% !important;
          .remark {
            position: absolute;
            top: 0.2rem;
            right: 0.4rem;
            z-index: 99;
            color: #fff;
            height: 0.6rem !important;
            line-height: 0.6rem !important;
            font-size: 0.3rem;
            letter-spacing: 0.03rem;
            text-indent: -0.03rem;
          }
          img {
            display: block;
            width: 100%;
            height: 100%;
          }
          .typeIcon {
            display: block;
            width: 0.75rem;
            height: 0.6rem;
            position: absolute;
            left: 0.5rem;
            top: 0.46rem;
          }
          .typeIcon_phone {
            top: 0.35rem;
            width: 0.42rem;
            height: 0.7rem;
          }
          .item_user {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #eee;

            div {
              line-height: 0.6rem;
              //color: #E7E7EF;
              color: #888;
            }
            div:nth-child(1) {
              font-size: 0.45rem;
              letter-spacing: 0.05rem;
              text-indent: -0.05rem;
            }
            div:nth-child(2) {
              font-size: 0.3rem;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}

</style>
<style lang="less">
.moreServices {
  .el-dialog {
    width: fit-content;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.16rem;
    .el-dialog__header {
      display: none;
    }
    .el-dialog__body {
      .callMyFriend {
        .myFriendsBtn {
          width: 20vw;
          height: 0.6rem;
          line-height: 0.6rem;
          background: #00CCFF;
          color: #fff;
          border-radius: 0.16rem;
          margin-bottom: 0.2rem;
          text-align: center;
          font-weight: bold;
          font-size: 0.24rem;
        }
        .myFriendsBtn:last-child {
          margin-bottom: 0;
        }
      }
      .popupMessage {
        .popupMessage {
          line-height: 0.4rem;
          text-align: center;
          font-size: 0.24rem;
          margin-bottom: 0.2rem;
        }
        .popupMessageBtn {
          width: 20vw;
          height: 0.6rem;
          line-height: 0.6rem;
          background: #00CCFF;
          font-weight: bold;
          text-align: center;
          font-size: 0.24rem;
          color: #fff;
          border-radius: 0.16rem;
        }
      }


    }
  }

}


</style>
