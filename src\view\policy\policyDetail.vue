<template>
  <div class="policyDetail">
    <div class="container">
      <div class="left scrollParent" :key="this.leftList.length">
        <div class="leftList" :style="{ top: '0rem' }">
          <div
            class="leftItem"
            :ref="item.ref"
            v-for="(item, index) in leftList"
            :key="index"
          >
            <div class="title">
              {{ item.title }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getInfoDetail } from '@/api/index'

export default {
  name: 'policyDetail',
  components: {},
  data() {
    return {
      leftList: [],
      rightList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      content: '',
      video_url: '',
    }
  },
  created() {
    //设置页面左上角标题
    this.$nextTick(() => {
      this.$store.dispatch('index/setMainTitle', this.$route.query.title)
    })
  },
  computed: {},
  watch: {},
  mounted() {
    this.getData()
    if (sessionStorage.getItem('indexPolicyDetail')) {
      this.leftNums = Number(sessionStorage.getItem('indexPolicyDetail'))
      sessionStorage.removeItem('indexPolicyDetail')
    }
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.leftNums < this.leftList.length - 1) {
          this.$refs.active[0].classList.remove('select')
          this.leftList[this.leftNums].ref = ''
          this.leftNums++
          this.leftList[this.leftNums].ref = 'active'
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
          this.$refs.active[0].classList.add('select')
        })
      },
      up: () => {
        if (this.leftNums > 0 && this.leftNums - 1 >= 0) {
          this.$refs.active[0].classList.remove('select')
          this.leftList[this.leftNums].ref = ''
          this.leftNums--
          this.leftList[this.leftNums].ref = 'active'
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
          this.$refs.active[0].classList.add('select')
        })
      },
      left: () => {},
      right: () => {},
      enter: () => {
        if (this.leftList[this.leftNums]) {
          sessionStorage.setItem('indexPolicyDetail', this.leftNums)

          this.$nextTick(() => {
            this.$router.push({
              path: '/detailsPage',
              query: {
                id: this.leftList[this.leftNums].id,
                title: this.leftList[this.leftNums].title,
              },
            })
          })
        }
      },
      esc: () => {
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      // 获取信息大全列表详情
      getInfoDetail({
        classification_id: this.$route.query.classification_id,
        user_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].id,
      }).then((res) => {
        if (res.code == 200) {
          let leftClone = JSON.parse(JSON.stringify(res.data))
          leftClone.map((item) => {
            item.ref = ''
          })
          this.leftList = leftClone

          if (this.leftList.length > 0) {
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
              this.$refs.active[0].classList.add('select')
            })
          }
        }
      })
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.policyDetail {
  height: 7rem;

  .container {
    height: 7rem;
    overflow: hidden;
    font-size: 0.2rem;
    color: #e7e7ef;

    .left {
      width: 15.7rem;
      height: 7rem;
      margin-left: 0.5rem;
      position: relative;

      .leftList {
        width: 94%;
        text-align: center;
        position: absolute;
        transition: all 0.3s;

        .leftItem {
          width: 100%;
          text-align: left;
          height: 0.9003rem;
          line-height: 0.7rem;
          // margin-top: 0.2rem;
          margin-bottom: 0.321rem;
          margin-left: 0.5rem;
          // background: #343d74;
          border-radius: 0.2rem;
          position: relative;
          font-weight: bold;
          transition: all 0.3s;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          font-size: 0.34rem;
          color: #b4c0ff;
          text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);

          .title {
            width: 100%;
            // padding-left: 0.5rem;
            text-indent: 0.8rem;
            position: absolute;
            font-size: 0.38rem;
            font-weight: bold;
            line-height: 0.95rem;
            letter-spacing: 0.03rem;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .title::before {
            content: '';
            width: 0.14rem;
            height: 0.14rem;
            border-radius: 50%;
            background: #6699ff;
            position: absolute;
            top: 0.405rem;
            left: 0.2rem;
            // margin-top: -0.07rem;
          }
        }

        .select {
          color: #fff;
          background: #c09165;
          transition: all 0.3s;
          text-shadow: 0.03rem 0.03rem 0.01rem rgba(102, 129, 218, 0.6);
        }

        .select::before {
          content: '';
          width: 0.14rem;
          height: 0.14rem;
          border-radius: 50%;
          background: #fff;
          position: absolute;
          top: 0.405rem;
          left: 0.2rem;
          // margin-top: -0.028rem;
          z-index: 9;
        }
      }
    }
  }
}
</style>
