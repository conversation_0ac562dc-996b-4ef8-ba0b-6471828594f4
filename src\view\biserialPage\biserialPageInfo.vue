<template>
  <div class="biserialPageInfo">
    <div class="container">
      <div class="left scrollParent" v-if="leftList.length > 0">
        <div class="leftList" :ref="item.ref" v-for="(item,index) in leftList" :key="index">
          <img :src="require('../../assets/video_img.png')" alt="">
        </div>
      </div>

      <div class="right scrollParent" ref="refRight" :style="{ width: leftList.length > 0 ?'12rem' : '16rem'}">
        <div class="list" ref="refList" :style="{ top: '0rem' }">
          <div class="content" v-html="content"></div>
        </div>
      </div>
    </div>
    <div class="notice" v-if="supplierInfo && supplierInfo.telephone">
      <img :src="require('../../assets/phone.png')" alt="">
      客服热线: {{supplierInfo.telephone}}
    </div>
  </div>
</template>
      
<script>
import { getBiserialPageItemInfo } from '@/api/index'
import media from "@/utils/mediaPlayer";
import store from "@/store";

export default {
  name: 'biserialPageInfo',
  components: {},
  data() {
    return {
      thisOrder: null,
      statusList:[],
      leftList: [],
      rightList: [],

      nextNums: 0, // -1  说明焦点在左侧   > -1  在右侧

      leftNums: 0,
      rightNums: 0,
      orderNums: 0,

      title: '',
      content: '',
      supplierInfo: null,
      playTimer: null
    }
  },
  created() {

    if (localStorage.getItem('statusList')) {
      this.statusList = JSON.parse(localStorage.getItem('statusList'))
    }
  },
  computed: {},
  watch: {

  },
  mounted() {
    this.supplierInfo = JSON.parse(sessionStorage.getItem('supplierInfo'))
    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
          return
        }
        if (this.nextNums > -1) {
          const listEl = this.$refs.refList
          const scrollBar = this.$refs.refList.parentNode.querySelector('.scrollBar')

          if (listEl && scrollBar) {
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight

            const radio = listHeight / listVisHeight

            const potentialNewTop = currentTop - 150 // 预计的新top值

            const scrollNewTop = currentScrollTop + 150 / radio
            const maxScrollableHeight = listEl.clientHeight - listEl.parentNode.clientHeight // 最大可滚动高度
            const maxSrcollBarHeight = scrollBar.parentNode.clientHeight - scrollBar.clientHeight
            if (listVisHeight < listHeight) {
              // 将元素的top值与最大可滚动高度进行比较，以确定是否已经到达或超过了底部
              if (-potentialNewTop < maxScrollableHeight) {
                listEl.style.top = `${potentialNewTop}px`
              } else {
                // 已经到达或超过底部，调整top以确保内容底部和容器底部对齐
                listEl.style.top = `${-maxScrollableHeight - 50}px`
              }
            } else {
              return
            }

            if (scrollNewTop < maxSrcollBarHeight) {
              scrollBar.style.top = `${scrollNewTop}px`
            } else {
              scrollBar.style.top = `${maxSrcollBarHeight}px`
            }
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }else
        if (this.nextNums == -1 &&  this.leftNums < this.leftList.length - 1) {
          this.leftList[this.leftNums].ref = ""
          this.leftNums ++
          this.leftList[this.leftNums].ref = "active"
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }

      },
      up: () => {
        if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          const listEl = this.$refs.refList
          // const scrollBar = this.$refs.refScroll
          const scrollBar = this.$refs.refList.parentNode.querySelector('.scrollBar')

          if (listEl && scrollBar) {
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight
            const radio = listHeight / listVisHeight
            const potentialNewTop = currentTop + 150 // 预计的新top值
            const scrollNewTop = currentScrollTop - 135 / radio

            // 检查是否已经到达最顶部
            if (potentialNewTop >= 0) {
              listEl.style.top = `0px` // 直接设置为0，确保不会超过顶部
              scrollBar.style.top = `0px`
            } else {
              listEl.style.top = `${potentialNewTop}px`
              scrollBar.style.top = `${scrollNewTop}px`
            }
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }else
        if (this.nextNums == -1 &&  this.leftNums > 0) {
          this.leftList[this.leftNums].ref = ""
          this.leftNums --
          this.leftList[this.leftNums].ref = "active"
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }

      },
      left: () => {
        if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
          return
        }
        // 在右侧
        if (this.nextNums > -1 && this.leftList.length > 0) {
          this.$refs.active = []
          this.nextNums = -1
          this.leftList[this.leftNums].ref = "active"
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      right: () => {
        if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
          return
        }
        // 在左
        if (this.nextNums == -1) {
          this.leftList[this.leftNums].ref = ""
          this.$refs.active.push(this.$refs.refRight)
          this.nextNums = 0
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
          return
        }
        if (this.nextNums == -1) {
          this.playVideo()
        }
      },
      esc: () => {
        if (this.$store.getters.loadingState) {
          clearTimeout(this.playTimer)
          this.playTimer = null
          this.$store.dispatch('app/setLoadingState', false)
          return
        }
        // this.$store.state.app.media.fullscreen('t')
        if (this.$store.state.app.media) {
          this.$store.state.app.media.fullscreen('t')
          this.$store.state.app.media.stop(()=>{
            this.$store.dispatch('index/setFocusDom', this.$refs.active);
          })
          return
        }
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    getData() {
      getBiserialPageItemInfo({
        fk_huodonglueying_content_id: this.$route.query.id
      })
      .then(res=>{
        if (res.code == 200) {

          this.title = res.data.title
          //设置页面左上角标题
          this.$nextTick(() => {
            this.$store.dispatch('index/setMainTitle', this.title)
          })
          this.content = res.data.content
          if (this.content && this.content.indexOf('<img') > -1) {
            let reg = new RegExp('/public/storage/', 'g')
            let regImg = new RegExp('<img', 'g')
            this.content = this.content.replace(reg, process.env.VUE_APP_API + '/public/storage/')
            //this.content = this.content.replace(regImg, '<img style="width: 10rem" ')

            this.content = this.content
                // 匹配包含 style 的 img 标签，并在 style 内增加 width:10rem
                .replace(/(<img[^>]*style="[^"]*)"/g, '$1; width:90%"')
                // 匹配没有 style 的 img 标签，并增加 style="width:10rem"
                .replace(/(<img(?![^>]*style)[^>]*)(>)/g, '$1 style="width:90%"$3');
          }

          this.content = this.content
              .replace(/(<p[^>]*style="[^"]*)"/g, '$1; word-break:break-all;"')
              .replace(/(<p(?![^>]*style)[^>]*)(>)/g, '$1 style="word-break:break-all;"$3');

          setTimeout(() => {
            this.fuc.setScroll()
          }, 100)

          let videoList = ['video1','video2','video3']
          let dataInfo = JSON.parse(JSON.stringify(res.data))
          videoList.map(item=>{
            if (dataInfo[item]) {
              dataInfo[item] = dataInfo[item].indexOf('http') > -1 ? dataInfo[item] : process.env.VUE_APP_API + dataInfo[item]
              this.leftList.push({
                ref:'',
                url: dataInfo[item]
              })
            }
          })


          this.$refs.active = []
          this.$refs.active.push(this.$refs.refRight)
          this.nextNums = 0
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
            this.fuc.setScroll()
          })

        }
      })
    },
    playVideo() {
      this.$store.dispatch('app/setLoadingState', true)
      clearTimeout(this.playTimer)
      this.playTimer = null
      this.playTimer = setTimeout(()=>{
        this.$nextTick(()=>{

          var obj={
            x:0,
            y:0,
            w:1,
            h:1,
            r: 0
          }
          let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0]) / 100

          let videoList = []
          videoList.push(this.leftList[this.leftNums].url)
          if (!this.$store.state.app.media) {
            this.$store.state.app.media = media.DetermineKernel({
              videoList: videoList,
              loop:1,
              windowPos: obj,
              fontSize: fontSize
            },()=>{
              this.$nextTick(()=>{
                this.$store.dispatch('index/setFocusDom', null);
                this.$store.state.app.media.fullscreen('t')
              })
              this.$store.dispatch('app/setLoadingState', false)
            });
          }
        })
      },1000)
    }
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.biserialPageInfo {
  display: flex;
  position: relative;
  width: 100%;
  height: 7rem;
  .notice {
    position: fixed;
    font-size: 0.28rem;
    right: 1.2rem;
    bottom: 0.6rem;
    color: #B2BAEF;
    display: flex;
    flex-direction: row;
    align-items: center;
    img {
      position: relative;
      top: 0.02rem;
      left: -0.05rem;
      width: 0.32rem;
      height: 0.32rem;
    }
  }
  .container {
    position: relative;
    width: 100%;
    height: 100%;
    .left {
      width: 3.2rem;
      height: 2.1rem;
      position: absolute;
      top: 0 !important;
      .leftList {
        width: 3.4rem;
        height: 2.10rem;
        margin-bottom: 0.32rem;
        border-radius: 0.26rem;
        img {
          display: block;
          width: 100%;
          height: 100%;

        }
      }
    }

    .right {
      height: 6.8rem;
      position: absolute;
      right: 0.4rem;
      background: #25294f;
      font-size: 0.21rem;
      overflow: hidden;
      border-radius: 0.24rem;
      padding: 0.2rem;
      padding-bottom: 0;

      .list {
        overflow-y: auto;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        .content {
          position: relative;
          width: 100% !important;
          height: initial !important;
        }
      }
    }

    .done {
      width: 3.9rem;
      position: fixed;
      top: 1.6rem;
      right: 0.9rem;
      .item {
        font-size: 0.36rem;
        font-weight: bold;
        letter-spacing: 0.03rem;
        color: #b5c0ff;
      }
      .item::before {
        content: '';
        width: 0.1rem;
        height: 0.1rem;
        position: absolute;
        background: #b5c0ff;
        border-radius: 50%;
        top: 50%;
        left: -0.25rem;
      }
    }
  }
}
</style>
<style lang="less">
.biserialPageInfo {
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    // background: repeating-linear-gradient(to right, #c98693, #a95361);
    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        letter-spacing: 0.03rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        justify-content: center !important;
        align-items: center !important;
        .title {
          font-size: 0.48rem;
          font-weight: bold;
          // padding-bottom: 0.12rem !important;
          p {
            margin-bottom: 0.22rem;
          }
        }
      }
      .result {
        font-size: 0.48rem !important;
        font-weight: bold !important;
        letter-spacing: 0.03rem !important;
      }
      .cancel {
        flex-direction: column;
        div {
          text-align: center;
        }
        div:nth-child(2) {
          margin-top: 0.1rem;
          color: red;
          line-height: 0.48rem;
        }
        //align-items: normal !important;
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

          // background: repeating-linear-gradient(to right, #c98693, #a95361);
        }
      }
    }
  }
  .container {
    .right {
      .scroll {
        top: 2.25rem !important;
        left: 17.8rem !important;
      }
    }
  }
}
</style>
      