<template>
  <div class="firmDetails">
      <div v-if="getFirmDetailInfo"  class="details_list">
        <div class="list_title">证照公示</div>
        <div class="picList">
          <div  class="pic" v-lazy-container="{ selector: 'img' }">
            <img v-if="getFirmDetailInfo" :data-src="reqUrl + getFirmDetailInfo.zhengzhao_one"  :data-error="lazyError" alt="">
          </div>

          <div  class="pic" v-lazy-container="{ selector: 'img' }">
            <img v-if="getFirmDetailInfo" :data-src="reqUrl + getFirmDetailInfo.zhengzhao_two"  :data-error="lazyError" alt="">
          </div>
        </div>

      </div>

      <div v-if="getFirmDetailInfo"  class="details_list">
        <div class="list_title">店铺展示</div>
        <div class="picList">
          <div  class="pic" v-lazy-container="{ selector: 'img' }">
            <img v-if="getFirmDetailInfo" :data-src="reqUrl + getFirmDetailInfo.dianpu_img"  :data-error="lazyError" alt="">
          </div>
        </div>
        <div class="storeInfo" v-if="getFirmDetailInfo">
          <p>
            <span>名称</span>
            <span>:</span>
            <span>{{this.getFirmDetailInfo.title}}</span>
          </p>
          <p>
            <span>地址</span>
            <span>:</span>
            <span>{{this.getFirmDetailInfo.addr}}</span>
          </p>
          <p>
            <span>电话</span>
            <span>:</span>
            <span>{{this.getFirmDetailInfo.phone}}</span>
          </p>
        </div>


      </div>

      <div v-if="getFirmDetailInfo" class="details_list small">
        <div class="list_title">医生资质</div>
        <div class="picList">
          <div  class="pic" v-lazy-container="{ selector: 'img' }">
            <img v-if="getFirmDetailInfo" :data-src="reqUrl + getFirmDetailInfo.zizhi"  :data-error="lazyError" alt="">
          </div>
        </div>
      </div>

      <div v-if="!getFirmDetailInfo">
        <div class="no_data">当前药房暂无资质信息</div>
      </div>


    <div class="inFirm" ref="active">
      <img :src="require('@/assets/stores.png')" alt="">
      <p>进入药房</p>
    </div>
  </div>
</template>

<script>
import { getFirmDetails, } from '@/api/index'
export default {
  name: "firmDetails",
  data() {
    return {
      lazyError: require('@/assets/icon_17.png'),
      reqUrl: process.env.VUE_APP_API,
      supplierInfo: null,
      getFirmDetailInfo: null
    }
  },
  created() {
    this.supplierInfo =  JSON.parse(sessionStorage.getItem('supplierInfo'))
    //设置页面左上角标题
    this.$nextTick(() => {
      this.$store.dispatch('index/setMainTitle', this.supplierInfo.title)
    })

  },
  watch: {
  },
  mounted() {
    this.getFirmDetail()
    this.$nextTick(()=>{
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
    })


    this.fuc.KeyboardEvents({
      down: () => {

      },
      up: () => {

      },
      left: () => {

      },
      right: () => {

      },
      enter: () => {
        this.$router.push({
          path: './drugList'
        })
      },
      esc: () => {
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })

  },
  methods: {
    getFirmDetail() {
      getFirmDetails({
        id: this.supplierInfo.fk_supplier_id
      })
      .then(res=>{
        if (res.code == 200) {
          this.getFirmDetailInfo = res.data

        }
      })
      .catch(err=>{

      })
    }
  }
}
</script>

<style scoped lang="less">
  .firmDetails {
    display: flex;
    position: relative;
    height: 7rem;
    .no_data {
      position: absolute;
      width: 100%;
      height: 100%;
      text-align: center;
      line-height: 40vh;
      font-size: 0.35rem;
    }
    .details_list {
      width: 5.2rem;
      height: 7rem;
      background: #282B58;
      border-radius: 0.24rem;
      float: left;
      margin-right: 0.58rem;
      position: relative;
      overflow: hidden;
      .list_title {
        width: 2.15rem;
        height: 0.52rem;
        line-height: 0.52rem;
        border-radius: 0.24rem;
        font-size: 0.32rem;
        font-weight: bold;
        text-align: center;
        position: absolute;
        top: 0.2rem;
        left: 50%;
        transform: translateX(-50%);
        background: #4A5280;
        letter-spacing: 0.05rem;
      }
      .picList {
        width: 73%;
        margin: 0 auto;
        margin-top: 0.9rem;
        .pic {
          margin-bottom: 0.24rem;
          img {
            display: block;
            width: 100%;
          }
        }
      }
      .storeInfo {
        width: 4rem;
        font-size: 0.24rem;
        margin: auto;
        overflow: hidden;
        p {
          width: 100%;
          margin-bottom: 0.16rem;
          margin-top: 0.14rem;
          color: #B6C0FB;
          display: flex;
          flex-direction: row;
          span {
            display: block;
          }
          span:nth-child(1) {
            width: 0.7rem;
            letter-spacing: 0.1rem;
          }
          span:nth-child(3) {
            margin-left: 0.1rem;
            width: 3.1rem;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            letter-spacing: 0.05rem;
          }
        }
      }
    }

    .details_list:nth-child(2) .picList{
      width: 4.6rem;
      height: 4rem;
    }

    .details_list:nth-child(3) .picList{
      width: 63.6%;
    }
    .inFirm {
      width: 5.2rem;
      height: 1.1rem;
      position: absolute;
      right: 0;
      bottom: 0;
      font-size: 0.4rem;
      font-weight: bold;
      letter-spacing: 0.1rem;
      line-height: 1.1rem;
      text-align: center;
      border-radius: 0.26rem;
      background-image: linear-gradient(to right, #5F7EB7, #4763A2);
      overflow: hidden;
      text-indent: 1em;
      img {
        width: 0.55rem;
        height: 0.52rem;
        position: absolute;
        left: 1.1rem;
        top: 52%;
        transform: translateY(-50%);
      }
    }


    .small {
      height: 5.5rem;
    }
    .details_list:nth-child(3) {
      margin-right: 0 !important;
    }
  }

</style>