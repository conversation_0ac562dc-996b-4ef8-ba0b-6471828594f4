<template>
<!-- 公用的头部和底部的信息,背景切换 -->
<div id='home'  :style="{background:'url('+require(`../assets/${(!this.$store.state.app.changBg ? this.$route.meta.backgroundImage : this.$store.state.app.changBg)}`)+') no-repeat','backgroundSize':' 100% 100% '}" v-show="!this.$store.state.app.fullscreen">
<!--  <transition name="fade">-->
    <div v-show="!this.$store.state.app.settingShow">
      <div class="loading" v-if='this.$store.getters.loadingState'>
        <div class="loading_content">
          <img src="../assets/loading.gif" alt="">
          <p>请稍候...</p>
        </div>
      </div>
      <Header></Header>
<!--      <transition name="fade">-->
<!--        <div class="overallPage" v-if='transitionFlag'>-->

        <div class="overallPage">
          <!-- 每个模块的核心内容 -->
            <div class="content">
<!--              <keep-alive>-->
                <router-view  v-if="isRouterAlive" />
<!--              </keep-alive>-->
            </div>

            <div  :style='borderStyle' id="focus_border" ref="focus_border" v-if="borderStyle.length"></div>

          <!--          <transition name="fade">-->
          <!--AI智能语音助手-->
          <div class="ai_message" v-if="$store.getters.AIShow">
            <el-dialog
                :visible.sync="$store.getters.AIShow"
                :fullscreen="true"
                :show-close="false"
                :close-on-click-modal="false"
            >
              <div class="popupContent" ref="popupContent">
                <div class="popupItem" v-for="(item,index) in $store.getters.AISayList" :key="index">
                  <div class="floatLeft" style="display: flex;flex-direction: row;align-items: center;">
                    <!--                      <img :src="require('@/assets/logo.png')" alt="">-->
                    <img :src="require('@/assets/fubao_logo.png')">
                    <div :style="{color:item.type == 0 ? '#fff' : '#D78127'}" v-html="item.message"></div>
                  </div>
                  <!--                    <div class="floatRight" style="display: flex;flex-direction: row-reverse;align-items: center;" v-else>-->
                  <!--                      <img :src="require('@/assets/fubao_logo.png')" alt="">-->
                  <!--                      <div style="color:#D78127" v-html="item.message"></div>-->
                  <!--                    </div>-->
                </div>

              </div>
            </el-dialog>
          </div>
          <!--          </transition>-->


        </div>

<!--      </transition>-->
      <div class="footer">

        <div class="footer_left notice" v-if="showNotice.length > 0">
            <!-- 小喇叭 -->
            <div class="tips">
              <img src="../assets/notice_pic.png" alt="">
            </div>
            <div class="shadow"></div>
<!--          transform:translateX(10px)-->
          <!-- 滚动文字外层div，文字能展示的区域-->
<!--          <div class="noticeBox" :style="'width:'+noticeWidth+'px;height:20px;position:relative;overflow:hidden;display:inline-block;vertical-align:middle'">-->
          <div class="noticeBox" :style="'width:12rem;height:0.2rem;position:relative;overflow:hidden;display:inline-block;vertical-align:middle'">
              <marquee style="margin-left: 0.2rem">{{showNotice}}</marquee>
              <!-- 滚动div，marginLeft变化-->
<!--              <div :style="'margin-left:'+marginLeft+'px;white-space:nowrap'">-->
<!--                <span class="showNotice">{{showNotice}}</span>-->
<!--                &lt;!&ndash; 不会展示，用来测量第二条与第一条重合时的长度 &ndash;&gt;-->
<!--                <span class="notice">{{notice}}</span>-->
<!--              </div>-->
            </div>
        </div>

        <div class="footer_left" v-else>
          <img :src="require('@/assets/footer_left.png')" alt="" v-if="!this.$route.meta.operationTip">
<!--          <img :src="require(`../assets/${this.$route.meta.operationTip}`)" alt="" v-else>-->
        </div>
      </div>


<!--      <transition name="fade">-->
        <div class="netWorkError" v-if="!this.$store.state.app.onLineList[1].show">
          <div class="errorText">
            网络异常，请去设置检查互联网连接情况!
          </div>

          <div ref="setting" class="setting">
            设置
          </div>
        </div>
<!--      </transition>-->
    </div>
<!--  </transition>-->

<!--  <transition name="fade">-->
    <div class="settingPage" v-show="this.$store.state.app.settingShow">
      <div class="left_header">
        设置
      </div>
      <div class="settingBtn">
        <div :class="'settingItem' + ((item.ref == 'setting_active') ? ' setting_active' : '')" :ref="item.ref" v-for="(item,index) in this.$store.state.app.settingBtn" :key="index">
          <div class="title">{{item.title}}</div>
          <div class="icon">
            <img :src="item.icon" alt="">
          </div>
          <div class="status">
            {{item.status}}
          </div>
        </div>

      </div>
    </div>
<!--  </transition>-->

    <div class="version" v-html="version" style="position: absolute;bottom: 0.1rem;right: 0.2rem;color: rgba(255,255,255,0.2)"></div>

</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import Header from '../components/Header.vue';
import {GetUserInfo } from "@/api/index";
import websocketData  from "@/utils/websocketData";
import store from "@/store";
import Axiso from "@/api/resouce";
export default {
//import引入的组件需要注入到对象中才能使用
components: {
    Header
},
provide(){
  return {
    reload: this.reload
  }
},
data() {
    return {
      isRouterAlive: true,
      version: '',
      versionContent: '',
      versionMsg: null,
      // 适应屏幕分辨率
      noticeWidth: window.screen.width - 150 - 240 - 300,
      // 公告展示（过长时会重复两遍）
      showNotice: '',
      // 用于公告过长时，获取重复两遍中第一遍的长度
      notice: '',
      // 公告初始位置
      marginLeft: 0,
      noticeOutTimer: null,
      noticeOutTimer2: null,
      noticeintervalTimer: null,

      userInfo:true,
      transitionFlag:true,
      homeTimer:null,
      borderStyle: '',
      timer: null,
      scrollTimer: null,
      offsetTop: 0,
      timerScroll: null,
      fistDraw: true,
      oldDom: null,
      loadingTimer: null,
      loadingTime: 0

    };
},
created(){
  this.$store.state.lock = true
  if (sessionStorage.getItem('selectUserIndex')) {
    this.$store.state.app.selectUserIndex = Number(sessionStorage.getItem('selectUserIndex'))
  }
  this.fetchFileContent();
},
computed: {

},
watch: {
    $route (to, from) {
      this.fistDraw = true
      this.fuc.dateTime()
      this.offsetTop = 0;
      this.transitionFlag=false;
      clearTimeout(this.homeTimer)
      // this.fuc.getTicket()
      this.homeTimer=setTimeout(()=>{
        this.transitionFlag = true;
      },300)


      // 积分埋点  存在当前页面 且 存在任务  且  未完成任务 且为进入页面获取非功能获取
      this.$store.state.app.pointList.map(item=>{
        if (item.redirect && item.redirect.indexOf(to.path) > -1 && !item.harvest && item.redirect.indexOf('pointType') == -1) {
          this.fuc.insertPointNum(item.id)
        }
      })
    },
    "$store.state.app.token": {
      handler() {
        if (this.$store.state.app.token == "") {
          this.fuc.getToken();
        }
      },
      deep: true
    },
    "$store.getters.loadingState": {
      handler() {
        if (this.$store.getters.loadingState) {
          this.loadingTimer = setInterval(()=>{
            this.loadingTime++
            if (this.loadingTime > 10) {
              this.$store.dispatch('app/setLoadingState', false)
              clearInterval(this.loadingTimer)
              this.loadingTimer = null
              this.loadingTime = 0
            }
          },1000)
        } else {
          clearInterval(this.loadingTimer)
          this.loadingTimer = null
          this.loadingTime = 0
        }
      },
      deep: true
    },
    "$store.getters.focus_border" : {
      handler(newValue, oldValue) {
        this.borderStyle = ""
        let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0]) / 100
        let borderWidth = (1 - fontSize) > 0 ? 4 * (1 - fontSize) : 4
        borderWidth = borderWidth * fontSize
        if (borderWidth <= 2) {
          borderWidth = 2
        }
        this.$store.getters.focus_border.borderColor = "#fff"
        Object.keys(this.$store.getters.focus_border).forEach((key)=>{
          if (['width','height','top','left'].includes(key)) {
            if (['width','height'].includes(key)) {
              this.borderStyle += key + ":" + ( this.$store.getters.focus_border[key] + (borderWidth * 4)) + 'px;'
            } else if(['top'].includes(key)) {
              this.borderStyle += key + ":" + ( this.$store.getters.focus_border[key] - (borderWidth * 3) - 0.17) + 'px;'
            } else{
              this.borderStyle += key + ":" + ( this.$store.getters.focus_border[key] - (borderWidth * 3)) + 'px;'
            }
          } else if(['borderRadius'].includes(key)){
            let radius = Number(this.$store.getters.focus_border[key].split('px')[0])
            if (radius > 0) {
              this.borderStyle += key + ":" + (radius + (5 * fontSize)) + 'px;'
            } else {
              this.borderStyle += key + ":" + radius + 'px;'
            }
          } else {
            this.borderStyle += key + ":" +this.$store.getters.focus_border[key] + ';borderWidth:' +  borderWidth + 'px;'
          }

        })
      },
      deep: true,
    },
    "$store.getters.focus_dom": {
      handler(newValue, oldValue) {
        let scrollElement = document.getElementsByClassName('scroll');
        const element = this.$store.getters.focus_dom
        // element.parentNode && element.parentNode.parentNode
        // if (oldValue) {
        //   oldValue.style.transform = "scale(1)"
        //   oldValue.style.transition = "all 0.3s"
        // }
        // newValue.style.transform = "scale(1.1)";
        // newValue.style.transition = "all 0.3s"


        let scrollBarTop = 0
        let scrollHeight = 0
        let heightGap = 50

        if (element) {
          if (!element.parentNode) {
            return
          }
          let isTrue = false
          if (element.getAttribute('isParentNode')) {
            isTrue = true
          } else {
            isTrue = false
          }



          const windowHeight = isTrue ? element.parentNode.parentNode.parentNode.parentNode.clientHeight  : element.parentNode.parentNode.clientHeight  // 可视区域高度
          const elementHeight = isTrue ? Number(element.parentNode.parentNode.clientHeight) : Number(element.clientHeight)   // 当前元素高度
          const elementOffsetTop = isTrue ? element.parentNode.parentNode.offsetTop : element.offsetTop  // 当前元素距离顶端的高度
          const windowScrollTop = isTrue ? element.parentNode.parentNode.parentNode.offsetTop  : element.parentNode.offsetTop  // 页面下拉高度
          // let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
          let fontSize = 1

          const styles = isTrue ? getComputedStyle(element.parentNode.parentNode) : getComputedStyle(element);
          let styleMargin = Number(styles.getPropertyValue('margin-bottom').split('px')[0])

          const windowWidth = element.parentNode.parentNode.clientWidth  // 可视区域高度
          const elementWidth = Number(element.clientWidth)   // 当前元素高度
          const elementOffsetLeft = element.offsetLeft  // 当前元素距离顶端的高度
          const windowScrollLeft = element.parentNode.offsetLeft  // 页面下拉高度

          let styleMarginRight = Number(styles.getPropertyValue('margin-right').split('px')[0])

          this.fuc.setScroll()

          // 竖直 翻页||不翻页

          if (((elementOffsetTop  + windowScrollTop  < 0) || elementOffsetTop + elementHeight + windowScrollTop > windowHeight + 1) &&
              (isTrue ? element.parentNode.parentNode.parentNode.clientHeight : element.parentNode.clientHeight) > windowHeight
          ) {
            /**
             * 初始化滚动条高度  位置
             */
            this.$nextTick(()=>{
              let scrollElement = isTrue ? element.parentNode.parentNode.parentNode.parentNode.querySelector('.scroll') : element.parentNode.parentNode.querySelector('.scroll');
              if (scrollElement) {
                scrollHeight = (windowHeight * (windowHeight / (isTrue ? element.parentNode.parentNode.parentNode.clientHeight: element.parentNode.clientHeight)))
                scrollBarTop = Number(getComputedStyle(isTrue ? element.parentNode.parentNode.parentNode.parentNode.querySelector('.scrollBar') : element.parentNode.parentNode.querySelector('.scrollBar')).getPropertyValue('top').split('px')[0])
                if (scrollBarTop == 0 && (elementOffsetTop > windowHeight)) {  // elementOffsetTop > windowHeight 判断当前元素超出可视区域范围
                  scrollBarTop = windowHeight * ((elementOffsetTop - windowHeight  - styleMargin)/( isTrue ? element.parentNode.parentNode.parentNode.clientHeight :element.parentNode.clientHeight))
                }
              }
            })

            /**
             * 初始化页面卷去高度位置
             */
            let lastNum = 0
            // let lastNum = isTrue ? element.parentNode.parentNode.parentNode.style.top.split('rem')[0] :  element.parentNode.style.top.split('rem')[0]
            // if (lastNum.indexOf('px') > -1) {
            //   lastNum = isTrue ? element.parentNode.parentNode.parentNode.style.top.split('px')[0] :  element.parentNode.style.top.split('px')[0]
            // }
            // lastNum = Number(lastNum)
            // if (lastNum == 0 && elementOffsetTop > windowHeight) {  // elementOffsetTop > windowHeight 判断当前元素超出可视区域范围
            //     lastNum = -(elementOffsetTop-windowHeight-styleMargin)/fontSize
            // }


            /**
             * 判断父元素是否有动画, 有动画则按动画时间延迟渲染焦点框 否则正常
             */
            const styles = isTrue ? getComputedStyle(element.parentNode.parentNode.parentNode) : getComputedStyle(element.parentNode);
            let styleTransition = 0
            if (styles.getPropertyValue('transition-duration').split('s').length > 1) {
              styleTransition = Number(styles.getPropertyValue('transition-duration').split('s')[0]) * 1000
            }



            let row = parseInt(windowWidth/elementWidth)
            let cloume = Math.round(windowHeight/(elementHeight + styleMargin))
            // 获取父元素
            const parentElement = element.parentNode;
            // 获取所有子元素
            const children = Array.from(parentElement.children);


            // 找到当前元素在子元素中的索引
            const index = children.indexOf(element) + 1;
            // 下拉行数  当前处于第几行 - 可视区域行数
            const scrollTopRow = Math.ceil(index/row) - cloume
            const scrollTopNum = (styleMargin + elementHeight) * scrollTopRow

            // 向上翻动
            if (oldValue && oldValue.offsetTop > elementOffsetTop) {
              let  elementOldOffsetTop = 0
              if (oldValue) {
                elementOldOffsetTop = isTrue ? oldValue.parentNode.parentNode.offsetTop : oldValue.offsetTop  // 当前元素距离顶端的高度
              }
              // 上拉高度 超过父元素的行数的一半，则证明非整行翻动
              if (elementOldOffsetTop - elementOffsetTop  - styleMargin > windowHeight / Math.round(windowHeight/elementHeight))  {

                if ((windowScrollTop / (elementHeight + styleMargin)).toFixed(2) %1 != 0) {
                  lastNum = windowScrollTop + (elementHeight - (windowHeight - parseInt(windowHeight/(elementHeight + styleMargin)) * (elementHeight + styleMargin)))
                  if (lastNum > -2) {
                    lastNum = 0
                  }

                  let eleHeightGap =  windowHeight / elementHeight
                  let domTop = this.$store.getters.focus_dom.getBoundingClientRect().top + elementHeight
                  let nums = windowHeight - (parseInt(eleHeightGap) * (elementHeight + styleMargin))
                  this.getDomRect(domTop - nums)
                } else {
                  // 下拉行数  当前处于第几行 - 上面只有一行
                  let scrollTopRowOne = Math.ceil(index/row) - 1
                  lastNum = -(styleMargin + elementHeight) * scrollTopRowOne

                  clearTimeout(this.timer)
                  this.timer = null
                  this.timer = setTimeout(()=>{
                    this.getDomRect()
                  },styleTransition)
                }


              } else {
                // 下拉行数  当前处于第几行 - 上面只有一行
                let scrollTopRowOne = Math.ceil(index/row) - 1
                lastNum = -(styleMargin + elementHeight) * scrollTopRowOne
                clearTimeout(this.timer)
                this.timer = null
                this.timer = setTimeout(()=>{
                  this.getDomRect()
                },styleTransition)
              }
              // 滚动条位置
              if (scrollElement) {
                this.$nextTick(()=>{
                  let testNum = (((isTrue ? element.parentNode.parentNode.parentNode.clientHeight : element.parentNode.clientHeight)  - windowHeight - styleMargin) / (elementHeight + styleMargin)).toFixed(1)
                  let num = 0
                  if (testNum.split('.')[1] > 0) {
                    num = (windowHeight -  scrollHeight) / Math.ceil(testNum)
                  } else {
                    num = (windowHeight -  scrollHeight) / testNum
                  }
                  scrollBarTop -= num
                  isTrue ? element.parentNode.parentNode.parentNode.parentNode.querySelector('.scrollBar').style.top = scrollBarTop + 'px' : element.parentNode.parentNode.querySelector('.scrollBar').style.top = scrollBarTop + 'px'
                })
              }

            }
            // 向下翻动
            else {

              let  elementOldOffsetTop = 0
              if (oldValue) {
                elementOldOffsetTop = isTrue ? oldValue.parentNode.parentNode.offsetTop : oldValue.offsetTop  // 当前元素距离顶端的高度
              }
              // 下拉高度 超过父元素的行数的一半，则证明非整行翻动
              if ( elementOffsetTop - elementOldOffsetTop - styleMargin > windowHeight / Math.round(windowHeight/elementHeight))  {
                // if (index == Math.round(windowHeight/elementHeight)) {
                if (elementOffsetTop + elementHeight + windowScrollTop - windowHeight < elementHeight) {
                  lastNum = -(scrollTopNum - (elementHeight + styleMargin)) - (elementHeight - (windowHeight - (cloume * (elementHeight + styleMargin))))

                  let eleHeightGap =  windowHeight / elementHeight
                  let domTop = this.$store.getters.focus_dom.getBoundingClientRect().top - elementHeight
                  let nums = windowHeight - (parseInt(eleHeightGap) * (elementHeight + styleMargin))
                  this.getDomRect(domTop + nums)
                } else {
                  lastNum = -(scrollTopNum - (windowHeight - ((elementHeight + styleMargin)  * cloume - styleMargin)))
                  clearTimeout(this.timer)
                  this.timer = null
                  this.timer = setTimeout(()=>{
                    this.getDomRect()
                  },styleTransition)
                }
              }
              else {
                lastNum = -scrollTopNum
                clearTimeout(this.timer)
                this.timer = null
                this.timer = setTimeout(()=>{
                  this.getDomRect()
                },styleTransition)
              }

              // 滚动条位置
              if (scrollElement) {
                this.$nextTick(()=>{
                  let testNum = (((isTrue ? element.parentNode.parentNode.parentNode.clientHeight : element.parentNode.clientHeight)  - windowHeight - styleMargin) / (elementHeight + styleMargin)).toFixed(1)
                  let num = 0
                  if (testNum.split('.')[1] > 0) {
                    num = (windowHeight -  scrollHeight) / Math.ceil(testNum)
                  } else {
                    num = (windowHeight -  scrollHeight) / testNum
                  }

                  scrollBarTop += num

                  // 解决非整行下拉  返回初次渲染  滚动条定位问题
                  let scrollElement = isTrue ? element.parentNode.parentNode.parentNode.parentNode.querySelector('.scroll') : element.parentNode.parentNode.querySelector('.scroll');
                  if (scrollElement.style.height.split('px')[0] - (scrollBarTop + scrollHeight) < 15) {
                    scrollBarTop = scrollElement.style.height.split('px')[0] - scrollHeight
                  }

                  isTrue ? element.parentNode.parentNode.parentNode.parentNode.querySelector('.scrollBar').style.top = scrollBarTop + 'px' : element.parentNode.parentNode.querySelector('.scrollBar').style.top = scrollBarTop + 'px'
                })
              }
            }

            this.$store.dispatch('app/setViewAreaOffsetTop', elementOffsetTop)

            isTrue ? element.parentNode.parentNode.parentNode.style.top = lastNum + 'px' : element.parentNode.style.top = lastNum + 'px'
          }   // 横向 翻页 && 不翻页
          else if(((elementOffsetLeft + elementWidth + windowScrollLeft < 0) || elementOffsetLeft + elementWidth + windowScrollLeft > windowWidth+1 && element.parentNode.clientWidth > windowWidth)) {  //&& element.parentNode.clientWidth > windowWidth
            /**
             * 初始化页面卷去高度位置
             */
            let lastNum = 0
            if (element.parentNode.style.left.indexOf('rem') > 0) {
              lastNum = Number(element.parentNode.style.left.split('rem')[0])
            } else {
              lastNum = Number(element.parentNode.style.left.split('px')[0])
            }

            if (lastNum == 0 && (elementOffsetLeft + elementWidth) > windowWidth) {  // elementOffsetTop > windowHeight 判断当前元素超出可视区域范围
              lastNum = -(elementOffsetLeft-windowWidth-styleMarginRight)/fontSize
            }

            if (oldValue && oldValue.offsetLeft > elementOffsetLeft) {  // 向左翻动
              lastNum += ((styleMarginRight + elementWidth) / fontSize)
            }
            else { // 向右翻动
              lastNum -= (styleMarginRight + elementWidth) / fontSize
            }

            // this.$store.dispatch('app/setViewAreaOffsetTop', elementOffsetTop)
            element.parentNode.style.left = lastNum + 'px'


            // 判断父元素是否有动画, 有动画则按动画时间延迟渲染焦点框 否则正常
            const styles = getComputedStyle(element.parentNode);
            let styleTransition = Number(styles.getPropertyValue('transition-duration').split('s')[0]) * 1000
            if (styleTransition > 0) {
              clearTimeout(this.timer)
              this.timer = null
              this.timer = setTimeout(()=>{
                this.getDomRect()
              },styleTransition)
            } else {
              this.$nextTick(()=>{
                this.getDomRect()
              })
            }

          }
          else {
            // 在可视区域范围内
            this.$nextTick(()=>{
              this.getDomRect()
            })
          }

        } else {
          this.borderStyle = ""
        }
        this.fistDraw = false
      },
      deep: true,
  },
    "$store.getters.AIShow"() {
      if (!this.$store.getters.AIShow) {
        let newArr = JSON.parse(JSON.stringify(this.$store.getters.AISayList))
        let obj = {
          type: 0,   // 0  ai   1  语音识别内容
          message:'有什么可以帮助您'
        }
        newArr[0] = obj
        this.$store.dispatch('app/setAiSay', newArr);

        return
        clearInterval(this.$store.state.app.storeTimer)
        this.$store.state.app.storeTimer = null
      }

    },
    "$store.getters.AISayList":{
      handler(){
        if (this.$store.getters.AIShow) {
          this.$nextTick(()=>{
            this.$refs.popupContent.scrollTop = this.$refs.popupContent.scrollHeight
          })
        }
      },
      deep: true,
      // immediate: true
    },
    "$store.state.app.onLineList": {
      handler(){
        if (this.$store.state.app.settingShow) {
          return
        }
        if (this.$store.getters.focus_dom) {
          // 有网络
          if (this.$store.state.app.onLineList[1].show && this.oldDom) {
            this.$store.dispatch('index/setFocusDom',this.oldDom);
            this.$refs.focus_border.style.zIndex = 9999

            if (this.oldDom && this.oldDom.className.indexOf("popupMessage") > -1) {
              if (this.$refs.focus_border) {
                setTimeout(()=>{
                  this.$refs.focus_border.style.borderColor = "rgb(84, 114, 176)"
                },100)
              }
            }


            this.oldDom = null
          }
          // 无网络
          else if(!this.$store.state.app.onLineList[1].show) {
            this.oldDom = this.$store.getters.focus_dom
            if (this.oldDom && this.oldDom.className.indexOf("popupMessage") > -1) {
              if (this.$refs.focus_border) {
                  this.$refs.focus_border.style.borderColor = "#fff"
              }
            }
            this.$nextTick(()=>{
              this.$store.dispatch('index/setFocusDom',this.$refs.setting);
              this.$refs.focus_border.style.zIndex = 99999
            })
          }
        }
      },
      deep: true,
      immediate: true
    },
    "$store.state.app.settingShow":{
      handler(){
        if (this.$store.state.app.settingShow) {
          this.$nextTick(()=>{
            this.$store.state.app.settingBtn[this.$store.state.app.settingBtnIndex].ref = "setting_active"
          })
        } else {
          // this.$store.state.app.settingBtn[this.$store.state.app.settingBtnIndex].ref = ""
          // this.$store.state.app.settingBtnIndex = 0
          this.$nextTick(()=>{
              // 有网络
              if (this.$store.state.app.onLineList[1].show && this.oldDom) {
                this.$store.dispatch('index/setFocusDom',this.oldDom);
                this.$refs.focus_border.style.zIndex = 9999
                this.oldDom = null
              }
              // 无网络
              else if(!this.$store.state.app.onLineList[1].show && this.oldDom){
                  this.$store.dispatch('index/setFocusDom',this.$refs.setting);
                  this.$refs.focus_border.style.zIndex = 99999
              } else {
                sessionStorage.removeItem('settingBtn')
              }

          })
        }
      },
      deep: true,
    },
    "$store.state.app.selectUserIndex":{
      handler(){
        websocketData.init();
      },
      deep: true,
      immediate: true

    },
    "$store.state.app.webscoketMessage":{
    handler(res, oldVal) {
      if (res.code >= 200 && res.code < 300) {
        switch (res.code) {
          // 主动关爱
          case 250:
            this.noticeData = []
            res.Data.map(item=>{
              this.noticeData.push(item.content)
            })
            // 初始化
            this.noticeShow()
            break;
          // TV用户数据被修改
          case 262:
            this.fuc.getUserInfo()
            break;
        }
      }
    },
    deep: true, // 开启深度监听
  },
    "$store.state.app.versionShow":{
      handler() {
        if (this.$store.state.app.versionShow) {
          this.open1()
        } else {
          if (this.versionMsg) {
            this.versionMsg.close()
          }

        }
      },
      deep: true,
      immediate: true
    }
},
mounted() {
  this.fuc.callTaxi(this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].phone,(res)=>{
  },(err=>{

  }))
  window.onresize = function () {
    location.reload()
  };
},
methods: {
  reload() {
    this.isRouterAlive = false
    this.$nextTick(()=> {
      this.isRouterAlive = true
    })
  },

  open1() {
    this.versionMsg = this.$message({
      dangerouslyUseHTMLString: true,
      onClose:()=>{
        this.$store.state.app.versionShow = false
      },
      duration: 30000,
      customClass: 'versionInfo',
      type:'info',
      message: `<div class="el-notification__title" style="margin-bottom: 0.1rem;font-size: 0.24rem">`+this.version +`\xa0\xa0\xa0\xa0`+ this.getUpdateDate(this.versionContent)+`</div>
                <div style="line-height: 0.28rem;font-size: 0.2rem">`+this.addLineBreaks(this.getLatestVersionContent(this.versionContent))+`</div>`,
    });
  },
  async fetchFileContent() {
    try {
      let url = window.location.href.split('://')
      let urlPath = url[1].split('/')[0]
      const res = await this.axios.get( url[0] + '://'+ urlPath + process.env.BASE_URL + '/version.txt');
      const firstLine = res.trim().split('\n')[0];
      const version = firstLine.split(' ')[0];
      this.version = version
      this.versionContent = this.getLatestVersionUpdate(res)
    } catch (error) {
      console.error('Error fetching file:', error);
    }
  },
  // 获取新版本内容
  getLatestVersionUpdate(text) {
    const lines = text.trim().split('\n');
    const versionInfo = [];

    for (const line of lines) {
      if (line.startsWith('v')) {
        if (versionInfo.length > 0) {
          break;
        }
      }
      versionInfo.push(line);
    }

    return versionInfo.join('\n');
  },
  // 去除版本号和时间
  getLatestVersionContent(versionInfo) {
    const pattern = /^v\d+\.\d+\.\d+\s+\d{12}\s*/;
    return versionInfo.replace(pattern, '').trim();
  },
  // 获取更新时间
  getUpdateDate(text) {
    const datePattern = /\d{12}/; // 匹配12位数字（即日期）
    const match = datePattern.exec(text);

    return match ? match[0] : null;
  },
  // 增加换行符
  addLineBreaks(text) {
    // 按行拆分
    const lines = text.split('\n').filter(line => line.trim() !== '');
    // 在每一行末尾添加<br/>
    const withLineBreaks = lines.map(line => line + '<br/>').join('\n');
    return withLineBreaks;
  },
  noticeShow() {
    clearTimeout(this.noticeOutTimer)
    clearTimeout(this.noticeOutTimer2)
    clearInterval(this.noticeintervalTimer)
    this.noticeOutTimer = null
    this.noticeOutTimer2 = null
    this.noticeintervalTimer = null
    this.showNotice = this.notice = ""

    this.noticeData.forEach((val, index) => {
      if (this.noticeData.length > 1) {
        this.showNotice += '\xa0\xa0\xa0\xa0' +'【'+Number(index+1)+'】'+ val
      } else {
        this.showNotice += '\xa0\xa0\xa0\xa0' + val
      }
    })
  },
  getDomRect(top) {
    // return
    if (!this.$store.getters.focus_dom) {
      this.borderStyle = ""
      return
    }

    let obj = {
      width: this.$store.getters.focus_dom.getBoundingClientRect().width,
      height: this.$store.getters.focus_dom.getBoundingClientRect().height,
      left: this.$store.getters.focus_dom.getBoundingClientRect().left,
      top: top ?  top : this.$store.getters.focus_dom.getBoundingClientRect().top,
      borderRadius: window.getComputedStyle(this.$store.getters.focus_dom,null)['borderRadius']
    }
    this.$store.dispatch('index/setFocusBorder', obj)
  }

},
beforeDestory() {}, //生命周期 - 销毁完成
}
</script>

<style lang='less'>
* {
  // 关闭所有动画
  transition: all 0s !important;
  animation-duration: 0s !important;
  font-family: 微软雅黑 !important;
}

html,body{
    margin: 0;
    padding: 0;
    width: 19.2rem;
    height: 10.8rem;
    -webkit-user-select:none;
    -moz-user-select:none;
    -ms-user-select:none;
    user-select:none;
    font-family: 微软雅黑;
    background: url("../assets/bg.jpg") no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
    video {
      object-fit: cover;
    }
  .el-dialog__wrapper,.el-carousel__indicator {
    outline: none;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    -webkit-focus-ring-color: rgba(0, 0, 0, 0);
  }
}
ul,li,p,h1,h2,h3,h4,h5,h6{
    margin: 0;
    padding: 0;
}
ul{
    list-style: none;
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
.el-notification,.el-message {
  z-index: 9999 !important;
  outline: none;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  -webkit-focus-ring-color: rgba(0, 0, 0, 0);
  padding: 0.15rem;
  padding-bottom: 0.1rem;
  .el-message__icon {
    display: none;
  }
}
.versionInfo {
  .el-notification__title, .updateTime {
    color: teal !important;
  }
}

#home{
    width:19.2rem;
    height: 10.8rem;
    background-size: 100% 100%!important;
    position: relative;
    z-index: 9999;
    font-size: 0.2rem;
    color: #fff;
  //animation: fade 1s ;
  .el-dialog__wrapper {
    //border: 0.1rem solid transparent;
    //box-sizing: border-box;
    .el-dialog__body {
      border: 0.1rem solid transparent;
      box-sizing: border-box;
    }
  }

  .settingPage {
    position: absolute;
    top: 0;
    left: 0;
    width: 19.2rem;
    height: 10.8rem;
    background: url("../assets/bg.jpg") no-repeat;
    background-size: 100% 100%;
    .left_header {
      color: #fff;
      background-image: linear-gradient(to bottom, #ffffff, rgba(255, 255, 255, 0.65));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 0.6rem;
      font-weight: bold;
      letter-spacing: 0.26rem;
      padding: 0.8rem 1.2rem;
      font-family: 黑体 !important;
    }
    .settingBtn {
      width: 16.9rem;
      height: 7rem;
      display: grid;
      grid-template-columns: repeat(4, 1fr); /* 每行显示两个元素 */
      gap: 0.16rem;
      margin: 0 auto;
      .settingItem {
        background: #003867;
        border-radius: 0.04rem;
        font-family: 黑体 !important;
        color: #eee;
        .title {
          padding: 0.15rem 0.1rem;
        }
        .icon {
          height: 60%;
          img {
            display: block;
            width: 1.5rem;
            margin:0 auto;
            padding-top: 0.2rem;
          }
        }
        .status {
          padding: 0.15rem 0.1rem;
          text-align: center;
        }
      }
      .setting_active {
        box-shadow: 0 0 0.1rem rgb(0,153,184) inset,0 0 0.28rem rgb(0,153,184);

        //-webkit-animation-name: scaleDraw; /*关键帧名称*/
        //-webkit-animation-timing-function: ease-in-out; /*动画的速度曲线*/
        //-webkit-animation-iteration-count: infinite;  /*动画播放的次数*/
        //-webkit-animation-duration: 2.8s; /*动画所花费的时间*/
      }

      @keyframes scaleDraw {  /*定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称*/
        0%{
          transform: scale(1);  /*开始为原始大小*/
        }
        25%{
          transform: scale(1.05); /*放大1.1倍*/
        }
        50%{
          transform: scale(1);
        }
        75%{
          transform: scale(1.05);
        }
      }

    }
  }
  .netWorkError {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: rgba(0,0,0,0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 0.6rem;
    .errorText {
      color: #fff;
      background-image: linear-gradient(to bottom, #ffffff, rgba(255, 255, 255, 0.65));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: bold;
      line-height: 4rem;
      margin-top: -2rem;
    }
    .setting {
      width: 2.5rem;
      height: 1rem;
      line-height: 1rem !important;
      background: #545D94;
      box-shadow: 0rem 0.02rem 0.02rem #7682bc inset;
      background-size: 100% 100%;
      border-radius: 0.3rem;
      text-align: center;
      letter-spacing: 0.16rem;
      text-indent: 0.16rem;
    }


  }
  .loading {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background: rgba(0,0,0,0.6);
    z-index: 99999;
    transition: all 0.35s;
    .loading_content {
      width: 4rem;
      height: 4rem;
      color: #fff;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%,-50%);

      img {
        display: block;
        width: 100%;
        height: 100%;
      }
      p {
        width: 100%;
        font-size: 0.22rem;
        text-align: center;
        position: absolute;
        bottom:1.3rem;
        letter-spacing: 0.03rem;
      }
    }

  }

  .overallPage {
      //width: 100%;
      //height: 100%;
      width:16.8rem;
      height:7.63rem;
      margin:0 auto;
      //position: relative;
      background-size: 100% 100%!important;
      .content{
        width:16.8rem;
        height:7.63rem;
        margin:0 auto;
        .scroll {
          width: 0.08rem;
          position: fixed;
          background: #242A4D;
          border-radius: 0.04rem;
          overflow: hidden;
          .scrollBar {
            width: 0.08rem;
            background: #7B87B6;
            border-radius: 0.04rem;
            position: absolute;
            transition: all 0.2s;
          }
        }

      }
      .el-dialog__wrapper {
        background: rgba(0,0,0,0.4);
      }
      #focus_border{
        position: absolute;
        border:0.04rem solid #fff;
        border-radius: 0.3rem;
        transition: all 0.3s;
        transform: translate3d(0, 0, 0);
        z-index: 9999;
      }

      .ai_message {
        position: fixed;
        z-index: 9999;
        .el-dialog__wrapper {
          background: transparent !important;
        }
        .el-dialog {
          width: 19.2rem;
          //height: 7.5rem;
          height: 100%;
          margin-top: 0 !important;
          bottom: 0;
          //top: 50%;
          //transform: translateY(-50%);
          border-radius: 0.16rem;
          background: transparent !important;
          //background: linear-gradient(to top, rgba(0,0,0,0.6) 4%, transparent);

          .el-dialog__header {
            height: 10%;
            display: flex;
            align-items: center;
            overflow: hidden;
            .el-dialog__title {
              display: block;
              line-height: normal !important;
              height: 100%;
              font-size: 0.4rem;
              color: #fff;
              //background-image: linear-gradient(to bottom,rgba(255,255,255,1),rgba(255,255,255,0.65));
              -webkit-background-clip: text;
              background-clip: text;
              color: transparent;
              font-weight: bold;
              width: 100%;
              text-align: center;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
          .el-dialog__body {
            width: 100%;
            height: 2.64rem;
            display: flex;
            align-items: center;
            position: absolute;
            bottom: 0;
            left: 0;
            //border-radius: 0.2rem;
            background: rgba(9,10,29,0.78);
            border: 0.02rem solid #39395A;
            border-image: linear-gradient(to right, #393963, transparent) 1;


            //border-top-color:  ;
            box-sizing: border-box;
            //background: #fff;
            padding: 0 !important;
            overflow: hidden;
            .popupContent {
              width: 100%;
              margin: 0.2rem 0;
              //height: calc(100% - 0.4rem);
              overflow-y: auto;
              transition: all 0.3s;
              .floatLeft,.floatRight {
                padding: 0rem 0.55rem;
                width: 100%;
                font-size: 0.26rem;
                //margin-bottom: 0.2rem;
                img {
                  width: 1.3rem;
                  height: 1.3rem;

                }
                div {
                  margin-left: 0.2rem;
                  font-size: 0.5rem;
                  font-weight: bold;
                  letter-spacing: 0.06rem;
                  text-indent: 0.06rem;
                }

              }
              .floatLeft {
                float: left;
                img {
                  margin-right: 0.2rem;
                }
              }
              .floatRight {
                float: right;
                img {
                  margin-left: 0.2rem;
                }
              }

            }
            .popupContent::-webkit-scrollbar {
              display: none;
            }


          }
        }
      }
    }
  .operatePopup {
    width: fit-content;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.2rem;
    background: #5472B0;

    .el-dialog__header {
      height: 20%;
      display: flex;
      align-items: center;
      padding: 0.2rem 0.2rem 0.2rem;

      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }

    .el-dialog__body {
      padding: 0.3rem 0.2rem;
      padding-top: 0 !important;
      padding-bottom: 0.1rem;
      border-radius: 0.2rem;
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      background: #fff;
    }
  }

  .footer{
    width: 16.8rem;
    height:0.93rem;
    //margin-left:1.2rem;
    display: flex;
    justify-content: space-between;
    position: relative;
    margin: 0 auto;
    .footer_left{
      //width:6.11rem;
      //width: initial !important;
      width: 6.8rem;
      height: 0.42rem;
      img {
        display: block;
        //width: 100%;
        height: 100%;
      }
      //background:url('../assets/footer_left.png') no-repeat;
      //background-size: 100% 100%;
    }
    .notice {
      width: 13rem;
      height: 0.5rem;
      display: flex;
      align-items: center;
      margin-top: -0.08rem;
      position: relative;
      .tips {
        width: 0.84rem;
        height: 0.5rem;
        img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
      .shadow {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background-image: linear-gradient(to right,rgba(55,56,102,0), rgba(55,56,102,1),rgba(55,56,102,0));
      }
      .noticeBox {
        height: 0.5rem !important;
        line-height: 0.58rem;
        font-size: 0.28rem;
        font-weight: 500;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        color: #B1BDF7;
        margin-left: -0.2rem;
        font-family: 黑体 !important;
        overflow: hidden;

        display: inline-block;
        background-image: linear-gradient(to right, rgba(177,189,247,0), rgba(177,189,247,1) 15% ,rgba(177,189,247,1) 85%, rgba(177,189,247,0) 100%);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-clip: text;
        -webkit-background-clip: text;
        //color: transparent;
        transform: translateZ(20px);

        div {
        }
      }
    }
    .footer_right{
      height: 0.48rem;
      background-size: 100% 100%;
      display: flex;
      position: absolute;
      right: 0;
      bottom: 0.68rem;
      .phone{
        width: 0.58rem;
        height: 0.58rem;
        background:url('../assets/phone.png') no-repeat;
        background-size:100% 100%;
        margin-right: 0.2rem;
        position: relative;
      }
      p{
        height: 0.48rem;
        line-height: 0.48rem;
        font-size:0.5rem;
        color:#9EB1D1;
        letter-spacing: 0.03rem;
        display: flex;
        align-items: center;
        .onlinePhone {
          display: inline-block;
          font-size: 0.58rem;
          font-weight: bold;
        }
      }
    }
  }
}
     
</style>