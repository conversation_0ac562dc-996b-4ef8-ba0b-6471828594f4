<template>
  <div class="pacman_container">
    <div class="game-panel">
      <div
        v-for="col in 400"
        :key="col"
        class="col-item"
        :class="{
          pacman: pacmanPosition == col,
          dot: isDot(col) && !isGhost(col),
          wall: isWall(col),
          'power-pellet': isPowerPellet(col) && !isGhost(col),
          'ghost-normal': isGhost(col) && !powerMode,
          'ghost-weak': isGhost(col) && powerMode,
        }"
      ></div>
    </div>
    <div class="score">
      <div class="score-container">
        <span>当前得分</span>
        <span>{{ score }}</span>
      </div>
      <div class="best-container">
        <span>最高得分</span>
        <span>{{ pacmanScore }}</span>
      </div>
    </div>
    <div class="game-over" v-if="gameOver">
      <span class="game-over-text">{{ gameOverText }}</span>
      <br />
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      :show-close="false"
      :close-on-click-modal="false"
      @opened="popupOpend"
      @close="popupClose"
      custom-class="operatePopup"
      :title="popupModule == 1 ? '操作' : '提示'"
    >
      <!--操作弹窗-->
      <div class="btnList" v-if="popupModule == 1">
        <div
          class="btnItem"
          :ref="item.ref"
          v-for="(item, index) in popupBtnList"
          :key="index"
        >
          <div class="box-btnItem" v-html="item.text"></div>
        </div>
      </div>

      <!--消息弹窗-->
      <div class="popupMessage" v-if="popupModule == 2">
        <div class="popupMessage" v-html="popupMessage"></div>
        <div
          class="popupMessageBtn"
          :ref="item.ref"
          v-for="(item, index) in popupBtnList"
          :key="index"
        >
          {{ item.text }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Pacman',
  data() {
    return {
      dialogVisible: false,
      flag: false,
      popupModule: 1, // 1、按钮弹窗  2、消息提示
      popupMessage: '',
      popupBtnNums: 0,
      score: 0,
      pacmanScore: localStorage.getItem('pacmanScore') || 0,
      popupBtnList: [],
      gridWidth: 20, // 网格宽度
      gridHeight: 20, // 网格高度
      pacmanPosition: 21, // 起始位置 (第二行第一个位置)
      direction: 'RIGHT',
      gameOver: false,
      gameOverText: '游戏结束',
      speed: 200, // 加快游戏速度
      isKeyPressed: false,
      isGameEnded: false,
      dots: [], // 豆子位置数组
      walls: [], // 墙壁位置数组
      powerPellets: [], // 能量豆位置数组
      totalDots: 0, // 总豆子数量
      ghosts: [], // NPC幽灵数组
      powerMode: false, // 能量模式
      powerModeTimer: null, // 能量模式计时器
      powerModeDuration: 10000, // 能量模式持续时间(10秒)
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '吃豆人')
    this.$store.dispatch('index/setFocusDom', null)
  },
  mounted() {
    this.init()
    document.addEventListener('keydown', (event) => {
      this.keyDown(event)
    })
    document.addEventListener('keyup', (event) => {
      this.keyUp(event)
    })
    this.setupKeyboardEvents()
  },
  methods: {
    setupKeyboardEvents() {
      this.fuc.KeyboardEvents({
        down: () => {
          if (this.dialogVisible) {
            if (this.popupBtnNums < this.popupBtnList.length - 1) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums++
              this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (!this.gameOver && !this.isGameEnded) {
            this.direction = 'DOWN'
          }
        },
        up: () => {
          if (this.dialogVisible) {
            if (this.popupBtnNums > 0) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums--
              this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (!this.gameOver && !this.isGameEnded) {
            this.direction = 'UP'
          }
        },
        left: () => {
          if (this.dialogVisible) {
            return
          }
          if (!this.gameOver && !this.isGameEnded) {
            this.direction = 'LEFT'
          }
        },
        right: () => {
          if (this.dialogVisible) {
            return
          }
          if (!this.gameOver && !this.isGameEnded) {
            this.direction = 'RIGHT'
          }
        },
        enter: () => {
          if (this.dialogVisible) {
            this.popupBtnList[this.popupBtnNums].fuc(
              this.popupBtnList[this.popupBtnNums]
            )
            return
          }
        },
        esc: () => {
          if (this.dialogVisible) {
            return
          }
          clearInterval(this.intId)
          this.isGameEnded = true
          this.dialogVisible = true
          this.popupModule = 1
          this.setupPauseMenu()
        },
      })
    },

    setupPauseMenu() {
      if (!this.gameOver) {
        this.popupBtnList = [
          {
            text: '继续游戏',
            ref: '',
            fuc: () => {
              this.dialogVisible = false
              this.flag = false
              this.isGameEnded = false
              this.move()
            },
          },
          {
            text: '重新开始',
            ref: '',
            fuc: () => {
              this.restart()
            },
          },
          {
            text: '退出游戏',
            ref: '',
            fuc: () => {
              this.$store.dispatch('index/setFocusDom', null)
              history.go(-1)
            },
          },
        ]
      } else {
        this.popupBtnList = [
          {
            text: '重新开始',
            ref: '',
            fuc: () => {
              this.restart()
            },
          },
          {
            text: '退出游戏',
            ref: '',
            fuc: () => {
              this.$store.dispatch('index/setFocusDom', null)
              history.go(-1)
            },
          },
        ]
      }
      this.popupBtnList[this.popupBtnNums].ref = 'active'
    },

    popupClose() {
      this.flag = false
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', null)
        setTimeout(() => {
          this.popupBtnList = []
          this.popupBtnNums = 0
          this.popupModule = 1
        }, 200)
      })
    },

    popupOpend() {
      this.flag = true
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        setTimeout(() => {
          document.getElementById('focus_border').style.borderColor = '#5472B0'
        }, 0)
        return
      })
    },

    setLocalstorage() {
      let score = localStorage.getItem('pacmanScore')
      if (score) {
        if (this.score > score) {
          localStorage.setItem('pacmanScore', this.score)
          this.pacmanScore = this.score
        }
      } else {
        localStorage.setItem('pacmanScore', this.score)
        this.pacmanScore = this.score
      }
    },

    restart() {
      this.pacmanPosition = 21
      this.direction = 'RIGHT'
      this.gameOver = false
      this.isGameEnded = false
      this.isKeyPressed = false
      this.speed = 200
      this.score = 0
      this.gameOverText = '游戏结束'
      this.powerMode = false
      if (this.powerModeTimer) {
        clearTimeout(this.powerModeTimer)
        this.powerModeTimer = null
      }
      // 重置幽灵位置
      this.ghosts = []
      this.init()
      this.dialogVisible = false
    },

    stop() {
      clearInterval(this.intId)
      this.gameOver = true
      this.isGameEnded = true
      this.setLocalstorage()
    },

    isDot(col) {
      return this.dots.includes(col)
    },

    isWall(col) {
      return this.walls.includes(col)
    },

    isPowerPellet(col) {
      return this.powerPellets.includes(col)
    },

    isGhost(col) {
      return this.ghosts.some(ghost => ghost.position === col)
    },

    initializeLevel() {
      // 初始化墙壁 - 创建一个20x20的迷宫
      this.walls = []
      this.dots = []
      this.powerPellets = []
      this.ghosts = []

      const totalCells = this.gridWidth * this.gridHeight // 400

      // 外围墙壁 (除了左右两边的中间通道)
      for (let i = 1; i <= this.gridWidth; i++) {
        this.walls.push(i) // 顶部
        this.walls.push(i + (this.gridHeight - 1) * this.gridWidth) // 底部
      }

      // 左右边墙 (留出中间通道)
      for (let i = 1; i <= this.gridHeight; i++) {
        const leftWall = (i - 1) * this.gridWidth + 1
        const rightWall = i * this.gridWidth

        // 在中间行留出通道 (第10和第11行)
        if (i !== 10 && i !== 11) {
          this.walls.push(leftWall) // 左侧
          this.walls.push(rightWall) // 右侧
        }
      }

      // 内部墙壁 - 创建简洁、对称且完全连通的迷宫结构
      const innerWalls = [
        // 上部水平墙壁 - 左右对称
        43, 44, 45, 55, 56, 57, 58,

        // 左上角L型结构
        63, 64, 65,
        83, 85,
        103, 105,
        123, 124, 125,

        // 右上角L型结构 (对称)
        75, 76, 77,
        95, 98,
        115, 118,
        135, 136, 137,

        // 中央上部分隔
        68, 69, 70, 71, 72,

        // 左侧中部结构
        143, 144,
        163, 164,
        183, 184,

        // 右侧中部结构 (对称)
        156, 157,
        176, 177,
        196, 197,

        // 中央NPC区域 - 简化设计，确保连通
        189, 190, 192, 193, 195, 197, 198, // 上方，留出191和194、196通道
        209, 218, // 左右两侧
        229, 238, // 左右两侧
        249, 250, 252, 253, 255, 257, 258, // 下方，留出251和254、256通道

        // 左下角结构
        263, 264, 265,
        283, 285,
        303, 305,
        323, 324, 325,

        // 右下角结构 (对称)
        275, 276, 277,
        295, 298,
        315, 318,
        335, 336, 337,

        // 下部水平墙壁 - 左右对称
        343, 344, 345, 355, 356, 357, 358,
      ]
      this.walls.push(...innerWalls)

      // 初始化NPC幽灵 - 在中央区域的通道位置
      const ghostStartPositions = [210, 211] // 中央区域内部，通过通道191和251可以与外界连通
      ghostStartPositions.forEach((position, index) => {
        this.ghosts.push({
          id: index,
          position: position,
          direction: ['UP', 'DOWN', 'LEFT', 'RIGHT'][index % 4],
          lastDirection: null,
          color: ['red', 'pink'][index], // 不同颜色的幽灵
          recentPositions: [], // 记录最近访问的位置，避免打转
          stuckCounter: 0, // 卡住计数器
          targetChangeCounter: 0 // 目标改变计数器
        })
      })

      // 初始化豆子 - 在所有非墙壁、非起始位置、非NPC位置的地方放置豆子
      const ghostPositions = this.ghosts.map(ghost => ghost.position)
      for (let i = 1; i <= totalCells; i++) {
        if (!this.walls.includes(i) &&
            i !== this.pacmanPosition &&
            !ghostPositions.includes(i)) {
          this.dots.push(i)
        }
      }

      // 添加能量豆 - 在四个角落附近
      this.powerPellets = [42, 59, 342, 359]
      // 从普通豆子中移除能量豆位置
      this.powerPellets.forEach(pellet => {
        const index = this.dots.indexOf(pellet)
        if (index > -1) {
          this.dots.splice(index, 1)
        }
      })

      this.totalDots = this.dots.length + this.powerPellets.length
    },

    move() {
      this.intId = setInterval(() => {
        // 移动吃豆人
        this.movePacman()

        // 移动NPC
        this.moveGhosts()

        // 检查碰撞
        this.checkCollisions()

        // 检查获胜条件
        if (this.dots.length === 0 && this.powerPellets.length === 0) {
          this.gameOverText = '恭喜！你赢了！'
          this.stop()
        }
      }, this.speed)
    },

    movePacman() {
      let nextPosition = this.getNextPosition(this.pacmanPosition, this.direction)

      // 检查是否撞墙 - 撞墙不移动，但不游戏结束
      if (this.walls.includes(nextPosition)) {
        return
      }

      // 移动吃豆人
      this.pacmanPosition = nextPosition

      // 检查是否吃到豆子
      if (this.dots.includes(nextPosition)) {
        const index = this.dots.indexOf(nextPosition)
        this.dots.splice(index, 1)
        this.score += 10
        this.setLocalstorage()
      }

      // 检查是否吃到能量豆
      if (this.powerPellets.includes(nextPosition)) {
        const index = this.powerPellets.indexOf(nextPosition)
        this.powerPellets.splice(index, 1)
        this.score += 50
        this.setLocalstorage()
        this.activatePowerMode()
      }
    },

    moveGhosts() {
      this.ghosts.forEach(ghost => {
        // 更新最近位置记录
        this.updateGhostRecentPositions(ghost)

        let nextPosition = this.getNextPosition(ghost.position, ghost.direction)
        let needNewDirection = false

        // 检查是否需要改变方向
        if (this.walls.includes(nextPosition)) {
          needNewDirection = true
          ghost.stuckCounter++
        } else {
          // 检查是否在最近访问过的位置（避免打转）
          if (this.isRecentlyVisited(ghost, nextPosition)) {
            needNewDirection = true
            ghost.stuckCounter++
          } else {
            ghost.stuckCounter = 0
            // 检查是否在十字路口或多选择点
            const validDirections = this.getValidDirections(ghost.position)
            if (validDirections.length >= 3) {
              // 在十字路口增加改变方向的概率
              needNewDirection = Math.random() < 0.15
            } else if (validDirections.length <= 2) {
              // 在狭窄通道减少改变方向的概率
              needNewDirection = Math.random() < 0.05
            } else {
              needNewDirection = Math.random() < 0.08
            }
          }
        }

        // 如果卡住太久，强制改变策略
        if (ghost.stuckCounter > 5) {
          needNewDirection = true
          ghost.targetChangeCounter++
        }

        if (needNewDirection) {
          const newDirection = this.chooseGhostDirection(ghost)
          if (newDirection) {
            ghost.direction = newDirection
            nextPosition = this.getNextPosition(ghost.position, ghost.direction)
          }
        }

        // 移动幽灵
        if (!this.walls.includes(nextPosition)) {
          ghost.lastDirection = ghost.direction
          ghost.position = nextPosition
          ghost.stuckCounter = 0
        } else {
          // 如果仍然撞墙，强制选择一个有效方向
          const emergencyDirection = this.getEmergencyDirection(ghost)
          if (emergencyDirection) {
            ghost.direction = emergencyDirection
            const emergencyPosition = this.getNextPosition(ghost.position, ghost.direction)
            if (!this.walls.includes(emergencyPosition)) {
              ghost.position = emergencyPosition
              ghost.stuckCounter = 0
            }
          }
        }
      })
    },

    updateGhostRecentPositions(ghost) {
      // 记录最近5个位置
      ghost.recentPositions.push(ghost.position)
      if (ghost.recentPositions.length > 5) {
        ghost.recentPositions.shift()
      }
    },

    isRecentlyVisited(ghost, position) {
      // 检查位置是否在最近3步内访问过
      return ghost.recentPositions.slice(-3).includes(position)
    },

    chooseGhostDirection(ghost) {
      const validDirections = this.getValidDirections(ghost.position)
      if (validDirections.length === 0) return null

      // 移除相反方向（除非没有其他选择）
      const oppositeDirection = this.getOppositeDirection(ghost.direction)
      let filteredDirections = validDirections.filter(dir => dir !== oppositeDirection)
      if (filteredDirections.length === 0) {
        filteredDirections = validDirections
      }

      // 移除会导致访问最近位置的方向
      let smartDirections = filteredDirections.filter(dir => {
        const testPos = this.getNextPosition(ghost.position, dir)
        return !this.isRecentlyVisited(ghost, testPos)
      })
      if (smartDirections.length === 0) {
        smartDirections = filteredDirections
      }

      // 根据游戏状态选择方向
      if (this.powerMode) {
        // 能量模式：远离Pacman
        const awayDirection = this.getBestDirectionAwayFromPacman(ghost.position, smartDirections)
        return awayDirection || smartDirections[Math.floor(Math.random() * smartDirections.length)]
      } else {
        // 普通模式：根据卡住情况决定策略
        if (ghost.stuckCounter > 3 || ghost.targetChangeCounter % 10 > 7) {
          // 如果经常卡住，使用随机移动一段时间
          return smartDirections[Math.floor(Math.random() * smartDirections.length)]
        } else {
          // 正常追踪
          const towardDirection = this.getBestDirectionTowardsPacman(ghost.position, smartDirections)
          return towardDirection || smartDirections[Math.floor(Math.random() * smartDirections.length)]
        }
      }
    },

    getEmergencyDirection(ghost) {
      const validDirections = this.getValidDirections(ghost.position)
      if (validDirections.length === 0) return null

      // 紧急情况下，选择任何有效方向
      return validDirections[Math.floor(Math.random() * validDirections.length)]
    },

    getValidDirections(position) {
      const possibleDirections = ['UP', 'DOWN', 'LEFT', 'RIGHT']
      return possibleDirections.filter(dir => {
        const testPosition = this.getNextPosition(position, dir)
        return !this.walls.includes(testPosition)
      })
    },

    getOppositeDirection(direction) {
      const opposites = {
        'UP': 'DOWN',
        'DOWN': 'UP',
        'LEFT': 'RIGHT',
        'RIGHT': 'LEFT'
      }
      return opposites[direction]
    },

    getBestDirectionTowardsPacman(ghostPosition, validDirections) {
      let bestDirection = null
      let shortestDistance = Infinity

      validDirections.forEach(direction => {
        const testPosition = this.getNextPosition(ghostPosition, direction)
        const distance = this.getDistance(testPosition, this.pacmanPosition)

        if (distance < shortestDistance) {
          shortestDistance = distance
          bestDirection = direction
        }
      })

      return bestDirection
    },

    getBestDirectionAwayFromPacman(ghostPosition, validDirections) {
      let bestDirection = null
      let longestDistance = 0

      validDirections.forEach(direction => {
        const testPosition = this.getNextPosition(ghostPosition, direction)
        const distance = this.getDistance(testPosition, this.pacmanPosition)

        if (distance > longestDistance) {
          longestDistance = distance
          bestDirection = direction
        }
      })

      return bestDirection
    },

    getDistance(pos1, pos2) {
      // 计算两个位置之间的曼哈顿距离
      const row1 = Math.ceil(pos1 / this.gridWidth)
      const col1 = ((pos1 - 1) % this.gridWidth) + 1
      const row2 = Math.ceil(pos2 / this.gridWidth)
      const col2 = ((pos2 - 1) % this.gridWidth) + 1

      return Math.abs(row1 - row2) + Math.abs(col1 - col2)
    },

    checkCollisions() {
      // 检查吃豆人与幽灵的碰撞
      this.ghosts.forEach((ghost, index) => {
        if (ghost.position === this.pacmanPosition) {
          if (this.powerMode) {
            // 能量模式下，吃掉幽灵
            this.score += 200
            this.setLocalstorage()
            // 重置幽灵到中央区域并清除记忆
            ghost.position = [210, 211][index % 2]
            ghost.recentPositions = []
            ghost.stuckCounter = 0
            ghost.targetChangeCounter = 0
          } else {
            // 普通模式下，游戏结束
            this.gameOverText = '被幽灵吃掉了！游戏结束'
            this.stop()
          }
        }
      })
    },

    activatePowerMode() {
      this.powerMode = true

      // 清除之前的计时器
      if (this.powerModeTimer) {
        clearTimeout(this.powerModeTimer)
      }

      // 设置新的计时器
      this.powerModeTimer = setTimeout(() => {
        this.powerMode = false
      }, this.powerModeDuration)
    },

    getNextPosition(current, direction) {
      let nextPosition

      switch (direction) {
        case 'LEFT':
          // 检查是否在左边界，如果是则穿越到右边
          if (current % this.gridWidth === 1) {
            // 检查是否在穿越通道中 (第10-11行)
            const row = Math.ceil(current / this.gridWidth)
            if (row >= 10 && row <= 11) {
              nextPosition = current + this.gridWidth - 1 // 穿越到右边
            } else {
              nextPosition = current // 撞墙，不移动
            }
          } else {
            nextPosition = current - 1
          }
          break
        case 'UP':
          nextPosition = current <= this.gridWidth ? current + (this.gridHeight - 1) * this.gridWidth : current - this.gridWidth
          break
        case 'RIGHT':
          // 检查是否在右边界，如果是则穿越到左边
          if (current % this.gridWidth === 0) {
            // 检查是否在穿越通道中 (第10-11行)
            const row = Math.ceil(current / this.gridWidth)
            if (row >= 10 && row <= 11) {
              nextPosition = current - this.gridWidth + 1 // 穿越到左边
            } else {
              nextPosition = current // 撞墙，不移动
            }
          } else {
            nextPosition = current + 1
          }
          break
        case 'DOWN':
          nextPosition = current > (this.gridHeight - 1) * this.gridWidth ? current - (this.gridHeight - 1) * this.gridWidth : current + this.gridWidth
          break
        default:
          nextPosition = current
          break
      }

      return nextPosition
    },

    keyDown(event) {
      if (!this.flag && !this.isGameEnded) {
        const newDirection = this.getNewDirection(event.keyCode)
        if (newDirection) {
          this.direction = newDirection
        }
      }
    },

    getNewDirection(keyCode) {
      const directionMap = {
        37: 'LEFT',
        38: 'UP',
        39: 'RIGHT',
        40: 'DOWN',
      }
      return directionMap[keyCode]
    },

    keyUp() {
      // 在Pacman游戏中，我们不需要keyUp事件处理
    },

    init() {
      this.initializeLevel()
      this.move()
    },
  },
}
</script>

<style scoped lang="less">
.pacman_container {
  position: relative;
  .game-panel {
    position: absolute;
    width: 8rem;
    height: 8rem;
    top: -0.6rem;
    left: 4.6rem;
    overflow: hidden;
    line-height: 0;
    background: #000;
    border: 0.02rem solid #0000ff;
    .col-item {
      background: #000;
      width: 0.34rem;
      height: 0.34rem;
      border-radius: 0.03rem;
      margin: 0.03rem;
      display: inline-block;
      border: none;
      box-sizing: border-box;
    }
    .pacman {
      background: #ffff00;
      border-radius: 50%;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0.08rem 0.12rem 0.08rem 0;
        border-color: transparent #000 transparent transparent;
        transform: translate(-50%, -50%);
      }
    }

    .dot {
      background: #ffb897;
      border-radius: 50%;
      width: 0.08rem !important;
      height: 0.08rem !important;
      margin: 0.16rem !important;
    }

    .power-pellet {
      background: #ffb897;
      border-radius: 50%;
      width: 0.16rem !important;
      height: 0.16rem !important;
      margin: 0.12rem !important;
      animation: pulse 1s infinite;
    }

    .wall {
      background: #0000ff;
      border: 0.01rem solid #4040ff;
    }

    .ghost-normal {
      background: #ff0000;
      border-radius: 50% 50% 0 0;
      position: relative;
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 0.1rem;
        background: linear-gradient(90deg, #ff0000 25%, #000 25%, #000 50%, #ff0000 50%, #ff0000 75%, #000 75%);
      }
    }

    .ghost-weak {
      background: #4040ff;
      border-radius: 50% 50% 0 0;
      position: relative;
      animation: ghostWeak 0.5s infinite;
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 0.1rem;
        background: linear-gradient(90deg, #4040ff 25%, #000 25%, #000 50%, #4040ff 50%, #4040ff 75%, #000 75%);
      }
    }
  }

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }

  @keyframes ghostWeak {
    0% { background: #4040ff; }
    50% { background: #ffffff; }
    100% { background: #4040ff; }
  }

  .score {
    position: absolute;
    top: -1.05rem;
    left: 1rem;
    display: flex;
    flex-direction: column;
    height: 4rem;
    div {
      width: 3rem;
      height: 100%;
      display: flex;
      flex-direction: column;
      margin-top: 0.5rem;
      align-items: center;
      justify-content: center;
      padding-left: 0.05rem;
      padding-right: 0.05rem;
      border-radius: 0.05rem;
      background: url('../../../assets/matrix_state.png') no-repeat;
      background-size: 100% 100%;

      span {
        display: block;
        width: 80%;
        margin: 0 auto;
      }
      span:nth-child(1) {
        font-size: 0.26rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        font-weight: 600;
        letter-spacing: 0.05rem;
        color: #1de9f4;
      }
      span:nth-child(2) {
        font-size: 0.5rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    span {
      font-size: 0.4rem;
      color: #fff;
      font-weight: bold;
    }
  }

  .game-over {
    position: absolute;
    width: 8rem;
    height: 8rem;
    top: -0.6rem;
    left: 4.6rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.8);
    z-index: 99;
    border-radius: 0.03rem;
    color: #ffff00;
    display: flex;
    align-items: center;
    justify-content: center;
    .game-over-text {
      font-size: 0.7rem;
      font-weight: bold;
    }
  }


}
</style>

<style lang="less">
.pacman_container {
  .el-dialog {
    .el-dialog__header {
      .el-dialog__title {
        font-size: 0.5rem !important;
      }
    }
    .el-dialog__body {
      .btnList {
        padding: 0.2rem 0;

        .btnItem {
          width: 8.75rem;
          height: 1rem;
          line-height: 1.08rem;
          background: #5472b0;
          border-radius: 0.2rem;
          margin-bottom: 0.3rem;
          text-align: center;
          font-weight: bold;
          font-size: 0.5rem;
          position: relative;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          img {
            width: 0.48rem;
            height: 0.48rem;
            position: absolute;
            top: 50%;
            left: 2.2rem;
            transform: translateY(-45%);
          }
          .box-btnItem {
            background: linear-gradient(to bottom, #fdfeff 30%, #bed1fb 90%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        .btnItem:last-child {
          margin-bottom: 0;
        }
      }
      .popupMessage {
        padding: 0.2rem 0;
        .popupMessage {
          text-align: center;
          font-size: 0.45rem;
          margin-bottom: 0.3rem;
        }
        .popupMessageBtn {
          width: 8.75rem;
          height: 1rem;
          line-height: 1rem;
          background: #5472b0;
          font-weight: bold;
          text-align: center;
          font-size: 0.5rem;
          color: #fff;
          border-radius: 0.16rem;
          margin: 0 auto;
          margin-top: 0.2rem !important;
        }
        .popupMessageBtn:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
