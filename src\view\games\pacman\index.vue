<template>
  <div class="pacman_container">
    <div class="game-panel">
      <div
        v-for="col in 400"
        :key="col"
        class="col-item"
        :class="{
          pacman: pacmanPosition == col,
          dot: isDot(col) && !isGhost(col),
          wall: isWall(col),
          'power-pellet': isPowerPellet(col) && !isGhost(col),
          'ghost-normal': isGhost(col) && !powerMode,
          'ghost-weak': isGhost(col) && powerMode,
        }"
      ></div>
    </div>
    <div class="score">
      <div class="score-container">
        <span>当前得分</span>
        <span>{{ score }}</span>
      </div>
      <div class="best-container">
        <span>最高得分</span>
        <span>{{ pacmanScore }}</span>
      </div>
    </div>
    <div class="game-over" v-if="gameOver">
      <span class="game-over-text">{{ gameOverText }}</span>
      <br />
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      :show-close="false"
      :close-on-click-modal="false"
      @opened="popupOpend"
      @close="popupClose"
      custom-class="operatePopup"
      :title="popupModule == 1 ? '操作' : '提示'"
    >
      <!--操作弹窗-->
      <div class="btnList" v-if="popupModule == 1">
        <div
          class="btnItem"
          :ref="item.ref"
          v-for="(item, index) in popupBtnList"
          :key="index"
        >
          <div class="box-btnItem" v-html="item.text"></div>
        </div>
      </div>

      <!--消息弹窗-->
      <div class="popupMessage" v-if="popupModule == 2">
        <div class="popupMessage" v-html="popupMessage"></div>
        <div
          class="popupMessageBtn"
          :ref="item.ref"
          v-for="(item, index) in popupBtnList"
          :key="index"
        >
          {{ item.text }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Pacman',
  data() {
    return {
      dialogVisible: false,
      flag: false,
      popupModule: 1, // 1、按钮弹窗  2、消息提示
      popupMessage: '',
      popupBtnNums: 0,
      score: 0,
      pacmanScore: localStorage.getItem('pacmanScore') || 0,
      popupBtnList: [],
      gridWidth: 20, // 网格宽度
      gridHeight: 20, // 网格高度
      pacmanPosition: 21, // 起始位置 (第二行第一个位置)
      direction: 'RIGHT',
      gameOver: false,
      gameOverText: '游戏结束',
      speed: 200, // 加快游戏速度
      isKeyPressed: false,
      isGameEnded: false,
      dots: [], // 豆子位置数组
      walls: [], // 墙壁位置数组
      powerPellets: [], // 能量豆位置数组
      totalDots: 0, // 总豆子数量
      ghosts: [], // NPC幽灵数组
      powerMode: false, // 能量模式
      powerModeTimer: null, // 能量模式计时器
      powerModeDuration: 10000, // 能量模式持续时间(10秒)
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '吃豆人')
    this.$store.dispatch('index/setFocusDom', null)
  },
  mounted() {
    this.init()
    document.addEventListener('keydown', (event) => {
      this.keyDown(event)
    })
    document.addEventListener('keyup', (event) => {
      this.keyUp(event)
    })
    this.setupKeyboardEvents()
  },
  methods: {
    setupKeyboardEvents() {
      this.fuc.KeyboardEvents({
        down: () => {
          if (this.dialogVisible) {
            if (this.popupBtnNums < this.popupBtnList.length - 1) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums++
              this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (!this.gameOver && !this.isGameEnded) {
            this.direction = 'DOWN'
          }
        },
        up: () => {
          if (this.dialogVisible) {
            if (this.popupBtnNums > 0) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums--
              this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (!this.gameOver && !this.isGameEnded) {
            this.direction = 'UP'
          }
        },
        left: () => {
          if (this.dialogVisible) {
            return
          }
          if (!this.gameOver && !this.isGameEnded) {
            this.direction = 'LEFT'
          }
        },
        right: () => {
          if (this.dialogVisible) {
            return
          }
          if (!this.gameOver && !this.isGameEnded) {
            this.direction = 'RIGHT'
          }
        },
        enter: () => {
          if (this.dialogVisible) {
            this.popupBtnList[this.popupBtnNums].fuc(
              this.popupBtnList[this.popupBtnNums]
            )
            return
          }
        },
        esc: () => {
          if (this.dialogVisible) {
            return
          }
          clearInterval(this.intId)
          this.isGameEnded = true
          this.dialogVisible = true
          this.popupModule = 1
          this.setupPauseMenu()
        },
      })
    },

    setupPauseMenu() {
      if (!this.gameOver) {
        this.popupBtnList = [
          {
            text: '继续游戏',
            ref: '',
            fuc: () => {
              this.dialogVisible = false
              this.flag = false
              this.isGameEnded = false
              this.move()
            },
          },
          {
            text: '重新开始',
            ref: '',
            fuc: () => {
              this.restart()
            },
          },
          {
            text: '退出游戏',
            ref: '',
            fuc: () => {
              this.$store.dispatch('index/setFocusDom', null)
              history.go(-1)
            },
          },
        ]
      } else {
        this.popupBtnList = [
          {
            text: '重新开始',
            ref: '',
            fuc: () => {
              this.restart()
            },
          },
          {
            text: '退出游戏',
            ref: '',
            fuc: () => {
              this.$store.dispatch('index/setFocusDom', null)
              history.go(-1)
            },
          },
        ]
      }
      this.popupBtnList[this.popupBtnNums].ref = 'active'
    },

    popupClose() {
      this.flag = false
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', null)
        setTimeout(() => {
          this.popupBtnList = []
          this.popupBtnNums = 0
          this.popupModule = 1
        }, 200)
      })
    },

    popupOpend() {
      this.flag = true
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        setTimeout(() => {
          document.getElementById('focus_border').style.borderColor = '#5472B0'
        }, 0)
        return
      })
    },

    setLocalstorage() {
      let score = localStorage.getItem('pacmanScore')
      if (score) {
        if (this.score > score) {
          localStorage.setItem('pacmanScore', this.score)
          this.pacmanScore = this.score
        }
      } else {
        localStorage.setItem('pacmanScore', this.score)
        this.pacmanScore = this.score
      }
    },

    restart() {
      this.pacmanPosition = 21
      this.direction = 'RIGHT'
      this.gameOver = false
      this.isGameEnded = false
      this.isKeyPressed = false
      this.speed = 200
      this.score = 0
      this.gameOverText = '游戏结束'
      this.powerMode = false
      if (this.powerModeTimer) {
        clearTimeout(this.powerModeTimer)
        this.powerModeTimer = null
      }
      this.init()
      this.dialogVisible = false
    },

    stop() {
      clearInterval(this.intId)
      this.gameOver = true
      this.isGameEnded = true
      this.setLocalstorage()
    },

    isDot(col) {
      return this.dots.includes(col)
    },

    isWall(col) {
      return this.walls.includes(col)
    },

    isPowerPellet(col) {
      return this.powerPellets.includes(col)
    },

    isGhost(col) {
      return this.ghosts.some(ghost => ghost.position === col)
    },

    initializeLevel() {
      // 初始化墙壁 - 创建一个20x20的迷宫
      this.walls = []
      this.dots = []
      this.powerPellets = []
      this.ghosts = []

      const totalCells = this.gridWidth * this.gridHeight // 400

      // 外围墙壁 (除了左右两边的中间通道)
      for (let i = 1; i <= this.gridWidth; i++) {
        this.walls.push(i) // 顶部
        this.walls.push(i + (this.gridHeight - 1) * this.gridWidth) // 底部
      }

      // 左右边墙 (留出中间通道)
      for (let i = 1; i <= this.gridHeight; i++) {
        const leftWall = (i - 1) * this.gridWidth + 1
        const rightWall = i * this.gridWidth

        // 在中间行留出通道 (第10和第11行)
        if (i !== 10 && i !== 11) {
          this.walls.push(leftWall) // 左侧
          this.walls.push(rightWall) // 右侧
        }
      }

      // 内部墙壁 - 创建迷宫结构，确保NPC区域与外界连通
      const innerWalls = [
        // 上部分水平墙壁
        43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58,
        // 左侧垂直墙壁
        63, 83, 103, 123, 143, 163, 183, 203, 223, 243, 263, 283, 303, 323, 343,
        // 右侧垂直墙壁
        78, 98, 118, 138, 158, 178, 198, 218, 238, 258, 278, 298, 318, 338, 358,
        // 中央区域部分墙壁 (留出通道让NPC可以出入)
        189, 190, 192, 193, 194, 195, 196, 197, 198, // 上方墙壁，留出191通道
        209, 218, // 左右两侧
        229, 238, // 左右两侧
        249, 250, 252, 253, 254, 255, 256, 257, 258, // 下方墙壁，留出251通道
        // 下部分结构
        323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338,
        // 更多内部结构 - 简化一些墙壁以增加连通性
        125, 126, 127, 129, 130, 131, 133, 134, 135, 136, 137, 138,
        145, 158, 165, 178, 185,
        225, 226, 227, 229, 230, 231, 233, 234, 235, 236, 237, 238,
        245, 258, 265, 278, 285, 298,
      ]
      this.walls.push(...innerWalls)

      // 初始化NPC幽灵 - 在中央区域的通道位置
      const ghostStartPositions = [191, 251] // 中央区域的通道位置，可以直接与外界连通
      ghostStartPositions.forEach((position, index) => {
        this.ghosts.push({
          id: index,
          position: position,
          direction: ['UP', 'DOWN', 'LEFT', 'RIGHT'][index % 4],
          lastDirection: null,
          color: ['red', 'pink'][index] // 不同颜色的幽灵
        })
      })

      // 初始化豆子 - 在所有非墙壁、非起始位置、非NPC位置的地方放置豆子
      const ghostPositions = this.ghosts.map(ghost => ghost.position)
      for (let i = 1; i <= totalCells; i++) {
        if (!this.walls.includes(i) &&
            i !== this.pacmanPosition &&
            !ghostPositions.includes(i)) {
          this.dots.push(i)
        }
      }

      // 添加能量豆 - 在四个角落附近
      this.powerPellets = [42, 59, 342, 359]
      // 从普通豆子中移除能量豆位置
      this.powerPellets.forEach(pellet => {
        const index = this.dots.indexOf(pellet)
        if (index > -1) {
          this.dots.splice(index, 1)
        }
      })

      this.totalDots = this.dots.length + this.powerPellets.length
    },

    move() {
      this.intId = setInterval(() => {
        // 移动吃豆人
        this.movePacman()

        // 移动NPC
        this.moveGhosts()

        // 检查碰撞
        this.checkCollisions()

        // 检查获胜条件
        if (this.dots.length === 0 && this.powerPellets.length === 0) {
          this.gameOverText = '恭喜！你赢了！'
          this.stop()
        }
      }, this.speed)
    },

    movePacman() {
      let nextPosition = this.getNextPosition(this.pacmanPosition, this.direction)

      // 检查是否撞墙 - 撞墙不移动，但不游戏结束
      if (this.walls.includes(nextPosition)) {
        return
      }

      // 移动吃豆人
      this.pacmanPosition = nextPosition

      // 检查是否吃到豆子
      if (this.dots.includes(nextPosition)) {
        const index = this.dots.indexOf(nextPosition)
        this.dots.splice(index, 1)
        this.score += 10
        this.setLocalstorage()
      }

      // 检查是否吃到能量豆
      if (this.powerPellets.includes(nextPosition)) {
        const index = this.powerPellets.indexOf(nextPosition)
        this.powerPellets.splice(index, 1)
        this.score += 50
        this.setLocalstorage()
        this.activatePowerMode()
      }
    },

    moveGhosts() {
      this.ghosts.forEach(ghost => {
        let nextPosition = this.getNextPosition(ghost.position, ghost.direction)

        // 如果撞墙或者随机改变方向，选择新方向
        if (this.walls.includes(nextPosition) || Math.random() < 0.15) {
          const possibleDirections = ['UP', 'DOWN', 'LEFT', 'RIGHT']
          let validDirections = []

          possibleDirections.forEach(dir => {
            const testPosition = this.getNextPosition(ghost.position, dir)
            if (!this.walls.includes(testPosition)) {
              validDirections.push(dir)
            }
          })

          if (validDirections.length > 0) {
            // 在能量模式下，幽灵随机移动；否则尝试朝向Pacman
            if (this.powerMode) {
              ghost.direction = validDirections[Math.floor(Math.random() * validDirections.length)]
            } else {
              // 简单的追踪逻辑：选择能让幽灵更接近Pacman的方向
              const bestDirection = this.getBestDirectionTowardsPacman(ghost.position, validDirections)
              ghost.direction = bestDirection || validDirections[Math.floor(Math.random() * validDirections.length)]
            }
            nextPosition = this.getNextPosition(ghost.position, ghost.direction)
          }
        }

        // 移动幽灵
        if (!this.walls.includes(nextPosition)) {
          ghost.position = nextPosition
        }
      })
    },

    getBestDirectionTowardsPacman(ghostPosition, validDirections) {
      let bestDirection = null
      let shortestDistance = Infinity

      validDirections.forEach(direction => {
        const testPosition = this.getNextPosition(ghostPosition, direction)
        const distance = this.getDistance(testPosition, this.pacmanPosition)

        if (distance < shortestDistance) {
          shortestDistance = distance
          bestDirection = direction
        }
      })

      return bestDirection
    },

    getDistance(pos1, pos2) {
      // 计算两个位置之间的曼哈顿距离
      const row1 = Math.ceil(pos1 / this.gridWidth)
      const col1 = ((pos1 - 1) % this.gridWidth) + 1
      const row2 = Math.ceil(pos2 / this.gridWidth)
      const col2 = ((pos2 - 1) % this.gridWidth) + 1

      return Math.abs(row1 - row2) + Math.abs(col1 - col2)
    },

    checkCollisions() {
      // 检查吃豆人与幽灵的碰撞
      this.ghosts.forEach((ghost, index) => {
        if (ghost.position === this.pacmanPosition) {
          if (this.powerMode) {
            // 能量模式下，吃掉幽灵
            this.score += 200
            this.setLocalstorage()
            // 重置幽灵到中央区域的通道位置
            ghost.position = [191, 251][index % 2]
          } else {
            // 普通模式下，游戏结束
            this.gameOverText = '被幽灵吃掉了！游戏结束'
            this.stop()
          }
        }
      })
    },

    activatePowerMode() {
      this.powerMode = true

      // 清除之前的计时器
      if (this.powerModeTimer) {
        clearTimeout(this.powerModeTimer)
      }

      // 设置新的计时器
      this.powerModeTimer = setTimeout(() => {
        this.powerMode = false
      }, this.powerModeDuration)
    },

    getNextPosition(current, direction) {
      let nextPosition

      switch (direction) {
        case 'LEFT':
          // 检查是否在左边界，如果是则穿越到右边
          if (current % this.gridWidth === 1) {
            // 检查是否在穿越通道中 (第10-11行)
            const row = Math.ceil(current / this.gridWidth)
            if (row >= 10 && row <= 11) {
              nextPosition = current + this.gridWidth - 1 // 穿越到右边
            } else {
              nextPosition = current // 撞墙，不移动
            }
          } else {
            nextPosition = current - 1
          }
          break
        case 'UP':
          nextPosition = current <= this.gridWidth ? current + (this.gridHeight - 1) * this.gridWidth : current - this.gridWidth
          break
        case 'RIGHT':
          // 检查是否在右边界，如果是则穿越到左边
          if (current % this.gridWidth === 0) {
            // 检查是否在穿越通道中 (第10-11行)
            const row = Math.ceil(current / this.gridWidth)
            if (row >= 10 && row <= 11) {
              nextPosition = current - this.gridWidth + 1 // 穿越到左边
            } else {
              nextPosition = current // 撞墙，不移动
            }
          } else {
            nextPosition = current + 1
          }
          break
        case 'DOWN':
          nextPosition = current > (this.gridHeight - 1) * this.gridWidth ? current - (this.gridHeight - 1) * this.gridWidth : current + this.gridWidth
          break
        default:
          nextPosition = current
          break
      }

      return nextPosition
    },

    keyDown(event) {
      if (!this.flag && !this.isGameEnded) {
        const newDirection = this.getNewDirection(event.keyCode)
        if (newDirection) {
          this.direction = newDirection
        }
      }
    },

    getNewDirection(keyCode) {
      const directionMap = {
        37: 'LEFT',
        38: 'UP',
        39: 'RIGHT',
        40: 'DOWN',
      }
      return directionMap[keyCode]
    },

    keyUp() {
      // 在Pacman游戏中，我们不需要keyUp事件处理
    },

    init() {
      this.initializeLevel()
      this.move()
    },
  },
}
</script>

<style scoped lang="less">
.pacman_container {
  position: relative;
  .game-panel {
    position: absolute;
    width: 8rem;
    height: 8rem;
    top: -0.6rem;
    left: 4.6rem;
    overflow: hidden;
    line-height: 0;
    background: #000;
    border: 0.02rem solid #0000ff;
    .col-item {
      background: #000;
      width: 0.34rem;
      height: 0.34rem;
      border-radius: 0.03rem;
      margin: 0.03rem;
      display: inline-block;
      border: none;
      box-sizing: border-box;
    }
    .pacman {
      background: #ffff00;
      border-radius: 50%;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0.08rem 0.12rem 0.08rem 0;
        border-color: transparent #000 transparent transparent;
        transform: translate(-50%, -50%);
      }
    }

    .dot {
      background: #ffb897;
      border-radius: 50%;
      width: 0.08rem !important;
      height: 0.08rem !important;
      margin: 0.16rem !important;
    }

    .power-pellet {
      background: #ffb897;
      border-radius: 50%;
      width: 0.16rem !important;
      height: 0.16rem !important;
      margin: 0.12rem !important;
      animation: pulse 1s infinite;
    }

    .wall {
      background: #0000ff;
      border: 0.01rem solid #4040ff;
    }

    .ghost-normal {
      background: #ff0000;
      border-radius: 50% 50% 0 0;
      position: relative;
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 0.1rem;
        background: linear-gradient(90deg, #ff0000 25%, #000 25%, #000 50%, #ff0000 50%, #ff0000 75%, #000 75%);
      }
    }

    .ghost-weak {
      background: #4040ff;
      border-radius: 50% 50% 0 0;
      position: relative;
      animation: ghostWeak 0.5s infinite;
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 0.1rem;
        background: linear-gradient(90deg, #4040ff 25%, #000 25%, #000 50%, #4040ff 50%, #4040ff 75%, #000 75%);
      }
    }
  }

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }

  @keyframes ghostWeak {
    0% { background: #4040ff; }
    50% { background: #ffffff; }
    100% { background: #4040ff; }
  }

  .score {
    position: absolute;
    top: -1.05rem;
    left: 1rem;
    display: flex;
    flex-direction: column;
    height: 4rem;
    div {
      width: 3rem;
      height: 100%;
      display: flex;
      flex-direction: column;
      margin-top: 0.5rem;
      align-items: center;
      justify-content: center;
      padding-left: 0.05rem;
      padding-right: 0.05rem;
      border-radius: 0.05rem;
      background: url('../../../assets/matrix_state.png') no-repeat;
      background-size: 100% 100%;

      span {
        display: block;
        width: 80%;
        margin: 0 auto;
      }
      span:nth-child(1) {
        font-size: 0.26rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        font-weight: 600;
        letter-spacing: 0.05rem;
        color: #1de9f4;
      }
      span:nth-child(2) {
        font-size: 0.5rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    span {
      font-size: 0.4rem;
      color: #fff;
      font-weight: bold;
    }
  }

  .game-over {
    position: absolute;
    width: 8rem;
    height: 8rem;
    top: -0.6rem;
    left: 4.6rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.8);
    z-index: 99;
    border-radius: 0.03rem;
    color: #ffff00;
    display: flex;
    align-items: center;
    justify-content: center;
    .game-over-text {
      font-size: 0.7rem;
      font-weight: bold;
    }
  }


}
</style>

<style lang="less">
.pacman_container {
  .el-dialog {
    .el-dialog__header {
      .el-dialog__title {
        font-size: 0.5rem !important;
      }
    }
    .el-dialog__body {
      .btnList {
        padding: 0.2rem 0;

        .btnItem {
          width: 8.75rem;
          height: 1rem;
          line-height: 1.08rem;
          background: #5472b0;
          border-radius: 0.2rem;
          margin-bottom: 0.3rem;
          text-align: center;
          font-weight: bold;
          font-size: 0.5rem;
          position: relative;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          img {
            width: 0.48rem;
            height: 0.48rem;
            position: absolute;
            top: 50%;
            left: 2.2rem;
            transform: translateY(-45%);
          }
          .box-btnItem {
            background: linear-gradient(to bottom, #fdfeff 30%, #bed1fb 90%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        .btnItem:last-child {
          margin-bottom: 0;
        }
      }
      .popupMessage {
        padding: 0.2rem 0;
        .popupMessage {
          text-align: center;
          font-size: 0.45rem;
          margin-bottom: 0.3rem;
        }
        .popupMessageBtn {
          width: 8.75rem;
          height: 1rem;
          line-height: 1rem;
          background: #5472b0;
          font-weight: bold;
          text-align: center;
          font-size: 0.5rem;
          color: #fff;
          border-radius: 0.16rem;
          margin: 0 auto;
          margin-top: 0.2rem !important;
        }
        .popupMessageBtn:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
