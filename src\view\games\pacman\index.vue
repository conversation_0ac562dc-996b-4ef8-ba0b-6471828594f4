<template>
  <div class="pacman_container">
    <div class="game-panel">
      <div
        v-for="col in 400"
        :key="col"
        class="col-item"
        :class="{
          pacman: pacmanPosition == col,
          dot: isDot(col),
          wall: isWall(col),
          'power-pellet': isPowerPellet(col),
        }"
      ></div>
    </div>
    <div class="score">
      <div class="score-container">
        <span>当前得分</span>
        <span>{{ score }}</span>
      </div>
      <div class="best-container">
        <span>最高得分</span>
        <span>{{ pacmanScore }}</span>
      </div>
    </div>
    <div class="game-over" v-if="gameOver">
      <span class="game-over-text">{{ gameOverText }}</span>
      <br />
    </div>
    <div class="rule">
      <h2>操作说明</h2>
      <h3>游戏目标：</h3>
      <p>
        使用你的上、下、左、右方向键来移动吃豆人，吃掉所有的小豆子以获得胜利。
      </p>
      <h3>操作方式：</h3>
      <p>• 遥控器左按键：吃豆人向左移动。</p>
      <p>• 遥控器右按键：吃豆人向右移动。</p>
      <p>• 遥控器上按键：吃豆人向上移动。</p>
      <p>• 遥控器下按键：吃豆人向下移动。</p>
      <p>• 遥控器"返回"按键实现游戏暂停。</p>
      <p>• 遥控器"中心键"为确认按键。</p>
      <h3>游戏规则：</h3>
      <p>• 游戏使用一个20x20的网格。</p>
      <p>• 游戏开始时，吃豆人会出现在起始位置，棋盘上会有豆子和墙壁。</p>
      <p>• 玩家通过上下左右方向键控制吃豆人的移动方向。</p>
      <p>• 当吃豆人触碰到豆子时，会吃掉豆子并得分。</p>
      <p>• 当吃豆人撞到墙壁时，游戏结束。</p>
      <p>• 吃完所有豆子即可获胜。</p>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :show-close="false"
      :close-on-click-modal="false"
      @opened="popupOpend"
      @close="popupClose"
      custom-class="operatePopup"
      :title="popupModule == 1 ? '操作' : '提示'"
    >
      <!--操作弹窗-->
      <div class="btnList" v-if="popupModule == 1">
        <div
          class="btnItem"
          :ref="item.ref"
          v-for="(item, index) in popupBtnList"
          :key="index"
        >
          <div class="box-btnItem" v-html="item.text"></div>
        </div>
      </div>

      <!--消息弹窗-->
      <div class="popupMessage" v-if="popupModule == 2">
        <div class="popupMessage" v-html="popupMessage"></div>
        <div
          class="popupMessageBtn"
          :ref="item.ref"
          v-for="(item, index) in popupBtnList"
          :key="index"
        >
          {{ item.text }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Pacman',
  data() {
    return {
      dialogVisible: false,
      flag: false,
      popupModule: 1, // 1、按钮弹窗  2、消息提示
      popupMessage: '',
      popupBtnNums: 0,
      score: 0,
      pacmanScore: localStorage.getItem('pacmanScore') || 0,
      popupBtnList: [],
      pacmanPosition: 21, // 起始位置
      direction: 'RIGHT',
      gameOver: false,
      gameOverText: '游戏结束',
      speed: 300,
      isKeyPressed: false,
      isGameEnded: false,
      dots: [], // 豆子位置数组
      walls: [], // 墙壁位置数组
      powerPellets: [], // 能量豆位置数组
      totalDots: 0, // 总豆子数量
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '吃豆人')
    this.$store.dispatch('index/setFocusDom', null)
  },
  mounted() {
    this.init()
    document.addEventListener('keydown', (event) => {
      this.keyDown(event)
    })
    document.addEventListener('keyup', (event) => {
      this.keyUp(event)
    })
    this.setupKeyboardEvents()
  },
  methods: {
    setupKeyboardEvents() {
      this.fuc.KeyboardEvents({
        down: () => {
          if (this.dialogVisible) {
            if (this.popupBtnNums < this.popupBtnList.length - 1) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums++
              this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (!this.gameOver && !this.isGameEnded) {
            this.direction = 'DOWN'
          }
        },
        up: () => {
          if (this.dialogVisible) {
            if (this.popupBtnNums > 0) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums--
              this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (!this.gameOver && !this.isGameEnded) {
            this.direction = 'UP'
          }
        },
        left: () => {
          if (this.dialogVisible) {
            return
          }
          if (!this.gameOver && !this.isGameEnded) {
            this.direction = 'LEFT'
          }
        },
        right: () => {
          if (this.dialogVisible) {
            return
          }
          if (!this.gameOver && !this.isGameEnded) {
            this.direction = 'RIGHT'
          }
        },
        enter: () => {
          if (this.dialogVisible) {
            this.popupBtnList[this.popupBtnNums].fuc(
              this.popupBtnList[this.popupBtnNums]
            )
            return
          }
        },
        esc: () => {
          if (this.dialogVisible) {
            return
          }
          clearInterval(this.intId)
          this.isGameEnded = true
          this.dialogVisible = true
          this.popupModule = 1
          this.setupPauseMenu()
        },
      })
    },

    setupPauseMenu() {
      if (!this.gameOver) {
        this.popupBtnList = [
          {
            text: '继续游戏',
            ref: '',
            fuc: () => {
              this.dialogVisible = false
              this.flag = false
              this.isGameEnded = false
              this.move()
            },
          },
          {
            text: '重新开始',
            ref: '',
            fuc: () => {
              this.restart()
            },
          },
          {
            text: '退出游戏',
            ref: '',
            fuc: () => {
              this.$store.dispatch('index/setFocusDom', null)
              history.go(-1)
            },
          },
        ]
      } else {
        this.popupBtnList = [
          {
            text: '重新开始',
            ref: '',
            fuc: () => {
              this.restart()
            },
          },
          {
            text: '退出游戏',
            ref: '',
            fuc: () => {
              this.$store.dispatch('index/setFocusDom', null)
              history.go(-1)
            },
          },
        ]
      }
      this.popupBtnList[this.popupBtnNums].ref = 'active'
    },

    popupClose() {
      this.flag = false
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', null)
        setTimeout(() => {
          this.popupBtnList = []
          this.popupBtnNums = 0
          this.popupModule = 1
        }, 200)
      })
    },

    popupOpend() {
      this.flag = true
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        setTimeout(() => {
          document.getElementById('focus_border').style.borderColor = '#5472B0'
        }, 0)
        return
      })
    },

    setLocalstorage() {
      let score = localStorage.getItem('pacmanScore')
      if (score) {
        if (this.score > score) {
          localStorage.setItem('pacmanScore', this.score)
          this.pacmanScore = this.score
        }
      } else {
        localStorage.setItem('pacmanScore', this.score)
        this.pacmanScore = this.score
      }
    },

    restart() {
      this.pacmanPosition = 21
      this.direction = 'RIGHT'
      this.gameOver = false
      this.isGameEnded = false
      this.isKeyPressed = false
      this.speed = 300
      this.score = 0
      this.gameOverText = '游戏结束'
      this.init()
      this.dialogVisible = false
    },

    stop() {
      clearInterval(this.intId)
      this.gameOver = true
      this.isGameEnded = true
      this.setLocalstorage()
    },

    isDot(col) {
      return this.dots.includes(col)
    },

    isWall(col) {
      return this.walls.includes(col)
    },

    isPowerPellet(col) {
      return this.powerPellets.includes(col)
    },

    initializeLevel() {
      // 初始化墙壁 - 创建一个简单的迷宫
      this.walls = []
      this.dots = []
      this.powerPellets = []

      // 外围墙壁
      for (let i = 1; i <= 20; i++) {
        this.walls.push(i) // 顶部
        this.walls.push(i + 380) // 底部
      }
      for (let i = 1; i <= 20; i++) {
        this.walls.push(i * 20) // 右侧
        this.walls.push((i - 1) * 20 + 1) // 左侧
      }

      // 内部墙壁 - 创建简单的迷宫结构
      const innerWalls = [
        // 水平墙壁
        43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58,
        // 垂直墙壁
        63, 83, 103, 123, 143, 163, 183, 203, 223, 243, 263, 283, 303, 323, 343,
        // 更多内部结构
        125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138,
        145, 158, 165, 178, 185, 198,
        225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238,
        245, 258, 265, 278, 285, 298,
      ]
      this.walls.push(...innerWalls)

      // 初始化豆子 - 在所有非墙壁、非起始位置的地方放置豆子
      for (let i = 1; i <= 400; i++) {
        if (!this.walls.includes(i) && i !== this.pacmanPosition) {
          this.dots.push(i)
        }
      }

      // 添加几个能量豆
      this.powerPellets = [42, 59, 342, 359]
      // 从普通豆子中移除能量豆位置
      this.powerPellets.forEach(pellet => {
        const index = this.dots.indexOf(pellet)
        if (index > -1) {
          this.dots.splice(index, 1)
        }
      })

      this.totalDots = this.dots.length + this.powerPellets.length
    },

    move() {
      this.intId = setInterval(() => {
        let nextPosition = this.getNextPosition()

        // 检查是否撞墙
        if (this.walls.includes(nextPosition)) {
          this.gameOverText = '撞墙了！游戏结束'
          this.stop()
          return
        }

        // 移动吃豆人
        this.pacmanPosition = nextPosition

        // 检查是否吃到豆子
        if (this.dots.includes(nextPosition)) {
          const index = this.dots.indexOf(nextPosition)
          this.dots.splice(index, 1)
          this.score += 10
          this.setLocalstorage()
        }

        // 检查是否吃到能量豆
        if (this.powerPellets.includes(nextPosition)) {
          const index = this.powerPellets.indexOf(nextPosition)
          this.powerPellets.splice(index, 1)
          this.score += 50
          this.setLocalstorage()
        }

        // 检查是否获胜
        if (this.dots.length === 0 && this.powerPellets.length === 0) {
          this.gameOverText = '恭喜！你赢了！'
          this.stop()
        }
      }, this.speed)
    },

    getNextPosition() {
      const current = this.pacmanPosition
      let nextPosition

      switch (this.direction) {
        case 'LEFT':
          nextPosition = current % 20 === 1 ? current + 19 : current - 1
          break
        case 'UP':
          nextPosition = current <= 20 ? current + 380 : current - 20
          break
        case 'RIGHT':
          nextPosition = current % 20 === 0 ? current - 19 : current + 1
          break
        case 'DOWN':
          nextPosition = current > 380 ? current - 380 : current + 20
          break
        default:
          nextPosition = current
          break
      }

      return nextPosition
    },

    keyDown(event) {
      if (!this.flag && !this.isGameEnded) {
        const newDirection = this.getNewDirection(event.keyCode)
        if (newDirection) {
          this.direction = newDirection
        }
      }
    },

    getNewDirection(keyCode) {
      const directionMap = {
        37: 'LEFT',
        38: 'UP',
        39: 'RIGHT',
        40: 'DOWN',
      }
      return directionMap[keyCode]
    },

    keyUp() {
      // 在Pacman游戏中，我们不需要keyUp事件处理
    },

    init() {
      this.initializeLevel()
      this.move()
    },
  },
}
</script>

<style scoped lang="less">
.pacman_container {
  position: relative;
  .game-panel {
    position: absolute;
    width: 8rem;
    height: 8rem;
    top: -0.6rem;
    left: 4.6rem;
    overflow: hidden;
    line-height: 0;
    background: #000;
    border: 0.02rem solid #0000ff;
    .col-item {
      background: #000;
      width: 0.34rem;
      height: 0.34rem;
      border-radius: 0.03rem;
      margin: 0.03rem;
      display: inline-block;
      border: none;
      box-sizing: border-box;
    }
    .pacman {
      background: #ffff00;
      border-radius: 50%;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0.08rem 0.12rem 0.08rem 0;
        border-color: transparent #000 transparent transparent;
        transform: translate(-50%, -50%);
      }
    }

    .dot {
      background: #ffb897;
      border-radius: 50%;
      width: 0.08rem !important;
      height: 0.08rem !important;
      margin: 0.16rem !important;
    }

    .power-pellet {
      background: #ffb897;
      border-radius: 50%;
      width: 0.16rem !important;
      height: 0.16rem !important;
      margin: 0.12rem !important;
      animation: pulse 1s infinite;
    }

    .wall {
      background: #0000ff;
      border: 0.01rem solid #4040ff;
    }
  }

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }

  .score {
    position: absolute;
    top: -1.05rem;
    left: 1rem;
    display: flex;
    flex-direction: column;
    height: 4rem;
    div {
      width: 3rem;
      height: 100%;
      display: flex;
      flex-direction: column;
      margin-top: 0.5rem;
      align-items: center;
      justify-content: center;
      padding-left: 0.05rem;
      padding-right: 0.05rem;
      border-radius: 0.05rem;
      background: url('../../../assets/matrix_state.png') no-repeat;
      background-size: 100% 100%;

      span {
        display: block;
        width: 80%;
        margin: 0 auto;
      }
      span:nth-child(1) {
        font-size: 0.26rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        font-weight: 600;
        letter-spacing: 0.05rem;
        color: #1de9f4;
      }
      span:nth-child(2) {
        font-size: 0.5rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    span {
      font-size: 0.4rem;
      color: #fff;
      font-weight: bold;
    }
  }

  .game-over {
    position: absolute;
    width: 8rem;
    height: 8rem;
    top: -0.6rem;
    left: 4.6rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.8);
    z-index: 99;
    border-radius: 0.03rem;
    color: #ffff00;
    display: flex;
    align-items: center;
    justify-content: center;
    .game-over-text {
      font-size: 0.7rem;
      font-weight: bold;
    }
  }

  .rule {
    position: absolute;
    width: 3.6rem;
    right: -0.2rem;
    top: -0.6rem;
    padding: 0.2rem;
    background: url("../../../assets/connectFour_state.png") no-repeat;
    background-size: 100% 100%;
    h3 {
      margin-top: 0.15rem;
      margin-bottom: 0.08rem;
    }
    p {
      font-size: 0.21rem;
    }
  }
}
</style>

<style lang="less">
.pacman_container {
  .el-dialog {
    .el-dialog__header {
      .el-dialog__title {
        font-size: 0.5rem !important;
      }
    }
    .el-dialog__body {
      .btnList {
        padding: 0.2rem 0;

        .btnItem {
          width: 8.75rem;
          height: 1rem;
          line-height: 1.08rem;
          background: #5472b0;
          border-radius: 0.2rem;
          margin-bottom: 0.3rem;
          text-align: center;
          font-weight: bold;
          font-size: 0.5rem;
          position: relative;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          img {
            width: 0.48rem;
            height: 0.48rem;
            position: absolute;
            top: 50%;
            left: 2.2rem;
            transform: translateY(-45%);
          }
          .box-btnItem {
            background: linear-gradient(to bottom, #fdfeff 30%, #bed1fb 90%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        .btnItem:last-child {
          margin-bottom: 0;
        }
      }
      .popupMessage {
        padding: 0.2rem 0;
        .popupMessage {
          text-align: center;
          font-size: 0.45rem;
          margin-bottom: 0.3rem;
        }
        .popupMessageBtn {
          width: 8.75rem;
          height: 1rem;
          line-height: 1rem;
          background: #5472b0;
          font-weight: bold;
          text-align: center;
          font-size: 0.5rem;
          color: #fff;
          border-radius: 0.16rem;
          margin: 0 auto;
          margin-top: 0.2rem !important;
        }
        .popupMessageBtn:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
