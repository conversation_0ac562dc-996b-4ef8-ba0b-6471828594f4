const emptyGrid = [
    [{}, {}, {}, {}, {}, {}, {}],
    [{}, {}, {}, {}, {}, {}, {}],
    [{}, {}, {}, {}, {}, {}, {}],
    [{}, {}, {}, {}, {}, {}, {}],
    [{}, {}, {}, {}, {}, {}, {}],
    [{}, {}, {}, {}, {}, {}, {}]
];

import { music } from '@/unit/music'

export default {
    name: 'connectFour',
    created() {
        //设置页面左上角标题
        this.$store.dispatch('index/setMainTitle','四子棋')
        this.$store.dispatch('index/setFocusDom', null);
    },
    mounted() {
        this.btnList[this.btnIndex].ref="active"
        // this.$nextTick(()=>{
        //     this.$store.dispatch('index/setFocusDom',this.$refs.active);
        // })
        this.fuc.KeyboardEvents({
            down:()=>{

            },
            up:()=>{

            },
            left:()=>{
                if (this.dialogVisible) {
                    return
                }
                if (!this.player) {
                    return
                }
                if (this.btnIndex > 0) {
                    this.btnList[this.btnIndex].ref = ""
                    this.btnIndex--
                    this.btnList[this.btnIndex].ref = "active"
                    if (music.move) {
                        music.move()
                    }
                }
            },
            right:()=>{
                if (this.dialogVisible) {

                    return
                }
                if (!this.player) {
                    return
                }
                if (this.btnIndex < this.btnList.length-1) {
                    this.btnList[this.btnIndex].ref = ""
                    this.btnIndex++
                    this.btnList[this.btnIndex].ref = "active"
                    if (music.move) {
                        music.move()
                    }
                }
            },
            enter:()=>{
                if (this.dialogVisible) {
                    this.startNewGame()
                    this.closePopup()
                    return
                } else if (this.gameOver || this.isADraw) {
                    this.startNewGame()
                    return;
                }
                if (!this.player) {
                    return
                }
                this.dropPiece(this.$refs.active[0])
            },
            esc:()=>{
                if (this.dialogVisible) {
                    this.closePopup()
                    return
                }
                this.$store.dispatch('index/setFocusDom', null);
                history.go(-1)
            }
        })


        // window.onbeforeunload = () => {
        //     if (this.areThereMoves) return "你確定要離開此遊戲嗎？";
        // };
        this.fistDown = Math.floor(Math.random() * 2);
        this.player = this.fistDown

    },
    destroyed() {

    },
    watch:{
        'player'() {
            this.$nextTick(()=>{
                if (!this.player) {
                    this.aiShow()
                } else {
                    if (this.$refs.active[0]) {
                        this.$refs.active[0].classList.remove('ai')
                    }
                }

            })
        }
    },
    data() {
        return {
            fistDown: 0,
            player: null,
            dialogVisible: false,
            popupModule: 2,
            popupMessage:'',
            popupBtnList:[
                {
                    text: '再来一局',
                    ref:'',
                    fuc: this.startNewGame
                }
            ],


            btnIndex: 0,
            btnList:[
            {
                index: 0,
                ref:''
            },{
                index: 1,
                ref:''
            },{
                index: 2,
                ref:''
            },{
                index: 3,
                ref:''
            },{
                index: 4,
                ref:''
            },{
                index: 5,
                ref:''
            },{
                index: 6,
                ref:''
            }],
            // grid: emptyGrid,
            grid: [
                [{}, {}, {}, {}, {}, {}, {}],
                [{}, {}, {}, {}, {}, {}, {}],
                [{}, {}, {}, {}, {}, {}, {}],
                [{}, {}, {}, {}, {}, {}, {}],
                [{}, {}, {}, {}, {}, {}, {}],
                [{}, {}, {}, {}, {}, {}, {}]
                // [{color: 'red'}, {color: 'yellow'}, {color: 'yellow'}, {color: 'red'}, {color: 'red'}, {color: 'yellow'}, {}],
                // [{color: 'yellow'}, {color: 'red'}, {color: 'red'}, {color: 'red'}, {color: 'yellow'}, {color: 'yellow'}, {}],
                // [{color: 'red'}, {color: 'yellow'}, {color: 'yellow'}, {color: 'yellow'}, {color: 'red'}, {color: 'yellow'}, {color: 'yellow'}],
                // [{color: 'yellow'}, {color: 'red'}, {color: 'red'}, {color: 'red'}, {color: 'yellow'}, {color: 'red'}, {color: 'red'}],
                // [{color: 'red'}, {color: 'yellow'}, {color: 'yellow'}, {color: 'red'}, {color: 'red'}, {color: 'yellow'}, {color: 'yellow'}],
                // [{color: 'yellow'}, {color: 'red'}, {color: 'red'}, {color: 'red'}, {color: 'yellow'}, {color: 'yellow'}, {color: 'red'}]
            ],
            redTurn: true,
            gameOver: false
        }
    },
    components: {

    },

    computed: {
        currentTurnColor() {
            return this.redTurn ? "red" : "yellow";
        },
        currentTurnColorText() {
            return this.redTurn ? "红方" : "黄方";
        },

        message() {
            if (this.isADraw) {
                this.openPopup()
                return `<strong style="color: inherit">平手！</strong>`;
            } else if (this.gameOver) {
                this.openPopup()
                // return `<strong class="${this.currentTurnColor}">${this.currentTurnColorText} 贏了！</strong>`;
                return `<strong class="${this.currentTurnColor}">${this.redTurn ? this.$refs.redDom.innerHTML : this.$refs.yellowDom.innerHTML} 贏了！</strong>`;
            } else {
                return `<strong class="${this.currentTurnColor}">${this.currentTurnColorText}</b>的回合`;
            }
        },

        areThereMoves() {
            let moves = false;
            this.grid.forEach(row => {
                row.forEach(column => {
                    if (column.color) moves = true;
                });
            });
            return moves;
        },

        isADraw() {
            let draw = true;
            this.grid.forEach(row => {
                row.forEach(column => {
                    if (!column.color) draw = false;
                });
            });
            return draw;
        }
    },
    methods: {
        aiShow() {
            if (this.isADraw) {
                return
            }
            if (this.$refs.active[0]) {
                this.$refs.active[0].classList.add('ai')
            }
            let aiPlayer = Math.floor(Math.random() * 7)
            // 假设grid是包含对象的二维数组
            let allHaveColorProperty = true;

            for (let i = 0; i < this.grid.length; i++) {
                let obj = this.grid[i][aiPlayer]; // 获取当前数组中的第三个对象
                if (!obj || !obj.hasOwnProperty('color')) {
                    allHaveColorProperty = false;
                    break; // 如果有一个不包含color属性，则立即跳出循环
                }
            }
            if (!allHaveColorProperty) {
                setTimeout(()=>{
                    this.btnList[this.btnIndex].ref = ""
                    this.btnIndex = aiPlayer
                    this.btnList[this.btnIndex].ref = "active"
                    this.$refs.active[0].classList.remove('ai')
                    this.$nextTick(()=>{
                        this.dropPiece(this.$refs.active[0])
                    })
                },500)

            } else {
                this.aiShow()
            }
        },
        closePopup() {
            this.dialogVisible = false
            this.popupBtnList[0].ref = ""
        },
        openPopup() {
            this.btnList[this.btnIndex].ref = ""
            this.popupBtnList[0].ref = "active"
            this.dialogVisible = true
            this.btnIndex = 0
        },
        popupOpend() {
            this.$nextTick(()=>{
                this.$store.dispatch('index/setFocusDom', this.$refs.active[0]);
                setTimeout(()=>{
                    document.getElementById('focus_border').style.borderColor = "#00CCFF"
                },150)

            })
        },
        popupClose() {
            this.$nextTick(()=>{
                this.popupBtnList[0].ref = ""
                this.btnList[this.btnIndex].ref = "active"
                this.$store.dispatch('index/setFocusDom', null);
                document.getElementById('focus_border').style.borderColor = "#fff"
            })
        },
        dropPiece(e) {
            if (this.gameOver) return;

            const column = e.target ? e.target.dataset.column: e.dataset.column;

            for (let i = this.grid.length - 1; i >= 0; i--) {

                if (!this.grid[i][column].color) {

                    this.grid[i][column].color = this.currentTurnColor;
                    this.refreshGrid();
                    const isGameOver = this.victoryCheck();

                    if (isGameOver) {
                        this.gameOver = true;
                    } else {
                        this.switchTurn();
                        if (music.fall) {
                            music.fall()
                        }
                        this.player = !this.player
                    }
                    break;
                }
            }

        },

        handleMoveCursor(e, x) {
            const currentBtn = parseInt(e.target.dataset.column);
            if (!e.key) return;

            if (["ArrowRight", "ArrowDown"].includes(e.key)) {
                this.focusAdjacentBtn(e, currentBtn, 1);
            } else if (["ArrowLeft", "ArrowUp"].includes(e.key)) {
                this.focusAdjacentBtn(e, currentBtn, -1);
            }
        },

        focusAdjacentBtn(e, currentBtn, direction) {
            e.preventDefault();
            if (!direction) return;
            const adjacentBtn = document.querySelector(
                `button[data-column="${currentBtn + direction}"]`);

            if (adjacentBtn) {
                adjacentBtn.focus();
            } else if (direction > 0) {
                document.querySelector(`button[data-column]`).focus();
            } else {
                document.querySelector(`button[data-column="7"]`).focus();
            }
        },

        handleHover(e) {
            e.target.focus();
        },

        handleSlotHover(e) {
            const column = e.target.dataset.column;
            document.
            querySelector(`#button-board button[data-column="${column}"]`).
            focus();
        },

        switchTurn() {
            this.redTurn = !this.redTurn;
        },

        refreshGrid() {
            this.grid = [...this.grid];
        },

        getPieceIcon(color) {
            return color == "yellow" ? "⭐" : "🔴";
        },

        victoryCheck() {
            let isGameOver = false;
            this.grid.forEach((row, rowIndex) => {
                row.forEach((column, colIndex) => {
                    var _this$grid, _this$grid$rowIndex, _this$grid$rowIndex2, _this$grid2, _this$grid2$rowIndex, _this$grid2$rowIndex2, _this$grid3, _this$grid3$rowIndex, _this$grid3$rowIndex2, _this$grid4, _this$grid5, _this$grid5$colIndex, _this$grid6, _this$grid7, _this$grid7$colIndex, _this$grid8, _this$grid9, _this$grid9$colIndex, _this$grid10, _this$grid11, _this$grid12, _this$grid13, _this$grid14, _this$grid15, _this$grid16, _this$grid17, _this$grid18, _this$grid19, _this$grid20, _this$grid21, _this$grid22, _this$grid23, _this$grid24, _this$grid25, _this$grid26, _this$grid27;
                    const slotColor = column.color;
                    if (!slotColor || isGameOver) return;
                    if (
                        slotColor == ((_this$grid = this.grid) === null || _this$grid === void 0 ? void 0 : (_this$grid$rowIndex = _this$grid[rowIndex]) === null || _this$grid$rowIndex === void 0 ? void 0 : (_this$grid$rowIndex2 = _this$grid$rowIndex[colIndex + 1]) === null || _this$grid$rowIndex2 === void 0 ? void 0 : _this$grid$rowIndex2.color) &&
                        slotColor == ((_this$grid2 = this.grid) === null || _this$grid2 === void 0 ? void 0 : (_this$grid2$rowIndex = _this$grid2[rowIndex]) === null || _this$grid2$rowIndex === void 0 ? void 0 : (_this$grid2$rowIndex2 = _this$grid2$rowIndex[colIndex + 2]) === null || _this$grid2$rowIndex2 === void 0 ? void 0 : _this$grid2$rowIndex2.color) &&
                        slotColor == ((_this$grid3 = this.grid) === null || _this$grid3 === void 0 ? void 0 : (_this$grid3$rowIndex = _this$grid3[rowIndex]) === null || _this$grid3$rowIndex === void 0 ? void 0 : (_this$grid3$rowIndex2 = _this$grid3$rowIndex[colIndex + 3]) === null || _this$grid3$rowIndex2 === void 0 ? void 0 : _this$grid3$rowIndex2.color) ||
                        slotColor == ((_this$grid4 = this.grid) === null || _this$grid4 === void 0 ? void 0 : (_this$grid5 = _this$grid4[rowIndex - 1]) === null || _this$grid5 === void 0 ? void 0 : (_this$grid5$colIndex = _this$grid5[colIndex]) === null || _this$grid5$colIndex === void 0 ? void 0 : _this$grid5$colIndex.color) &&
                        slotColor == ((_this$grid6 = this.grid) === null || _this$grid6 === void 0 ? void 0 : (_this$grid7 = _this$grid6[rowIndex - 2]) === null || _this$grid7 === void 0 ? void 0 : (_this$grid7$colIndex = _this$grid7[colIndex]) === null || _this$grid7$colIndex === void 0 ? void 0 : _this$grid7$colIndex.color) &&
                        slotColor == ((_this$grid8 = this.grid) === null || _this$grid8 === void 0 ? void 0 : (_this$grid9 = _this$grid8[rowIndex - 3]) === null || _this$grid9 === void 0 ? void 0 : (_this$grid9$colIndex = _this$grid9[colIndex]) === null || _this$grid9$colIndex === void 0 ? void 0 : _this$grid9$colIndex.color) ||
                        slotColor == ((_this$grid10 = this.grid) === null || _this$grid10 === void 0 ? void 0 : (_this$grid11 = _this$grid10[rowIndex - 1]) === null || _this$grid11 === void 0 ? void 0 : (_this$grid12 = _this$grid11[colIndex + 1]) === null || _this$grid12 === void 0 ? void 0 : _this$grid12.color) &&
                        slotColor == ((_this$grid13 = this.grid) === null || _this$grid13 === void 0 ? void 0 : (_this$grid14 = _this$grid13[rowIndex - 2]) === null || _this$grid14 === void 0 ? void 0 : (_this$grid15 = _this$grid14[colIndex + 2]) === null || _this$grid15 === void 0 ? void 0 : _this$grid15.color) &&
                        slotColor == ((_this$grid16 = this.grid) === null || _this$grid16 === void 0 ? void 0 : (_this$grid17 = _this$grid16[rowIndex - 3]) === null || _this$grid17 === void 0 ? void 0 : (_this$grid18 = _this$grid17[colIndex + 3]) === null || _this$grid18 === void 0 ? void 0 : _this$grid18.color) ||
                        slotColor == ((_this$grid19 = this.grid) === null || _this$grid19 === void 0 ? void 0 : (_this$grid20 = _this$grid19[rowIndex + 1]) === null || _this$grid20 === void 0 ? void 0 : (_this$grid21 = _this$grid20[colIndex + 1]) === null || _this$grid21 === void 0 ? void 0 : _this$grid21.color) &&
                        slotColor == ((_this$grid22 = this.grid) === null || _this$grid22 === void 0 ? void 0 : (_this$grid23 = _this$grid22[rowIndex + 2]) === null || _this$grid23 === void 0 ? void 0 : (_this$grid24 = _this$grid23[colIndex + 2]) === null || _this$grid24 === void 0 ? void 0 : _this$grid24.color) &&
                        slotColor == ((_this$grid25 = this.grid) === null || _this$grid25 === void 0 ? void 0 : (_this$grid26 = _this$grid25[rowIndex + 3]) === null || _this$grid26 === void 0 ? void 0 : (_this$grid27 = _this$grid26[colIndex + 3]) === null || _this$grid27 === void 0 ? void 0 : _this$grid27.color)) {
                        isGameOver = true;
                    }
                });
            });
            return isGameOver;
        },

        startNewGame() {
            // if (this.areThereMoves) {
            //     const areYouSure = confirm(
            //         "开始新的游戏？");
            //
            //     if (!areYouSure) return;
            // }
            this.grid.forEach(row => {
                row.forEach(column => {
                    column.color = null;
                });
            });
            this.refreshGrid();
            this.gameOver = false;
            this.redTurn = true;

            this.player = null
            this.fistDown = Math.floor(Math.random() * 2);
            this.player = this.fistDown

            this.grid = JSON.parse(JSON.stringify(emptyGrid))
            // if (!this.player) {
            //     this.aiShow()
            // }
        }
    }
}