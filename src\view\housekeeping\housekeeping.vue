<!--助浴助洁-->
<template>
  <div class="housekeeping">
    <div class="container">
      <div class="left">
        <div class="leftContent scrollParent"  v-if="this.leftList.length > 0" :style="{width: leftList.length> 6 ? '16.68rem' : '16.8rem'}">
          <div class="leftList" :style="{ top: '0rem'}">
            <div class="messageItem" :ref="item.ref" v-for="(item, index) in leftList" :key="index" :style="{width: leftList.length> 6 ? '5.28rem' : '5.4rem'}">
              <div class="item_user">
                <div :class="item.name ? 'pic' : 'picItem'" v-lazy-container="{ selector: 'img' }">
                  <img :data-src="item.img" :data-error="lazyError" :key="index" alt=""  />
                  <div class="payNum" v-if="item.reserved > 0">
                    已预约
                  </div>
                </div>

                <div :class="item.name ? 'contentInfo' : 'contentInfo'">
                  <div class="title">
                    {{ item.name || item.title }}
                  </div>
                  <div class="vegetables_name">
                    {{item.describe}}
                  </div>


                </div>
              </div>
            </div>
          </div>
        </div>
<!--        <div class="no_content" v-else>暂无商品列表</div>-->
      </div>

    </div>
    <div class="address">
      <template>
        <div class="name">(地址:{{this.supplierInfo.address}}-电话:{{this.supplierInfo.telephone}})</div>
      </template>
    </div>
    <div class="tel">
      <img :src="require('@/assets/quan.png')" alt="">
      <span>温馨提示:</span>
      {{this.supplierInfo.remark}}
    </div>
  </div>
</template>
      
<script>
import { getHouseKeepingList } from '@/api/index'

export default {
  name:'housekeeping',
  components: {},
  data() {
    return {
      lazyError: require('@/assets/zhujie.png'),
      sendData: [],
      canSend: false,
      supplierInfo: null,
      leftList: [],
      rightList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      order_type: 1,
      order_detail: [],

      firstShow: true,


      messageList: [],
      statusList:[]
    }
  },
  created() {
    this.supplierInfo =  JSON.parse(sessionStorage.getItem('supplierInfo'))

    //设置页面左上角标题
    this.$nextTick(() => {
        this.$store.dispatch('index/setMainTitle', this.supplierInfo.title)
    })

  },
  computed: {},
  watch: {},
  mounted() {
    if (localStorage.getItem('statusList')) {
      this.statusList = JSON.parse(localStorage.getItem('statusList'))
    }

    if (sessionStorage.getItem('housekeeping_focus_index')) {
      this.leftNums = Number(sessionStorage.getItem('housekeeping_focus_index'))
      sessionStorage.removeItem('housekeeping_focus_index')
    }

    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        this.leftList[this.leftNums].ref = ''
        if (this.leftList[this.leftNums + 3]) {
          this.leftNums = this.leftNums + 3
        } else {
          if (this.leftNums % 3 != 0) {
            this.leftNums = this.leftList.length - 1
          }
        }
        this.leftList[this.leftNums].ref = 'active'

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.leftList[this.leftNums - 3]) {
          this.leftList[this.leftNums].ref = ''
          this.leftNums -= 3
          this.leftList[this.leftNums].ref = 'active'
        }

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.leftNums % 3 != 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
          }

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        // 在左侧
        if (this.nextNums == -1) {
          if (this.leftNums % 3 != 2) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            this.leftList[this.leftNums].ref = 'active'
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        sessionStorage.setItem('housekeeping_focus_index',this.leftNums)
        this.$router.push({
          path: './housekeepDetail',
          query:{
            title: this.leftList[this.leftNums].name,
            id:  this.leftList[this.leftNums].id
          }
        })
      },
      esc: () => {
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {

    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState', true)

      //获取商品列表
      getHouseKeepingList({
        fk_jiazheng_org_id: JSON.parse(sessionStorage.getItem('supplierInfo')).fk_supplier_id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
      }).then((res) => {
        this.$store.dispatch('app/setLoadingState', false)
        if (res.code == 200) {
          if (res.data) {
            let leftListClone = JSON.parse(JSON.stringify(res.data))
            leftListClone.map((item) => {
              item.ref = ''
              if (item.img) {
                item.img = item.img.indexOf('http') > -1 ? item.img : process.env.VUE_APP_API + item.img
              }
            })
            this.leftList = leftListClone
          }

          if (this.leftList.length > 0) {
            this.nextNums = -1
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      }).catch(err=>{
        this.$store.dispatch('app/setLoadingState', false)
      })


    },

  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = null
  },
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.housekeeping {
  width: 16.73rem;
  .address {
    display: flex;
    position: absolute;
    top: 1.6rem;
    left: 1.45rem;
    .name {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
      max-width: 16.4rem;
    }
    .name::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .tel {
    position: absolute;
    bottom: 0.54rem;
    right: 1rem;
    letter-spacing: 0.03rem;
    font-size: 0.28rem;
    color: #B2BAEF;
    display: flex;
    align-items: center;
    img {
      display: block;
      width: 0.32rem;
      height: 0.32rem;
      margin-right: 0.04rem;
      margin-top: 0.03rem;
    }
  }
  .container {
    height: 7rem;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    .noData {
      div {
        font-size: 0.5rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -200%);
        letter-spacing: 0.05rem;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
      }
    }
    .left {
      width: 16.8rem;
      height: 7rem;
      margin-left: 0;
      .leftContent {
        width: 16.8rem;
        height: 100%;
        position: relative;
        display: inline-block;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          // left: 0.8rem;
          transition: all 0.2s;
          .messageItem {
            width: 5.28rem;
            height: 3.3603rem;
            //padding: 0.45rem;
            position: relative;
            margin-right: 0.28rem;
            margin-bottom: 0.28rem;
            background-size: 100% 100% !important;
            background: #262954;
            border-radius: 0.3rem;
            overflow: hidden;
            .item_user {
              display: flex;
              align-items: center;
              //width: 100%;
              height: 100%;
              padding: 0 0.35rem;
              .pic {
                width: 1.7rem !important;
                height: 1.75rem !important;
                border-radius: 0.2rem;
                border: 0.05rem solid #A8ABBC;
                background: #fff;
                position: relative;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .payNum {
                width: 0.9rem;
                height: 0.4rem;
                position: absolute;
                top: -0.05rem;
                left: -0.05rem;
                background: #FF8A15;
                color: #fff;
                font-size: 0.24rem;
                font-weight: bold;
                text-align: center;
                line-height: 0.4rem;
                border-radius: 0.05rem;
              }
              .picItem {
                width: 1.12rem !important;
                height: 1.1rem !important;
                border-radius: 0.15rem;
                position: relative;
                img {
                  width: 100%;
                  height: 100%;
                }

              }
              .contentInfo {
                margin-left: 0.5rem !important;
                height: calc(100% - 1.4rem);
                width: 2.6rem;
                position: relative;
                .title {
                  font-size: 0.36rem !important;
                  font-weight: bold;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                }
                .vegetables_name {
                  font-size: 0.28rem;
                  color: #C7CFFF;
                  margin: 0.1rem 0;
                  overflow: hidden;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  white-space: wrap;
                }

              }
            }
            .choose_tip {
              width: 4rem;
              height: 0.37rem;
              position: relative;
              top: 0.13rem;
              right: 0.5rem;
              div {
                width: 100%;
                height: 100%;
                font-size: 0.22rem;
                background: #3cc92d;
                border-radius: 0.05rem;
                position: absolute;
                text-align: center;
                line-height: 0.35rem;
                font-weight: bold;
                color: #fff;
                letter-spacing: 0.03rem;
              }
            }
          }
           .messageItem:nth-child(3n + 3) {
             margin-right: 0;
           }
        }
      }
      .no_content {
        position: absolute;
        line-height: 7rem;
        left: 6rem;
        // width: 4rem;
        // height: 7rem;
        text-align: center;
        font-size: 0.4rem;
        letter-spacing: 0.05rem;
        line-height: 6rem;
        color: #b5c0ff;
      }
    }
    .no_info {
      //position: absolute;
      line-height: 7rem;
      // left: 6rem;
      //right: 2.3rem;
      // width: 4rem;
      // height: 7rem;
      text-align: center;
      font-size: 0.4rem;
      letter-spacing: 0.05rem;
      text-indent: 0.05rem;
      line-height: 6rem;
      color: #b5c0ff;
    }
  }
}
</style>
<style lang="less">
.housekeeping {

}
</style>
      