<template>
  <div class="policy">
    <div class="left">
      <div class="leftList">
        <div class="leftTitle">
          <div class="pic"><img src="@/assets/policy_search.png" alt="" /></div>
          <div class="title">信息大全</div>
        </div>
        <div class="container">
          <div class="list scrollParent">
            <div class="list-detail" :style="{ top: '0rem' }">
              <div
                class="leftItem"
                :ref="item.ref"
                v-for="(item, index) in leftList"
                :key="index"
              >
                <div class="title">
                  {{ item.title }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="right" ref="refRight">
      <div class="messageCenterList">
        <div class="rightTitle">
          <div class="pic"><img src="@/assets/policy_news.png" alt="" /></div>
          <div class="title">我的推送</div>
        </div>
        <div class="rightList" v-if="this.rightList.length > 0">
          <el-carousel
            :interval="6000"
            arrow="never"
            @change="handleChange"
            :initial-index="initNum"
            :autoplay="autoplay"
          >
            <el-carousel-item v-for="(item, index) in rightList" :key="index">
              <div class="title">{{ item.title }}</div>
              <div class="content" v-html="item.content"></div>
            </el-carousel-item>
          </el-carousel>
        </div>
        <div class="no_content" v-else>{{ info }}</div>
      </div>
    </div>
  </div>
</template>


  <script>
import { getNewsList, getInfoList } from '@/api/index'

export default {
  name: 'policy',
  components: {},
  data() {
    return {
      leftList: [],
      rightList: [],
      info: '',
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      initNum: 0,
      stutas: 0,
      messageList: [],
      video_url: '',
      autoplay: true,
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '政策顾问')
  },
  computed: {},
  watch: {
    leftList(list) {
      if (list.length > 0) {
        // 确保列表不为空
        if (sessionStorage.getItem('indexPolicyOne')) {
          setTimeout(() => {
            this.leftNums = Number(sessionStorage.getItem('indexPolicyOne'))
            this.leftList[this.leftNums].ref = 'active'
            this.nextNums = -1
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
              this.$refs.active[0].classList.add('select')
            })
            sessionStorage.removeItem('indexPolicyOne')
          }, 150)
        }
      }
    },
  },
  mounted() {
    this.getData()
    // this.getRightList()
    // if (sessionStorage.getItem('indexPolicyOne')) {
    //   this.$nextTick(() => {
    //     setTimeout(() => {
    //   this.leftNums = Number(sessionStorage.getItem('indexPolicyOne'))
    //   this.leftList[this.leftNums].ref = 'active'
    //   this.nextNums = -1
    //   this.$nextTick(() => {
    //     this.$store.dispatch('index/setFocusDom', this.$refs.active)
    //     this.$refs.active[0].classList.add('select')
    //   })
    //   sessionStorage.removeItem('indexPolicyOne')
    //     }, 50)
    //   })
    // }
    if (sessionStorage.getItem('indexPolicyTwo')) {
      this.initNum = Number(sessionStorage.getItem('indexPolicyTwo'))
      sessionStorage.removeItem('indexPolicyTwo')
    }
    this.fuc.KeyboardEvents({
      down: () => {
        if (
          this.$store.state.app.media &&
          this.$store.state.app.media.isFullscreen
        ) {
          return
        }
        if (this.nextNums == -1) {
          if (this.leftNums < this.leftList.length - 1) {
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            this.leftList[this.leftNums].ref = 'active'
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
          this.$refs.active[0].classList.add('select')
        })
      },
      up: () => {
        if (
          this.$store.state.app.media &&
          this.$store.state.app.media.isFullscreen
        ) {
          return
        }
        if (this.nextNums == -1) {
          if (this.leftNums > 0 && this.leftNums - 1 >= 0) {
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
          this.$refs.active[0].classList.add('select')
        })
      },
      left: () => {
        if (
          this.$store.state.app.media &&
          this.$store.state.app.media.isFullscreen
        ) {
          return
        }
        if (this.nextNums > -1) {
          if (this.leftList.length > 0) {
            this.$refs.active = []
            this.leftList[this.leftNums].ref = 'active'
            this.nextNums = -1
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
          this.$refs.active[0].classList.add('select')
        })
      },
      right: () => {
        if (
          this.$store.state.app.media &&
          this.$store.state.app.media.isFullscreen
        ) {
          return
        }
        if (this.nextNums == -1) {
          if (this.leftList.length > 0) {
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ''
            this.$refs.active.splice(0, 1, this.$refs.refRight)
            this.nextNums = this.leftNums
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      enter: () => {
        if (this.nextNums == -1) {
          if (this.leftList[this.leftNums]) {
            sessionStorage.setItem('indexPolicyOne', this.leftNums)
            this.$nextTick(() => {
              if (this.leftList[this.leftNums].template == 'V1') {
                this.$router.push({
                  path: '/policyDetail',
                  query: {
                    classification_id: this.leftList[this.leftNums].id,
                    title: this.leftList[this.leftNums].title,
                  },
                })
              } else if (this.leftList[this.leftNums].template == 'V2') {
                this.$router.push({
                  path: '/serviceAgency',
                })
              }
            })
          }
          return
        }
        if (this.nextNums > -1) {
          let content = this.rightList[this.rightNums].content
          sessionStorage.setItem('indexPolicyTwo', this.rightNums)

          // if (!content || content == '<p><br></p>') {
          //   this.autoplay = false
          //   if (this.$store.state.app.media) {
          //     if (this.$store.state.app.media.isFullscreen) {
          //       return
          //     }

          //     // 全屏切换
          //     this.$store.state.app.media.fullscreen('t')
          //     this.$nextTick(() => {
          //       this.$store.dispatch('index/setFocusDom', document.body)
          //     })
          //   }
          // } else {
          this.$nextTick(() => {
            this.$router.push({
              path: '/detailsPage',
              query: {
                id: this.rightList[this.rightNums].id,
                title: this.rightList[this.rightNums].title,
              },
            })
          })
          // }

          return
        }
      },
      esc: () => {
        if (
          this.$store.state.app.media &&
          this.$store.state.app.media.isFullscreen
        ) {
          this.$store.state.app.media.fullscreen('t')
          this.autoplay = true
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
          return
        }
        if (this.$store.state.app.media) {
          this.$store.state.app.media.stop()
          sessionStorage.removeItem('indexPolicyTwo')
        }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    handleChange(index) {
      this.rightNums = index
    },

    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState', true)

      // 获取信息大全列表
      getInfoList({
        user_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].id,
      }).then((res) => {
        this.$store.dispatch('app/setLoadingState', false)

        if (res.code == 200) {
          let leftClone = JSON.parse(JSON.stringify(res.data))
          leftClone.map((item) => {
            item.ref = ''
          })
          this.leftList = leftClone

          this.$nextTick(() => {
            if (!sessionStorage.getItem('indexPolicyOne')) {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            }
            this.getRightList()
          })
        }
      })
    },

    // 获取推送列表
    getRightList() {
      this.$store.dispatch('app/setLoadingState', true)

      getNewsList({
        // user_id:
        //   this.$store.getters.getUserInfo.oldsters[
        //     this.$store.state.app.selectUserIndex
        //   ].id,
      }).then(
        (res) => {
          this.$store.dispatch('app/setLoadingState', false)

          if (res.code == 200) {
            let rightClone = JSON.parse(JSON.stringify(res.data))
            rightClone.map((item) => {
              item.ref = ''
              item.content = item.content.replace(
                new RegExp('<p', 'g'),
                '<p style="word-break:break-all;" '
              )
              if (item.content.indexOf('<img') > -1) {
                let reg = new RegExp('/public/storage/', 'g')
                item.content = item.content.replace(
                  reg,
                  process.env.VUE_APP_API + '/public/storage/'
                )
              }
            })
            this.rightList = rightClone
            if (!sessionStorage.getItem('indexPolicyOne')) {
              this.nextNums = this.leftNums
              this.$refs.active = []
              this.$refs.active.push(this.$refs.refRight)
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
              sessionStorage.removeItem('indexPolicyOne')
            } else {
              this.nextNums = -1
            }
          }
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          if (
            error.response &&
            error.response.data &&
            error.response.data.msg
          ) {
            this.info += "暂无数据"
          }
          this.nextNums = -1
          this.$refs.active = []
          const storedIndex = sessionStorage.getItem('indexPolicyOne')
          this.leftNums = storedIndex ? Number(storedIndex) : 0
          this.leftList[this.leftNums].ref = 'active'

          this.$nextTick(() => {
            if (this.$refs.active && this.$refs.active[0]) {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
              this.$refs.active[0].classList.add('select')
            }
          })
        }
      )
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
  <style lang='less' scoped>
.policy {
  // width: 16.8rem;
  height: 7rem;
  overflow: hidden;
  // display: grid;
  // grid-template-columns: repeat(1, 1fr); /* 每行显示两个元素 */
  // gap: 0.16rem;
  display: flex;
  font-size: 0.2rem;
  color: #e7e7ef;

  .left {
    width: 3.7rem;
    height: 7rem;
    background-color: #252b4c;
    border-radius: 0.25rem;
    position: relative;

    .leftList {
      // width: 94%;
      // text-align: center;
      position: absolute;
      transition: all 0.25s;

      .leftTitle {
        display: flex;
        margin-left: 0.3rem;
        margin-bottom: 0.15rem;
        border-bottom: 0.02rem solid #424a6e;
        .pic {
          margin-left: 0.2rem;
          margin-top: 0.3rem;
          width: 0.6rem;
          height: 0.56rem;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .title {
          width: 2rem;
          height: 0.88rem;
          font-size: 0.42rem;
          font-weight: bold;
          color: #b4c0ff;
          line-height: 1rem;
          // float: left;
          letter-spacing: 0.05rem;
          margin-left: 0.25rem;
          margin-top: 0.05rem;
        }
      }
      .container {
        height: 5.75rem;
        overflow: hidden;
        .list {
          height: 100%;
          position: relative;
          .list-detail {
            width: 94%;
            text-align: center;
            position: absolute;
            transition: all 0.3s;
            .leftItem {
              width: 2.7rem;
              text-align: left;
              height: 0.7rem;
              line-height: 0.7rem;
              margin-bottom: 0.305rem;
              margin-left: 0.5rem;
              border-radius: 0.2rem;
              position: relative;
              font-weight: bold;
              transition: all 0.4s;
              letter-spacing: 0.05rem;
              text-indent: 0.05rem;
              font-size: 0.34rem;
              color: #b4c0ff;
              text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
              .title {
                width: 2rem;
                padding-left: 0.5rem;
              }
              .title::before {
                content: '';
                width: 0.14rem;
                height: 0.14rem;
                border-radius: 50%;
                background: #6699ff;
                position: absolute;
                top: 50%;
                left: 0.2rem;
                margin-top: -0.07rem;
              }
            }
            .select {
              color: #fff;
              background: #c09165;
              transition: all 0.4s;
              text-shadow: 0.03rem 0.03rem 0.01rem rgba(102, 129, 218, 0.6);
            }
            .select::before {
              content: '';
              width: 0.14rem;
              height: 0.14rem;
              border-radius: 50%;
              background: #fff;
              position: absolute;
              top: 50%;
              left: 0.2rem;
              margin-top: -0.07rem;
              z-index: 9;
            }
          }
        }
      }
    }
  }
  .right {
    width: 12.5rem;
    height: 7rem;
    margin-left: 0.5rem;
    background-color: #252b4c;
    border-radius: 0.25rem;
    //padding-left: 0.1rem;
    //margin-left: 0.05rem;

    .messageCenterList {
      width: 13rem;
      height: 100%;
      padding: 0 0.5rem;
      position: relative;
      .rightTitle {
        width: 11.5rem;
        display: flex;
        border-bottom: 0.02rem solid #424a6e;
        .pic {
          // margin-left: 0.1rem;
          margin-top: 0.3rem;
          width: 0.6rem;
          height: 0.56rem;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .title {
          width: 2rem;
          height: 1rem;
          font-size: 0.42rem;
          font-weight: bold;
          color: #b4c0ff;
          line-height: 1rem;
          float: left;
          letter-spacing: 0.05rem;
          margin-left: 0.2rem;
          margin-top: 0.05rem;
        }
      }
      .rightList {
        width: 11.2rem !important;
        margin-top: 0.2rem;
        // width: 5.8rem !important;
        // border-radius: 0.3rem;
        position: absolute;
        left: 0.65rem;
        transition: all 0.2s;
        overflow: hidden;

        .messageItem {
          // width: 100%;
          height: 2.3rem;
          padding: 0.5rem;
          margin-bottom: 0.28rem;
          background-size: 100% 100% !important;
          // background: #ccc !important;
          background: #262954;
          border-radius: 0.3rem;
          overflow: hidden;
          .item_user {
            display: flex;
            height: 100%;

            img {
              width: 2.45rem;
              height: 2.45rem;
              border-radius: 50%;
            }
            .content {
              padding: 0.3rem;
              .title {
                font-weight: bold;
                color: #fff;
                font-size: 0.4rem;
                margin-bottom: 0.2rem;
              }
              .sub_title {
                font-size: 0.3rem;
                color: #b7c0f9;
                margin-bottom: 0.2rem;
              }
              .addr {
                font-size: 0.3rem;
                color: #b7c0f9;
              }
            }
            .status {
              width: 1.2rem;
              height: 0.4rem;
              right: 0rem;
              top: 0rem;
              left: inherit;
              div {
                position: absolute;
                top: 0rem;
                // right: 0.01rem;
                width: 1.2rem;
                height: 0.4rem;
                background: #fd8b19;
                border-radius: 0.05rem;
                text-align: center;
                line-height: 0.35rem;
                font-weight: bold;
                color: #fff;
                letter-spacing: 0.03rem;
              }
            }
          }
        }
      }
      .no_content {
        position: absolute;
        line-height: 7rem;
        left: 5.3rem;
        bottom: 0.5rem;
        text-align: center;
        font-size: 0.4rem;
        letter-spacing: 0.05rem;
        line-height: 6rem;
        color: #b5c0ff;
      }
    }
  }
}
</style>
<style lang="less">
.policy {
  .el-carousel {
    height: 5.8rem;
    overflow-y: inherit;
    position: relative;
    .el-carousel__container {
      position: relative;
      width: 11.5rem;
      height: 5rem;
      .el-carousel__item {
        width: 11rem;
        padding-left: 0.4rem;
        outline: none;
        -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
        -webkit-focus-ring-color: rgba(0, 0, 0, 0);
        .title {
          width: 11rem;
          font-size: 0.38rem;
          font-weight: 500;
          color: #dae0ff;
        }
        .content {
          width: 10.5rem !important;
          height: 4.25rem !important;
          margin-top: 0.1rem !important;
          font-size: 0.31rem !important;
          font-weight: normal !important;
          text-overflow: ellipsis !important;
          overflow: hidden;
          // display: -webkit-box;
          // -webkit-line-clamp: 9;
          // -webkit-box-orient: vertical;
        }
      }
      .el-carousel__item::before {
        content: '';
        font-size: 0.32rem;
        font-weight: bold;
        color: #f1f1f1;
        width: 0.13rem;
        height: 0.13rem;
        border-radius: 50%;
        top: 0.2rem;
        left: 0rem;
        background: #6699ff;
        position: absolute;
      }
    }
    .el-carousel__indicators {
      position: absolute;
      bottom: 0.3rem;
      li {
        .el-carousel__button {
          width: 0.1rem;
          height: 0.1rem;
          background-color: #aeb2c9;
          border-radius: 0.05rem;
        }
      }
    }
    .el-carousel__indicators .el-carousel__indicator.is-active {
      .el-carousel__button {
        width: 0.25rem;
      }
    }
  }
  .left {
    .leftList {
      .scroll {
        background: #171443 !important;
        top: 3.3rem !important;
        left: 4.65rem !important;
      }
    }
  }
}
</style>