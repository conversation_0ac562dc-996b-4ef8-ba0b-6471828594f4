###网关TV项目
#### 默认MAC  98BC57B83846
#### 焦点获取方式为:获取当前需要显示焦点的元素的ref。设置完成，需保存到vuex   示例:
```vue
    this.$nextTick(()=>{
        this.$store.dispatch('index/setFocusDom',this.$refs.active);
    })
```

#### 需要焦点  且需要翻页的 元素  需要边距的 必须存在margin-bottom
#### 页面翻页 需要动画的  必须用单位秒（s）
#### 页面翻页 需要滚动的部分，必须position: absolute;top: 0rem;
#### 需要滚动条显示的 需要在父节点增加类名 scrollParent  相邻两个scrollParent 需要使用key区分,页面渲染完毕，调用下方函数
```vue
    this.$nextTick(()=>{
      this.fuc.setScroll()
    })
```

#### 离开需要翻页的页面（翻页页面消失）  需要 this.$store.dispatch('app/setViewAreaOffsetTop', 0)


