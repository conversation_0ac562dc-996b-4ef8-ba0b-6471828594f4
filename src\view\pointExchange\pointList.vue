<template>
  <div class="pointList">
    <div class="pointList-container">
      <div class="point-current">
        <img src="@/assets/point_rest.png" alt="" />
        <div class="rest">
          我的积分：
          <div class="pic">
            <img src="@/assets/point_icon.png" alt="" />
          </div>
          <span>{{ restPoint }}</span>
        </div>
      </div>
      <div class="list">
        <div class="listContent">
          <div class="goodList" style="left: 0rem" v-if="leftList.length > 0">
            <div
              class="goodItem"
              :ref="item.ref"
              v-for="(item, index) in leftList"
              :key="index"
              :data="swiperIndex"
            >
              <div class="pic" v-lazy-container="{ selector: 'img' }">
                <img
                  :data-src="apiUrl + item.img"
                  alt=""
                  :data-error="errorImg"
                />
              </div>
              <div class="title">{{ item.title }}</div>
              <div class="point">
                <div class="pic">
                  <img src="@/assets/point_icon.png" alt="" />
                </div>
                <div class="num">
                  <span>{{ item.point }}</span> 积分
                </div>
              </div>
            </div>
          </div>
          <div class="no_info" v-else>暂无兑换物品</div>

          <div class="pointer" v-if="leftList.length > 3">
            <div
              v-for="(item, index) in leftList.length"
              :key="index"
              :style="{ width: pointerIndex == index ? '0.2rem' : '0.08rem' }"
            ></div>
          </div>
        </div>
      </div>
      <el-dialog
        class="popup_box_bg"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        title="操作"
      >
        <!--是否兑换-->
        <div class="message_box sendCall" v-if="popupModule == 1">
          <div class="message_content">
            <div class="content">
              <div class="title">
                你要兑换：
                <span>{{
                  this.leftList[this.swiperIndex] &&
                  this.leftList[this.swiperIndex].title
                }}</span>
              </div>
              <div class="tip">
                你的兑换申请一经提交，将无法更改或者撤销，请确认是否兑换。
              </div>
            </div>
          </div>
        </div>
        <div
          class="message_box"
          v-if="popupModule == 2"
          v-html="popupMessage"
        ></div>
        <div class="popupBtnList">
          <div
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
          >
            {{ item.text }}
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getPointList, exchangeGoods, getRestPoint } from '@/api/index'
export default {
  data() {
    return {
      restPoint: 0,
      pointerIndex: 0,
      swiperIndex: 0,
      leftList: [],
      leftNums: 0,
      apiUrl: process.env.VUE_APP_API,
      errorImg: require('@/assets/customer.png'),
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '积分兑换')
    this.$store.dispatch('index/setFocusDom', null)
  },
  mounted() {
    // this.restPoint = this.$route.query.restPoint
    this.updataTotalPoint()
    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {},
      up: () => {},
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        if (this.leftList.length > 1) {
          if (this.swiperIndex > 0) {
            this.leftList[this.swiperIndex].ref = ''
            this.swiperIndex--
            this.leftList[this.swiperIndex].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
              // 指示器
              const element = this.$refs.active[0]
              if (element) {
                const windowWidth = element.parentNode.parentNode.clientWidth // 可视区域宽度
                const elementWidth = Number(element.clientWidth) // 当前元素宽度
                const elementOffsetLeft = element.offsetLeft // 当前元素距离顶端的宽度
                const windowScrollLeft = element.parentNode.offsetLeft // 页面下拉宽度

                if (element.parentNode.clientWidth > windowWidth) {
                  this.pointerIndex--
                }
              }
            })
          }
        }
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        if (this.leftList.length > 1) {
          if (this.swiperIndex < this.leftList.length - 1) {
            this.leftList[this.swiperIndex].ref = ''
            this.swiperIndex++
            this.leftList[this.swiperIndex].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
              // 指示器
              const element = this.$refs.active[0]
              if (element) {
                const windowWidth = element.parentNode.parentNode.clientWidth // 可视区域宽度
                const elementWidth = Number(element.clientWidth) // 当前元素宽度
                const elementOffsetLeft = element.offsetLeft // 当前元素距离顶端的宽度
                const windowScrollLeft = element.parentNode.offsetLeft // 页面下拉宽度

                if (element.parentNode.clientWidth > windowWidth) {
                  this.pointerIndex++
                }
              }
            })
          }
        }
      },
      enter: () => {
        if (this.dialogVisible) {
          // 弹窗
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.leftList[this.swiperIndex].ref = 'active'
          this.dialogVisible = false
          this.popupBtnList[this.popupBtnNums].fuc(
            this.leftList[this.swiperIndex]
          )
          return
        }
        if (this.leftList.length > 0) {
          this.popupBtnList = [
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            },
            {
              text: '确认',
              ref: '',
              fuc: () => {
                this.sendOrder()
              },
            },
          ]

          this.dialogVisible = true
          this.popupModule = 1
          this.leftList[this.swiperIndex].ref = ''
          this.popupBtnList[this.popupBtnNums].ref = 'active'
        }
      },
      esc: () => {
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.leftList[this.swiperIndex].ref = 'active'
          this.dialogVisible = false
          return
        }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        this.popupBtnNums = 0
        this.popupModule = 1
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },
    getData() {
      getPointList().then((res) => {
        if (res.code == 200) {
          // const copies = 2;
          // let listClone = JSON.parse(JSON.stringify([].concat(...Array(copies).fill(res.data))))
          let listClone = JSON.parse(JSON.stringify(res.data))
          listClone.map((item) => {
            item.ref = ''
          })
          this.leftList = listClone
        }
        if (this.leftList.length > 0) {
          this.leftList[this.leftNums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      })
    },
    updataTotalPoint() {
      getRestPoint({
        fk_home_id: this.$store.getters.getUserInfo.home_id,
      }).then((res) => {
        if (res.code == 200) {
          this.restPoint = res.data.point
        }
      })
    },
    sendOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      exchangeGoods({
        fk_home_id: this.$store.getters.getUserInfo.home_id,
        fk_item_id: this.leftList[this.swiperIndex].id,
      }).then(
        (res) => {
          if (res.code == 200) {
            setTimeout(() => {
              this.$store.dispatch('app/setLoadingState', false)
              this.popupModule = 2
              this.leftList[this.swiperIndex].ref = ''
              this.popupBtnList[this.popupBtnNums].fuc(
                this.leftList[this.leftNums]
              )
              this.popupBtnList = [
                {
                  text: '确认',
                  ref: 'active',
                  fuc: () => {
                    this.dialogVisible = false
                    this.updataTotalPoint()
                  },
                },
              ]
              this.popupMessage = '<br/>兑换成功!<br>'
              this.popupBtnNums = 0
              this.dialogVisible = true
            }, 1000)
          }
        },
        (err) => {
          setTimeout(() => {
            this.$store.dispatch('app/setLoadingState', false)
            this.popupModule = 2
            this.leftList[this.swiperIndex].ref = ''
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                },
              },
            ]

            if (err.response && err.response.data && err.response.data.msg) {
              this.popupMessage = err.response.data.msg
            }
            this.popupBtnNums = 0
            this.dialogVisible = true
          }, 1000)
        }
      )
    },
  },
}
</script>

<style lang="less" scoped>
.pointList {
  .pointList-container {
    .point-current {
      display: flex;
      align-items: center;
      // justify-content: center;
      font-size: 0.3rem;
      // font-weight: bold;
      line-height: 0.5rem;
      color: #fff;
      letter-spacing: 0.05rem;
      position: relative;
      img {
        width: 100%;
        height: 1rem;
        border-radius: 0.3rem;
        object-fit: cover;
      }
      .rest {
        position: absolute;
        left: 0.35rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.35rem;
        // font-weight: bold;
        line-height: 0.5rem;
        color: #fff;
        // color: #2074f2;
        letter-spacing: 0.05rem;
        // text-shadow: 0.01rem 0 0 #fff;
        .pic {
          width: 0.45rem;
          height: 0.45rem;
          img {
            width: 100%;
            height: 100%;
          }
        }

        span {
          margin-left: 0.1rem;
          font-size: 0.5rem;
        }
      }
    }
    .list {
      width: 16.8rem;
      height: 6rem;
      margin-top: 0.2rem;
      .listContent {
        width: 100%;
        height: 6.5rem;
        overflow: hidden;
        position: relative;
        // .popupTitle {
        //   height: 0.4rem;
        //   line-height: 0.4rem;
        //   font-size: 0.18rem;
        //   font-weight: bold;
        //   margin-bottom: 0.06rem;
        // }
        .goodList {
          width: max-content;
          height: 5.5rem;
          position: absolute;
          left: 0;
          transition: all 0.3s;
          margin-top: 0.16rem;
          .goodItem {
            width: 5.49rem;
            height: 100%;
            margin-right: 0.165rem;
            float: left;
            display: flex;
            flex-direction: column;
            align-items: center;
            border-radius: 0.18rem;
            // margin-top: 0.1rem;
            .pic {
              width: 5.5rem;
              height: 4.5rem;
              // margin-top: 0.1rem;
              img {
                width: 100%;
                height: 100%;
                border-radius: 0.18rem;
              }
            }
            .title {
              font-size: 0.3rem;
              font-weight: bold;
              margin: 0.08rem 0;
            }
            .point {
              display: flex;
              .pic {
                width: 0.45rem;
                height: 0.45rem;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .num {
                margin-left: 0.1rem;
                line-height: 0.4rem;
                color: #f0bf3d;
                font-size: 0.3rem;
                span {
                  font-size: 0.35rem;
                  font-weight: bold;
                }
              }
            }
          }
        }
        .no_info {
          position: absolute;
          line-height: 6rem;
          left: 50%;
          transform: translateX(-50%);
          // width: 4rem;
          // height: 7rem;
          text-align: center;
          font-size: 0.6rem;
        }
        .pointer {
          // width: 4rem;
          position: absolute;
          bottom: 0.3rem;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          justify-content: center;
          align-items: center;
          div {
            width: 0.08rem;
            height: 0.08rem;
            transition: all 0.3s;
            border-radius: 0.04rem;
            background: #888;
            display: inline-block;
            margin-right: 0.08rem;
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.pointList {
  // position: relative;
  .pointList-container {
    .el-dialog {
      width: 10rem;
      height: 8rem;
      margin-top: 0 !important;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 0.26rem;
      background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

      .el-dialog__header {
        height: 10%;
        display: flex;
        align-items: center;
        padding-top: 0.4rem;
        .el-dialog__title {
          display: block;
          line-height: normal !important;
          height: 100%;
          font-size: 0.4rem;
          color: #fff;
          background-image: linear-gradient(
            to bottom,
            rgba(255, 255, 255, 1),
            rgba(255, 255, 255, 0.65)
          );
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          font-weight: bold;
          width: 100%;
          text-align: center;
        }
      }
      .el-dialog__body {
        width: 92%;
        height: 80%;
        position: absolute;
        bottom: 4%;
        left: 4%;
        border-radius: 0.2rem;
        background: #fff;
        padding: 0 !important;
        .message_box {
          padding: 0.4rem;
          color: #464646;
          font-size: 0.6rem;
          font-weight: bold;
          height: 60%;
          display: flex;
          align-items: center;
          justify-content: center;
          .message_content {
            margin-top: 0.3rem;
            .content {
              width: 100% !important;
              height: 100% !important;
              .title {
                font-size: 0.5rem;
                font-weight: bold;
                // text-align: center;
              }
              .tip {
                font-size: 0.3rem;
                margin-top: 0.2rem;
                line-height: 0.5rem;
              }
            }
          }
        }
        .cancel {
          flex-direction: column;
          div {
            text-align: center;
          }
          div:nth-child(2) {
            margin-top: 0.1rem;
            color: red;
            line-height: 0.48rem;
          }
          //align-items: normal !important;
        }
        .popupBtnList {
          display: flex;
          flex-direction: row;
          justify-content: space-evenly;
          margin-top: 0.25rem;
          div {
            width: 3.84rem;
            height: 1rem;
            line-height: 1.08rem;
            text-align: center;
            border-radius: 0.3rem;
            color: #fff;
            font-size: 0.45rem;
            font-weight: bold;
            letter-spacing: 0.05rem;
            text-indent: 0.05rem;
            background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
          }
        }
      }
    }
  }

  .popup_box_bg {
    .el-dialog {
      width: 10rem;
      height: 7rem;
      background: url('../../assets/point_tip.png') no-repeat;
      background-size: 100% 100%;
      .el-dialog__header {
        display: none;
      }
      .el-dialog__body {
        background: transparent;

        .info {
          display: flex;
          justify-content: center !important;
          align-items: center !important;
          margin-top: -0.4rem;
          .title {
            display: flex;
            flex-direction: column;
            align-items: center !important;
            text-align: center !important;
            margin-top: -0.3rem;
            span {
              text-align: center !important;
              font-size: 0.5rem !important;
              color: #e7f1fa !important;
              letter-spacing: 0.14rem !important;
            }
          }
          .phone {
            width: 7rem;
            text-align: center;
            position: absolute;
            top: 1.3rem;
            font-size: 0.3rem !important;
            color: yellow !important;
          }
          .tip {
            color: #3288dc;
            font-size: 0.4rem;
            margin-top: 1.2rem;
          }
        }
      }
    }
  }
}
</style>