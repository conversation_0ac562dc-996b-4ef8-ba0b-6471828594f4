<template>
  <div class="homeCare">
    <div class="container">
      <div class="left">
        <div
          class="messageCenterList scrollParent"
          v-if="this.leftList.length > 0"
        >
          <div class="leftList" :style="{ top: '0rem' }">
            <div
              class="messageItem"
              :ref="item.ref"
              v-for="(item, index) in leftList"
              :key="index"
            >
              <div class="item_user">
                <div
                  :class="item.name ? 'pic' : 'picItem'"
                  v-lazy-container="{ selector: 'img' }"
                >
                  <template v-if="supplier_type == '19'">
                    <img
                      :data-src="apiUrl + item.img"
                      alt=""
                      :data-error="errorOne"
                    />
                  </template>
                  <template v-else>
                    <img
                      :data-src="apiUrl + item.img"
                      alt=""
                      :data-error="errorTwo"
                    />
                  </template>
                </div>
                <div :class="item.name ? 'content' : 'contentItem'">
                  <div class="title">
                    <div class="name">{{ item.name || item.title }}</div>
                    <div class="price" v-if="item.price">
                      {{ item.price }}<span>元</span>
                    </div>
                  </div>
                  <div class="info">
                    <template v-if="item.fk_vegetables_name"
                      >套餐内容：{{ item.fk_vegetables_name }}</template
                    >
                    <template v-else>{{ item.describe }}</template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_content" v-else>暂无商品列表</div>
      </div>
      <div
        class="right scrollParent"
        :key="this.rightList.length"
        v-if="this.rightList.length > 0"
      >
        <div class="list" :style="{ top: '0rem' }">
          <div
            :class="item.name ? 'rightList' : 'rightListItem'"
            :ref="item.ref"
            v-for="(item, index) in rightList"
            :key="index"
          >
            <div class="listContent">
              <div class="status">
                <!-- <template v-if="item.status_name">
                {{ item.status_name }}
              </template> -->
                <template>
                  <div
                    :style="{
                      color: statusList[item.status].color,
                    }"
                  >
                    {{ statusList[item.status].text }}
                  </div>
                </template>
              </div>
              <div class="good">
                <template v-if="item.name">
                  <div class="title">商品名称：</div>
                  <div class="time">{{ item.name }}</div></template
                >
                <template v-else
                  ><div class="title">服务内容：</div>
                  <div class="time">{{ item.type_name }}</div></template
                >
              </div>
              <div class="price" v-if="item.price">
                <div class="title">商品价格：</div>
                <div class="time">{{ item.price }} 元</div>
              </div>
              <div class="pay" v-if="item.order_type">
                <div class="title">付款方式：</div>
                <div class="name">
                  {{
                    item.order_type === 1
                      ? '货到付款（送货上门）'
                      : item.order_type === 2
                      ? '在线支付（送货上门）'
                      : '在线支付（到店自提）'
                  }}
                </div>
              </div>
              <div class="time">
                <template v-if="item.created_at">
                  <div class="title">申请时间：</div>
                  <div class="name">{{ item.created_at }}</div></template
                >
                <template v-else>
                  <div class="title">申请时间：</div>
                  <div class="name">{{ item.apply_datetime }}</div></template
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="no_info" v-else>暂无记录</div>
      <el-dialog
        :class="
          popupModule == 4
            ? 'popup_box_bg'
            : supplier_type == '22'
            ? 'popup_box_bgc'
            : 'popup_box'
        "
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        :title="
          this.supplier_type == '19'
            ? '社区买菜'
            : this.supplier_type == '8'
            ? '日常维修'
            : '居家服务'
        "
      >
        <!--发起预约-->
        <div
          class="message_box sendCall"
          :style="{
            'align-items': supplier_type == '19' ? 'flex-start' : 'center',
          }"
          v-if="popupModule == 1"
        >
          <template v-if="this.supplier_type == '19'">
            <div class="title">
              是否要订购
              <span>{{ leftList[leftNums] && leftList[leftNums].name }}</span>
              ？
            </div>
            <div class="detail">
              套餐内容：{{
                leftList[leftNums] && leftList[leftNums].fk_vegetables_name
              }}
            </div>
            <div class="price">
              价格：
              <span
                >{{ leftList[leftNums] && leftList[leftNums].price }}元</span
              >
            </div>
          </template>
          <template v-else>
            <div class="sendCall_home">
              <div class="title">
                <p>请确认需要申请的服务。</p>
              </div>
              <div class="detail">
                <p>服务内容：</p>
                <span>{{
                  leftList[leftNums] && leftList[leftNums].title
                }}</span>
              </div>
            </div>
          </template>
        </div>

        <!--取消预约-->
        <div
          class="message_box cancel"
          :style="{
            'align-items': supplier_type == '19' ? 'flex-start' : 'center',
          }"
          v-if="popupModule == 2"
        >
          <template v-if="this.supplier_type == '19'">
            <div class="status">
              当前状态：{{
                rightList[rightNums] && rightList[rightNums].status_name
              }}
            </div>
            <div class="apply">
              商品名称：{{ rightList[rightNums] && rightList[rightNums].name }}
            </div>
            <div class="time">
              商品价格：{{
                rightList[rightNums] && rightList[rightNums].price
              }}元
            </div>
            <div class="name">
              付款方式：{{
                rightList[rightNums].order_type === 1
                  ? '货到付款（送货上门）'
                  : rightList[rightNums].order_type === 2
                  ? '在线支付（送货上门）'
                  : '在线支付（到店自提）'
              }}
            </div>
            <div class="hospital">
              商家名称：{{
                rightList[rightNums] && rightList[rightNums].fk_supplier_name
              }}
            </div>
            <div class="department">
              商家电话：{{
                rightList[rightNums] && rightList[rightNums].telephone
              }}
            </div>
          </template>
          <template v-else>
            <div class="cancel_item">
              <div class="title" v-if="rightList[rightNums].status == 2">
                <p>你即将取消当前服务。</p>
              </div>
              <div class="detail">
                <p>服务内容：</p>
                <span>{{
                  rightList[rightNums] && rightList[rightNums].type_name
                }}</span>
              </div>
            </div>
          </template>
        </div>

        <div
          class="message_box result"
          v-html="popupMessage"
          v-if="popupModule == 3"
        ></div>
        <div class="message_box info" v-if="popupModule == 4">
          <div class="title">
            <span>你的预约</span><span>已经发送成功！</span>
          </div>
          <div class="phone">
            请在手机号为<span>{{
              user_telephone.substring(0, 3) +
              '****' +
              user_telephone.substring(7, 11)
            }}</span
            >设备上完成付款。
          </div>
          <div class="tip">您可以在“预约记录”查看预约信息。</div>
        </div>
        <div class="popupBtnList">
          <div
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
          >
            {{ item.text }}
          </div>
        </div>
      </el-dialog>

      <el-dialog
        class="select_pay_box"
        :visible.sync="selectDialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="selectOpend"
        @close="selectClose"
        title="选择付款方式"
      >
        <div class="selectBtnList">
          <div
            class="btn"
            :ref="item.ref"
            v-for="(item, index) in selectBtnList"
            :key="index"
          >
            <div class="name">
              <span>{{ item.name }}</span>
            </div>
            <div class="text">
              <span>{{ item.text }}</span>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
    <div class="address">
      <template v-if="this.supplier_type == '19'">
        <div class="name">商品列表</div>
      </template>
      <template v-else>
        <div class="name">服务列表</div>
      </template>
    </div>
    <div class="tel">
      <template v-if="supplier_type == '19'">
        客服热线：{{ hotLine }}
      </template>
    </div>
    <div class="done"><div class="item">服务记录</div></div>
  </div>
</template>
      
<script>
import {
  getSaleList,
  submitSaleList,
  cancelSaleList,
  getServiceList,
  cancelService,
  getHomeList,
  submitHome,
  cancelHome,
} from '@/api/index'

export default {
  name: 'homeCare',
  components: {},
  data() {
    return {
      leftList: [],
      rightList: [],
      statusList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      apiUrl: process.env.VUE_APP_API,
      errorOne: require('@/assets/shop_icon.png'),
      errorTwo: require('@/assets/service_icon.png'),
      sessionStorage_supplier_id: '',
      order_type: 1,
      user_telephone: '',
      supplier_type: '', // 19为社区买菜   8为日常维修  22为居家服务
      hotLine: '',
      dialogVisible: false,
      selectDialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      selectBtnNums: 0,
      selectBtnList: [],
    }
  },
  created() {
    //设置页面左上角标题
    this.$nextTick(() => {
      if (this.supplier_type == '19') {
        this.$store.dispatch('index/setMainTitle', '社区买菜')
      } else if (this.supplier_type == '8') {
        this.$store.dispatch('index/setMainTitle', '日常维修')
      } else {
        this.$store.dispatch('index/setMainTitle', '居家服务')
      }
    })
  },
  computed: {},
  watch: {},
  mounted() {
    this.statusList = JSON.parse(localStorage.getItem('statusList'))
    this.hotLine = JSON.parse(sessionStorage.getItem('supplierInfo')).telephone
    this.sessionStorage_supplier_id = JSON.parse(
      sessionStorage.getItem('supplierInfo')
    ).fk_supplier_id
    if (sessionStorage.getItem('indexFocusService')) {
      this.leftNums = Number(sessionStorage.getItem('indexFocusService'))
      sessionStorage.removeItem('indexFocusService')
    }
    if (sessionStorage.getItem('content')) {
      sessionStorage.removeItem('content')
    }
    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          return
        }
        if (this.selectDialogVisible) {
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums++
            this.rightList[this.rightNums].ref = 'active'
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        //左侧
        else {
          if (this.leftNums < this.leftList.length - 1) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            this.leftList[this.leftNums].ref = 'active'
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          return
        }
        if (this.selectDialogVisible) {
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums > 0 && this.rightList.length - 1 >= 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums--
            this.rightList[this.rightNums].ref = 'active'
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 左侧
        else {
          if (this.leftNums > 0 && this.leftList.length - 1 >= 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        if (this.selectDialogVisible) {
          if (this.selectBtnNums > 0) {
            this.selectBtnList[this.selectBtnNums].ref = ''
            this.selectBtnNums--
            this.selectBtnList[this.selectBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        else if (this.nextNums > -1) {
          if (this.leftList.length > 0) {
            this.rightList[this.rightNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.nextNums = -1
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        if (this.selectDialogVisible) {
          if (this.selectBtnNums < this.selectBtnList.length - 1) {
            this.selectBtnList[this.selectBtnNums].ref = ''
            this.selectBtnNums++
            this.selectBtnList[this.selectBtnNums].ref = 'active'

            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左侧
        if (this.nextNums == -1) {
          if (this.rightList.length > 0) {
            this.leftList[this.leftNums].ref = ''
            this.rightList[this.rightNums].ref = 'active'
            this.nextNums = this.leftNums
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        if (this.nextNums == -1) {
          if (this.supplier_type == '8') {
            sessionStorage.setItem('indexFocusService', this.leftNums)
            sessionStorage.setItem(
              'content',
              this.leftList[this.leftNums].content
            )
            this.$nextTick(() => {
              this.$router.push({
                path: '/reformCreate',
                query: {
                  title: this.leftList[this.leftNums].title,
                  fk_supplier_id: this.leftList[this.leftNums].fk_supplier_id,
                  fk_routine_maintenance_id: this.leftList[this.leftNums].id,
                  // content: this.leftList[this.leftNums].content,
                },
              })
            })
          } else {
            if (this.selectDialogVisible) {
              this.selectBtnList[this.selectBtnNums].ref = ''
              this.leftList[this.leftNums].ref = 'active'
              this.selectDialogVisible = false
              // console.log(123,this.popupBtnList[this.popupBtnNums].fuc(this.leftList[this.leftNums]));
              this.selectBtnList[this.selectBtnNums].fuc(
                this.leftList[this.leftNums]
              )
              return
            }
            if (this.dialogVisible) {
              // 弹窗
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.leftList[this.leftNums].ref = 'active'
              this.dialogVisible = false
              // console.log(123,this.popupBtnList[this.popupBtnNums].fuc(this.leftList[this.leftNums]));
              this.popupBtnList[this.popupBtnNums].fuc(
                this.leftList[this.leftNums]
              )
              return
            }
            this.popupBtnList = [
              {
                text: '关闭',
                ref: '',
                fuc: () => {
                  this.dialogVisible = false
                },
              },
              {
                text: '确认',
                ref: '',
                fuc: this.senOrder,
              },
            ]
            this.dialogVisible = true
            this.popupModule = 1
            this.leftList[this.leftNums].ref = ''
            this.popupBtnList[this.popupBtnNums].ref = 'active'
          }
        }

        if (this.nextNums > -1 && this.rightList[this.rightNums]) {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.rightList[this.rightNums].ref = 'active'
            this.dialogVisible = false
            // console.log(123,this.popupBtnList[this.popupBtnNums].fuc(this.leftList[this.leftNums]));
            this.popupBtnList[this.popupBtnNums].fuc(
              this.rightList[this.rightNums]
            )
            return
          }
          this.popupBtnList = [
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          if ([2, 5].includes(this.rightList[this.rightNums].status)) {
            this.popupBtnList.push({
              text: '取消订单',
              ref: '',
              fuc: this.cancelOrder,
            })
          }
          this.dialogVisible = true
          this.popupModule = 2
          this.rightList[this.rightNums].ref = ''
          this.popupBtnList[this.popupBtnNums].ref = 'active'
        }
      },
      esc: () => {
        if (this.nextNums == -1) {
          if (this.dialogVisible) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.dialogVisible = false
            return
          } else if (this.selectDialogVisible) {
            this.selectBtnList[this.selectBtnNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.selectDialogVisible = false
            return
          }
        } else if (this.nextNums > -1) {
          if (this.dialogVisible) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.rightList[this.rightNums].ref = 'active'
            this.dialogVisible = false
            return
          }
        }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      if (this.nextNums == -1) {
        this.popupBtnList[this.popupBtnNums].ref = ''
        this.leftList[this.leftNums].ref = 'active'
      } else if (this.nextNums > -1) {
        this.popupBtnList[this.popupBtnNums].ref = ''
        this.rightList[this.rightNums].ref = 'active'
      }

      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        if (this.selectDialogVisible) {
          document.getElementById('focus_border').style.borderColor =
            'rgb(0,0,0,0)'
        } else {
          document.getElementById('focus_border').style.borderColor = '#fff'
        }
        this.popupBtnNums = 0
        this.popupModule = 1
      })
    },
    popupOpend() {
      if (this.supplier_type == '22') {
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
          document.getElementById('focus_border').style.borderColor = '#5194dd'
        })
      } else {
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
          document.getElementById('focus_border').style.borderColor = '#4b68a7'
        })
      }
    },
    selectClose() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        if (this.dialogVisible) {
          document.getElementById('focus_border').style.borderColor =
            'rgb(0,0,0,0)'
        } else {
          document.getElementById('focus_border').style.borderColor = '#fff'
        }
        this.selectBtnNums = 0
        this.popupModule = 1
      })
    },
    selectOpend() {
      if (this.selectDialogVisible) {
        this.leftList[this.leftNums].ref = ''
        if (this.rightList[this.rightNums]) {
          this.rightList[this.rightNums].ref = ''
        }
        this.selectBtnList[this.selectBtnNums].ref = 'active'
      }
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState', true)
      let urlType = JSON.parse(sessionStorage.getItem('redirectInfo')).redirect
      let urlTypeIsIndex = urlType.indexOf('=') + 1
      let urlTypeAndIndex
      if (urlType.indexOf('&') > -1) {
        urlTypeAndIndex = urlType.indexOf('&') + 1
      } else {
        urlTypeAndIndex = urlType.length
      }
      this.supplier_type = urlType.slice(urlTypeIsIndex, urlTypeAndIndex)
      if (
        this.supplier_type == '19' ||
        this.supplier_type == '8' ||
        this.supplier_type == '22'
      ) {
        const fetchData =
          this.supplier_type == '19'
            ? getSaleList
            : this.supplier_type == '8'
            ? getServiceList
            : getHomeList

        fetchData({
          fk_supplier_id:
            this.supplier_type != '22' ? this.sessionStorage_supplier_id : null,
          user_id:
            this.supplier_type != '8'
              ? this.$store.getters.getUserInfo.oldsters[
                  this.$store.state.app.selectUserIndex
                ].id
              : null,
          supplier_type: this.supplier_type == '22' ? this.supplier_type : null,
          supplier_id:
            this.supplier_type == '22' ? this.sessionStorage_supplier_id : null,
          fk_user_id:
            this.supplier_type == '8'
              ? this.$store.getters.getUserInfo.oldsters[
                  this.$store.state.app.selectUserIndex
                ].id
              : null,
        })
          .then((res) => {
            this.$store.dispatch('app/setLoadingState', false)
            if (res.code == 200) {
              if (res.data.sale_list || res.data.service_list) {
                let leftListClone = res.data.sale_list
                  ? JSON.parse(JSON.stringify(res.data.sale_list))
                  : JSON.parse(JSON.stringify(res.data.service_list))
                leftListClone.map((item, index) => {
                  item.ref = ''
                })
                this.leftList = leftListClone
              }

              if (res.data.unfinish_list) {
                let rightListClone = JSON.parse(
                  JSON.stringify(res.data.unfinish_list)
                )
                rightListClone.map((item) => {
                  item.ref = ''
                })
                this.rightList = rightListClone
              }

              if (this.leftList.length > 0) {
                this.nextNums = -1
                this.leftList[this.leftNums].ref = 'active'
              } else {
                this.nextNums = this.rightNums
                this.rightList[this.rightNums].ref = 'active'
              }

              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          })
          .catch(() => {
            this.$store.dispatch('app/setLoadingState', false)
          })
      }
    },
    senOrder() {
      this.leftList[this.leftNums].ref = ''
      if (this.supplier_type == '19') {
        this.selectBtnList = [
          {
            name: '货到付款',
            text: '送货上门',
            ref: '',
            fuc: this.submitInfo,
          },
          {
            name: '在线支付',
            text: '送货上门',
            ref: '',
            fuc: this.submitInfo,
          },
          {
            name: '在线支付',
            text: '到店自提',
            ref: '',
            fuc: this.submitInfo,
          },
        ]
        this.selectBtnNums = 0
        this.selectDialogVisible = true
      } else {
        this.submitHomeInfo()
      }
    },
    // 提交买菜订单
    submitInfo() {
      this.$store.dispatch('app/setLoadingState', true)
      this.order_type = this.selectBtnNums + 1

      submitSaleList({
        user_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].id,
        home_id: this.$store.getters.getUserInfo.home_id,
        order_type: this.order_type,
        device_type: 1,
        combination_id: this.leftList[this.leftNums].combination_id,
      }).then(
        (res) => {
          if (res.code == 200) {
            if (this.order_type == 1) {
              this.popupModule = 3
              this.leftList[this.leftNums].ref = ''
              this.selectBtnList[this.selectBtnNums].ref = ''
              this.popupBtnList = [
                {
                  text: '确认',
                  ref: 'active',
                  fuc: () => {
                    this.dialogVisible = false
                    let rightClone = JSON.parse(JSON.stringify(res.data))
                    rightClone.map((item) => {
                      item.ref = ''
                    })
                    this.rightList = rightClone

                    this.leftList[this.leftNums].ref = 'active'
                    this.rightNums = 0
                    this.nextNums = -1
                    // this.$nextTick(() => {
                    //   this.$store.dispatch(
                    //     'index/setFocusDom',
                    //     this.$refs.active
                    //   )
                    // })
                  },
                },
              ]
              this.popupMessage = '订购成功！<br/>'
              this.popupBtnNums = 0
              this.dialogVisible = true
            } else {
              this.user_telephone = res.data[this.rightNums].user_phone
              this.popupModule = 4
              this.leftList[this.leftNums].ref = ''
              this.selectBtnList[this.selectBtnNums].ref = ''
              this.popupBtnList = [
                {
                  text: '确认',
                  ref: 'active',
                  fuc: () => {
                    this.dialogVisible = false
                    let rightClone = JSON.parse(JSON.stringify(res.data))
                    rightClone.map((item) => {
                      item.ref = ''
                    })
                    this.rightList = rightClone
                    this.leftList[this.leftNums].ref = 'active'
                    this.rightNums = 0
                    this.nextNums = -1
                    this.$nextTick(() => {
                      this.$store.dispatch(
                        'index/setFocusDom',
                        this.$refs.active
                      )
                    })
                  },
                },
              ]
              this.popupBtnNums = 0
              this.dialogVisible = true
            }
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.leftList[this.leftNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br/>订购失败!<br>'
          if (
            error.response &&
            error.response.data &&
            error.response.data.msg
          ) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },
    // 提交居家服务
    submitHomeInfo() {
      this.$store.dispatch('app/setLoadingState', true)
      submitHome({
        device_type: this.leftList[this.leftNums].type,
        user_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].id,
        home_id: this.$store.getters.getUserInfo.home_id,
        supplier_type: this.supplier_type,
        supplier_id: this.sessionStorage_supplier_id,
      }).then(
        (res) => {
          if (res.code == 200) {
            this.popupModule = 3
            this.leftList[this.leftNums].ref = ''
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  let rightClone = JSON.parse(JSON.stringify(res.data))
                  rightClone.map((item) => {
                    item.ref = ''
                  })
                  this.rightList = rightClone

                  this.leftList[this.leftNums].ref = 'active'
                  this.rightNums = 0
                  this.nextNums = -1
                },
              },
            ]
            this.popupMessage = '订购成功！<br/>'
            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.$refs.active = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br>订单发送失败!</br>'
          if (
            error.response &&
            error.response.data &&
            error.response.data.msg
          ) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },
    // 取消订单
    cancelOrder() {
      this.$store.dispatch('app/setLoadingState', true)

      if (this.supplier_type == '19') {
        cancelSaleList({
          order_id: this.rightList[this.rightNums].order_id,
          user_id:
            this.$store.getters.getUserInfo.oldsters[
              this.$store.state.app.selectUserIndex
            ].id,
          order_no: this.rightList[this.rightNums].order_no,
        }).then(
          (res) => this.successResult(res),

          (error) => this.errorResult(error)
        )
      } else if (this.supplier_type == '8') {
        cancelService({
          order_id: this.rightList[this.rightNums].order_id,
          fk_supplier_id: this.sessionStorage_supplier_id,
          fk_user_id:
            this.$store.getters.getUserInfo.oldsters[
              this.$store.state.app.selectUserIndex
            ].id,
        }).then(
          (res) => this.successResult(res),
          (error) => this.errorResult(error)
        )
      } else {
        cancelHome({
          user_id:
            this.$store.getters.getUserInfo.oldsters[
              this.$store.state.app.selectUserIndex
            ].id,
          order_id: this.rightList[this.rightNums].order_id,
          supplier_type: this.supplier_type,
          supplier_id: this.sessionStorage_supplier_id,
        }).then(
          (res) => this.successResult(res),
          (error) => this.errorResult(error)
        )
      }
    },
    successResult(res) {
      this.popupModule = 3
      this.rightList[this.rightNums].ref = ''
      this.popupBtnList = [
        {
          text: '确认',
          ref: 'active',
          fuc: () => {
            this.dialogVisible = false
            if (res.data) {
              let rightClone = JSON.parse(JSON.stringify(res.data))
              rightClone.map((item) => {
                item.ref = ''
              })
              this.rightList = rightClone
            } else {
              this.rightList = []
            }
            // if (this.leftList.length > 0) {
            //   this.leftList[this.leftNums].ref = 'active'
            //   this.rightNums = 0
            //   this.nextNums = -1
            // } else {
            //   this.rightNums = 0
            //   this.rightList[this.rightNums].ref = 'active'
            //   this.nextNums = this.rightNums
            // }
            this.rightList[this.rightNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          },
        },
      ]
      this.popupMessage = '<br/>退订成功!<br>'
      this.popupBtnNums = 0
      this.dialogVisible = true
      this.$store.dispatch('app/setLoadingState', false)
    },
    errorResult(error) {
      this.$store.dispatch('app/setLoadingState', false)
      this.popupModule = 3
      this.rightList[this.rightNums].ref = ''
      this.popupBtnList = [
        {
          text: '确认',
          ref: 'active',
          fuc: () => {
            this.dialogVisible = false
          },
        },
      ]
      this.popupMessage = '<br/>取消失败!<br>'
      if (error.response && error.response.data && error.response.data.msg) {
        this.popupMessage += error.response.data.msg
      }
      this.popupBtnNums = 0
      this.dialogVisible = true
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
      <style lang='less' scoped>
.homeCare {
  width: 16.73rem;
  .address {
    display: flex;
    position: absolute;
    top: 1.6rem;
    left: 1.45rem;
    .name {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .name::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .done {
    position: absolute;
    top: 1.6rem;
    right: 4rem;
    .item {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .item::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .tel {
    position: absolute;
    top: 1.6rem;
    left: 5rem;
    font-size: 0.36rem;
    font-weight: bold;
    letter-spacing: 0.03rem;
    color: #b5c0ff;
  }
  .container {
    height: 7rem;
    overflow: hidden;
    display: flex;
    // grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
    // gap: 0.16rem;
    // font-size: 0.2rem;
    // color: #e7e7ef;
    .noData {
      div {
        font-size: 0.5rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -200%);
        letter-spacing: 0.05rem;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
      }
    }
    .aside {
      width: 2rem;
      height: 0.7rem;
      text-align: center;
      line-height: 0.7rem;
      margin-bottom: 0.2rem;
      background: #343d74;
      border-radius: 0.2rem;
      position: relative;
      font-weight: bold;
      transition: all 0.3s;
      letter-spacing: 0.05rem;
      text-indent: 0.05rem;
      font-size: 0.34rem;
      color: #e7e7ef;
      text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
    }
    .left {
      width: 12.75rem;
      height: 7rem;
      margin-left: 0;
      //padding-left: 0.1rem;
      //margin-left: 0.05rem;

      .messageCenterList {
        width: 11.7rem;
        height: 100%;
        position: relative;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          // left: 0.8rem;
          transition: all 0.2s;
          .messageItem {
            width: 100%;
            height: 1.2rem;
            padding: 0.45rem;
            margin-bottom: 0.35rem;
            position: relative;
            margin-right: 0.28rem;
            background-size: 100% 100% !important;
            // background: #ccc !important;
            background: #262954;
            border-radius: 0.3rem;
            overflow: hidden;
            .item_user {
              display: flex;
              // padding: 0.3rem;
              .pic {
                width: 2.15rem !important;
                height: 1.1rem !important;
                border-radius: 0.15rem;
                position: relative;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .picItem {
                width: 1.12rem !important;
                height: 1.1rem !important;
                border-radius: 0.15rem;
                position: relative;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .content {
                // width: initial !important;
                margin-left: 0.5rem !important;
                position: relative;
                top: -0.1rem;
                // padding-top: 0.06rem;
                .title {
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 0.06rem;
                  .name {
                    font-size: 0.35rem !important;
                    font-weight: bold;
                    background-image: linear-gradient(
                      to bottom,
                      rgba(255, 255, 255, 1),
                      rgba(255, 255, 255, 0.65)
                    );
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                  }
                  .price {
                    font-size: 0.42rem;
                    color: #f64e23;
                    font-weight: bold;
                    span {
                      font-size: 0.25rem;
                      padding-left: 0.05rem;
                      margin-top: 0.15rem;
                    }
                  }
                }
                .info {
                  font-size: 0.24rem !important;
                  font-weight: inherit !important;
                  letter-spacing: 0.03rem;
                }
              }
              .contentItem {
                width: initial !important;
                margin-left: 0.7rem !important;
                position: relative;
                top: -0.1rem;
                // padding-top: 0.06rem;
                .title {
                  display: flex;
                  justify-content: space-between;
                  margin-top: 0.15rem;
                  margin-bottom: 0.06rem;
                  .name {
                    font-size: 0.42rem !important;
                    font-weight: bold;
                    background-image: linear-gradient(
                      to bottom,
                      rgba(255, 255, 255, 1),
                      rgba(255, 255, 255, 1)
                    );
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                  }
                  .price {
                    font-size: 0.42rem;
                    color: #f64e23;
                    span {
                      font-size: 0.25rem;
                      padding-left: 0.05rem;
                      margin-top: 0.15rem;
                    }
                  }
                }
                .info {
                  font-size: 0.24rem !important;
                  font-weight: inherit !important;
                  letter-spacing: 0.03rem;
                }
              }
            }
            .choose_tip {
              width: 4rem;
              height: 0.37rem;
              position: relative;
              top: 0.13rem;
              right: 0.5rem;
              div {
                width: 100%;
                height: 100%;
                font-size: 0.22rem;
                background: #3cc92d;
                border-radius: 0.05rem;
                position: absolute;
                text-align: center;
                line-height: 0.35rem;
                font-weight: bold;
                color: #fff;
                letter-spacing: 0.03rem;
              }
            }
          }
          // .messageItem:nth-child(3n + 3) {
          //   margin-right: 0;
          // }
        }
      }
      .no_content {
        position: absolute;
        line-height: 7rem;
        left: 6rem;
        // width: 4rem;
        // height: 7rem;
        text-align: center;
        font-size: 0.4rem;
        letter-spacing: 0.05rem;
        line-height: 6rem;
        color: #b5c0ff;
      }
    }
    .right {
      width: 4.75rem;
      height: 7rem;
      position: relative;
      .list {
        // margin-right: 0.22rem;
        position: relative;
        transition: all 0.3s;
        .rightList {
          height: 3.4rem;
          padding: 0.25rem 0.35rem;
          margin-bottom: 0.28rem;
          margin-right: 0.25rem;
          background: #262954;
          border-radius: 0.24rem;
          font-size: 0.28rem;
          font-weight: bold;
          .status {
            color: #e77302;
            margin-bottom: 0.1rem;
            margin-top: 0.1rem;
            letter-spacing: 0.03rem;
          }
          .good,
          .price,
          .pay,
          .time {
            margin-bottom: 0.1rem;
            .title {
              color: #b6c0fd;
              margin-right: 0.06rem;
            }
          }
          .good,
          .price {
            display: flex;
          }
        }
        .rightListItem {
          height: 3.3603rem;
          margin-bottom: 0.28rem;
          margin-right: 0.25rem;
          background: #262954;
          border-radius: 0.24rem;
          font-size: 0.28rem;
          font-weight: bold;
          .listContent {
          padding: 0.05rem 0.35rem;
            .status {
              color: #e77302;
              margin-bottom: 0.5rem;
              margin-top: 0.3rem;
              letter-spacing: 0.03rem;
            }
            .good,
            .price,
            .pay,
            .time {
              margin-bottom: 0.1rem;
              .title {
                color: #b6c0fd;
                margin-right: 0.06rem;
              }
            }
            .good,
            .price {
              display: flex;
            }
          }
        }
      }
    }
    .no_info {
      position: absolute;
      line-height: 7rem;
      // left: 6rem;
      right: 2.3rem;
      // width: 4rem;
      // height: 7rem;
      text-align: center;
      font-size: 0.4rem;
      letter-spacing: 0.05rem;
      line-height: 6rem;
      color: #b5c0ff;
    }
    // .tab_list {
    //   width: 4.2rem;
    //   height: 6.65rem;
    //   position: absolute;
    //   top: 0.69rem;
    //   right: 0.3rem;
    //   overflow: hidden;
    //   .no_content {
    //     margin: auto;
    //     line-height: 6.2rem;
    //     width: 4rem;
    //     height: 0.8rem;
    //     text-align: center;
    //     font-size: 0.4rem;
    //   }
    // }
  }
}
</style>
      <style lang="less">
.homeCare {
  // position: relative;

  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    // background: repeating-linear-gradient(to right, #c98693, #a95361);
    background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        align-items: flex-start;
        // justify-content: center;
        flex-direction: column;
        .title,
        .detail,
        .price {
          display: flex;
          font-size: 0.35rem;
          font-weight: bold;
          margin-bottom: 0.22rem !important;
          letter-spacing: 0.03rem;
        }
        .title {
          span {
            color: #f64e23;
            margin-left: 0.2rem;
          }
        }
        .price {
          span {
            color: #f64e23;
          }
        }
        .sendCall_home,
        .cancel_item {
          padding: 0.4rem;
          letter-spacing: 0.03rem;
          height: 60%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          margin-top: 0.3rem;
          .title,
          .detail {
            p,
            span {
              color: #464646;
              font-size: 0.48rem;
              font-weight: bold;
            }
          }
        }
      }

      .cancel {
        font-size: 0.38rem !important;
        div {
          margin-bottom: 0.2rem;
        }
        div:nth-child(2) {
          margin-top: 0 !important;
          color: #464646 !important;
          line-height: 0.48rem;
        }
      }
      .result {
        display: flex;
        text-align: center;
        font-size: 0.37rem;
        justify-content: center;
        align-items: center;
      }

      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          // background: repeating-linear-gradient(to right, #c98693, #a95361);
          background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
        }
      }
    }
  }
  .popup_box_bg {
    .el-dialog {
      width: 8.61rem;
      height: 5.73rem;
      background: url('../../assets/zc_tan_new.png') no-repeat;
      background-size: 100% 100%;
      .el-dialog__header {
        display: none;
      }
      .el-dialog__body {
        background: transparent;

        .info {
          display: flex;
          justify-content: center !important;
          align-items: center !important;
          margin-top: -0.4rem;
          .title {
            display: flex;
            flex-direction: column;
            align-items: center !important;
            text-align: center !important;
            margin-top: -0.3rem;
            span {
              text-align: center !important;
              font-size: 0.5rem !important;
              color: #e7f1fa !important;
              letter-spacing: 0.14rem !important;
            }
          }
          .phone {
            width: 7rem;
            text-align: center;
            position: absolute;
            top: 1.3rem;
            font-size: 0.3rem !important;
            color: yellow !important;
          }
          .tip {
            color: #3288dc;
            font-size: 0.4rem;
            margin-top: 1.2rem;
          }
        }
      }
    }
  }
  .popup_box_bgc {
    .el-dialog {
      width: 9.4rem;
      height: 8rem;
      margin-top: 0 !important;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 0.26rem;
      background: repeating-linear-gradient(to right, #7cb6ea, #5194dd);
      .el-dialog__body {
        .popupBtnList {
          display: flex;
          flex-direction: row;
          justify-content: space-evenly;
          margin-top: 0.25rem;
          div {
            width: 3.84rem;
            height: 1rem;
            line-height: 1.08rem;
            text-align: center;
            border-radius: 0.3rem;
            color: #fff;
            font-size: 0.45rem;
            font-weight: bold;
            letter-spacing: 0.05rem;
            text-indent: 0.05rem;
            background: repeating-linear-gradient(to right, #7cb6ea, #5194dd);
          }
        }
      }
    }
  }
  .select_pay_box {
    .el-dialog {
      width: 8.4rem;
      height: 5.5rem;
      margin-top: 0 !important;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 0.26rem;
      background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
      // background: repeating-linear-gradient(to right, #c98693, #a95361);
      .el-dialog__header {
        height: 10%;
        display: flex;
        align-items: center;
        padding: 15px 20px !important ;
        .el-dialog__title {
          display: block;
          line-height: normal !important;
          height: 100%;
          font-size: 0.4rem;
          color: #fff;
          background-image: linear-gradient(
            to bottom,
            rgba(255, 255, 255, 1),
            rgba(255, 255, 255, 0.65)
          );
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          font-weight: bold;
          width: 100%;
          text-align: center;
        }
      }
      .el-dialog__body {
        .selectBtnList {
          display: flex;
          flex-direction: row;
          justify-content: space-evenly;
          margin-top: 0.6rem;
          border-radius: 0.3rem;

          .btn {
            position: relative;
            width: 2.2rem;
            height: 3rem;
            line-height: 0.6rem;
            text-align: center;
            border-radius: 0.3rem;
            color: #fff;
            font-size: 0.45rem;
            font-weight: bold;
            letter-spacing: 0.05rem;
            text-indent: 0.05rem;
            // background: repeating-linear-gradient(to right, #c98693, #a95361);
            background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
            .name {
              position: relative;
              width: 1rem;
              margin: 0.7rem auto;
              font-size: 0.4rem;
              text-align: center;
              letter-spacing: 0.06rem;
              color: #fff;
              background-image: linear-gradient(
                to bottom,
                rgba(255, 255, 255, 1),
                rgba(255, 255, 255, 0.65)
              );
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              font-weight: bold;
            }
            .text {
              display: block;
              font-size: 0.26rem;
              position: absolute;
              bottom: 0.3rem;
              width: 100%;
              text-align: center;
              letter-spacing: 0.06rem;
              text-indent: 0.06rem;
              text-shadow: 0.04rem 0.04rem 0.05rem #283c7f;
            }
          }
        }
      }
    }
  }
}
</style>
      