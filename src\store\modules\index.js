import Vue from 'vue'
//index模块中的数据，及相应的数据操作
const state = {
    mainTitle: '',
    userPoints: '1000',
    focusBorder: {//用来存储边框的相关数值
        top: '0px',
        left: '0px',
        width: '0px',
        height:'0px',
    },
    focusDom: null,
    headerArr:[],
}

const mutations = {
    SET_MAIN_TITLE: (state, str) => {
        state.mainTitle = str;
    },
    SET_USER_POINTS: (state, str) => {
        state.userPoints = str;
    },
    SET_FOCUS_BORDER: (state, obj) => {
        Vue.set(state, "focusBorder", obj)
    },
    SET_FOCUS_DOM: (state, obj) => {
        if (obj == null) {
            Vue.set(state, "focusDom", obj)
        } else {
            Vue.set(state, "focusDom", (obj.length ? obj[0] : obj))
        }
    },
    SET_HEADER_ARR: (state, array) => {
        Vue.set(state, "headerArr", array)
    },
   
}
const actions = {
    setMainTitle({ commit, state }, str) {
        return new Promise((resolve, reject) => {
            commit("SET_MAIN_TITLE", str)
        })
    },
    setUserPoints({ commit, state }, str) {
        return new Promise((resolve, reject) => {
            commit("SET_USER_POINTS", str)
        })
    },
    setFocusBorder({ commit, state }, obj) {
        return new Promise((resolve, reject) => {
            commit("SET_FOCUS_BORDER", obj)
        })
    },
    setFocusDom({ commit, state }, obj) {
        return new Promise((resolve, reject) => {
            commit("SET_FOCUS_DOM", obj)
        })
    },
    setHeaderArr({ commit, state }, array) {
        return new Promise((resolve, reject) => {
            commit("SET_HEADER_ARR", array)
        })
    },
    
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}