<template>
  <div class="connectFour">
    <div id="game">
      <div class="game_page">
        <div id="winner-burst" :class="[currentTurnColor, gameOver && 'burst']"></div>

        <div class="flex">
          <!--        <p v-html="message" role="status"></p>-->
          <!--        <button :disabled="!areThereMoves" @click="startNewGame">新遊戲</button>-->
        </div>

        <div id="button-board">
          <div v-for="(i,index) in btnList" :key="index" :ref="i.ref"  :data-column="i.index">
            <div style="visibility: hidden;border: none" :data-column="i.index" :class="i.ref == 'active' ? 'active' : ''"    :tabindex="gameOver || isADraw ? '-1' : '0'">
              <span></span>
            </div>
            <div class="slot" :hidden="gameOver || isADraw" aria-hidden="true">
              <div :class="currentTurnColor">
<!--                {{ getPieceIcon(currentTurnColor) }}-->
              </div>
            </div>
          </div>
        </div>

        <div id="board">
          <template v-for="(row, rowNumber) in grid">
            <template v-for="(slot, slotNumber) in row">
              <div
                  class="slot shadow"

                  :style="slot.color && `animation: drop 0.05s forwards steps(6)`"
                  :data-row="rowNumber"
                  :data-column="slotNumber"
                  :key="`${rowNumber}${slotNumber}`"
                  :aria-label="`${slot.color && slot.color + ' piece' || 'Blank slot'} in column ${slotNumber +1} of 8, row ${rowNumber +1} of 6.` "
              >
                <div v-if="slot.color" :class="[slot.color, gameOver && currentTurnColor != slot.color && 'loser']" aria-hidden="true">
<!--                  {{ getPieceIcon(slot.color) }}-->
                </div>
              </div>
            </template>
          </template>
        </div>


      </div>
    </div>

    <div class="game_state">
      <div>
        <span style="color: #FA686B">红色：</span>
        <span ref="redDom">{{fistDown ? '你' : '人机'}}</span>
      </div>

      <div>
        <span style="color: #FFDD3E">黄色：</span>
        <span ref="yellowDom">{{!fistDown ? '你' : '人机'}}</span>
      </div>

      <div>
        <span style="color: #0CF0FB">执棋：</span>
        <span>{{player ? '你' : '人机'}}</span>
      </div>

      <div>
        <span style="color: #0CF0FB">难易度：</span>
        <span>简单</span>
      </div>

    </div>


    <div class="message">
      <h2>操作说明:</h2>
      <p>
        <span>1.</span>
        <span>白框出现，该你落子，否则人机落子。</span>
      </p>
      <p>
        <span>2.</span>
        <span>左右键调节落子列。</span>
      </p>
      <p>
        <span>3.</span>
        <span>确认键落子。</span>
      </p>
      <p>
        <span>4.</span>
        <span>返回键退出游戏。</span>
      </p>
    </div>


    <el-dialog
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
    >

      <!--消息弹窗-->
      <div class="popupMessage" v-if="popupModule == 2">
        <div class="popupMessage" v-html="message"></div>
        <div class="popupMessageBtn" :ref="item.ref" v-for="(item,index) in popupBtnList" :key="index">
          {{ item.text}}
        </div>
      </div>


    </el-dialog>



  </div>
</template>

<style lang="less">
@import './index.less';
</style>

<script src="./index.js"></script>

