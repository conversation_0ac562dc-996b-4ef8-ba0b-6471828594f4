<template>
  <div class="reformCreate">
    <div class="container">
      <div class="left">
        <div class="leftList">
          <div class="pic" ref="refLeft">
            <img :src="require('@/assets/send_server.png')" alt="" />
          </div>
        </div>
      </div>

      <div class="right scrollParent" ref="refRight">
        <div class="list" ref="refList" :style="{ top: '0rem' }">
          <div class="content" v-html="content"></div>
        </div>
      </div>
      <!-- <div class="scroll_side" ref="refScrollSide">
        <div
          class="scrollBar"
          ref="refScroll"
          :style="{ top: '0rem', height: scrollBarHeight + 'rem' }"
        ></div>
      </div> -->
      <el-dialog
        class="popup_box"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        title="日常维修"
      >
        <!--发起预约-->
        <div class="message_box sendCall" v-if="popupModule == 1">
          <div class="title">
            <p>请确认需要申请的服务。</p>
            <span>服务内容：</span><span>{{ title }}</span>
          </div>
        </div>
        <div
          class="message_box result"
          style="text-align: center; font-size: 0.37rem; justify-content: center"
          v-html="popupMessage"
          v-if="popupModule == 2"
        ></div>
        <div class="popupBtnList">
          <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index">
            {{ item.text }}
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
      
<script>
import { submitService } from '@/api/index'

export default {
  name: 'reformCreate',
  components: {},
  data() {
    return {
      leftList: [],
      rightList: [],
      // scrollBarHeight: 7,
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      title: '',
      content: '',
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      messageList: [],
    }
  },
  created() {
    //设置页面左上角标题
    this.$nextTick(() => {
      this.$store.dispatch('index/setMainTitle', this.title)
    })
  },
  computed: {},
  watch: {
    // '$refs.refScroll.style.height': {
    //   handler() {
    //     setTimeout(() => {
    //       const list = this.$refs.refList
    //       const scrollBar = this.$refs.refScroll
    //       const scrollSide = this.$refs.refScrollSide
    //       const visibleHeight = list.parentNode.clientHeight
    //       const totalHeight = list.clientHeight
    //       const ratio = visibleHeight / totalHeight
    //       if (totalHeight <= visibleHeight) {
    //         scrollBar.style.height = 0
    //         scrollSide.style.height = 0
    //       } else {
    //         this.scrollBarHeight = parseInt(scrollBar.style.height, 10) * ratio
    //       }
    //     }, 30)
    //   },
    //   immediate: true,
    //   deep: true,
    // },
  },
  mounted() {
    this.title = this.$route.query.title
    if(sessionStorage.getItem('content')){
      this.content = sessionStorage.getItem('content')
      if (this.content.indexOf('<img') > -1) {
        let reg = new RegExp('/public/storage/', 'g')
        this.content = this.content.replace(reg, process.env.VUE_APP_API + '/public/storage/')
      }
    }
    this.content =  this.content.replace(
        new RegExp('<p', 'g'),
        '<p style="word-break:break-all;" '
    )
    setTimeout(() => {
      this.fuc.setScroll()
    }, 100)

    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          return
        }
        if (this.nextNums > -1) {
          const listEl = this.$refs.refList
          // const scrollBar = this.$refs.refScroll
          const scrollBar = this.$refs.refList.parentNode.querySelector('.scrollBar')

          // console.log('可视区域', listEl.parentNode.clientHeight)
          // console.log('元素距离顶部', listEl.offsetTop)
          // console.log('元素高度', listEl.clientHeight)
          // console.log('下拉高度', listEl.parentNode.offsetTop)

          if (listEl && scrollBar) {
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight

            const radio = listHeight / listVisHeight

            const potentialNewTop = currentTop - 150 // 预计的新top值

            const scrollNewTop = currentScrollTop + 150 / radio
            const maxScrollableHeight = listEl.clientHeight - listEl.parentNode.clientHeight // 最大可滚动高度
            const maxSrcollBarHeight = scrollBar.parentNode.clientHeight - scrollBar.clientHeight
            if (listVisHeight < listHeight) {
              // 将元素的top值与最大可滚动高度进行比较，以确定是否已经到达或超过了底部
              if (-potentialNewTop < maxScrollableHeight) {
                listEl.style.top = `${potentialNewTop}px`
              } else {
                // 已经到达或超过底部，调整top以确保内容底部和容器底部对齐
                listEl.style.top = `${-maxScrollableHeight - 50}px`
              }
            } else {
              return
            }

            if (scrollNewTop < maxSrcollBarHeight) {
              scrollBar.style.top = `${scrollNewTop}px`
            } else {
              scrollBar.style.top = `${maxSrcollBarHeight}px`
            }
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          const listEl = this.$refs.refList
          // const scrollBar = this.$refs.refScroll
          const scrollBar = this.$refs.refList.parentNode.querySelector('.scrollBar')

          if (listEl && scrollBar) {
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight
            const radio = listHeight / listVisHeight
            const potentialNewTop = currentTop + 150 // 预计的新top值
            const scrollNewTop = currentScrollTop - 135 / radio

            // 检查是否已经到达最顶部
            if (potentialNewTop >= 0) {
              listEl.style.top = `0px` // 直接设置为0，确保不会超过顶部
              scrollBar.style.top = `0px`
            } else {
              listEl.style.top = `${potentialNewTop}px`
              scrollBar.style.top = `${scrollNewTop}px`
            }
          }
        }

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          this.$refs.active = []
          this.$refs.active.push(this.$refs.refLeft)
          this.nextNums = -1
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左
        if (this.nextNums == -1) {
          this.$refs.active = []
          this.$refs.active.push(this.$refs.refRight)
          this.nextNums = 0
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        if (this.$refs.active && this.nextNums == -1) {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.$refs.active.push(this.$refs.refLeft)
            this.dialogVisible = false
            // console.log(123,this.popupBtnList[this.popupBtnNums].fuc(this.leftList[this.leftNums]));
            this.popupBtnList[this.popupBtnNums].fuc(this.leftList[this.leftNums])
            return
          }
          this.popupBtnList = [
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            },
            {
              text: '确认',
              ref: '',
              fuc: this.submitInfo,
            },
          ]
          this.dialogVisible = true
          this.popupModule = 1
          this.$refs.active = ''
          this.popupBtnList[this.popupBtnNums].ref = 'active'
        }
        return
      },
      esc: () => {
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.$refs.active.push(this.$refs.refLeft)
          this.dialogVisible = false
          return
        }
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        this.popupBtnNums = 0
        this.popupModule = 1
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },

    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      if (this.nextNums == -1) {
        this.$refs.active = []
        this.$refs.active.push(this.$refs.refLeft)
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      }
    },
    submitInfo() {
      this.$store.dispatch('app/setLoadingState', true)
      submitService({
        fk_supplier_id: this.$route.query.fk_supplier_id,
        fk_user_id:
          this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        fk_routine_maintenance_id: this.$route.query.fk_routine_maintenance_id,
      }).then(
        (res) => {
          if (res.code == 200) {
            this.popupModule = 2
            this.$refs.active = ''
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  history.go(-1)
                },
              },
            ]
            this.popupMessage = '订单发送成功！'
            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 2
          this.$refs.active = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br>订单发送失败!</br>'
          if (error.response && error.response.data && error.response.data.msg) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.reformCreate {
  display: flex;
  position: relative;
  .container {
    position: relative;
    .left {
      width: 3.2rem;
      height: 2.1rem;
      position: absolute;
      top: 0 !important;
      .leftList {
        .pic {
          width: 3.2rem;
          height: 2.1rem;
          position: absolute;
          font-size: 0.48rem;
          font-weight: bold;
          letter-spacing: 0.1rem;
          text-align: center;
          line-height: 1.1rem;
          // background-image: linear-gradient(to right, #5f7eb7, #4763a2);
          border-radius: 0.3rem;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .right {
      width: 12rem;
      height: 6.8rem;
      position: absolute;
      background: #25294f;
      font-size: 0.21rem;
      overflow: hidden;
      border-radius: 0.24rem;
      margin-left: 4rem;
      padding: 0.2rem;

      .list {
        overflow-y: auto;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        .content {
          position: relative;
          width: 100% !important;
          height: initial !important;
        }
      }
    }
    // .scroll_side {
    //   width: 0.08rem;
    //   height: 7.15rem;
    //   position: absolute;
    //   top: 0.05rem;
    //   left: 16.75rem;
    //   background: #242a4d;
    //   border-radius: 0.04rem;
    //   overflow: hidden;
    //   .scrollBar {
    //     width: 0.08rem;
    //     // height: 2rem;
    //     background: #7b87b6;
    //     border-radius: 0.04rem;
    //     position: absolute;
    //     transition: all 0.2s;
    //   }
    // }
  }
}
</style>
<style lang="less">
.reformCreate {
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    // background: repeating-linear-gradient(to right, #c98693, #a95361);
    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        letter-spacing: 0.03rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        justify-content: center !important;
        align-items: center !important;
        .title {
          font-size: 0.48rem;
          font-weight: bold;
          // padding-bottom: 0.12rem !important;
          p {
            margin-bottom: 0.22rem;
          }
        }
      }
      .result {
        font-size: 0.48rem !important;
        font-weight: bold !important;
        letter-spacing: 0.03rem !important;
      }
      .cancel {
        flex-direction: column;
        div {
          text-align: center;
        }
        div:nth-child(2) {
          margin-top: 0.1rem;
          color: red;
          line-height: 0.48rem;
        }
        //align-items: normal !important;
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

          // background: repeating-linear-gradient(to right, #c98693, #a95361);
        }
      }
    }
  }
  .container {
    .right {
      .scroll {
        top: 2.25rem !important;
        left: 17.9rem !important;
      }
    }
  }
}
</style>
      