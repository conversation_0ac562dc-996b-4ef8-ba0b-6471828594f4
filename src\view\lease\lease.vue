<template>
  <div class="lease">
    <div class="container">
      <div class="left">
        <div class="messageCenterList scrollParent" v-if="leftList.length > 0">
          <div class="leftList" :style="{ top: '0rem' }">
            <div
              class="messageItem"
              :ref="item.ref"
              v-for="(item, index) in leftList"
              :key="index"
            >
              <div class="item_user">
                <div class="pic" v-lazy-container="{ selector: 'img' }">
                  <img
                    :data-src="apiUrl + item.list_pic"
                    alt=""
                    :data-error="errorImg"
                  />
                </div>
                <div class="content">
                  <div class="title">
                    <div class="name">{{ item.name }}</div>
                    <div class="price">
                      押金：<span>{{ item.deposit_price }}元</span>
                    </div>
                  </div>
                  <div class="info">
                    单次租赁价格：<span>{{ item.price }}</span
                    >（为期{{ item.month }}个月）
                  </div>
                  <div class="subsidy">
                    补贴后的租赁价格：<span>{{ item.subsidy }}</span
                    >元（{{ item.month }}个月）<span
                      >{{ item.subsidy / item.month }}元/月</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_content" v-else>暂无商品列表</div>
      </div>
      <div class="right">
        <div
          class="right_up scrollParent"
          :key="this.rightList.length"
          v-if="this.rightList.length > 0"
        >
          <div class="list" :style="{ top: '0rem' }">
            <div
              class="rightList"
              :ref="item.ref"
              v-for="(item, index) in rightList"
              :key="index"
            >
              <div class="listContent">
                <div
                  class="status"
                  :style="{ color: statusList[item.status].color }"
                >
                  {{ statusList[item.status].text }}
                </div>
                <div class="good">
                  <div class="title">商品名称：</div>
                  <div class="name">{{ item.equipment_name }}</div>
                </div>
                <div class="time">
                  <div class="title">申请时间：</div>
                  <div class="name">{{ item.created_at }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_content" v-else>暂无记录</div>
        <div class="videoList" ref="refVideo">
          <!-- <video :src="videoUrl" controls autoplay loop></video> -->
        </div>
      </div>
      <el-dialog
        class="popup_box"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        title="辅具租赁"
      >
        <!--取消预约-->
        <div class="message_box cancel" v-if="popupModule == 2">
          <div class="detail">
            <div class="text" v-if="rightList[rightNums].status == 2">
              你即将取消当前服务。
            </div>
            <div class="content">
              服务内容：{{ this.rightList[this.rightNums].equipment_name }}
            </div>
          </div>
        </div>

        <div
          class="message_box result"
          v-html="popupMessage"
          v-if="popupModule == 3"
        ></div>
        <div class="popupBtnList">
          <div
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
          >
            {{ item.text }}
          </div>
        </div>
      </el-dialog>
    </div>
    <div class="address">
      <div class="name">商品列表</div>
    </div>
    <div class="tel" v-if="supplier_phone">客服热线：{{ supplier_phone }}</div>
    <div class="done"><div class="item">服务记录</div></div>
  </div>
</template>
      
<script>
import { getLeaseList, cancelLeaseList } from '@/api/index'
import media from '@/utils/mediaPlayer.js'
export default {
  name: 'lease',
  components: {},
  data() {
    return {
      timeNum: new Date().getTime(),
      leftList: [],
      rightList: [],
      statusList: [],
      errorImg: require('@/assets/lease_icon.png'),
      apiUrl: process.env.VUE_APP_API,
      videoUrl: '',
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      supplier_phone: '',
      sessionStorage_supplier_id: '',
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      selectBtnNums: 0,
      selectBtnList: [],
      isReady: false,
      playTimer: null,
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '辅具租赁')
  },
  computed: {},
  watch: {},
  mounted() {
    this.statusList = JSON.parse(localStorage.getItem('statusList'))

    this.supplier_phone = JSON.parse(
      sessionStorage.getItem('supplierInfo')
    ).telephone
    this.sessionStorage_supplier_id = JSON.parse(
      sessionStorage.getItem('supplierInfo')
    ).fk_supplier_id
    this.getData()
    if (sessionStorage.getItem('indexLease')) {
      this.leftNums = Number(sessionStorage.getItem('indexLease'))
      sessionStorage.removeItem('indexLease')
    }
    this.fuc.KeyboardEvents({
      down: () => {
        if (
          this.$store.state.app.media &&
          this.$store.state.app.media.isFullscreen
        ) {
          return
        }
        if (this.dialogVisible) {
          return
        }
        // 右上
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums++
            this.rightList[this.rightNums].ref = 'active'
          } else {
            this.rightList[this.rightNums].ref = ''
            this.$refs.active.splice(0, 1, this.$refs.refVideo)
            this.nextNums = -2
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        //左侧
        else if (this.nextNums == -1) {
          if (this.leftNums < this.leftList.length - 1) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            this.leftList[this.leftNums].ref = 'active'
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (
          this.$store.state.app.media &&
          this.$store.state.app.media.isFullscreen
        ) {
          return
        }
        if (this.dialogVisible) {
          return
        }
        // 右上
        if (this.nextNums > -1) {
          if (this.rightNums > 0 && this.rightList.length - 1 >= 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums--
            this.rightList[this.rightNums].ref = 'active'
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 右下
        else if (this.nextNums == -2) {
          if (this.rightList.length > 0) {
            this.$refs.active = []
            this.rightList[this.rightList.length - 1].ref = 'active'
            this.nextNums = this.rightNums
          }
        }

        // 左侧
        else {
          if (this.leftNums > 0 && this.leftList.length - 1 >= 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (
          this.$store.state.app.media &&
          this.$store.state.app.media.isFullscreen
        ) {
          return
        }
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }

        // 在右上
        if (this.nextNums > -1) {
          if (this.leftList.length > 0) {
            this.rightList[this.rightNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.nextNums = -1
          }
        }
        // 在右下
        else if (this.nextNums == -2) {
          if (this.leftList.length > 0) {
            this.$refs.active = []
            this.leftList[this.leftNums].ref = 'active'
            this.nextNums = -1
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (
          this.$store.state.app.media &&
          this.$store.state.app.media.isFullscreen
        ) {
          return
        }
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左侧
        if (this.nextNums == -1) {
          if (this.rightList.length > 0) {
            this.leftList[this.leftNums].ref = ''
            this.rightList[this.rightNums].ref = 'active'
            this.nextNums = this.leftNums
          } else {
            this.leftList[this.leftNums].ref = ''
            this.$refs.active.splice(0, 1, this.$refs.refVideo)
            this.nextNums = -2
          }
          // if (this.leftNums <= this.leftList.length - 1) {
          //   this.leftList[this.leftNums].ref = ''
          //   this.rightList[this.rightNums].ref = 'active'
          //   this.nextNums = this.leftNums
          // }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        if (this.nextNums == -2) {
          if (this.$store.state.app.media) {
            if (this.$store.state.app.media.isFullscreen) {
              return
            }
            // 全屏切换
            this.$store.state.app.media.fullscreen('t')
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', document.body)
            })
          }
        }
        if (this.nextNums == -1) {
          if (this.leftList[this.leftNums].name) {
            sessionStorage.setItem('indexLease', this.leftNums)
            // 页面跳转，视频dom销毁
            if (this.$store.state.app.media) {
              if (!this.isReady) {
                return
              }
              let time = new Date().getTime()
              if (time - this.timeNum < 0) {
                return
              }
              this.$store.state.app.media.stop(() => {
                this.isReady = false
                this.$nextTick(() => {
                  this.$router.push({
                    path: '/leaseDetail',
                    query: {
                      list: JSON.stringify(this.leftList[this.leftNums]),
                    },
                  })
                })
              })
              return
            }
            this.isReady = false
            this.$nextTick(() => {
              this.$router.push({
                path: '/leaseDetail',
                query: {
                  list: JSON.stringify(this.leftList[this.leftNums]),
                },
              })
            })
          }
        }
        if (this.nextNums > -1) {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.rightList[this.rightNums].ref = 'active'
            this.dialogVisible = false
            // console.log(123,this.popupBtnList[this.popupBtnNums].fuc(this.leftList[this.leftNums]));
            this.popupBtnList[this.popupBtnNums].fuc(
              this.rightList[this.rightNums]
            )
            return
          }

          if (!this.isReady) {
            return
          }
          if (this.$store.state.app.media) {
            let time = new Date().getTime()
            if (time - this.timeNum < 0) {
              return
            }
            this.$store.state.app.media.stop(() => {
              this.isReady = false
              if (this.rightList[this.rightNums].status == 2) {
                this.popupBtnList = [
                  {
                    text: '关闭',
                    ref: '',
                    fuc: () => {
                      this.dialogVisible = false
                      this.playVideo()
                    },
                  },
                  {
                    text: '取消订单',
                    ref: '',
                    fuc: this.cancelOrder,
                  },
                ]
              } else {
                this.popupBtnList = [
                  {
                    text: '关闭',
                    ref: '',
                    fuc: () => {
                      this.dialogVisible = false
                      this.playVideo()
                    },
                  },
                ]
              }

              this.dialogVisible = true
              this.popupModule = 2
              this.rightList[this.rightNums].ref = ''
              this.popupBtnList[this.popupBtnNums].ref = 'active'
            })
            return
          }

          if (this.rightList[this.rightNums].status == 2) {
            this.popupBtnList = [
              {
                text: '关闭',
                ref: '',
                fuc: () => {
                  this.dialogVisible = false
                },
              },
              {
                text: '取消订单',
                ref: '',
                fuc: this.cancelOrder,
              },
            ]
          } else {
            this.popupBtnList = [
              {
                text: '关闭',
                ref: '',
                fuc: () => {
                  this.dialogVisible = false
                  this.playVideo()
                },
              },
            ]
          }

          this.dialogVisible = true
          this.popupModule = 2
          this.rightList[this.rightNums].ref = ''
          this.popupBtnList[this.popupBtnNums].ref = 'active'
        }
        return
      },
      esc: () => {
        if (
          this.$store.state.app.media &&
          this.$store.state.app.media.isFullscreen
        ) {
          this.$store.state.app.media.fullscreen('t')
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
          return
        }
        if (this.nextNums > -1) {
          if (this.dialogVisible) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.rightList[this.rightNums].ref = 'active'
            this.dialogVisible = false
            this.playVideo()
            return
          }
        }
        if (this.$store.state.app.media) {
          if (!this.isReady) {
            return
          }
          let time = new Date().getTime()
          if (time - this.timeNum < 0) {
            return
          }
          this.$store.state.app.media.stop(() => {
            this.$store.dispatch('index/setFocusDom', null)
            history.go(-1)
          })
          return
        }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    playVideo() {
      clearTimeout(this.playTimer)
      this.playTimer = null
      this.playTimer = setTimeout(() => {
        this.isReady = false
        this.$nextTick(() => {
          var obj = {
            x: 1400,
            y: 700,
            w: 380,
            h: 220,
            r: 0,
          }
          let fontSize =
            Number(
              document
                .getElementsByTagName('html')[0]
                .style.fontSize.split('px')[0]
            ) / 100

          let videoList = [this.videoUrl]

          this.$store.state.app.media = media.DetermineKernel(
            {
              videoList: videoList,
              loop: 1,
              windowPos: obj,
              fontSize: fontSize,
            },
            () => {
              this.isReady = true
            }
          )
        })
      }, 1000)
    },
    popupClose() {
      this.timeNum = new Date().getTime()
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        this.popupBtnNums = 0
        this.popupModule = 1
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        if (this.$store.state.app.media) {
          this.$store.state.app.media.stop()
        }
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },

    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState', true)

      // 获取商品列表
      getLeaseList({
        supplier_id: this.sessionStorage_supplier_id,
        user_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].id,

        supplier_type: 13,
      })
        .then((res) => {
          this.$store.dispatch('app/setLoadingState', false)
          if (res.code == 200) {
            if (res.data.equipment_list) {
              let leftListClone = JSON.parse(
                JSON.stringify(res.data.equipment_list)
              )
              leftListClone.map((item) => {
                item.ref = ''
              })
              this.leftList = leftListClone
            }

            if (res.data.unfinish_list) {
              let rightListClone = JSON.parse(
                JSON.stringify(res.data.unfinish_list)
              )
              rightListClone.map((item) => {
                item.ref = ''
              })
              this.rightList = rightListClone
            }

            this.videoUrl =
              process.env.VUE_APP_API + res.data.index_video_list[0].video_url
            this.playVideo()

            if (this.leftList.length > 0) {
              this.nextNums = -1
              this.leftList[this.leftNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          }
        })
        .catch(() => {
          this.$store.dispatch('app/setLoadingState', false)
        })
    },

    cancelOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      cancelLeaseList({
        user_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].id,
        id: this.rightList[this.rightNums].id,
        supplier_type: 13,
        supplier_id: this.sessionStorage_supplier_id,
      }).then(
        (res) => {
          if (res.code == 200) {
            this.popupModule = 3
            this.rightList[this.rightNums].ref = ''
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  if (res.data) {
                    let rightClone = JSON.parse(JSON.stringify(res.data))
                    rightClone.map((item) => {
                      item.ref = ''
                    })
                    this.rightList = rightClone
                  }
                  this.rightList[this.rightNums].ref = 'active'
                  this.nextNums = this.rightNums
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active)
                    this.playVideo()
                  })
                },
              },
            ]
            this.popupMessage = '<br/>退订成功!<br>'
            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.rightList[this.rightNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br/>取消失败!<br>'
          if (
            error.response &&
            error.response.data &&
            error.response.data.msg
          ) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },
  },
  destroyed() {
    clearTimeout(this.playTimer)
    this.playTimer = null
  },
  beforeDestory() {},
}
</script>
      <style lang='less' scoped>
.lease {
  // height: 7rem;
  // overflow: hidden;
  // display: flex;
  // grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
  // gap: 0.16rem;
  // font-size: 0.2rem;
  // color: #e7e7ef;
  .address {
    display: flex;
    position: absolute;
    top: 1.6rem;
    left: 1.45rem;
    .name {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .name::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .done {
    position: absolute;
    top: 1.6rem;
    right: 3.45rem;
    .item {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .item::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .tel {
    position: absolute;
    top: 1.6rem;
    left: 5rem;
    font-size: 0.36rem;
    font-weight: bold;
    letter-spacing: 0.03rem;
    color: #b5c0ff;
  }
  .container {
    height: 7rem;
    overflow: hidden;
    display: flex;
    grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
    gap: 0.16rem;
    font-size: 0.2rem;
    color: #e7e7ef;
    .noData {
      div {
        font-size: 0.5rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -200%);
        letter-spacing: 0.05rem;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
      }
    }
    .aside {
      width: 2rem;
      height: 0.7rem;
      text-align: center;
      line-height: 0.7rem;
      margin-bottom: 0.2rem;
      background: #343d74;
      border-radius: 0.2rem;
      position: relative;
      font-weight: bold;
      transition: all 0.3s;
      letter-spacing: 0.05rem;
      text-indent: 0.05rem;
      font-size: 0.34rem;
      color: #e7e7ef;
      text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
    }
    .left {
      width: 12.5rem;
      height: 7rem;
      margin-left: 0;
      //padding-left: 0.1rem;
      //margin-left: 0.05rem;

      .messageCenterList {
        width: 12.5rem;
        height: 100%;
        position: relative;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          // left: 0.8rem;
          transition: all 0.2s;
          .messageItem {
            width: 100%;
            height: 1.1rem;
            padding: 0.5rem;
            margin-bottom: 0.35rem;
            position: relative;
            margin-right: 0.2rem !important;
            background-size: 100% 100% !important;
            // background: #ccc !important;
            background: #262954;
            border-radius: 0.3rem;
            overflow: hidden;
            .item_user {
              display: flex;
              .pic {
                width: 1.9rem !important;
                height: 1.15rem !important;
                border-radius: 0.15rem;
                position: relative;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .content {
                // width: initial !important;
                position: relative;
                margin-left: 0.5rem !important;
                top: -0.17rem;
                .title {
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 0.06rem;
                  .name {
                    font-size: 0.4rem;
                    font-weight: bold;
                  }
                  .price {
                    font-size: 0.3rem;
                    font-weight: bold;
                    span {
                      color: #f64e23;
                      font-size: 0.4rem;
                    }
                  }
                }
                .info,
                .subsidy {
                  font-size: 0.3rem;
                  font-weight: bold;
                  letter-spacing: 0.03rem;
                  span {
                    color: #f64e23;
                  }
                }
                .subsidy {
                  margin-top: 0.06rem;
                }
              }
            }
            .choose_tip {
              width: 4rem;
              height: 0.37rem;
              position: relative;
              top: 0.13rem;
              right: 0.5rem;
              div {
                width: 100%;
                height: 100%;
                font-size: 0.22rem;
                background: #3cc92d;
                border-radius: 0.05rem;
                position: absolute;
                text-align: center;
                line-height: 0.35rem;
                font-weight: bold;
                color: #fff;
                letter-spacing: 0.03rem;
              }
            }
          }
          .messageItem:nth-child(3n + 3) {
            margin-right: 0;
          }
        }
      }
      .no_content {
        position: absolute;
        line-height: 7rem;
        left: 6rem;
        // width: 4rem;
        // height: 7rem;
        text-align: center;
        font-size: 0.4rem;
      }
    }
    .right {
      .right_up {
        width: 4.17rem;
        height: 4.5rem;
        position: relative;
        overflow: hidden;

        .list {
          margin-left: 0.1rem;
          width: 3.87rem;
          position: relative;
          transition: all 0.3s;
          .rightList {
            height: 2.7rem;
            margin-bottom: 0.28rem;
            background: #262954;
            border-radius: 0.24rem;
            font-size: 0.28rem;
            font-weight: bold;
            .listContent {
              padding: 0.05rem 0.25rem;
              .status {
                color: #dd9c38;
                margin-bottom: 0.35rem;
                margin-top: 0.2rem;
                letter-spacing: 0.01rem;
              }
              .good,
              .price,
              .pay,
              .time {
                margin-bottom: 0.1rem;
                .title {
                  color: #b6c0fd;
                  min-width: 1.4rem;
                }
              }
              .good,
              .price {
                display: flex;
              }
            }
          }
        }
      }
      .videoList {
        position: absolute;
        right: 1.425rem;
        bottom: 1.6rem;
        width: 3.77rem;
        height: 2.2rem;
        // z-index: 1000;
        video {
          width: 100%;
          height: 100%;
        }
      }
      .no_content {
        //   margin: auto;
        line-height: 4.5rem;
        width: 4rem;
        height: 0.8rem;
        text-align: center;
        font-size: 0.4rem;
      }
    }
    // .tab_list {
    //   width: 4.2rem;
    //   height: 6.65rem;
    //   position: absolute;
    //   top: 0.69rem;
    //   right: 0.3rem;
    //   overflow: hidden;
    //   .no_content {
    //     margin: auto;
    //     line-height: 6.2rem;
    //     width: 4rem;
    //     height: 0.8rem;
    //     text-align: center;
    //     font-size: 0.4rem;
    //   }
    // }
  }
}
</style>
      <style lang="less">
.lease {
  // position: relative;

  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    // background: repeating-linear-gradient(to right, #c98693, #a95361);
    background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        justify-content: center;
        align-items: center;
        .title,
        .detail,
        .price {
          display: flex;
          font-size: 0.35rem;
          font-weight: bold;
          padding-bottom: 0.22rem !important;
          letter-spacing: 0.03rem;
        }
        .title {
          span {
            color: #f64e23;
            margin-left: 0.2rem;
          }
        }
        .price {
          span {
            color: #f64e23;
          }
        }
      }
      .cancel {
        // flex-direction: column;
        // text-align: center;
        .detail {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .text {
            font-size: 0.48rem;
            font-weight: bold;
          }
          .content {
            font-size: 0.48rem;
            font-weight: bold;
            height: initial !important;
            margin: 0 !important;
          }

          div {
            width: 100% !important;
            // height: 100%;
            font-size: 0.38rem;
          }
          div:nth-child(2) {
            margin-top: 0.2rem !important;
            line-height: 0.48rem;
          }
          //align-items: normal !important;
        }
      }
      .result {
        display: flex;
        text-align: center;
        font-size: 0.37rem;
        justify-content: center;
        align-items: center;
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          // background: repeating-linear-gradient(to right, #c98693, #a95361);
          background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
        }
      }
    }
  }
}
</style>
      