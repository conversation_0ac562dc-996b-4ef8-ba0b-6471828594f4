
// socket数据
import Socket from "./websocket.js";
import store from "@/store/index";
const CryptoJS = require("crypto-js");

// 当前实例化socket对象
let nowSocket = {};

// 实例化创建socket
const init = () => {
    close(()=>{
        nowSocket.socket = new Socket({
            url: process.env.VUE_APP_WS + `/api/v1/ws?user_type=web&home_id=` + store.getters.getUserInfo.home_id + `&user_id=` + store.getters.getUserInfo.oldsters[store.state.app.selectUserIndex].id+'&token=' + CryptoJS.AES.decrypt(store.state.app.token, store.state.app.s).toString(CryptoJS.enc.Utf8),
            heartTime: 10000,
            reconnectTime: 5000,
        });
    })
    // if (!nowSocket.socket) {
    //     nowSocket.socket = new Socket({
    //         url: process.env.VUE_APP_WS + `/api/v1/ws?user_type=web&home_id=` + store.getters.getUserInfo.home_id + `&user_id=` + store.getters.getUserInfo.oldsters[store.state.app.selectUserIndex].id,
    //         heartTime: 10000,
    //         reconnectTime: 5000,
    //     });
    // }
};
const value = () =>{
    // console.log(nowSocket)
    return nowSocket
}


// 消息回调函数
const message = (callback) => {
    if (nowSocket.socket) {
        nowSocket.socket.message = callback;
    }
};

const close = (callback) => {
    if (nowSocket.socket) {
        nowSocket.socket.closeWs(()=>{
            nowSocket.socket = null
            nowSocket={}
            if (callback) {
                callback()
            }
        })
    } else {
        if (callback) {
            callback()
        }
    }
};

export default {
    // value: nowSocket,
    init,
    value,
    message,
    close
};