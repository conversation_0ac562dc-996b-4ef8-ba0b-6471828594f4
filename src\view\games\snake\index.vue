<template>
  <div class="snake_container">
    <div class="game-panel">
      <div
        v-for="col in 400"
        :key="col"
        class="col-item"
        :class="{
          snake: isSnake(col),
          food: food == col,
          'snake-head': isHead(col),
        }"
      ></div>
    </div>
    <div class="score">
      <div class="score-container">
        <span>当前得分</span>
        <span>{{ score }}</span>
      </div>
      <div class="best-container">
        <span>最高得分</span>
        <span>{{ snakeScore }}</span>
      </div>
    </div>
    <!-- <div class="operation">
      <div
        class="operation-item operation-top-down"
        @click="changeDirection('TOP')"
      >
        上
      </div>
      <div class="operation-left-right-col">
        <div class="operation-item" @click="changeDirection('LEFT')">左</div>
        <div class="operation-item" @click="changeDirection('RIGHT')">右</div>
      </div>
      <div
        class="operation-item operation-top-down"
        @click="changeDirection('DOWN')"
      >
        下
      </div>
    </div> -->
    <div class="game-over" v-if="gameOver">
      <span class="game-over-text">游戏结束</span>
      <br />
      <!-- <span class="restart" @click="restart">重新开始</span> -->
    </div>
    <div class="rule">
      <h2>操作说明</h2>
      <h3>游戏目标：</h3>
      <p>
        使用你的上、下、左、右方向键来移动小蛇，吃掉红色小方块,以争取更高的分数。
      </p>
      <h3>操作方式：</h3>
      <p>• 遥控器左按键：方块向左移动。</p>
      <p>• 遥控器右按键：方块向右移动。</p>
      <p>• 遥控器上按键：方块向上移动。</p>
      <p>• 遥控器下按键：方块向下移动。</p>
      <p>• 遥控器“返回”按键实现游戏暂停。</p>
      <p>• 遥控器“中心键”为确认按键。</p>
      <h3>游戏规则：</h3>
      <p>• 游戏使用一个20x20的网格。</p>
      <p>• 游戏开始时，小蛇会处于移动状态，并且棋盘上会随机生成一个小方块。</p>
      <p>
        •
        玩家通过上下左右方向键控制方块的移动方向。所有的方块会沿着指定的方向滑动。
      </p>
      <p>
        • 当小蛇触碰到方块的时候，会吃掉方块，小蛇的身体随之增加，即可得分。
      </p>
      <p>• 当小蛇移动过程中头部触碰到自己身体的任意部位，则游戏结束。</p>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :show-close="false"
      :close-on-click-modal="false"
      @opened="popupOpend"
      @close="popupClose"
      custom-class="operatePopup"
      :title="popupModule == 1 ? '操作' : '提示'"
    >
      <!--操作弹窗-->
      <div class="btnList" v-if="popupModule == 1">
        <div
          class="btnItem"
          :ref="item.ref"
          v-for="(item, index) in popupBtnList"
          :key="index"
        >
          <div class="box-btnItem" v-html="item.text"></div>
        </div>
      </div>

      <!--消息弹窗-->
      <div class="popupMessage" v-if="popupModule == 2">
        <div class="popupMessage" v-html="popupMessage"></div>
        <div
          class="popupMessageBtn"
          :ref="item.ref"
          v-for="(item, index) in popupBtnList"
          :key="index"
        >
          {{ item.text }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
export default {
  name: 'Snake',
  data() {
    return {
      dialogVisible: false,
      flag: false,
      popupModule: 1, // 1、按钮弹窗  2、消息提示
      popupMessage: '',
      popupBtnNums: 0,
      score: 0,
      snakeScore: localStorage.getItem('snakeScore'),
      popupBtnList: [],
      snakeBody: [1],
      food: 0,
      direction: 'RIGHT',
      gameOver: false,
      speed: 400,
      isKeyPressed: false,
      isGameEnded: false,
    }
  },
  watch: {
    snakeBody: 'checkRule',
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '贪吃蛇')
    this.$store.dispatch('index/setFocusDom', null)
  },
  mounted() {
    this.init()
    document.addEventListener('keydown', (event) => {
      this.keyDown(event)
    })
    document.addEventListener('keyup', (event) => {
      this.keyUp(event)
    })
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
      },
      up: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
      },
      left: () => {
        if (this.dialogVisible) {
          return
        }
      },
      right: () => {
        if (this.dialogVisible) {
          return
        }
      },
      enter: () => {
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].fuc(
            this.popupBtnList[this.popupBtnNums]
          )
          return
        }
      },
      esc: () => {
        if (this.dialogVisible) {
          return
        }
        clearInterval(this.intId)
        this.isGameEnded = true
        this.dialogVisible = true
        this.popupModule = 1
        if (!this.gameOver) {
          this.popupBtnList = [
            {
              text: '继续游戏',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
                this.flag = false
                this.isGameEnded = false
                this.move()
              },
            },
            {
              text: '重新开始',
              ref: '',
              fuc: () => {
                this.restart()
              },
            },
            {
              text: '退出游戏',
              ref: '',
              fuc: () => {
                this.$store.dispatch('index/setFocusDom', null)
                history.go(-1)
              },
            },
          ]
        } else {
          this.popupBtnList = [
            {
              text: '重新开始',
              ref: '',
              fuc: () => {
                this.restart()
              },
            },
            {
              text: '退出游戏',
              ref: '',
              fuc: () => {
                this.$store.dispatch('index/setFocusDom', null)
                history.go(-1)
              },
            },
          ]
        }

        this.popupBtnList[this.popupBtnNums].ref = 'active'
      },
    })
  },
  methods: {
    popupClose() {
      this.flag = false
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', null)
        setTimeout(() => {
          this.popupBtnList = []
          this.popupBtnNums = 0
          this.popupModule = 1
        }, 200)
      })
    },
    popupOpend() {
      this.flag = true
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        setTimeout(() => {
          document.getElementById('focus_border').style.borderColor = '#5472B0'
        }, 0)
        return
      })
    },

    setLocalstorage() {
      let score = localStorage.getItem('snakeScore')
      if (score) {
        if (this.score > score) {
          localStorage.setItem('snakeScore', this.score)
          this.snakeScore = this.score
        }
      } else {
        localStorage.setItem('snakeScore', this.score)
        this.snakeScore = this.score
      }
    },
    checkRule() {
      let uniqBady = [...new Set(this.snakeBody)]
      if (uniqBady.length !== this.snakeBody.length) {
        this.stop()
      } else {
        const snakeLength = uniqBady.length - 1
        const baseSpeed = 400 // 初始速度
        const minSpeed = 100 // 最小速度（最快）

        // 根据蛇的长度计算新速度
        let newSpeed = Math.max(baseSpeed - snakeLength * 10, minSpeed)

        if (this.isKeyPressed) {
          newSpeed = 50
        }

        if (newSpeed !== this.speed) {
          this.changeSpeed(newSpeed)
        }
      }
    },
    changeSpeed(speed) {
      clearInterval(this.intId)
      this.speed = speed
      this.move()
    },
    restart() {
      this.snakeBody = [1]
      this.direction = 'RIGHT'
      this.gameOver = false
      this.isGameEnded = false
      this.isKeyPressed = false
      this.speed = 400
      this.init()
      this.dialogVisible = false
      this.score = 0
    },
    stop() {
      clearInterval(this.intId)
      this.gameOver = true
      this.isGameEnded = true
    },
    isSnake(col) {
      //   return _.indexOf(this.snakeBody, col) > -1
      return this.snakeBody.indexOf(col) > -1
    },
    isHead(col) {
      //   return _.last(this.snakeBody) === col
      return this.snakeBody.at(-1) === col
    },
    // changeDirection(direction) {
    //   this.direction = direction
    // },
    touch(event) {
      const gameHeight = 600 // 游戏面板高度
      const gameWidth = 600 // 游戏面板宽度
      const topThreshold = gameHeight / 3
      const bottomThreshold = (gameHeight * 2) / 3

      if (event.clientY < topThreshold) {
        this.direction = 'TOP'
      } else if (event.clientY > bottomThreshold) {
        this.direction = 'DOWN'
      } else if (event.clientX > gameWidth / 2) {
        this.direction = 'RIGHT'
      } else {
        this.direction = 'LEFT'
      }
    },
    move() {
      this.intId = setInterval(() => {
        const head = this.snakeBody.at(-1)
        let newBody = [...this.snakeBody]
        let nextPosition

        if (head > 400) {
          nextPosition = head - 400
        } else if (head < 1) {
          nextPosition = 400 + head
        } else {
          switch (this.direction) {
            case 'LEFT':
              nextPosition = head % 20 === 1 ? head + 19 : head - 1
              break
            case 'TOP':
              nextPosition = head <= 20 ? head + 380 : head - 20
              break
            case 'RIGHT':
              nextPosition = head % 20 === 0 ? head - 19 : head + 1
              break
            case 'DOWN':
              nextPosition = head > 380 ? head - 380 : head + 20
              break
            default:
              break
          }
        }

        if (nextPosition === this.food) {
          newBody.push(nextPosition)
          this.food = Math.floor(Math.random() * 400) + 1
          this.score = (newBody.length - 1) * 10
          this.setLocalstorage()
        } else {
          newBody.shift()
          newBody.push(nextPosition)
        }

        this.snakeBody = newBody
      }, this.speed)
    },
    keyDown(event) {
      if (!this.flag && !this.isKeyPressed && !this.isGameEnded) {
        const newDirection = this.getNewDirection(event.keyCode)
        if (newDirection && !this.isOppositeDirection(newDirection)) {
          this.isKeyPressed = true
          this.changeSpeed(50)
          this.direction = newDirection
        }
      }
    },
    getNewDirection(keyCode) {
      const directionMap = {
        37: 'LEFT',
        38: 'TOP',
        39: 'RIGHT',
        40: 'DOWN',
      }
      return directionMap[keyCode]
    },

    isOppositeDirection(newDirection) {
      const opposites = {
        LEFT: 'RIGHT',
        RIGHT: 'LEFT',
        TOP: 'DOWN',
        DOWN: 'TOP',
      }
      return opposites[this.direction] === newDirection
    },
    keyUp() {
      if (!this.flag && !this.isGameEnded) {
        this.isKeyPressed = false
        this.changeSpeed(400)
      }
    },
    init() {
      //   this.food = _.random(1, 3600)
      this.food = Math.floor(Math.random() * 400) + 1
      this.move()
    },
  },
}
</script>
  
<style scoped lang="less">
.snake_container {
  position: relative;
  .game-panel {
    position: absolute;
    width: 8rem;
    height: 8rem;
    top: -0.6rem;
    left: 4.6rem;
    overflow: hidden;
    line-height: 0;
    .col-item {
      background: #267fb7;
      width: 0.34rem;
      height: 0.34rem;
      border-radius: 0.03rem;
      margin: 0.03rem;
      display: inline-block;
      border: none;
      box-sizing: border-box;
    }
    .snake {
      background: #57faf7;
    }

    .snake-head {
      background: rgb(216, 52, 234);
    }

    .food {
      background: rgb(241, 60, 60);
    }
  }

  .score {
    position: absolute;
    top: -1.05rem;
    left: 1rem;
    display: flex;
    //   justify-content: space-between;
    flex-direction: column;
    height: 4rem; // line-height: 60px;
    div {
      width: 3rem;
      height: 100%;
      display: flex;
      flex-direction: column;
      margin-top: 0.5rem;
      align-items: center;
      justify-content: center;
      padding-left: 0.05rem;
      padding-right: 0.05rem;
      border-radius: 0.05rem;
      // background: #bbada0;
      background: url('../../../assets/matrix_state.png') no-repeat;
      background-size: 100% 100%;

      span {
        display: block;
        width: 80%;
        margin: 0 auto;
      }
      span:nth-child(1) {
        font-size: 0.26rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        font-weight: 600;
        letter-spacing: 0.05rem;
        color: #1de9f4;
      }
      span:nth-child(2) {
        font-size: 0.5rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        font-weight: 600;
        white-space: nowrap; /* 防止换行 */
        overflow: hidden; /* 溢出隐藏 */
        text-overflow: ellipsis; /* 使用省略号表示溢出 */
      }
    }

    span {
      font-size: 0.4rem;
      color: #fff;
      font-weight: bold;
    }
  }

  .game-over {
    position: absolute;
    width: 8rem;
    height: 8rem;
    top: -0.6rem;
    left: 4.6rem;
    text-align: center;
    background: rgba(238, 228, 218, 0.73);
    z-index: 99;
    border-radius: 0.03rem;
    text-align: center;
    color: #8f7a66;
    .game-over-text {
      font-size: 0.7rem;
      font-weight: bold;
      height: 0.6rem;
      line-height: 7rem;
    }
  }

  .restart {
    font-size: 0.15rem;
    color: red;
  }
  .rule {
    position: absolute;
    width: 3.6rem;
    right: -0.2rem;
    top: -0.6rem;
    padding: 0.2rem;
    background: url("../../../assets/connectFour_state.png") no-repeat;
    background-size: 100% 100%;
    h3 {
      margin-top: 0.15rem;
      margin-bottom: 0.08rem;
    }
    p {
      font-size: 0.21rem;
    }
  }
  .source-wrap {
    position: absolute;
    top: 3rem;
    left: 2rem;
    display: flex;
    //   justify-content: space-between;
    flex-direction: column;
    height: 2rem; // line-height: 60px;
    div {
      // width: 100px;
      width: 2rem;
      height: 100%;
      display: flex;
      flex-direction: column;
      margin-top: 0.5rem;
      align-items: center;
      justify-content: center;
      padding-left: 0.05rem;
      padding-right: 0.05rem;
      border-radius: 0.05rem;
      background: #bbada0;
      .num {
        font-size: 0.5rem;
        font-weight: bold;
        color: #ffffff;
      }
      &:last-child {
        //   margin-left: 0.05rem;
      }
    }
    span {
      font-size: 0.4rem;
      color: #fff;
      font-weight: bold;
    }
  }
}
</style>
<style lang="less">
.snake_container {
  .el-dialog {
    //width: fit-content;
    //margin-top: 0 !important;
    //top: 50%;
    //transform: translateY(-50%);
    //border-radius: 0.16rem;
    .el-dialog__header {
      .el-dialog__title {
        font-size: 0.5rem !important;
      }
    }
    .el-dialog__body {
      .btnList {
        padding: 0.2rem 0;

        .btnItem {
          width: 8.75rem;
          height: 1rem;
          line-height: 1.08rem;
          background: #5472b0;
          //color: #fff;
          border-radius: 0.2rem;
          margin-bottom: 0.3rem;
          text-align: center;
          font-weight: bold;
          font-size: 0.5rem;
          position: relative;
          //text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.3);
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          img {
            width: 0.48rem;
            height: 0.48rem;
            position: absolute;
            top: 50%;
            left: 2.2rem;
            transform: translateY(-45%);
          }
          .box-btnItem {
            background: linear-gradient(to bottom, #fdfeff 30%, #bed1fb 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            //text-shadow: 0rem 0.02rem 0.05rem rgba(0, 0, 0, 0.5);
          }
        }
        .btnItem:last-child {
          margin-bottom: 0;
        }
      }
      .popupMessage {
        padding: 0.2rem 0;
        .popupMessage {
          //line-height: 0.4rem;
          text-align: center;
          //font-size: 0.24rem;
          font-size: 0.45rem;
          margin-bottom: 0.3rem;
        }
        .popupMessageBtn {
          width: 8.75rem;
          height: 1rem;
          //width: 20vw;
          //height: 0.6rem;
          //line-height: 0.68rem;
          line-height: 1rem;
          background: #5472b0;
          font-weight: bold;
          text-align: center;
          //font-size: 0.24rem;
          font-size: 0.5rem;
          color: #fff;
          border-radius: 0.16rem;
          margin: 0 auto;
          margin-top: 0.2rem !important;
        }
        .popupMessageBtn:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>