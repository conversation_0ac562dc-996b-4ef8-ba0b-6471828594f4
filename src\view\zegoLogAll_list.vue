<template>
  <div class="zegoLogAll_list">
    <div class="right_zego">
      <!--通话记录-->
      <div class="applyFriendList scrollParent" :key="2">
        <div class="applyList" :style="{ top: '0rem' }" v-if="applyList.length > 0">
          <div
            class="applyItem"
            :ref="item.ref"
            v-for="(item, index) in applyList"
            :key="index"
          >
            <div class="applyItemAll">
              <div class="item_user">
                <div class="userContent" v-html="item.content"></div>
              </div>
              <div class="time">
                {{ item.created_at }}
              </div>
              <div v-lazy-container="{ selector: 'img' }">
                <img class="avatar" v-if="item.avatar" :data-src="item.avatar" :data-error="lazyError" alt="" />
              </div>
            </div>

          </div>
        </div>

        <div class="noData" v-else>
          <div v-if="!this.$store.getters.loadingState">暂无近期记录</div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import {
  GetCallLogList,
} from '@/api/index'

export default {
  name:'zegoCall',
  components: {},
  data() {
    return {
      lazyError: require('@/assets/care_avatar.png'),
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      applyBgList: [
        {
          // color: 'rgba(89,167,216,1)',
          // badgeColor: 'rgba(131,205,242,1)',
          // bg: require('@/assets/1_apply_bg_pic.png'),
          color: 'rgba(141,133,218,1)',
          badgeColor: 'rgba(175,169,244,1)',
          bg: require('@/assets/3_apply_bg_pic.png'),
        },
        {
          // color: 'rgba(198,164,100,1)',
          // badgeColor: 'rgba(223,196,133,1)',
          // bg: require('@/assets/2_apply_bg_pic.png'),
          color: 'rgba(141,133,218,1)',
          badgeColor: 'rgba(175,169,244,1)',
          bg: require('@/assets/3_apply_bg_pic.png'),
        },
        {
          color: 'rgba(141,133,218,1)',
          badgeColor: 'rgba(175,169,244,1)',
          bg: require('@/assets/3_apply_bg_pic.png'),
        },
      ],
      applyList: [],
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '通话记录')
    if (window.location.href.indexOf('type') > -1) {
      this.leftNums = Number(window.location.href.split('type=')[1].split('&')[0])
    }
  },
  computed: {},
  watch: {


  },
  mounted() {

    this.applyForList()

    this.fuc.KeyboardEvents({
      down: () => {
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.applyList.length - 1) {
            this.applyList[this.rightNums].ref = ''
            // this.setIsRead(this.applyList[this.rightNums])
            this.rightNums++
            this.applyList[this.rightNums].ref = 'active'
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      up: () => {
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums > 0) {
            this.applyList[this.rightNums].ref = ''
            // this.setIsRead(this.applyList[this.rightNums])
            this.rightNums--
            this.applyList[this.rightNums].ref = 'active'
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      left: () => {

      },
      right: () => {

      },
      enter: () => {

      },
      esc: () => {
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
    sessionStorage.removeItem('zegoListIndex')

  },
  methods: {
    popupClose() {
      // if (this.leftNums == 0) {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        setTimeout(()=>{
          this.popupBtnNums = 0
          this.popupModule = 1
        },0)

      })
      // }
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#5472B0'
      })
    },


    //通话记录
    applyForList() {
      if (!this.$route.query || !this.$route.query.fk_friend_id) {
        return
      }

      this.applyList = []
      this.$store.dispatch('app/setLoadingState', true);
      GetCallLogList({
        user_id: this.$store.getters.getUserInfo.home_id,
        fk_friend_id: this.$route.query.fk_friend_id
      }).then((res) => {
        this.$store.dispatch('app/setLoadingState', false);
        if (res.code == 200 && res.data.data) {
          let applyList = JSON.parse(JSON.stringify(res.data.data))
          let itemIndex = 0
          let badgeShow = false
          applyList.map((item, index) => {
            item.ref = ''
            if (index % 3 == 0) {
              itemIndex = 0
            }
            if (item.avatar != '') {
              item.avatar = process.env.VUE_APP_API + item.avatar
            }
            item.bg = this.applyBgList[itemIndex].bg
            item.color = this.applyBgList[itemIndex].color
            item.badgeColor = this.applyBgList[itemIndex].badgeColor
            this.applyList.push(item)
            itemIndex++

          })

          this.$nextTick(() => {
            this.fuc.setScroll()
          })

          if (this.applyList.length > 0) {
            this.nextNums = 0
            this.applyList[this.rightNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })

          }
        }
      }).catch(err=>{
        this.$store.dispatch('app/setLoadingState', false);
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      })
    },

  },
  destroyed() {

  },
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.zegoLogAll_list {
  height: 7rem;
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(1, 1fr); /* 每行显示两个元素 */
  gap: 0.16rem;
  font-size: 0.2rem;
  color: #e7e7ef;
  .noData {
    div {
      font-size: 0.5rem;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -200%);
      letter-spacing: 0.05rem;
      text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
    }
  }
  .right_zego {
    width: 100%;
    height: 7rem;
    margin-left: 0;
    //padding-left: 0.1rem;
    //margin-left: 0.05rem;
    .item_title {
      height: 0.6rem;
      line-height: 0.6rem;
      background: #ccc;
      font-size: 0.28rem;
      font-weight: bold;
      color: #000;
      padding-left: 0.2rem;
    }

    .applyFriendList {
      height: 100%;
      position: relative;
      .applyList {
        width: 98.6%;
        border-radius: 0.3rem;
        position: absolute;
        //left: 0.8rem;
        transition: all 0.2s;
        .applyItem {
          width: 100%;
          height: 1.7402rem;
          margin-bottom: 0rem;
          background-size: 100% 100% !important;
          border-radius: 0.3rem;
          overflow: hidden;
          position: relative;
          .applyItemAll {
            width: 100%;
            height: 100%;
            border-bottom: 0.06rem #fff dashed;
            .item_user {
              text-align: left;
              position: absolute;
              left: 2.1rem;
              top: 50%;
              transform: translateY(-50%);
              .userContent {
                //line-height: 0.6rem;
                color: #e7e7ef;
                width: 8.8rem;
                overflow: hidden;
                /*文本不会换行*/
                white-space: nowrap;
                /*当文本溢出包含元素时，以省略号表示超出的文本*/
                text-overflow: ellipsis;
                font-weight: bold;
              }
              div:nth-child(1) {
                font-size: 0.45rem;
                letter-spacing: 0.02rem;
                //text-indent: -0.05rem;
              }
              div:nth-child(2) {
                font-size: 0.3rem;
                font-weight: 600;
              }
            }
            .time {
              position: absolute;
              right: 0.4rem;
              bottom: 0.25rem;
              font-size: 0.26rem;
              font-weight: bold;
            }
            .avatar {
              width: 1rem;
              height: 1rem;
              border-radius: 50%;
              position: absolute;
              left: 4.3%;
              top: 52%;
              transform: translateY(-50%);
              background: rgba(255,255,255,0.8);
              border: 0.04rem solid #B1ABD2;
              box-sizing: border-box;
            }
            .badge {
              width: 0.15rem;
              height: 0.15rem;
              border-radius: 50%;
              background: #ff5f5f;
              position: absolute;
              top: 0.45rem;
              left: 1rem;
              border: 0.03rem solid transparent;
            }
          }

        }
      }
    }
  }
}
</style>
<style lang="less">
.zegoLogAll_list {
  .called_name {
    padding: 0 0.1rem;
    font-weight: bold;
    //color: rgba(210,126,126,1);
  }
}
</style>
