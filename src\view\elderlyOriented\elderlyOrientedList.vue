<template>
  <div class='elderlyOrientedList'>
    <div class="left_more">
      <img v-if="leftList.length > 0 && isImageUrl(leftList[0].cover_img)" :src="leftList[0].cover_img" alt="">

    </div>

    <div class="right_more">
      <div class="item_list scrollParent" :key="leftNums">
        <div class="rightList" :key="leftNums" :style="{top: '0rem'}">
          <div class="friendsItem" :ref="item.ref" v-for="(item,index) in rightList" :key="index" :data="rightNums">
              <img v-if="item.show" @error="defImg(index)" :src="item.cover_img" alt="">
              <div v-else class="item_user" :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }">
                <div>
                  {{item.title}}
                </div>
              </div>
          </div>
        </div>
      </div>

    </div>

    <div class="notice" v-if="supplierInfo.telephone">
      <img :src="require('../../assets/phone.png')" alt="">
      客服热线: {{supplierInfo.telephone}}
    </div>
  </div>

</template>

<script>

import { getShiLaoHuaList } from "@/api/index";
import media from "@/utils/mediaPlayer";

export default {
  name:'elderlyOrientedList',
  components: {
  },
  data() {
    return {
      timer: null,
      leftList:[],
      leftNums: 0,
      nextNums: 0,   // -1  说明焦点在左侧   > -1  在右侧
      supplierInfo: null,
      rightList:[],
      rightNums: 0,
    };
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle','适老化改造')
    this.supplierInfo = JSON.parse(sessionStorage.getItem('supplierInfo'))
    if (sessionStorage.getItem('elderlyFocus')) {
      this.leftNums = 0
      this.rightNums=Number(sessionStorage.getItem('elderlyFocus'))
      this.nextNums = this.leftNums
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.getData()

    this.fuc.KeyboardEvents({
      down:()=>{
        // 右侧
        if (this.nextNums > -1) {
            if (this.rightNums < this.rightList.length - 1) {
              if (this.rightList[this.rightNums + 3]) {
                this.rightList[this.rightNums].ref = ""
                this.rightNums += 3
                this.rightList[this.rightNums].ref = "active"
              } else {
                // console.log(this.rightNums)
                // console.log(this.rightList.length -(this.rightList.length % 3))
                if (this.rightNums  < (this.rightList.length - this.rightList.length % 3)) {
                  this.rightList[this.rightNums].ref = ""
                  this.rightNums = this.rightList.length - 1
                  this.rightList[this.rightNums].ref = "active"
                }
              }
          }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      up:()=>{
        // 右侧
        if (this.nextNums > -1) {
            if (this.rightList[this.rightNums - 2]) {
              this.rightList[this.rightNums].ref = ""
              this.rightNums -= 2
              this.rightList[this.rightNums].ref = "active"
            }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      left:()=>{
        if (this.dialogVisible) {
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          if(this.rightNums % 2 != 0) {
            this.rightList[this.rightNums].ref = ""
            this.rightNums --
            this.rightList[this.rightNums].ref = "active"
          }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      right:()=>{
        // 在右侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1) {
            if (this.rightNums % 2 != 1) {
              this.rightList[this.rightNums].ref = ""
              this.rightNums ++
              this.rightList[this.rightNums].ref = "active"
            }
          }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      enter:()=>{
        if (this.nextNums > -1 && this.rightList.length > 0) {
          clearTimeout(this.playTimer)
          this.playTimer = null
          if (this.$store.state.app.media) {
            this.$store.state.app.media.stop(()=>{
              sessionStorage.setItem('elderlyFocus',this.rightNums)
              sessionStorage.setItem('elderlyOrientedContent',this.rightList[this.rightNums].content)
              this.$router.push({
                path: './elderlyOrientedCreate?id=' + this.rightList[this.rightNums].id + '&title=' + this.rightList[this.rightNums].title
              })
            })
            return
          }
          sessionStorage.setItem('elderlyFocus',this.rightNums)
          sessionStorage.setItem('elderlyOrientedContent',this.rightList[this.rightNums].content)
          this.$router.push({
            path: './elderlyOrientedCreate?id=' + this.rightList[this.rightNums].id + '&title=' + this.rightList[this.rightNums].title
          })


        }
      },
      esc:()=>{
        clearTimeout(this.playTimer)
        this.playTimer = null
        if (this.$store.state.app.media) {
          this.$store.state.app.media.stop(()=>{
            this.$store.dispatch('index/setFocusDom', null);
            history.go(-1)
          })
          return
        }
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      }
    })
  },
  methods: {
    defImg(index){
      this.rightList[index].show = false
    },
    isImageUrl(url) {
      // 定义常见的图片格式
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];

      // 从URL中提取文件扩展名
      const extension = url.split('.').pop().toLowerCase();

      // 检查扩展名是否在常见的图片格式中
      return imageExtensions.includes(extension);
    },
    getData(){
      this.$store.dispatch('index/setFocusDom',this.$refs.active);
      getShiLaoHuaList({
        fk_supplier_id: this.supplierInfo.fk_supplier_id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        home_id: this.$store.getters.getUserInfo.home_id,
      })
      .then(res=>{
        // 首页宣传
        this.leftList = []
        this.rightList = []
        let dataList = JSON.parse(JSON.stringify(res.data.class_list))
        dataList.map((item,index)=>{
          item.cover_img = item.cover_img.indexOf('http') > -1 ? item.cover_img : process.env.VUE_APP_API + item.cover_img
          if (index == 0) {
            this.leftList.push(item)
          } else {
            this.rightList.push(item)
          }
        })
        if (this.leftList.length > 0) {
          if (this.isImageUrl(this.leftList[0].cover_img)) {
            if (this.$store.state.app.media) {
              this.$store.state.app.media.stop(()=>{

              })
            }
          } else {
            this.playVideo()
          }
        }
        if (this.rightList.length > 0) {
          this.getRightList(this.rightList)
        }
      })
    },
    // 右侧数据
    getRightList(data) {
      if (!data) {
        return
      }
      this.rightList = data
      this.rightList.map(item=>{
        item.ref = ""
        item.show = false
        if (item.cover_img) {
          item.cover_img = item.cover_img.indexOf('http') > -1 ? item.cover_img : process.env.VUE_APP_API + item.cover_img
          item.show = true
        } else {
          item.show = false
        }
      })

      // if (this.firstShow && this.rightList.length > 0) {
      if (sessionStorage.getItem('elderlyFocus')) {
        this.nextNums = this.leftNums
        this.rightList[this.rightNums].ref = "active"
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
          this.fuc.setScroll()
        })
        this.firstShow = false
        sessionStorage.removeItem('elderlyFocus')
      }
      else {
        this.rightNums = 0
        this.rightList[this.rightNums].ref = "active"
        this.nextNums = 0
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
          this.fuc.setScroll()
        })
      }

      this.$nextTick(()=>{
        this.fuc.setScroll()
      })

    },
    playVideo() {
      clearTimeout(this.playTimer)
      this.playTimer = null
      this.playTimer = setTimeout(()=>{
        this.$nextTick(()=>{
          // || this.$store.state.app.media
          if (this.leftList.length < 1 ) {
            return
          }

          var obj={
            x:127,
            y:231,
            w:980,
            h:680,
            r: 24
          }
          let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0]) / 100

          let videoList = []
          this.leftList.map(item=>{
            if (item.cover_img) {
              item.cover_img = item.cover_img.indexOf('http') == -1 ? process.env.VUE_APP_API + item.cover_img : item.cover_img
              // item.url = 'http://192.168.10.68:24002/public/storage/uploaded/2024_03/b89717afc46b8ef7c5b9737543cde9b8.mp4'
              videoList.push(item.cover_img)
            }
          })
          if (!this.$store.state.app.media) {
            this.$store.state.app.media = media.DetermineKernel({
              videoList: videoList,
              loop:1,
              windowPos: obj,
              fontSize: fontSize
            },()=>{
              this.isReady = true
            });
          }
        })
      },1000)
    }
  },
  destroyed() {

  },
  beforeDestory() {
  },
}
</script>
<style lang='less' scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
.elderlyOrientedList {
  height: 7rem;
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
  gap: 0.16rem;
  font-size: 0.2rem;
  color: #E7E7EF;
  .notice {
    position: absolute;
    font-size: 0.28rem;
    right: 1.2rem;
    bottom: 0.6rem;
    color: #B2BAEF;
    display: flex;
    flex-direction: row;
    align-items: center;
    img {
      position: relative;
      top: 0.02rem;
      left: -0.05rem;
      width: 0.32rem;
      height: 0.32rem;
    }
  }
  .left_more {
    width: 9.94rem;
    height: 6.94rem;
    border-radius: 0.2rem;
    position: relative;
    background: url("../../assets/reform_video.png") no-repeat;
    background-size: 100% 100%;
    img {
      width: 9.78rem;
      height: 6.78rem;
      border-radius: 0.24rem;
      object-fit: cover;
      margin-left: 0.08rem;
      margin-top: 0.08rem;
    }
  }
  .right_more {
    width: 6.8rem;
    height: 7rem;
    margin-left: 0.2rem;
    .item_list {
      height: 100%;
      border-radius: 0.26rem;
      overflow: hidden;
      position: relative;
      .rightList {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
        //gap: 0.16rem;
        gap: 0;
        position: absolute;
        top: 0;
        transition: all 0.2s;
        .friendsItem {
          border-radius: 0.26rem;
          height: 2.1rem;
          overflow: hidden;
          color: #000;
          font-weight: bold;
          position: relative;
          margin-bottom: 0.3rem;
          margin-right: 0.3rem;
          background-size: 100% 100% !important;
          img {
            display: block;
            width: 100%;
            height: 100%;
          }
          .typeIcon {
            display: block;
            width: 0.75rem;
            height: 0.6rem;
            position: absolute;
            left: 0.5rem;
            top: 0.46rem;
          }
          .typeIcon_phone {
            top: 0.35rem;
            width: 0.42rem;
            height: 0.7rem;
          }
          .item_user {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #eee;

            div {
              line-height: 0.6rem;
              //color: #E7E7EF;
              color: #888;
            }
            div:nth-child(1) {
              font-size: 0.45rem;
              letter-spacing: 0.05rem;
              text-indent: -0.05rem;
            }
            div:nth-child(2) {
              font-size: 0.3rem;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}

</style>
<style lang="less">
.elderlyOrientedList {

}


</style>
