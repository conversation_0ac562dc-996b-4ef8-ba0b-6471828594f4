<template>
  <div class="hospital">
    <div class="container">
      <div class="left">
        <div
          class="messageCenterList scrollParent"
          v-if="this.leftList.length > 0"
        >
          <div class="leftList" :style="{ top: '0rem' }">
            <div
              class="messageItem"
              :ref="item.ref"
              v-for="(item, index) in leftList"
              :key="index"
            >
              <div class="item_user">
                <div class="content">
                  <div class="title">{{ item.hos_name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_info" v-else>暂无内容</div>
      </div>
      <!-- <div class="no_content" v-else>暂无记录</div> -->
    </div>
    <div class="btnList_bottom">
      <div class="btnList">
        <div
          class="btnItem"
          v-for="(item, index) in btnList"
          :key="index"
          :ref="item.ref"
        >
          <div class="text">{{ item.text }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSJHospitalList } from '@/api/index'
export default {
  data() {
    return {
      leftList: [],
      btnList: [
        {
          text: '重新开始',
          ref: '',
        },
        {
          text: '退出',
          ref: '',
        },
      ],
      lazyError: require('@/assets/customer.png'),
      apiBaseUrl: process.env.VUE_APP_API,
      leftNums: 0,
      btnNums: 0,
      nextNums: -1,
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '预约挂号-选择医院')
    this.$store.dispatch('index/setFocusDom', null)
  },
  mounted() {
    this.getData()
    if (sessionStorage.getItem('indexFocusHos')) {
      this.leftNums = Number(sessionStorage.getItem('indexFocusHos'))
      sessionStorage.removeItem('indexFocusHos')
    }
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.nextNums > -1) {
          if (
            this.leftNums < this.leftList.length - 1 &&
            this.leftList.length > 0
          ) {
            if (this.leftList[this.leftNums + 4]) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums += 4
              this.leftList[this.leftNums].ref = 'active'
            } else {
              if (
                this.leftNums <
                  this.leftList.length - (this.leftList.length % 4) &&
                this.leftList.length % 4 != 0
              ) {
                this.leftList[this.leftNums].ref = ''
                this.leftNums = this.leftList.length - 1
                this.leftList[this.leftNums].ref = 'active'
              } else {
                this.leftList[this.leftNums].ref = ''
                this.nextNums = -1
                this.btnList[this.btnNums].ref = 'active'
              }
            }
          } else {
            this.leftList[this.leftNums].ref = ''
            this.nextNums = -1
            this.btnList[this.btnNums].ref = 'active'
          }
        }

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.nextNums == -1 && this.leftList.length > 0) {
          this.btnList[this.btnNums].ref = ''
          this.nextNums = this.leftNums
          this.leftList[this.leftNums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else if (this.nextNums > -1) {
          if (this.leftNums > 0 && this.leftNums - 4 >= 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums -= 4
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      left: () => {
        if (this.nextNums > -1) {
          if (this.leftNums % 4 != 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        } else if (this.nextNums == -1) {
          if (this.btnNums > 0) {
            this.btnList[this.btnNums].ref = ''
            this.btnNums--
            this.btnList[this.btnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      right: () => {
        if (this.nextNums > -1) {
          if (this.leftNums < this.leftList.length - 1) {
            if (this.leftNums % 4 != 3) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums++
              this.leftList[this.leftNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          }
        } else if (this.nextNums == -1) {
          if (this.btnNums < this.btnList.length - 1) {
            this.btnList[this.btnNums].ref = ''
            this.btnNums++
            this.btnList[this.btnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      enter: () => {
        if (this.nextNums > -1) {
          sessionStorage.setItem('indexFocusHos', this.leftNums)
          this.$router.push({
            path: './department_sj',
            query: {
              hos_id: this.leftList[this.leftNums].id,
              hos_name: this.leftList[this.leftNums].hos_name,
              hos_org_code: this.leftList[this.leftNums].hos_org_code,
            },
          })
        } else if (this.nextNums == -1) {
          if (this.btnNums == 0) {
            this.btnList[this.btnNums].ref = ''
            this.leftNums = 0
            this.nextNums = this.leftNums
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          } else if (this.btnNums == 1) {
            history.go(-1)
          }
        }
      },
      esc: () => {
        //   if (this.dialogVisible) {
        //     this.popupBtnList[this.popupBtnNums].ref = ''
        //     this.leftList[this.leftNums].ref = 'active'
        //     this.dialogVisible = false
        //     return
        //   }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    getData() {
      getSJHospitalList({
        hos_name: '',
        page: 1,
        limit: 50,
      }).then(
        (res) => {
          if (res.code == 200) {
            let list = JSON.parse(JSON.stringify(res.data.data))

            // const copies = 15;
            // let list = JSON.parse(JSON.stringify([].concat(...Array(copies).fill(res.data))))
            list.map((item) => {
              item.ref = ''
            })
            this.leftList = list
            if (this.leftList.length > 0) {
              this.nextNums = this.leftNums
              this.leftList[this.leftNums].ref = 'active'
            } else {
              this.nextNums = -1
              this.btnList[this.btnNums].ref = 'active'
            }
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        },
        (err) => {
          if (err.response) {
            this.nextNums = -1
            this.btnList[this.btnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      )
    },
  },
}
</script>

<style lang="less" scoped>
.hospital {
  width: 16.6rem;
  position: relative;
  .container {
    width: 100%;
    height: 6rem;
    overflow: hidden;
    display: flex;
    // grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
    // gap: 0.16rem;
    font-size: 0.2rem;
    color: #e7e7ef;
    .left {
      width: 100%;
      height: 6rem;
      margin-left: 0rem;
      //padding-left: 0.1rem;
      //margin-left: 0.05rem;

      .messageCenterList {
        width: 100%;
        height: 100%;
        position: relative;
        display: inline-block;
        overflow: hidden;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          left: 0.4rem;
          transition: all 0.2s;
          .messageItem {
            width: 3.8rem;
            height: 1.8rem;
            // padding: 0.5rem;
            margin-bottom: 0.3rem;
            background-size: 100% 100% !important;
            // background: #262954;
            background: url('../../assets/hospital_btn_bg.png') no-repeat;

            border-radius: 0.24rem;
            overflow: hidden;
            .item_user {
              display: flex;
              height: 100%;
              // padding: 0.15rem;
              .content {
                display: flex;
                justify-content: center;
                align-items: center;
                height: initial !important;

                .title {
                  // width: 100%;
                  // height: 1.8rem;
                  text-align: center;
                  font-size: 0.4rem;
                  font-weight: bold;
                  // margin-left: 0.2rem;
                  // margin-top: 0.1rem;
                  overflow: hidden;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  text-overflow: ellipsis;
                  -webkit-line-clamp: 2 !important;
                }
              }
            }
          }
          .messageItem:not(:nth-child(4n + 4)) {
            margin-right: 0.28rem;
          }
        }
      }
      .no_info {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        line-height: 6rem;
        width: 4rem;
        height: 7rem;
        text-align: center;
        font-size: 0.6rem;
      }
    }
  }
  .btnList_bottom {
    position: absolute;
    bottom: -1.36rem;
    left: 50%;
    transform: translateX(-50%);
    height: 1rem;
    .btnList {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 100%;
      .btnItem {
        text-align: center;
        width: 3.5rem;
        height: 1rem;
        margin-left: 0.3rem;
        font-size: 0.4rem;
        font-weight: bold;
        border-radius: 0.24rem;
        background: url('../../assets/btn_gh_click_bg.png') no-repeat;
        background-size: 100% 100% !important;
        .text {
          line-height: 1.08rem;
        }
      }
    }
  }
}
</style>
<style lang="less">
.hospital {
  .container {
    .left {
      .scroll {
        top: 2.25rem !important;
        left: 18rem !important;
      }
    }
  }
}
</style>