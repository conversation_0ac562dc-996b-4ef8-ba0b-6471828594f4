<template>
  <div class='biserialPageList'>
    <div class="serve_title" v-if="supplierInfo">· {{supplierInfo.title}} ({{supplierInfo.address}})</div>

    <div class="left_more scrollParent">
      <div class="leftList" :style="{top: '0rem'}">
        <div class="leftItem" :ref="item.ref" v-for="(item,index) in leftList" :key="index">
          <div>
            {{item.title}}
          </div>
          <div class="badge" v-if="item.badge"></div>
        </div>
      </div>
    </div>

    <div class="right_more">
      <div class="item_list scrollParent" :key="leftNums" v-if="rightList.length > 0">
        <div class="rightList" :key="leftNums" :style="{top: '0rem'}">
          <div class="friendsItem" :ref="item.ref" v-for="(item,index) in rightList" :key="index">
              <div class="item_user" :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }">
                <img @error="defImg(index)" :src="item.img" alt="">
                <div class="itemContent">
                  <div class="title">
                    {{item.title}}
                  </div>
                  <div class="updateTime">
                    {{ new Date(item.created_at).format('yyyy-MM-dd') }}
                  </div>
                </div>
              </div>
          </div>
        </div>
      </div>
      <div v-else class="no_distribution">
        暂无数据
      </div>

      <el-dialog
          :visible.sync="dialogVisible"
          :show-close="false"
          :close-on-click-modal="false"
          @opened="popupOpend"
          @close="popupClose"
      >
        <!--呼叫弹窗-->
        <div class="callMyFriend" v-if="popupModule == 1">
          <div class="myFriendsBtn" :ref="item.ref" v-for="(item,index) in popupBtnList" :key="index">
            {{ item.text}}
          </div>
        </div>

        <!--消息弹窗-->
        <div class="popupMessage" v-if="popupModule == 2">
          <div class="popupMessage" v-html="popupMessage"></div>
          <div class="popupMessageBtn" :ref="item.ref" v-for="(item,index) in popupBtnList" :key="index">
            {{ item.text}}
          </div>
        </div>


      </el-dialog>

    </div>
    <div class="notice" v-if="supplierInfo && supplierInfo.telephone">
      <img :src="require('../../assets/phone.png')" alt="">
      客服热线: {{supplierInfo.telephone}}
    </div>
  </div>
</template>

<script>

import { getBiserialPageList, getBiserialPageListContent } from "@/api/index";

export default {
  name:'biserialPageList',
  components: {
  },
  data() {
    return {
      timer: null,
      leftList:[],
      leftNums: 0,
      nextNums: -1,   // -1  说明焦点在左侧   > -1  在右侧

      rightList:[],
      rightNums: 0,

      firstShow: true,

      dialogVisible: false,
      popupModule: 1,   // 1、好友弹窗  2、消息提示
      popupMessage:'',
      popupBtnNums: 0,
      popupBtnList:[
        {
          text: '呼叫好友',
          ref:'',
          fuc: this.callMyfriend
        },
        {
          text: '删除好友',
          ref:'',
          fuc: this.delectMyfriend
        },
      ],
      bgList:[
        {
          color:'rgba(89,167,216,1)',
          bg: require('@/assets/1_bg_pic.png')
        },{
          color:'rgba(210,126,126,1)',
          bg: require('@/assets/2_bg_pic.png')
        },{
          color:'rgba(90,184,148,1)',
          bg: require('@/assets/3_bg_pic.png')
        },{
          color:'rgba(141,133,218,1)',
          bg: require('@/assets/4_bg_pic.png')
        },{
          color:'rgba(108,151,206,1)',
          bg: require('@/assets/5_bg_pic.png')
        },{
          color:'rgba(198,164,100,1)',
          bg: require('@/assets/6_bg_pic.png')
        },
      ],

      supplierInfo: null

    };
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle','活动掠影')
    if (sessionStorage.getItem('biserialPageListFocus')) {
      this.leftNums=Number(sessionStorage.getItem('biserialPageListFocus').split(',')[0])
      this.rightNums=Number(sessionStorage.getItem('biserialPageListFocus').split(',')[1])
      this.nextNums = this.leftNums
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.supplierInfo = JSON.parse(sessionStorage.getItem('supplierInfo'))
    this.getData()

    this.fuc.KeyboardEvents({
      down:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length-1) {
            this.popupBtnList[this.popupBtnNums].ref = ""
            this.popupBtnNums ++
            this.popupBtnList[this.popupBtnNums].ref = "active"
            this.$nextTick(()=>{
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
            })
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
            if (this.rightNums < this.rightList.length - 1) {
              if (this.rightList[this.rightNums + 2]) {
                this.rightList[this.rightNums].ref = ""
                this.rightNums += 2
                this.rightList[this.rightNums].ref = "active"
              } else {
                if (this.rightNums  < (this.rightList.length - this.rightList.length % 2)) {
                  this.rightList[this.rightNums].ref = ""
                  this.rightNums = this.rightList.length - 1
                  this.rightList[this.rightNums].ref = "active"
                }
              }
          }
        }
        // 左侧
        else {
          if (this.leftNums < this.leftList.length -1) {
            this.rightNums = 0
            this.rightList = []
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ""
            this.leftNums ++
            this.leftList[this.leftNums].ref = "active"
            this.$nextTick(()=>{
              this.$refs.active[0].classList.add('select')
            })
            if (this.leftList[this.leftNums].fuc) {
              this.leftList[this.leftNums].fuc(this.leftList[this.leftNums])
            }
          }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      up:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ""
            this.popupBtnNums --
            this.popupBtnList[this.popupBtnNums].ref = "active"
            this.$nextTick(()=>{
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
            })
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
            if (this.rightList[this.rightNums - 2]) {
              this.rightList[this.rightNums].ref = ""
              this.rightNums -= 2
              this.rightList[this.rightNums].ref = "active"
            }
        }
        // 左侧
        else {
          if (this.leftNums > 0) {
            this.rightNums = 0
            this.rightList = []
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ""
            this.leftNums --
            this.leftList[this.leftNums].ref = "active"
            this.$nextTick(()=>{
              this.$refs.active[0].classList.add('select')
            })
            if (this.leftList[this.leftNums].fuc) {
              this.leftList[this.leftNums].fuc(this.leftList[this.leftNums])
            }
          }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      left:()=>{
        if (this.dialogVisible) {
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          if(this.rightNums % 2 == 0){
            this.rightList[this.rightNums].ref = ""
            this.leftList[this.nextNums].ref = "active"
            this.nextNums = -1
            // this.rightNums = 0
          } else {
            this.rightList[this.rightNums].ref = ""
            this.rightNums --
            this.rightList[this.rightNums].ref = "active"
          }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      right:()=>{
        if (this.dialogVisible) {
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1) {
            if (this.rightNums % 2 != 1) {
              this.rightList[this.rightNums].ref = ""
              this.rightNums ++
              this.rightList[this.rightNums].ref = "active"
            }
          }
        } else
            // 在左侧
        if (this.nextNums == -1 && this.rightList.length > 0) {
          this.leftList[this.leftNums].ref = ""
          this.nextNums = this.leftNums
          this.rightList[this.rightNums].ref = "active"
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      enter:()=>{
        if (this.dialogVisible) {  // 弹窗
          return
        }
        if (this.nextNums > -1 && this.rightList.length > 0) {
            sessionStorage.setItem('biserialPageListFocus',this.leftNums + ',' + this.rightNums)
            this.$nextTick(()=>{
                this.$router.push({
                  path: './biserialPageInfo?id=' + this.rightList[this.rightNums].id
                })
            })
        }
      },
      esc:()=>{
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ""
          this.rightList[this.rightNums].ref = "active"
          this.dialogVisible = false
          return
        }
        sessionStorage.removeItem('biserialPageListFocus')
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      }
    })
  },
  methods: {
    defImg(index){
      let img = event.srcElement;
      img.src = require("../../assets/yaowu.png");
      img.onerror = null; //防止闪图
    },
    popupClose() {
      if (this.leftNums == 0) {
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0]);
          document.getElementById('focus_border').style.borderColor = "#fff"
          this.popupBtnNums = 0
          this.popupModule = 1
        })
      }
    },
    popupOpend() {
      this.$nextTick(()=>{
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0]);
        document.getElementById('focus_border').style.borderColor = "#00CCFF"
      })
    },

    getData(){
      this.$store.dispatch('index/setFocusDom',this.$refs.active);
      getBiserialPageList({
        fk_huodonglueying_service_org_id: this.supplierInfo.fk_supplier_id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        home_id: this.$store.getters.getUserInfo.home_id,
      })
      .then(res=>{
        if (res.code == 200) {
          let leftICon = res.data
          leftICon.map((item,index)=>{
            item.ref = ""
            item.fuc = this.getRightList
          })
          leftICon[this.leftNums].ref = "active"
          this.leftList = leftICon


          this.$nextTick(()=>{
            if (!sessionStorage.getItem('biserialPageListFocus')) {
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
            }
            this.$refs.active[0].classList.add('select')
            this.leftList[this.leftNums].fuc(this.leftList[this.leftNums])
          })

        }
      })
    },
    // 右侧数据
    getRightList(item) {
      this.$store.dispatch('app/setLoadingState', true)
      this.rightList = []
      getBiserialPageListContent({
        fk_huodonglueying_service_org_id: this.supplierInfo.fk_supplier_id,
        fk_huodonglueying_service_org_class_id: this.leftList[this.leftNums].id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        home_id: this.$store.getters.getUserInfo.home_id
      })
      .then(res=>{
        this.$store.dispatch('app/setLoadingState', false)
        if (res.code == 200) {
          let rightList =  JSON.parse(JSON.stringify(res.data))

          rightList.map(item=>{
            item.ref = ""
            if (item.img) {
              item.img = item.img.indexOf('http') > -1 ? item.img : process.env.VUE_APP_API + item.img
            }
          })

          this.rightList = rightList




          if (this.firstShow && this.rightList.length > 0) {
            if (sessionStorage.getItem('biserialPageListFocus')) {
              this.nextNums = this.leftNums
              this.leftList[this.leftNums].ref = ""
              this.rightList[this.rightNums].ref = "active"
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active);
                this.fuc.setScroll()
              })
              sessionStorage.removeItem('biserialPageListFocus')
            } else {
              // this.rightNums = 0
              // this.nextNums = -1
              this.nextNums = this.leftNums
              this.leftList[this.leftNums].ref = ""
              this.rightList[this.rightNums].ref = "active"
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active);
                this.fuc.setScroll()
              })
            }
            this.firstShow = false
          }

          this.$nextTick(()=>{
            this.fuc.setScroll()
          })
        }
      })
      .catch(err=>{
        this.$store.dispatch('app/setLoadingState', false)
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      })


    }
  },
  destroyed() {
  },
  beforeDestory() {
  },
}
</script>
<style lang='less' scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
.biserialPageList {
  height: 7rem;
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
  gap: 0.16rem;
  font-size: 0.2rem;
  color: #E7E7EF;
  .serve_title {
    font-size: 0.36rem;
    width: 68%;
    font-weight: bold;
    letter-spacing: 0.03rem;
    color: #B5C0FF;
    position: absolute;
    top: 1.6rem;
  }
  .notice {
    position: absolute;
    font-size: 0.28rem;
    right: 1.2rem;
    bottom: 0.6rem;
    color: #B2BAEF;
    display: flex;
    flex-direction: row;
    align-items: center;
    img {
      position: relative;
      top: 0.02rem;
      left: -0.05rem;
      width: 0.32rem;
      height: 0.32rem;
    }
  }
  .left_more {
    width: 3.2rem;
    border-radius: 0.2rem;
    height: 7rem;
    position: relative;
    .leftList {
      width: 94%;
      text-align: center;
      position: absolute;
      transition: all 0.3s;
      .leftItem {
        height: 0.82rem;
        //height: 2.195rem;
        line-height: 0.92rem;
        margin-bottom: 0.2rem;
        background: #343D74;
        border-radius: 0.2rem;
        position: relative;
        font-weight: bold;
        transition: all 0.3s;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        //font-size: 0.34rem;
        font-size: 0.38rem;
        color: #E7E7EF;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0,0,0,0.6);
        .badge {
          width: 0.15rem;
          height: 0.15rem;
          border-radius: 50%;
          background: #FF5F5F;
          position: absolute;
          top: 50%;
          right: 0.2rem;
          transform: translateY(-50%);
        }
      }
      .select {
        background: #89A7FF;
        transition: all 0.3s;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(102,129,218,0.6);
      }
    }
  }
  .right_more {
    width: 12.95rem;
    height: 7rem;
    margin-left: 0;
    //padding-left: 0.1rem;
    //margin-left: 0.05rem;
    .no_distribution {
      width: 100%;
      height: 100%;
      font-size: 0.4rem;
      text-align: center;
      line-height: 6rem;
      color: #B5C0FF;
    }
    //.item_title {
    //  height: 0.6rem;
    //  line-height: 0.6rem;
    //  background: #ccc;
    //  font-size: 0.28rem;
    //  font-weight: bold;
    //  color: #000;
    //  padding-left: 0.2rem;
    //}
    .item_list {
      height: 100%;
      border-radius: 0.24rem;
      overflow: hidden;
      position: relative;
      .rightList {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
        //gap: 0.16rem;
        gap: 0;
        position: absolute;
        top: 0;
        transition: all 0.2s;
        .friendsItem {
          border-radius: 0.24rem;
          height: 3.3603rem;
          overflow: hidden;
          color: #000;
          font-weight: bold;
          position: relative;
          margin-bottom: 0.28rem;
          margin-right: 0.32rem;
          background-size: 100% 100% !important;

          .typeIcon {
            display: block;
            width: 0.75rem;
            height: 0.6rem;
            position: absolute;
            left: 0.5rem;
            top: 0.46rem;
          }
          .typeIcon_phone {
            top: 0.35rem;
            width: 0.42rem;
            height: 0.7rem;
          }
          .item_user {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            background: #262954;
            img {
              display: block;
              width: 2.45rem;
              height: 2.7rem;
              margin-left: 0.3rem;
            }
            .itemContent {
              width: 2.65rem;
              height: 2.7rem;
              margin-left: 0.25rem;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              .title {
                height: 1.62rem !important;
                font-size: 0.36rem;
                padding-top: 0.2rem;
                -webkit-line-clamp: 4;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                text-indent: 0.02rem ;
              }
              .updateTime {
                width: 100%;
                font-size: 0.3rem;
                text-align: right;
              }
            }
            div {
              line-height: 0.4rem;
              color: #E7E7EF;
              //color: #888;
            }
            div:nth-child(1) {
              font-size: 0.45rem;
              letter-spacing: 0.05rem;
              text-indent: -0.05rem;
            }
            div:nth-child(2) {
              font-size: 0.3rem;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}

</style>
<style lang="less">
.biserialPageList {
  .el-dialog {
    width: fit-content;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.16rem;
    .el-dialog__header {
      display: none;
    }
    .el-dialog__body {
      .callMyFriend {
        .myFriendsBtn {
          width: 20vw;
          height: 0.6rem;
          line-height: 0.6rem;
          background: #00CCFF;
          color: #fff;
          border-radius: 0.16rem;
          margin-bottom: 0.2rem;
          text-align: center;
          font-weight: bold;
          font-size: 0.24rem;
        }
        .myFriendsBtn:last-child {
          margin-bottom: 0;
        }
      }
      .popupMessage {
        .popupMessage {
          line-height: 0.4rem;
          text-align: center;
          font-size: 0.24rem;
          margin-bottom: 0.2rem;
        }
        .popupMessageBtn {
          width: 20vw;
          height: 0.6rem;
          line-height: 0.6rem;
          background: #00CCFF;
          font-weight: bold;
          text-align: center;
          font-size: 0.24rem;
          color: #fff;
          border-radius: 0.16rem;
        }
      }


    }
  }

}


</style>
