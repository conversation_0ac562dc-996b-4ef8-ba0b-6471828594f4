<template>
  <div class="familyAlbum">
    <div class="friends" ref="refFriends">
      <img src="@/assets/care_avatar.png" alt="" />
      <div class="title">我的好友</div>
    </div>
    <div class="container">
      <div class="left">
        <div
          class="messageCenterList scrollParent"
          v-if="this.leftList.length > 0"
        >
          <div class="leftList" :style="{ top: '0rem' }">
            <div
              class="messageItem"
              :ref="item.ref"
              v-for="(item, index) in leftList"
              :key="index"
            >
              <div class="item_user">
                <div class="content">
                  <div class="pic" v-lazy-container="{ selector: 'img' }">
                    <img
                      :data-src="apiBaseUrl + item.img[0]"
                      :data-error="lazyError"
                      alt=""
                    />
                  </div>
                </div>
              </div>
              <div class="status" v-if="item.is_read === 0">
                <div>新</div>
              </div>
              <div class="number" v-if="item.img.length > 1">
                <div class="el-icon-copy-document"></div>
                <div class="num">{{ item.img.length }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_info" v-else>暂无相册</div>
      </div>
    </div>
    <div class="detail" v-if="isShow">
      <div class="pic" v-lazy-container="{ selector: 'img' }">
        <img
          :data-src="apiBaseUrl + detailList.img[imgNums]"
          :data-error="lazyError"
          alt=""
        />
      </div>
      <div class="pointer" v-if="detailList.img.length > 1">
        <div
          v-for="(item, index) in detailList.img.length"
          :key="index"
          :style="{ width: imgNums == index ? '0.2rem' : '0.08rem' }"
        ></div>
      </div>
    </div>
    <div class="top-info" v-if="isInfo">
      <div class="avatar">
        <div class="pic" v-lazy-container="{ selector: 'img' }">
          <img
            :data-src="apiBaseUrl + detailList.fk_home_avatar"
            :data-error="lazyError2"
            alt=""
          />
        </div>
        <div class="title">{{ detailList.fk_home_title }}</div>
      </div>
      <div class="describe">{{ detailList.describe }}</div>
    </div>
    <div class="bottom-info" v-if="isInfo">
      <div class="time">{{ detailList.created_at }}</div>
      <div class="tip">"左右"键切换图片，按"确认"键关闭信息</div>
    </div>
  </div>
</template>
  
  <script>
import { getFamilyAlbumList, getFamilyAlbumDetails } from '@/api/index'
export default {
  data() {
    return {
      leftList: [],
      lazyError: require('@/assets/callOut_right_bg.jpg'),
      lazyError2: require('@/assets/care_avatar.png'),
      apiBaseUrl: process.env.VUE_APP_API,
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      isShow: false,
      isInfo: false,
      detailList: {},
      imgNums: 0,
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '家庭相册')
    this.$store.dispatch('index/setFocusDom', null)
  },
  mounted() {
    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.isShow || this.isInfo) {
          return
        }
        if (this.nextNums > -1 && this.leftNums < this.leftList.length - 1) {
          if (this.leftList[this.leftNums + 3]) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums += 3
            this.leftList[this.leftNums].ref = 'active'
          } else {
            if (
              this.leftNums <
                this.leftList.length - (this.leftList.length % 3) &&
              this.leftList.length % 3 != 0
            ) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums = this.leftList.length - 1
              this.leftList[this.leftNums].ref = 'active'
            }
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else if (this.nextNums == -1) {
          if (this.leftList.length > 0) {
            this.$refs.active = []
            this.nextNums = this.leftNums
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
              sessionStorage.removeItem('friendsIndex', true)
            })
          }
        }
      },
      up: () => {
        if (this.isShow || this.isInfo) {
          return
        }
        if (this.nextNums > -1 && this.leftNums > 0 && this.leftNums - 3 >= 0) {
          this.leftList[this.leftNums].ref = ''
          this.leftNums -= 3
          this.leftList[this.leftNums].ref = 'active'
        } else {
          if (this.leftList.length > 0) {
            this.leftList[this.leftNums].ref = ''
            this.$refs.active.splice(0, 1, this.$refs.refFriends)
          } else {
            this.$refs.active = []
            this.$refs.active.push(this.$refs.refFriends)
          }
          this.nextNums = -1
          sessionStorage.setItem('friendsIndex', true)
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.isShow || this.isInfo) {
          if (this.imgNums > 0) {
            this.imgNums--
          }
          return
        }
        if (this.nextNums == -1) {
          return
        }
        if (this.leftNums % 3 != 0) {
          this.leftList[this.leftNums].ref = ''
          this.leftNums--
          this.leftList[this.leftNums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      right: () => {
        if (this.isShow || this.isInfo) {
          if (this.imgNums < this.detailList.img.length - 1) {
            this.imgNums++
          }
          return
        }
        if (this.nextNums == -1) {
          return
        }
        if (this.leftNums < this.leftList.length - 1) {
          if (this.leftNums % 3 != 2) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      enter: () => {
        if (this.isInfo) {
          this.isInfo = !this.isInfo
          return
        }
        if (this.nextNums == -1) {
          this.$nextTick(() => {
            this.$router.push({
              path: '/familyFriends',
            })
          })
        } else if (this.nextNums > -1 && this.leftList.length > 0) {
          if (!this.isShow) {
            this.getDetail().then(() => {
              if (this.detailList.img) {
                this.$store.dispatch('index/setFocusDom', null)
                this.isShow = true
              }
            })
          }
          this.isInfo = true
        }
      },
      esc: () => {
        if (this.isShow || this.isInfo) {
          this.isShow = false
          this.isInfo = false
          this.getData()
          this.imgNums = 0
          return
        }
        if (sessionStorage.getItem('friendsIndex')) {
          sessionStorage.removeItem('friendsIndex')
        }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    getData() {
      getFamilyAlbumList({
        bind_home_id: this.$store.getters.getUserInfo.home_id,
      }).then(
        (res) => {
          if (res.code == 200) {
            let list = JSON.parse(JSON.stringify(res.data.data))
            list.map((item) => {
              item.ref = ''
            })
            this.leftList = list
          }
          if (this.leftList.length > 0) {
            if (sessionStorage.getItem('friendsIndex')) {
              this.$refs.active = []
              this.$refs.active.push(this.$refs.refFriends)
              this.nextNums = -1
            } else {
              this.leftList[this.leftNums].ref = 'active'
              this.nextNums = this.leftNums
            }
          } else {
            this.$refs.active = []
            this.$refs.active.push(this.$refs.refFriends)
            this.nextNums = -1
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        },
        (err) => {
          if (err.response) {
            this.$refs.active = []
            this.$refs.active.push(this.$refs.refFriends)
            this.nextNums = -1
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      )
    },
    getDetail() {
      return getFamilyAlbumDetails({
        id: this.leftList[this.leftNums].id,
        fk_home_id: this.$store.getters.getUserInfo.home_id,
      }).then((res) => {
        if (res.code == 200) {
          this.detailList = res.data
        }
      })
    },
  },
}
</script>
  
  <style lang="less" scoped>
.familyAlbum {
  width: 16.6rem;
  position: relative;
  .friends {
    display: flex;
    // align-items: center;
    width: 2.05rem;
    height: 0.5rem;
    border-radius: 0.15rem;
    position: absolute;
    top: -1.35rem;
    right: 3rem;
    img {
      display: block;
      width: 0.5rem;
      height: 0.5rem;
      border-radius: 50%;
      background: #fff;
    }
    .title {
      width: 1.5rem;
      height: 0.5rem;
      margin-left: 0.15rem;
      font-size: 0.3rem;
      font-weight: bold;
      line-height: 0.5rem;
      color: #fff;
      letter-spacing: 0.05rem;
    }
  }
  .container {
    width: 100%;
    height: 7rem;
    overflow: hidden;
    display: flex;
    // grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
    // gap: 0.16rem;
    font-size: 0.2rem;
    color: #e7e7ef;
    .left {
      width: 100%;
      height: 7rem;
      margin-left: 0rem;
      //padding-left: 0.1rem;
      //margin-left: 0.05rem;

      .messageCenterList {
        width: 100%;
        height: 100%;
        position: relative;
        display: inline-block;
        overflow: hidden;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          left: 0.4rem;
          transition: all 0.2s;
          .messageItem {
            position: relative;
            width: 5.2rem;
            height: 3.33rem;
            // padding: 0.5rem;
            margin-bottom: 0.3rem;
            background-size: 100% 100% !important;

            border-radius: 0.3rem;
            overflow: hidden;
            .item_user {
              display: flex;
              height: 100%;
              //   padding: 0.15rem 0.35rem;
              .content {
                display: flex;
                flex-direction: column;
                height: initial !important;
                .pic {
                  width: 5.2rem;
                  height: 3.33rem;
                  img {
                    width: 100%;
                    height: 100%;
                  }
                }
              }
            }
            .status {
              right: 0rem;
              top: 0rem;
              left: inherit;

              div {
                position: absolute;
                top: 0rem;
                right: 0rem;
                width: 1.2rem;
                height: 0.35rem;
                background: #f7251c;
                border-radius: 0.02rem;
                text-align: center;
                line-height: 0.35rem;
                font-weight: bold;
                font-size: 0.25rem;
                color: #fff;
                letter-spacing: 0.03rem;
              }
            }
            .number {
              right: 0rem;
              top: 0rem;
              left: inherit;
              .el-icon-copy-document,
              .num {
                position: absolute;
                bottom: 0.2rem;
                width: 1.2rem;
                height: 0.35rem;
                border-radius: 0.15rem;
                text-align: center;
                line-height: 0.35rem;
                font-weight: bold;
                font-size: 0.3rem;
                color: #fff;
                letter-spacing: 0.03rem;
              }
              .num {
                right: -0.2rem;
                padding: 0.05rem;
              }
              .el-icon-copy-document {
                width: 0.8rem;
                right: 0.2rem;
                padding: 0.05rem;
                margin-right: 0.08rem;
                background-color: rgba(0, 0, 0, 0.4);
                text-align: left;
              }
            }
          }
          .messageItem:not(:nth-child(3n + 3)) {
            margin-right: 0.28rem;
          }
        }
      }
      .no_info {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        line-height: 6rem;
        width: 4rem;
        height: 7rem;
        text-align: center;
        font-size: 0.6rem;
      }
    }
  }
  .detail {
    position: absolute;
    z-index: 9;
    top: -2.24rem;
    left: -1.2rem;
    background-color: #0e0f2f;
    .pic {
      width: 19.2rem;
      height: 10.8rem;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .pointer {
      // width: 4rem;
      position: absolute;
      bottom: 2rem;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9;
      div {
        width: 0.08rem;
        height: 0.08rem;
        transition: all 0.3s;
        border-radius: 0.04rem;
        background: #fff;
        display: inline-block;
        margin-right: 0.08rem;
      }
    }
  }
  .top-info {
    width: 19.2rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 99;
    position: absolute;
    top: -2.24rem;
    left: -1.2rem;
    background-color: rgba(0, 0, 0, 0.25);
    .avatar {
      display: flex;
      align-items: center;
      margin-left: 0.5rem;
      .pic {
        width: 1rem;
        height: 1rem;
        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          background: #fff;
        }
      }
      .title {
        font-size: 0.35rem;
        line-height: 0.5rem;
        color: #fff;
        letter-spacing: 0.05rem;
        margin-left: 0.2rem;
      }
    }
    .describe {
      font-size: 0.35rem;
      line-height: 0.5rem;
      color: #fff;
      letter-spacing: 0.05rem;
      margin-right: 0.5rem;
    }
  }
  .bottom-info {
    width: 19.2rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 99;
    position: absolute;
    bottom: -1.56rem;
    left: -1.2rem;
    background-color: rgba(0, 0, 0, 0.25);
    .time,
    .tip {
      font-size: 0.35rem;
      line-height: 0.5rem;
      color: #fff;
      letter-spacing: 0.05rem;
    }
    .time {
      margin-left: 0.5rem;
    }
    .tip {
      margin-right: 0.5rem;
    }
  }
}
</style>
  <style lang="less">
.familyAlbum {
  .container {
    .left {
      .scroll {
        top: 2.25rem !important;
        left: 18rem !important;
      }
    }
  }
}
</style>