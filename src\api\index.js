import axios from "./resouce";

// // 获取用户信息
// export function GetUserInfo(params) {
//     return axios.get("/api/v1/user/info", { params });
// }
//
// //修改联系方式
// export function EditUserPhone(params) {
//     return axios.post("/api/v1/user/phone", params);
// }

/***
 * 用户系统相关
 */
export function Login(params) {
    return axios.post("/api/v1/portal/login", params);
}

// 获取首页图标
export function GetIndexPageICon(params) {
    return axios.get("/api/v1/tv/index_page/top", { params });
}

// 获取更多服务图标&&供应商接口获取
export function GetIndexICon(params) {
    return axios.get("/api/v1/tv/index_page/list", { params });
}

// 获取zego 进入房间token
export function GetZegoToken(params) {
    return axios.get("/api/v1/zego/token", { params });
}

// 获取服务器当前时间
export function GetNowTime(params) {
    return axios.get("/api/v1/portal/system_time", { params });
}

// 获取用户信息
export function GetUserInfo(params) {
    return axios.get("/api/v1/tv/home_user/members", { params });
}

// 获取用户二维码
export function GetUserQR(params) {
    return axios.get("/api/v1/tv/qrcode/get", { params });
}

// 获取天气
export function GetWeather(params) {
    return axios.get("/api/v1/portal/weather_list", {params} );
}

// 右侧广告位
export function GetIndexMessage(params) {
    return axios.get("/api/v1/tv/index_page/msg", {params} );
}

// 获取订单状态及颜色
export function GetStatus(params) {
    return axios.get("/api/v1/service/get/status", {params} );
}

// 获取隐私政策
export function GetPrivacyPolicy(params) {
    return axios.get("/api/v1/privacy_policy/online", {params} );
}

export function SetPrivacyPolicy(params) {
    return axios.post("/api/v1/privacy_policy/agree", params);
}

export function GetCallTime(params) {
    return axios.get("/api/v1/call/timed/out", {params} );
}

export function GetCustomerService(params) {
    return axios.get("/api/v1/call/customer", {params} );
}









// // 供应商查询
// export function GetSupplierList(params) {
//     return axios.get("/api/v1/tv_index_page/list", {params} );
// }

/**
 * 视频通话相关
 */

// 查询好友是否占线
export function GetFriendIsCall(params) {
    return axios.get("/api/v1/call/is/call", { params });
}

// 通话记录
export function GetCallLogList(params) {
    return axios.get("/api/v1/tv/call/log_list", { params });
}

// 获取好友列表
export function GetMyfriendsList(params) {
    return axios.get("/api/v1/tv/friends/list", { params });
}

// 置顶好友
export function SetFriendTop(params) {
    return axios.post("/api/v1/tv/friends/top", params );
}

// 取消好友置顶
export function CancelFriendTop(params) {
    return axios.post("/api/v1/tv/friends/cancel_top", params );
}

// 设置免接听
export function SetFreeHands(params) {
    return axios.post("/api/v1/tv/friends/hands_free", params );
}

// 取消免接听
export function CancelFreeHands(params) {
    return axios.post("/api/v1/tv/friends/cancel_hands_free", params );
}

// 删除好友
export function DestroyFriend(params) {
    return axios.post("/api/v1/tv/friends/destroy", params );
}

// 搜索用户
export function SearchUser(params) {
    return axios.get("/api/v1/tv/home_user/info", {params} );
}

// 添加好友
export function AddFriend(params) {
    return axios.post("/api/v1/tv/friends/create", params );
}

// 通话记录查阅
export function SetLogRead(params) {
    return axios.post("/api/v1/tv/call/read", params );
}



/***
 * 叫车相关
 * @param params
 */

// 获取上次订单状态
export function GetCarLastOrder(params) {
    return axios.get("/api/v1/taxi/list_latest", { params });
}

// 立即叫车
export function sendCarOrder(params) {
    return axios.post("/api/v1/taxi/create", params );
}

// 取消叫车
export function cancelCarOrder(params) {
    return axios.post("/api/v1/taxi/cancel", params );
}

/**
 * 游戏
 */

// 游戏分数记录
export function recordGamePoints(params) {
    return axios.post("/api/v1/game/high_score_record", params );
}

//最高分查询
export function GetPointsHigh(params) {
    return axios.get("/api/v1/game/high_score_list", { params });
}

/**
 * 预约挂号
 */

// 查询医院列表
export function getHospitalList(params) {
    return axios.get("/api/v1/hospital/list", {params} );
}

// 查询科室列表
export function getDepartmentList(params) {
    return axios.get("/api/v1/hospital/keshi_list", {params} );
}

// 查询医生列表
export function getDoctorList(params) {
    return axios.get("/api/v1/hospital/keshi_doctor_list", {params} );
}

// 查询预约时间
export function getAppointmentList(params) {
    return axios.get("/api/v1/hospital/keshi_doctor_yysj", {params} );
}

// 提交预约挂号
export function submitAppointment(params) {
    return axios.post("/api/v1/hospital/yygh_commit", params );
}

// 取消预约挂号
export function cancelAppointment(params) {
    return axios.post("/api/v1/hospital/yygh_cancel", params );
}
// 根据订单查看详情
export function getDetailList(params) {
    return axios.get("/api/v1/service/detail", {params} );
}

/**
 * 专家门诊
 */
// 查询专家列表
export function getExpertList(params) {
    return axios.get("/api/v1/zhuanjiazuozhen/zhuanjia/list", {params} );
}

// 查询专家预约列表
export function getOrderList(params) {
    return axios.get("/api/v1/zhuanjiazuozhen/running/order", {params} );
}

// 查看预约详情
export function getOrderDetail(params) {
    return axios.get("/api/v1/zhuanjiazuozhen/order/detail", {params} );
}

// 查询时间列表
export function getTimeList(params) {
    return axios.get("/api/v1/zhuanjiazuozhen/time/list", {params} );
}

// 提交预约订单
export function submitOrder(params) {
    return axios.post("/api/v1/zhuanjiazuozhen/order/create", params );
}

// 取消预约订单
export function cancelOrder(params) {
    return axios.post("/api/v1/zhuanjiazuozhen/order/cancel", params );
}

/**
 * 社区买菜
 */

// 商品列表
export function getSaleList(params) {
    return axios.get("/api/v1/vegetables/sale_list", {params} );
}

// 发起订单
export function submitSaleList(params) {
    return axios.post("/api/v1/vegetables/create_order", params );
}

// 取消订单
export function cancelSaleList(params) {
    return axios.post("/api/v1/vegetables/cancel_order", params );
}


/**
 * 辅具租赁
 */

// 获取商品列表
export function getLeaseList(params) {
    return axios.get("/api/v1/gy_fjzl/get_equipment_list", {params} );
}

// 提交订单
export function submitLeaseList(params) {
    return axios.post("/api/v1/gy_fjzl/apply_equipment", params );
}

//取消订单
export function cancelLeaseList(params) {
    return axios.post("/api/v1/gy_fjzl/cancel_equipment", params );
}

/**
 *  辅具租赁index+++++++++++++++
 */

// 获取商品列表
export function getLeaseIndexList(params) {
    return axios.get("/api/v1/fjzl/list", {params} );
}

// 提交订单
export function submitLeaseIndexList(params) {
    return axios.post("/api/v1/fjzl/order/create", params );
}

//取消订单
export function cancelLeaseIndexList(params) {
    return axios.post("/api/v1/fjzl/order/cancel", params );
}

/**
 * 日常维修
 */

// 获取服务列表
export function getServiceList(params) {
    return axios.get("/api/v1/routineMaintenance/list", {params} );
}

// 取消订单
export function cancelService(params) {
    return axios.post("/api/v1/routineMaintenance/order/cancel", params );
}

// 提交订单
export function submitService(params) {
    return axios.post("/api/v1/routineMaintenance/order/create", params );
}

/**
 * 居家服务
 */

// 获取居家服务列表
export function getHomeList(params) {
    return axios.get("/api/v1/home_care/list", {params} );
}

// 申请居家服务
export function submitHome(params) {
    return axios.post("/api/v1/home_care/apply", params );
}

// 取消服务
export function cancelHome(params) {
    return axios.post("/api/v1/hmorder_cancel", params );
}

/**
 * 助购
 */
// 获取商品列表
export function getShopList(params) {
    return axios.get("/api/v1/shopping_aid/sale_list", {params} );
}

// 创建订单
export function submitPayShop(params) {
    return axios.post("/api/v1/shopping_aid/create_order", params );
}

// 取消订单
export function cancelPayShop(params) {
    return axios.post("/api/v1/shopping_aid/cancel_order", params );
}


/**
 * 政策
 */

// 信息大全列表
export function getInfoList(params) {
    return axios.get("/api/v1/portal/policy/classification_list", {params} );
}

// 信息大全详情
export function getInfoDetail(params) {
    return axios.get("/api/v1/portal/policy/title_list", {params} );
}

// 获取服务机构列表
export function getServiceAngency(params) {
    return axios.get("/api/v1/portal/policy/org/class_list", {params} );
}

// 获取服务机构详情
export function getServiceDetail(params) {
    return axios.get("/api/v1/portal/policy/org/list", {params} );
}

// 推送列表

export function getNewsList(params) {
    return axios.get("/api/v1/portal/policy/list_by_user_id", {params} );
}

// 信息详情
export function getNewsDetail(params) {
    return axios.get("/api/v1/portal/policy/content", {params} );
}

/**
 * 助餐
 */

// 获取食堂列表
export function GetDiningRoomList(params) {
    return axios.post("/api/aihuwang/getShopList", params );
}

// 获取次用户此食堂余额
export function GetUserBalance(params) {
    return axios.post("/api/aihuwang/getUserData", params );
}

// 获取当天餐别信息
export function GetFoodPeriodList(params) {
    return axios.post("/api/aihuwang/getFoodPeriodList", params );
}

// 获取每日餐品
export function GetDiningFoodList(params) {
    return axios.post("/api/aihuwang/getLiveFoodList", params );
}

// 清空购物车
export function ClearShopCart(params) {
    return axios.post("/api/aihuwang/clearShopCart", params );
}

// 创建购物车
export function CreateShopCart(params) {
    return axios.post("/api/aihuwang/shopCartAdd", params );
}

// 查询购物车
export function QueryShopCart(params) {
    return axios.post("/api/aihuwang/queryShopCart", params );
}

// 创建订单
export function CreatShopOrder(params) {
    return axios.post("/api/aihuwang/createOrderByCart", params );
}

// 余额支付
export function payH5(params) {
    return axios.post("/api/aihuwang/h5PayOrder", params );
}

// 短信支付
export function payEsim(params) {
    return axios.post("/api/aihuwang/messagePayOrder", params );
}

// 取消订单
export function cancelPayOrder(params) {
    return axios.post("/api/aihuwang/h5RefundPayOrder", params );
}

/**
 * 掌上看护
 */
// 生成二维码
export function getQrCode(params) {
    return axios.get("/api/v1/tv/handheldCare/qrcode/get", {params} );
}
// 获取看护列表
export function getCareList(params) {
    return axios.get("/api/v1/tv/handheldCare/list", {params} );
}

// 是否允许看护
export function isCareAllow(params) {
    return axios.post("/api/v1/tv/handheldCare/binding", params );
}

// 取消看护
export function cancleCare(params) {
    return axios.post("/api/v1/tv/handheldCare/destroy", params );
}

// 断开看护
export function disconnectCare(params) {
    return axios.post("/api/v1/tv/handheldCare/disconnect", params );
}

// 适老化改造
export function getShiLaoHuaList(params) {
    return axios.get("/api/v1/shilaohua/list", {params} );
}

export function sendShiLaoHuaOrder(params) {
    return axios.post("/api/v1/shilaohua/order/create", params );
}

export function cancelShiLaoHuaOrder(params) {
    return axios.post("/api/v1/shilaohua/order/cancel", params );
}

// 活动掠影
export function getBiserialPageList(params) {
    return axios.get("/api/v1/huodonglueying/service/org/class/list", {params} );
}

export function getBiserialPageListContent(params) {
    return axios.get("/api/v1/huodonglueying/content/list", {params} );
}

export function getBiserialPageItemInfo(params) {
    return axios.get("/api/v1/huodonglueying/content/detail", {params} );
}

export function youAreMyFriend (params) {
    return axios.get("/api/v1/call/be/friend", {params} );
}

/**
 * 助浴助洁
 */
// 项目查询
export function getHouseKeepingList (params) {
    return axios.get("/api/v1/jiazheng/get_service_list", {params} );
}
// 时间段查询
export function getHouseKeepingTimerList (params) {
    return axios.get("/api/v1/jiazheng/get_plan_list", {params} );
}
// 预约记录查询
export function getHouseKeepingOrder (params) {
    return axios.get("/api/v1/jiazheng/order/list", {params} );
}

// 下单
export function sendHouseKeepingOrder (params) {
    return axios.post("/api/v1/jiazheng/order/create", params );
}
// 取消订单
export function cancelHouseKeepingOrder (params) {
    return axios.post("/api/v1/jiazheng/order/cancel", params );
}

/**
 * 慢病配药
 */
// 药房资质
export function getFirmDetails (params) {
    return axios.get("/api/v1/dispensing/pharmacy/info", {params} );
}
// 我的药房 药品分类
export function getMedicalHomeList (params) {
    return axios.get("/api/v1/dispensing/class/list", {params} );
}
// 我的药房 分类药品详情
export function getMedicalHomeListInfo (params) {
    return axios.get("/api/v1/dispensing/list", {params} );
}
// 我的药房 创建订单
export function createMedicalHomeOrder (params) {
    return axios.post("/api/v1/dispensing/order/create", params );
}

// 我的药房 近期订单
export function getMedicalHomeOrderList (params) {
    return axios.get("/api/v1/dispensing/order/list/running", {params} );
}

// 我的药房 取消订单
export function cancelMedicalHomeOrder (params) {
    return axios.post("/api/v1/dispensing/order/cancel", params );
}

// 我的药箱 药品列表
export function getMedicalKitList (params) {
    return axios.get("/api/v1/dispensing/box/list", {params} );
}


// 我的药箱
export function removeMedicalKitDrug (params) {
    return axios.post("/api/v1/dispensing/box/destroy", params );
}


/**
 * 积分
 */

// 积分项目列表
export function getPointTask (params) {
    return axios.get("/api/v1/point/type/list", {params} );
}

// 剩余积分
export function getRestPoint (params) {
    return axios.get("/api/v1/point/record/get", {params} );
}

// 物品列表
export function getPointList (params) {
    return axios.get("/api/v1/point/item/list", {params} );
}

// 兑换物品
export function exchangeGoods (params) {
    return axios.post("/api/v1/point/item/exchange", params );
}

// 兑换记录
export function getRecord (params) {
    return axios.get("/api/v1/point/item/record/list", {params} );
}

// 兑换规则
export function getExchangeRule (params) {
    return axios.get("/api/v1/point/rule", {params} );
}

// 新增积分埋点
export function insertPoint (params) {
    return axios.post("/api/v1/point/record/insert", params );
}


/**
 * 活动预约 & 咨询服务
 */
export function getActiveOrgList (params) {
    return axios.get("/api/v1/huodongyuyue/service/org/class/list", {params} );
}

export function getActiveOrgInfo (params) {
    return axios.get("/api/v1/huodongyuyue/huodong/list", {params} );
}

export function getActiveOrgTime (params) {
    return axios.get("/api/v1/huodongyuyue/time/list", {params} );
}

export function creatActiveOrder (params) {
    return axios.post("/api/v1/huodongyuyue/order/create", params );
}

export function cancelActiveOrder (params) {
    return axios.post("/api/v1/huodongyuyue/order/cancel", params );
}

export function getActiveOrderList (params) {
    return axios.get("/api/v1/huodongyuyue/running/order", {params} );
}




export function getZiXunOrgList (params) {
    return axios.get("/api/v1/zixun/service/org/class/list", {params} );
}

export function getZiXunOrgInfo (params) {
    return axios.get("/api/v1/zixun/zixunshi/list", {params} );
}

export function getZiXunOrgTime (params) {
    return axios.get("/api/v1/zixun/time/list", {params} );
}

export function creatZiXunOrder (params) {
    return axios.post("/api/v1/zixun/order/create", params );
}

export function cancelZiXunOrder (params) {
    return axios.post("/api/v1/zixun/order/cancel", params );
}

export function getZiXunOrderList (params) {
    return axios.get("/api/v1/zixun/running/order", {params} );
}


/**
 * 家庭相册
 */

//获取家庭相册列表
export function getFamilyAlbumList (params) {
    return axios.get("/api/v1/family_album/list", {params} );
}

// 相册详情
export function getFamilyAlbumDetails (params) {
    return axios.get("/api/v1/family_album/details", {params} );
}

// 获取二维码

export function getFamilyAlbumQrcode (params) {
    return axios.get("/api/v1/family_album/qrcode/get", {params} );
}

// 好友列表
export function getFamilyAlbumFriends (params) {
    return axios.get("/api/v1/family_album/friends/list", {params} );
}

// 好友解绑
export function UnbindFriends (params) {
    return axios.post("/api/v1/family_album/friends/unbind", params );
}

/**
 * 预约挂号  松江
 */
// 获取医院列表
export function getSJHospitalList (params) {
    return axios.get("/api/v1/hospital_yd/get_hospital_list", {params} );
}

// 获取科室列表
export function getSJDepartmentList (params) {
    return axios.get("/api/v1/hospital_yd/get_available_medical_org", {params} );
}

// 获取医生列表
export function getSJDoctorList (params) {
    return axios.get("/api/v1/hospital_yd/get_doctor_dept_info", {params} );
}

// 获取日期
export function getSJDateList (params) {
    return axios.get("/api/v1/hospital_yd/get_fq_order_info_next7days", {params} );
}

// 获取时段
export function getSJTimeList (params) {
    return axios.get("/api/v1/hospital_yd/get_fq_order_info", {params} );
}


// 创建预约
export function createSJOrder (params) {
    return axios.post("/api/v1/hospital_yd/submit_fq_order", params );
}

// 取消预约
export function cancelSJOrder (params) {
    return axios.post("/api/v1/hospital_yd/cancel_fq_order", params );
}

// 获取订单列表
export function getSJOrderList (params) {    
    return axios.get("/api/v1/hospital_yd/order_list", {params} );
}


