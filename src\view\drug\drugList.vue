<template>
  <div class='drugList'>

    <div class="supplierContent">
      <div class="list" :style="{left: '0rem',position: drugList.length > 3 ? 'absolute' : 'relative'}" v-if="drugList.length > 0">
        <div class="listItem" :ref="item.ref" v-for="(item,index) in drugList" :key="index" :data="swiperIndex" :style="{background: 'url('+item.bg+')',
        marginRight: (drugList.length < 4 && index == drugList.length-1) ? '0rem' : '0.9rem' }">

<!--          <div :class="item.name ? 'pic' : 'picItem'" v-lazy-container="{ selector: 'img' }">-->
<!--            <img :data-src="item.icon_free"  :data-error="lazyError"  :key="index" alt="">-->

<!--          </div>-->


<!--          <div class="textContent">-->
<!--            <span>{{ item.title }}</span>-->
<!--            <span>{{ item.pinyin }}</span>-->
<!--          </div>-->
        </div>
      </div>
    </div>

    <div class="pointer" v-if="drugList.length > 3">
      <div v-for="(item,index) in drugList.length - 2"  :key="index" :style="{width: pointerIndex == index ? '0.24rem': '0.14rem' }"></div>
    </div>


  </div>
</template>

<script>

import pinyin from '@/utils/pinYin'
export default {
  name:'supplier',
  components: {
  },
  data() {
    return {
      lazyError:  require('@/assets/fubao_logo.png'),
      drugList:[
          {
            "title":"我的药房",
            "redirect":"/medicalHome",
          },
          {
            "title":"我的药箱",
            "redirect":"/medicalKit",
          }
        ],
       swiperIndex: 0,
      pointerIndex: 0,
      supplierInfo: null
      // iconList:[require('@/assets/icon_bg_1.png'),require('@/assets/icon_bg_2.png'),require('@/assets/icon_bg_3.png')]
    };
  },
  created() {
    this.supplierInfo =  JSON.parse(sessionStorage.getItem('supplierInfo'))
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', this.supplierInfo.title)
    this.getData(this.drugList)

  },
  computed: {},
  watch: {

  },
  mounted() {

    this.fuc.KeyboardEvents({
      down:()=>{

      },
      up:()=>{

      },
      left:()=>{
        if (this.swiperIndex > 0) {
          this.drugList[this.swiperIndex].ref = ""
          this.swiperIndex --
          this.drugList[this.swiperIndex].ref = "active"
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active);
            // 指示器
            const element = this.$refs.active[0]
            if (element) {
              const windowWidth = element.parentNode.parentNode.clientWidth  // 可视区域高度
              const elementWidth = Number(element.clientWidth)   // 当前元素高度
              const elementOffsetLeft = element.offsetLeft  // 当前元素距离顶端的高度
              const windowScrollLeft = element.parentNode.offsetLeft  // 页面下拉高度

              if(((elementOffsetLeft + elementWidth + windowScrollLeft < 0) || elementOffsetLeft + elementWidth + windowScrollLeft > windowWidth) &&
              element.parentNode.clientWidth > windowWidth) {
                this.pointerIndex--
              }
            }
          })
        }
      },
      right:()=>{
        if (this.swiperIndex < this.drugList.length -1 ) {
          this.drugList[this.swiperIndex].ref = ""
          this.swiperIndex ++
          this.drugList[this.swiperIndex].ref = "active"

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active);
            // 指示器
            const element = this.$refs.active[0]
            if (element) {
              const windowWidth = element.parentNode.parentNode.clientWidth  // 可视区域高度
              const elementWidth = Number(element.clientWidth)   // 当前元素高度
              const elementOffsetLeft = element.offsetLeft  // 当前元素距离顶端的高度
              const windowScrollLeft = element.parentNode.offsetLeft  // 页面下拉高度

              if(((elementOffsetLeft + elementWidth + windowScrollLeft < 0) || elementOffsetLeft + elementWidth + windowScrollLeft > windowWidth) &&
              element.parentNode.clientWidth > windowWidth) {
                this.pointerIndex++
              }
            }
          })
        }

      },
      enter:()=>{
        if (sessionStorage.getItem('redirectInfo')) {
          sessionStorage.setItem('drugListIndex',this.swiperIndex)
          this.$nextTick(()=>{
            if (this.drugList[this.swiperIndex].redirect) {
              this.$router.push({
                path: this.drugList[this.swiperIndex].redirect
              })
            }
          })
        }

      },
      esc:()=>{
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      }
    })
  },
  methods: {
    getData(list) {
      this.$store.dispatch('app/setLoadingState', true)
      if (sessionStorage.getItem('drugListIndex')) {
        this.swiperIndex = Number(sessionStorage.getItem('drugListIndex'))
        sessionStorage.removeItem('drugListIndex')
      }
      let iconName = ['yaofang','yaoxiang']
      list.map((item,index) =>{

        item.bg = require('@/assets/'+iconName[index]+'.png')
        item.ref = ""
        item.pinyin = pinyin.getPinYin(item.title,' ', true)
      })

      this.drugList[this.swiperIndex].ref = "active"

      this.$nextTick(()=>{
        this.$store.dispatch('index/setFocusDom',this.$refs.active);
      })
      setTimeout(()=>{
        this.$store.dispatch('app/setLoadingState', false)
      },100)


    }

  },
  destroyed() {

  },
  beforeDestory() {
  },
}
</script>
<style lang='less' scoped>
.drugList {
  height: 6.8rem;
  overflow: hidden;
  font-size: 0.2rem;
  color: #E7E7EF;
  display: flex;
  justify-content: center;
  position: relative;
  border-radius: 0.4rem;
  .supplierContent {
    position: relative;
    width: 100%;
    height: 6rem;
    border-radius: 0.4rem;
    display: flex;
    justify-content: center;
    overflow: hidden;
    .list {
      height: 100%;
      width: max-content;
      transition: all 0.3s;
      border-radius: 0.4rem;
      .listItem {
        width: 5rem;
        height: 100%;
        border-radius: 0.4rem;
        float: left;
        background-repeat: no-repeat !important;
        background-size: 100% 100% !important;
        position: relative;
        img {
          display: block;
          width: 2.3rem;
          height: 2.3rem;
          border-radius: 50%;
          position: absolute;
          top: 0.55rem;
          left: 50%;
          transform: translateX(-50%);

          //border: 0.058rem solid #8093BF;
          box-sizing: border-box;
          padding: 0.08rem;
        }
        .textContent {
          position: absolute;
          width: 90%;
          text-align: center;
          top: 3.9rem;
          left: 50%;
          transform: translateX(-50%);
          span:first-child {
            width: 100%;
            display: inline-block;
            font-weight: bold;
            letter-spacing: 0.1rem;
            text-indent: 0.1rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 0.55rem;
            color: #5372AF;
            height: 1rem;
          }
          span:last-child {
            width: 100%;
            display: inline-block;
            font-size: 0.26rem;
            color: #9EAEC9;
            letter-spacing: 0.03rem;
          }
        }
      }
    }
  }



  .pointer {
    width: 4rem;
    position: absolute;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    div {
      width: 0.14rem;
      height: 0.14rem;
      transition: all 0.3s;
      border-radius: 0.14rem;
      background: #EEE;
      display: inline-block;
      margin-right: 0.1rem;
    }
  }

}

</style>
<style lang="less">

</style>
