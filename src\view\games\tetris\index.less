.tetri_index {
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    //display: flex;
    //flex-direction: row;
    .r {
        float: right;
    }
    .l {
        float: left;
    }
    .clear {
        clear: both;
    }
    b {
        display: block;
        width: 0.32rem;
        height: 0.32rem;
        padding: 0.02rem;
        //border: 0.02rem solid #879372;
        margin: 0 0.02rem 0.02rem 0;
        float: left;
        &:after {
            content: '';
            display: block;
            width: 0.32rem;
            height: 0.32rem;
            border-radius: 0.05rem;

            background: #344466;
            overflow: hidden;
        }
        &.c {
            border-color: #ccc;
            &:after {
                border-radius: 0.05rem;
                background: #000;
            }
        }

        &.O {  // 正方形
            border-color: #F0D139 !important;
            &:after {
                border-radius: 0.05rem;
                background: #F0D139 !important;
                box-shadow: inset -0.01rem -0.04rem 0.04rem 0px rgba(0,0,0,0.3);
            }
        }
        &.T {  // 山形块
            border-color: #E64152 !important;
            &:after {
                border-radius: 0.05rem;
                background: #E64152 !important;
                box-shadow: inset -0.01rem -0.04rem 0.04rem 0px rgba(0,0,0,0.3);
            }
        }
        &.I {  // 横杠
            border-color: #22D9D7 !important;
            &:after {
                border-radius: 0.05rem;
                background: #22D9D7 !important;
                box-shadow: inset -0.01rem -0.04rem 0.04rem 0px rgba(0,0,0,0.3);
            }
        }

        &.J {  // 右L
            border-color: #F3753A !important;
            &:after {
                border-radius: 0.05rem;
                background: #F3753A !important;
                box-shadow: inset -0.01rem -0.04rem 0.04rem 0px rgba(0,0,0,0.3);
            }
        }

        &.L {  // 右L
            border-color: #2CA3FF !important;
            &:after {
                border-radius: 0.05rem;
                background: #2CA3FF !important;
                box-shadow: inset -0.01rem -0.04rem 0.04rem 0px rgba(0,0,0,0.3);
            }
        }

        &.S {  // 正摇把
            border-color: #BA2FFA !important;
            &:after {
                border-radius: 0.05rem;
                background: #BA2FFA !important;
                box-shadow: inset -0.01rem -0.04rem 0.04rem 0px rgba(0,0,0,0.3);
            }
        }

        &.Z {  // 反摇把
            border-color: #5ECC53 !important;
            &:after {
                border-radius: 0.05rem;
                background: #5ECC53 !important;
                box-shadow: inset -0.01rem -0.04rem 0.04rem 0px rgba(0,0,0,0.3);
            }
        }

        &.d {  // 消除行
            border-radius: 0.05rem;
            border-color: #560000;
            &:after {
                background: #560000;
                box-shadow: inset -0.01rem -0.04rem 0.04rem 0px rgba(0,0,0,0.3);
            }
        }





    }
    .bg {
        //background: url('//img.alicdn.com/tps/TB1qq7kNXXXXXacXFXXXXXXXXXX-400-186.png') no-repeat;
        background: url('../../../assets/matrix_logo.png') no-repeat;
        overflow: hidden;
    }



    .rect {
        width: 4.25rem;
        height: 100%;
        //margin: 0 auto;
        position: relative;
        &.drop {
            -webkit-transform: translateY(5px);
            transform: translateY(5px);
        }
    }

    .screen {
        width: 4.25rem;
        height: 7.85rem;
        //border: solid 0.05rem;
        border-color: #987f0f #fae36c #fae36c #987f0f;
        margin: 0 auto;
        position: relative;
        position: absolute;
        top: -0.6rem;
        .panel {
            width: 100%;
            height: 101%;
            margin: 0 auto;
            background: url("../../../assets/matrix_bg.png") no-repeat;
            background-size: 100% 100%;
            border-radius: 0.16rem;
            //border: 0.03rem solid #32D2D0;
            //box-sizing: border-box;
            //background: linear-gradient(#223055 85%,#35728E 100%);
        }
    }
    .leftState,.rightState {

        span {
            display: block;
            width: 80%;
            margin: 0 auto;
        }
        span:nth-child(1) {
            font-size: 0.26rem;
            height: 0.6rem;
            line-height: 0.6rem;
            text-align: center;
            font-weight: 600;
            letter-spacing: 0.05rem;
            color: #1DE9F4;
        }
        span:nth-child(2) {
            font-size: 0.5rem;
            height: 0.6rem;
            line-height: 0.6rem;
            text-align: center;
            font-weight: 600;
            white-space: nowrap; /* 防止换行 */
            overflow: hidden; /* 溢出隐藏 */
            text-overflow: ellipsis; /* 使用省略号表示溢出 */
        }
    }
    .leftState {
        width: 3.04rem;
        margin-top: -0.6rem;
        margin-right: 0.35rem;
        div {
            width: 100%;
            height: 1.58rem;
            margin-bottom: 0.35rem;
            border-radius: 0.16rem;
            background: url('../../../assets/matrix_state.png') no-repeat;
            background-size: 100% 100%;
        }
        //height: 1.58rem;
    }

    .rightState {
        width: 3.04rem;
        margin-top: -0.6rem;
        margin-left: 0.35rem;
        .nextMatrix {
            background: url('../../../assets/matrix_state.png') no-repeat;
            background-size: 100% 100%;
            height: 1.58rem;
            margin-bottom: 0.35rem;
            border-radius: 0.16rem;
        }
        .next {
            width: 1.52rem;
            margin: 0 auto;
        }

        p {
            line-height: 47px;
            height: 57px;
            padding: 10px 0 0;
            white-space: nowrap;
            clear: both;
            color: #000 !important;
        }
    }
    .message {
        position: absolute;
        height: 4rem;
        right: 4rem;
        bottom: 1.2rem;
        width: 2.38rem;
        padding: 0.3rem;
        //border: 0.04rem solid #5FC5DC;
        background: url("../../../assets/connectFour_state.png") no-repeat;
        background-size: 100% 100%;
        border-radius: 0.16rem;
        h2 {
            color: #1EE8F8;
            margin-bottom: 0.2rem;
        }
        p {
            font-size: 0.236rem;
            display: flex;
            margin-bottom: 0.1rem;
            span {
                display: inline-block;
            }
            span:nth-child(2) {
                margin-top: -0.04rem;
                margin-left: 0.06rem;
            }
        }
    }

    .logo {
        left: 0.15rem;
        top: 3rem;

    }
}
