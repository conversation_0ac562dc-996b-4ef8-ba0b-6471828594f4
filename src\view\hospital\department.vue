<template>
  <div class="department">
    <div class="container">
      <div class="left scrollParent">
        <div class="leftList" :style="{ top: '0rem' }">
          <div class="leftItem" :ref="item.ref" v-for="(item, index) in leftList" :key="index">
            <div v-if="item.ref != 'active'">
              {{ item.title }}
            </div>
            <div v-else-if="!item.marquee">
              {{ item.title }}
            </div>
            <marquee v-else> {{item.title}} </marquee>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="messageCenterList scrollParent" :key="leftNums">
          <div class="rightList" :style="{ top: '0rem' }">
            <div
              class="messageItem"
              :ref="item.ref"
              v-for="(item, index) in rightList"
              :key="index"
            >
              <div
                class="item_user"
                :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }"
              >
                <div class="content">
                  <div class="title">
                    {{ item.ks_name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="tab_list scrollParent"
        :key="this.unfinishList.length"
        v-if="this.unfinishList.length > 0"
      >
        <div class="list" :style="{ top: '0rem' }">
          <div
            class="unfinishList"
            :ref="item.ref"
            v-for="(item, index) in unfinishList"
            :key="index"
          >
            <div class="status" :style="{color:statusList[item.status].color}">{{ statusList[item.status].text }}</div>
            <div class="apply">
              <div class="title">申请时间</div>
              <div class="time">{{ item.created_at }}</div>
            </div>
            <div class="appoint">
              <div class="title">预约时间</div>
              <div class="time">{{ item.yy_date }} {{ item.yy_time_range }}</div>
            </div>
            <div class="depart">
              <div class="title">预约科室：</div>
              <div class="name">{{ item.ks_name }}</div>
            </div>
            <div class="doctor">
              <div class="title">预约医生：</div>
              <div class="name">{{ item.doctor_name }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="no_content" v-else>暂无预约</div>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :show-close="false"
      :close-on-click-modal="false"
      @opened="popupOpend"
      @close="popupClose"
      title="订单详情"
    >
      <!--发起预约-->
      <div class="message_box sendCall" v-if="popupModule == 1">
        <div class="message_content">
          <div class="status">
            当前状态：{{ unfinishList[unfinishNums] && unfinishList[unfinishNums].status_name }}
          </div>
          <div class="apply">
            申请时间：{{ unfinishList[unfinishNums] && unfinishList[unfinishNums].created_at }}
          </div>
          <div class="time">
            预约时间：{{ unfinishList[unfinishNums] && unfinishList[unfinishNums].yy_date }}
            {{ unfinishList[unfinishNums] && unfinishList[unfinishNums].morning_afternoon_name }}
            {{ unfinishList[unfinishNums] && unfinishList[unfinishNums].yy_time_range }}
          </div>
          <div class="name">
            患者姓名：{{ unfinishList[unfinishNums] && unfinishList[unfinishNums].patient_name }}
          </div>
          <div class="hospital">预约医院：{{ hospital_name }}</div>
          <div class="department">
            预约科室：{{ unfinishList[unfinishNums] && unfinishList[unfinishNums].ks_name }}
          </div>
          <div class="doctor">
            预约医生：{{ unfinishList[unfinishNums] && unfinishList[unfinishNums].doctor_name }}
          </div>
          <div class="fee">
            挂号费用：{{ unfinishList[unfinishNums] && unfinishList[unfinishNums].gh_fee }}元
          </div>
        </div>
      </div>

      <!--取消预约-->
      <div class="message_box cancel" v-if="popupModule == 2">
        <div>是否要取消预约?</div>
      </div>

      <div
        class="message_box"
        style="text-align: center; font-size: 0.37rem; justify-content: center"
        v-html="popupMessage"
        v-if="popupModule == 3"
      ></div>

      <div class="popupBtnList">
        <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index">
          {{ item.text }}
        </div>
      </div>
    </el-dialog>
    <div class="address">
      <div class="name">{{ hospital_name }}({{ hospital_addr }})</div>
    </div>
    <div class="done"><div class="item">服务记录</div></div>
  </div>
</template>
  
  <script>
import { getDepartmentList, cancelAppointment } from '@/api/index'

export default {
  name: 'department',
  components: {},
  data() {
    return {
      hospital_name: '', // 医院名称
      hospital_addr: '', //医院地址
      patient_name: '',
      patient: '',
      leftList: [],
      rightList: [],
      unfinishList: [],
      statusList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧  -2在最右侧
      rightNums: 0,
      unfinishNums: 0,
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      messageList: [],
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '预约挂号')
  },
  computed: {},
  watch: {},
  mounted() {
    this.statusList = JSON.parse(localStorage.getItem('statusList'))

    this.hospital_name = this.$route.query.title
    this.hospital_addr = this.$route.query.addr
    this.patient =
      this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].name
    this.getData()
    if (sessionStorage.getItem('indexFocusDOne') && sessionStorage.getItem('indexFocusDTwo')) {
      this.leftNums = Number(sessionStorage.getItem('indexFocusDOne'))
      this.rightNums = Number(sessionStorage.getItem('indexFocusDTwo'))
      sessionStorage.removeItem('indexFocusDOne')
      sessionStorage.removeItem('indexFocusDTwo')
    }
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1) {
            if (this.rightList[this.rightNums + 2]) {
              this.rightList[this.rightNums].ref = ''
              this.rightNums += 2
              this.rightList[this.rightNums].ref = 'active'
            } else {
              if (
                this.rightNums < this.rightList.length - (this.rightList.length % 2) &&
                this.rightList.length % 2 != 0
              ) {
                this.rightList[this.rightNums].ref = ''
                this.rightNums = this.rightList.length - 1
                this.rightList[this.rightNums].ref = 'active'
              }
            }
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 在最右侧
        else if (this.nextNums == -2) {
          if (this.unfinishNums < this.unfinishList.length - 1) {
            this.unfinishList[this.unfinishNums].ref = ''
            this.unfinishNums++
            this.unfinishList[this.unfinishNums].ref = 'active'
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 左侧
        else {
          if (this.leftNums < this.leftList.length - 1) {
            this.rightNums = 0
            this.rightList = []
            this.input = ''
            this.$refs.active[0].classList.remove('select')
            this.$store.dispatch('app/setViewAreaOffsetTop', 0)
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            let list = JSON.parse(JSON.stringify(this.messageList[this.leftNums].children))
            list.map((item) => {
              item.ref = ''
            })
            this.rightList.push(...list)

            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$refs.active[0].classList.add('select')
            })
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums > 0 && this.rightNums - 2 >= 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums -= 2
            this.rightList[this.rightNums].ref = 'active'
          }
          // console.log(1212,this.rightList[this.rightNums]);
        }
        // 在最右侧
        else if (this.nextNums == -2) {
          if (this.unfinishNums > 0 && this.unfinishNums - 1 >= 0) {
            this.unfinishList[this.unfinishNums].ref = ''
            this.unfinishNums--
            this.unfinishList[this.unfinishNums].ref = 'active'
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 左侧
        else {
          if (this.leftNums > 0) {
            this.rightNums = 0
            this.$store.dispatch('app/setViewAreaOffsetTop', 0)
            this.rightList = []
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            let list = JSON.parse(JSON.stringify(this.messageList[this.leftNums].children))
            list.map((item) => {
              item.ref = ''
            })
            this.rightList.push(...list)
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$refs.active[0].classList.add('select')
            })
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          if (this.rightNums % 2 != 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums--
            this.rightList[this.rightNums].ref = 'active'
            // console.log(12112,this.rightList[this.rightNums]);
          } else {
            this.rightList[this.rightNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.nextNums = -1
          }
        }
        // 在最右侧
        else if (this.nextNums == -2) {
          this.unfinishList[this.unfinishNums].ref = ''
          this.rightList[this.rightNums].ref = 'active'
          this.nextNums = this.rightNums
          // this.unfinishNums = 0
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          if (this.rightNums >= 0 && this.rightNums < this.rightList.length - 1) {
            if (this.rightNums % 2 != 1) {
              this.rightList[this.rightNums].ref = ''
              this.rightNums++
              this.rightList[this.rightNums].ref = 'active'
            } else {
              this.rightList[this.rightNums].ref = ''
              this.unfinishList[this.unfinishNums].ref = 'active'
              this.nextNums = -2
            }
          } else if (this.rightNums == this.rightList.length - 1) {
            this.rightList[this.rightNums].ref = ''
            this.unfinishList[this.unfinishNums].ref = 'active'
            this.nextNums = -2
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
            // console.log(11, this.$refs.active)
          })
        }

        // 在左侧
        else if (this.nextNums == -1) {
          if (this.rightList.length > 0) {
            this.leftList[this.leftNums].ref = ''
            this.nextNums = this.leftNums
            this.rightList[this.rightNums].ref = 'active'
          }
          // console.log(1212,this.rightList[this.rightNums].ks_bh);

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        if (this.nextNums > -1 && this.rightList.length > 0) {
          if (this.leftList[this.leftNums] && this.rightList[this.rightNums]) {
            sessionStorage.setItem('indexFocusDOne', this.leftNums)
            sessionStorage.setItem('indexFocusDTwo', this.rightNums)
            this.$nextTick(() => {
              this.$router.push({
                path: '/doctor',
                query: {
                  ksbh: this.rightList[this.rightNums].ks_bh,
                  hospital_id: this.$route.query.id,
                  title: this.$route.query.title,
                  addr: this.$route.query.addr,
                  ks_name: this.rightList[this.rightNums].ks_name,
                  patient: this.patient,
                },
              })
            })
          }
        } else if (this.nextNums == -2 && this.unfinishList.length > 0) {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.unfinishList[this.unfinishNums].ref = 'active'
            this.dialogVisible = false
            // console.log(123,this.popupBtnList[this.popupBtnNums].fuc(this.unfinishList[this.unfinishNums]));
            this.popupBtnList[this.popupBtnNums].fuc(this.unfinishList[this.unfinishNums])
            return
          }
          if (this.unfinishList[this.unfinishNums]) {
            if(this.unfinishList[this.unfinishNums].status == 2){

              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                },
                {
                  text: '取消订单',
                  ref: '',
                  fuc: this.cancelOrder,
                },
              ]
            }else{
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                },
              ]
            }
            this.dialogVisible = true
            this.popupModule = 1
            this.unfinishList[this.unfinishNums].ref = ''
            this.popupBtnList[this.popupBtnNums].ref = 'active'
          }
        }
      },
      esc: () => {
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.unfinishList[this.unfinishNums].ref = 'active'
          this.dialogVisible = false
          return
        }
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        this.popupBtnNums = 0
        this.popupModule = 1
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#b36371'
      })
    },
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState',true)
      // let leftICon = []
      getDepartmentList({
        mac: sessionStorage.getItem('MAC'),
        card_id:
          this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex]
            .card_id_md5,
        hospital_id: this.$route.query.id,
      }).then((res) => {
        this.$store.dispatch('app/setLoadingState',false)
        if (res.code == 200) {
          let leftIConList = JSON.parse(JSON.stringify(res.data.keshi_list))
          // this.leftList = leftIConList.map((item) => ({
          //   title: item.hospital_type_name,
          //   ref: '',
          // }))
          leftIConList.map((item)=>{
            item.ref= ''
            item.title= item.hospital_type_name
            item.marquee = false
            if (item.title.length > 6) {
              item.marquee = true
            }
          })

          this.leftList = leftIConList

          this.leftList[this.leftNums].ref = 'active'
          this.messageList = JSON.parse(JSON.stringify(res.data.keshi_list))
          // this.leftList = leftICon
          if (res.data.unfinish_orders) {
            let unfinishListClone = JSON.parse(JSON.stringify(res.data.unfinish_orders))
            unfinishListClone.map((item) => {
              item.ref = ''
            })
            this.unfinishList = unfinishListClone
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
            this.$refs.active[0].classList.add('select')
            // console.log(123, this.$refs)
            this.getRightList()
          })
        }
      }).catch(()=>{
        this.$store.dispatch('app/setLoadingState', false)
      })
    },

    // 科室右侧数据
    getRightList() {
      let list = JSON.parse(JSON.stringify(this.messageList[this.leftNums].children))
      list.map((item) => {
        item.ref = ''
      })
      this.rightList = list

      if (this.rightList.length > 0) {
        this.nextNums = this.leftNums
        this.leftList[this.leftNums].ref = ''
        this.rightList[this.rightNums].ref = 'active'
        // console.log(11111,this.$refs.active);
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      } else {
        this.nextNums = -1
      }
      this.$nextTick(() => {
        this.fuc.setScroll()
      })
    },
    cancelOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      cancelAppointment({
        mac: sessionStorage.getItem('MAC'),
        card_id:
          this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex]
            .card_id_md5,
        hospital_id: this.$route.query.id,
        yygh_order_id: this.unfinishList[this.unfinishNums].yygh_order_id,
      }).then(
        (res) => {
          if (res.code == 200) {
            this.popupModule = 3
            this.unfinishList[this.unfinishNums].ref = ''
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  this.unfinishList = res.data
                  // this.rightList[this.rightNums].ref = 'active'
                  // this.unfinishNums = 0
                  this.unfinishList[this.unfinishNums].ref = 'active'
                  this.nextNums = -2
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active)
                  })
                },
              },
            ]
            this.popupMessage = res.msg

            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.unfinishList[this.unfinishNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br/>取消失败!<br>'
          if (error.response && error.response.data && error.response.data.msg) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
  <style lang='less' scoped>
.department {
  .address {
    display: flex;
    // width: 7.8rem;
    position: absolute;
    top: 1.6rem;
    left: 1.45rem;
    .name {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .name::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .done {
    position: absolute;
    top: 1.6rem;
    right: 3.65rem;
    .item {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .item::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .container {
    height: 7rem;
    overflow: hidden;
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 每行显示两个元素 */
    gap: 0.16rem;
    font-size: 0.2rem;
    color: #e7e7ef;
    .noData {
      div {
        font-size: 0.5rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -200%);
        letter-spacing: 0.05rem;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
      }
    }
    .left {
      width: 3.2rem;
      border-radius: 0.2rem;
      height: 7rem;
      position: relative;
      .leftList {
        width: 94%;
        text-align: center;
        position: absolute;
        transition: all 0.3s;
        .leftItem {
          height: 0.69rem;
          line-height: 0.77rem;
          margin-bottom: 0.2rem;
          background: #343d74;
          border-radius: 0.2rem;
          position: relative;
          font-weight: bold;
          transition: all 0.3s;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          font-size: 0.34rem;
          color: #e7e7ef;
          text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
          marquee {
            width: 90%;
          }
          .badge {
            width: 0.15rem;
            height: 0.15rem;
            border-radius: 50%;
            background: #ff5f5f;
            position: absolute;
            top: 50%;
            right: 0.2rem;
            transform: translateY(-50%);
          }
        }
        .select {
          background: #89a7ff;
          transition: all 0.3s;
          text-shadow: 0.03rem 0.03rem 0.01rem rgba(102, 129, 218, 0.6);
        }
      }
      .myQR {
        padding: 0rem 0.6rem;
        .qr_img {
          width: 2.05rem;
          height: 2.05rem;
          background: #413f55;
          margin: 0 auto;
          padding: 0.06rem;
          margin-bottom: 0rem;
          border-radius: 0.18rem;
        }
        img {
          border-radius: 0.14rem;
          display: block;
          width: 100%;
          height: 100%;
        }
        div {
          width: 2.2rem;
          font-size: 0.31rem;
          font-weight: bold;
          line-height: 0.45rem;
          text-align: center;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          margin-top: 0.02rem;
          text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
        }
        div:nth-child(3) {
          margin-top: 0.05rem;
          letter-spacing: 0;
          font-weight: 500;
          font-size: 0.33rem;
        }
      }
    }
    .right {
      width: 12.95rem;
      height: 7rem;
      margin-left: 0;
      //padding-left: 0.1rem;
      //margin-left: 0.05rem;
      .item_title {
        height: 0.6rem;
        line-height: 0.6rem;
        background: #ccc;
        font-size: 0.28rem;
        font-weight: bold;
        color: #000;
        padding-left: 0.2rem;
      }
      .item_list {
        //height: calc(100% - 0.76rem);
        height: 100%;
        //background: #ccc;
        border-radius: 0.26rem;

        overflow: hidden;

        position: relative;
        .friendsList {
          width: 100%;
          display: grid;
          grid-template-columns: repeat(3, 1fr); /* 每行显示两个元素 */
          //gap: 0.16rem;
          gap: 0;
          position: absolute;
          top: 0;
          transition: all 0.2s;
          //.friendsItem:nth-child(3n + 3) {
          //  margin-right: 0;
          //}
        }
      }

      .messageCenterList {
        width: 8.5rem;
        height: 100%;
        margin-left: 0.17rem;
        position: relative;
        .rightList {
          display: flex;
          flex-wrap: wrap;
          // width: 83.6%;
          border-radius: 0.3rem;
          position: absolute;
          // left: 0.8rem;
          transition: all 0.2s;
          .messageItem {
            width: 3.2rem;
            height: 1.3rem;
            padding: 0.4rem;
            margin-bottom: 0.35rem;
            // margin-right: 0.28rem;
            background-size: 100% 100% !important;
            // background: #ccc !important;
            background: #262954;
            border-radius: 0.3rem;
            overflow: hidden;
            .item_user {
              display: flex;
              align-items: center;
              height: 100%;
              img {
                width: 2.45rem;
                height: 2.45rem;
                border-radius: 50%;
              }
              .content {
                height: initial !important;
                text-align: center;
                .title {
                  font-weight: bold;
                  color: #fff;
                  font-size: 0.36rem;
                }
              }
            }
          }
          .messageItem:nth-child(2n + 1) {
            margin-right: 0.28rem;
          }
        }
      }
    }
    .tab_list {
      width: 4.15rem;
      height: 7rem;
      position: relative;
      right: 3.9rem;
      .list {
        margin-right: 0.22rem;
        position: absolute;
        transition: all 0.3s;
        .unfinishList {
          height: 3rem;
          padding: 0.18rem 0.25rem;
          margin-bottom: 0.28rem;
          background: #262954;
          border-radius: 0.24rem;
          font-size: 0.28rem;
          font-weight: bold;
          .status {
            color: #3ce1ae;
            margin-bottom: 0.1rem;
            letter-spacing: 0.03rem;
          }
          .apply,
          .appoint,
          .depart,
          .doctor {
            margin-bottom: 0.1rem;
            .title {
              color: #b6c0fd;
              margin-right: 0.06rem;
            }
          }
          .depart,
          .doctor {
            display: flex;
          }
        }
      }
    }
    .no_content {
      position: absolute;
      right: 1.5rem;
      line-height: 7rem;
      width: 4rem;
      height: 7rem;
      text-align: center;
      font-size: 0.4rem;
    }
  }
}
</style>
<style lang="less">
.department {
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    background: repeating-linear-gradient(to right, #c98693, #a95361);
    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.5rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        align-items: center;
        .message_content {
          margin-top: 0.3rem;
          .status,
          .apply,
          .time,
          .name,
          .hospital,
          .department,
          .doctor,
          .fee {
            font-size: 0.35rem;
            font-weight: bold;
            padding-bottom: 0.12rem !important;
          }
          .time,
          .fee {
            color: #00cc00;
          }
        }
      }
      .cancel {
        flex-direction: column;
        div {
          text-align: center;
        }
        div:nth-child(2) {
          margin-top: 0.1rem;
          color: red;
          line-height: 0.48rem;
        }
        //align-items: normal !important;
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          background: repeating-linear-gradient(to right, #c98693, #a95361);
        }
      }
    }
  }
}
</style>