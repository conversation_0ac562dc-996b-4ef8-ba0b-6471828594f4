<template>
  <div class="shopping">
    <div class="container">
      <div class="left">
        <div class="leftContent scrollParent"  v-if="this.leftList.length > 0">
          <div class="leftList" :style="{ top: '0rem' }">
            <div class="messageItem" :ref="item.ref" v-for="(item, index) in leftList" :key="index">
              <div class="item_user">
                <div :class="item.name ? 'pic' : 'picItem'" v-lazy-container="{ selector: 'img' }">
                  <img :data-src="item.img" :data-error="lazyError" :key="index" alt=""  />
                </div>
                <div :class="item.name ? 'contentInfo' : 'contentInfo'">
                  <div class="title">
                    {{ item.name || item.title }}
                  </div>
                  <div class="vegetables_name">
                    {{item.fk_shopping_goods_name}}
                  </div>
                  <div class="price" v-if="item.price"><span>金额:</span>{{ item.price }}元</div>

                  <div class="payNum" v-if="item.payNums > 0">
                    已选{{item.payNums}}件
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
<!--        <div class="no_content" v-else>暂无商品列表</div>-->
      </div>
      <div class="right">
        <div class="rightContent scrollParent" ref="rightContent" v-if="this.rightList.length > 0">
          <div class="list" :style="{ top: '0rem' }">
            <div
                :class="item.name ? 'rightList' : 'rightListItem'"
                :ref="item.ref"
                v-for="(item, index) in rightList"
                :key="index"
            >
              <div class="rightListInfo">
                <div class="status" :style="{color: statusList[item.status].color}">
                  {{ statusList[item.status].text }}
                </div>
                <div class="good">
                  <div class="title">商品数量:</div>
                  <div class="time">{{ item.counts }}</div>
                </div>
                <div class="price">
                  <div class="title">商品价格:</div>
                  <div class="time">{{ item.total_price }}元
                  </div>
                </div>
                <div class="pay">
                  <div class="title">付款方式:</div>
                  <div class="name">
                    {{
                      item.order_type === 1
                          ? '货到付款（送货上门）'
                          : item.order_type === 2
                          ? '在线支付（送货上门）'
                          : '在线支付（到店自提）'
                    }}
                  </div>
                </div>
                <div class="time">
                  <div class="title">申请时间:</div>
                  <div class="name">{{ item.created_at }}</div>
                </div>
              </div>

            </div>
          </div>
        </div>
        <div class="no_info" v-else>暂无近期记录</div>

<!--        ;-->
        <div class="sendOrder" :style="{backgroundImage: canSend ? 'linear-gradient(to right, rgb(97, 126, 182), rgb(75, 104, 164))' : '',opacity: canSend ? 1 : 0.6}">
          <div class="sendOrderBtn" ref="sendOrder">
            预约申请
          </div>
        </div>

      </div>

<!--  二次确认弹窗-->
      <el-dialog
        :class="popupModule == 4 ? 'popup_info_box' : 'popup_box'"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        :title="this.supplierInfo.title"
        ref="popupBox"
      >
        <!--发起预约-->
        <div class="message_box sendCall scrollParent" v-if="popupModule == 1">
            <div class="message_content" ref="popupRef" :style="{top: '0rem'}">
              <div class="message_item" v-for="(item,index) in sendData" :key="index" >
                  <div class="itemType">
                    <span class="type">商品{{index + 1}}:</span>
                    <span class="value">{{item.name}}</span>
                  </div>
                  <div class="itemValue">
                    <span>数量:{{item.payNums}}{{item.fk_vegetables_unit_name}} </span>
                    <span>单价:{{item.price}}元</span>
                  </div>
              </div>
            </div>

        </div>

        <!--取消预约-->
        <div class="message_box cancel scrollParent" v-if="popupModule == 2">
          <div class="message_content" ref="popupRef" :style="{top: '0rem'}">
            <div class="message_item">
              <div class="type">
                当前状态:
              </div>
              <div class="value">
                {{ this.rightList[this.rightNums].status_name }}
              </div>
            </div>
            <div class="message_item">
              <div class="type">付款方式:</div>
              <div class="value">
                {{
                  this.rightList[this.rightNums].order_type === 1
                      ? '货到付款（送货上门）'
                      : this.rightList[this.rightNums].order_type === 2
                      ? '在线支付（送货上门）'
                      : '在线支付（到店自提）'
                }}
              </div>
            </div>

            <div class="message_item">
              <div class="type">申请时间:</div>
              <div class="value">{{ this.rightList[this.rightNums].created_at }}</div>
            </div>
            <div class="message_item">
              <div class="type">商家名称:</div>
              <div class="value">{{ this.rightList[this.rightNums].fk_supplier_name }}</div>
            </div>
            <div class="message_item">
              <div class="type">商家电话:</div>
              <div class="value">{{ this.rightList[this.rightNums].telephone }}</div>
            </div>

            <div class="orderInfo">
              <div class="orderItem" v-for="(item,index) in rightList[this.rightNums].children" :key="index">
                <div>
                  <span>商品{{index+1}}:</span>
                  <span>{{item.name}}</span>
                </div>
                <div>
                  <span>数量:{{item.amount}}份 </span>
                  <span>价格:{{item.price}}</span>
                </div>
              </div>

            </div>

          </div>
        </div>

        <div class="message_box result" style="text-align: center; font-size: 0.37rem; justify-content: center" v-html="popupMessage" v-if="popupModule == 3"></div>

        <div class="message_box info" v-if="popupModule == 4">
          <div class="title"><span>你的预约</span><span>已经发送成功！</span></div>
          <div class="phone">请在手机号为{{   rightList[0].user_phone.substring(0,3) + '****' + rightList[0].user_phone.substring(7,11)  }}设备上完成付款。</div>
          <div class="tip">您可以在“预约记录”查看预约信息。</div>
        </div>

        <div class="popupBtnList">
          <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index"> {{ item.text }}</div>
        </div>
      </el-dialog>
<!--  选择付款方式弹窗/选择件数-->
      <el-dialog
        :class="selectPopupModule ? 'select_pay_nums' : 'select_pay_box'"
        :visible.sync="selectDialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="selectOpend"
        @close="selectClose"
        :title="selectPopupModule ? '请选择件数' : '选择付款方式'"
      >
        <div class="selectBtnList">
          <div class="btn" :ref="item.ref" v-for="(item, index) in selectBtnList" :key="index">
            <div class="name">
              <span>{{ item.name }}</span>
            </div>
            <div class="text">
              <span>{{ item.text }}</span>
            </div>
          </div>
        </div>

      </el-dialog>

    </div>
    <div class="address">
      <template>
        <div class="name">商品列表</div>
      </template>
    </div>
    <div class="tel">
      <template v-if="this.supplierInfo.telephone.length > 0"> 客服热线：{{ this.supplierInfo.telephone }} </template>
    </div>
    <div class="done"><div class="item">服务记录</div></div>
  </div>
</template>
      
<script>
import {
  getShopList,
  submitPayShop,
  cancelPayShop,
} from '@/api/index'

export default {
  name:'shopping',
  components: {},
  data() {
    return {
      lazyError: require('@/assets/icon_17.png'),
      sendData: [],
      canSend: false,
      supplierInfo: null,
      leftList: [],
      rightList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      order_type: 1,
      order_detail: [],

      firstShow: true,
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],

      selectDialogVisible: false,
      selectPopupModule: false,   // false  付款方式  true 选择件数
      selectBtnNums: 0,
      selectBtnList: [],
      messageList: [],
      statusList:[]
    }
  },
  created() {
    this.supplierInfo =  JSON.parse(sessionStorage.getItem('supplierInfo'))

    //设置页面左上角标题
    this.$nextTick(() => {
        this.$store.dispatch('index/setMainTitle', this.supplierInfo.title)
    })

  },
  computed: {},
  watch: {},
  mounted() {
    if (localStorage.getItem('statusList')) {
      this.statusList = JSON.parse(localStorage.getItem('statusList'))
    }
    if (sessionStorage.getItem('indexFocusService')) {
      this.leftNums = Number(sessionStorage.getItem('indexFocusService'))
      sessionStorage.removeItem('indexFocusService')
    }
    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if ((Math.abs(styleTop) +scrollPageTop)  * fontSize > this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight) {
                this.$refs.popupRef.style.top = styleTop - (scrollTop/fontSize - Math.abs(styleTop)) + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * ((scrollTop/fontSize - Math.abs(styleTop)) * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight)) + 'px'
                return
              }
              if (scrollTop > 0) {
                  this.$refs.popupRef.style.top = styleTop - scrollPageTop + 'rem'
                  scrollBar.style.top = barTop + (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))   + 'px'
              }
            }
          }
          return
        }
        if (this.selectDialogVisible && this.selectPopupModule) {
          if (this.selectBtnNums < 2) {
            this.selectBtnList[this.selectBtnNums].ref = ''
            this.selectBtnNums += 2
            this.selectBtnList[this.selectBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1 && this.rightList[this.rightNums] && this.rightList[this.rightNums].ref) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums++
            this.rightList[this.rightNums].ref = 'active'

            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          else {
            if (this.rightList[this.rightNums]) {
              this.rightList[this.rightNums].ref = ""
            }
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.sendOrder)
            })
            return
          }

        }
        //左侧
        else {
            this.leftList[this.leftNums].ref = ''
            if (this.leftList[this.leftNums + 2]) {
              this.leftNums = this.leftNums + 2
            } else {
              if (this.leftNums % 2 == 1) {
                this.leftNums = this.leftList.length - 1
              }
            }
            this.leftList[this.leftNums].ref = 'active'
            // let { name, fk_vegetables_name, price, combination_id } = this.leftList[this.leftNums]
            // this.name = name
            // this.vegetables_name = fk_vegetables_name
            // this.price = price
            // this.combination_id = combination_id
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if (scrollTop > 0) {
                if ((styleTop + scrollPageTop) > 0) {
                  this.$refs.popupRef.style.top = '0rem'
                  scrollBar.style.top = '0px'
                  return
                } else {
                  this.$refs.popupRef.style.top = styleTop + scrollPageTop + 'rem'
                  scrollBar.style.top = barTop - (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))  + 'px'
                }
              }
            }
          }
          return
        }
        if (this.selectDialogVisible && this.selectPopupModule) {
          if (this.selectBtnNums > 1) {
            this.selectBtnList[this.selectBtnNums].ref = ''
            this.selectBtnNums -= 2
            this.selectBtnList[this.selectBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightList.length < 1 || !this.rightList[this.rightNums].ref) {
            if (this.rightList.length > 0) {
              this.rightList[this.rightNums].ref = "active"
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (this.rightNums > 0 && this.rightList.length - 1 >= 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums--
            this.rightList[this.rightNums].ref = 'active'


          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 左侧
        else {

          if (this.leftList[this.leftNums - 2]) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums -= 2
            this.leftList[this.leftNums].ref = 'active'
            // let { name, fk_vegetables_name, price, combination_id } = this.leftList[this.leftNums]
            // this.name = name
            // this.vegetables_name = fk_vegetables_name
            // this.price = price
            // this.combination_id = combination_id

          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        if (this.selectDialogVisible) {
          if (this.selectBtnNums > 0) {
            if (this.selectPopupModule) {
              if (this.selectBtnNums % 2 == 0) {
                return
              }
            }
            this.selectBtnList[this.selectBtnNums].ref = ''
            this.selectBtnNums--
            this.selectBtnList[this.selectBtnNums].ref = 'active'
            // this.order_type = this.selectBtnNums + 1
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        else if (this.nextNums > -1) {
          if (this.leftList.length > 0) {
            if (this.rightList.length > 0) {
              this.rightList[this.rightNums].ref = ''
            }
            this.leftList[this.leftNums].ref = 'active'
            this.nextNums = -1
          }
        } else if (this.nextNums == -1) {
          if (this.leftNums % 2 == 1) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums--
              this.leftList[this.leftNums].ref = 'active'
              // let { name, fk_vegetables_name, price, combination_id } = this.leftList[this.leftNums]
              // this.name = name
              // this.vegetables_name = fk_vegetables_name
              // this.price = price
              // this.combination_id = combination_id
            }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        if (this.selectDialogVisible) {
          if (this.selectBtnNums < this.selectBtnList.length - 1) {
            if (this.selectPopupModule) {
              if (this.selectBtnNums % 2 == 1) {
                return
              }
            }
            this.selectBtnList[this.selectBtnNums].ref = ''
            this.selectBtnNums++
            this.selectBtnList[this.selectBtnNums].ref = 'active'
            // this.order_type = this.selectBtnNums + 1
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左侧
        if (this.nextNums == -1) {
          if (this.leftNums % 2 == 1) {
            this.leftList[this.leftNums].ref = ''
            this.nextNums = this.leftNums
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.sendOrder)
            })
            return

          } else {
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            this.leftList[this.leftNums].ref = 'active'
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
          if (this.selectDialogVisible) {
            this.selectBtnList[this.selectBtnNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.selectDialogVisible = false
            this.selectBtnList[this.selectBtnNums].fuc(this.leftList[this.leftNums])
            this.canSend = false
            this.leftList.map((item,index)=>{
              if (item.payNums > 0) {
                this.canSend = true
              }
            })
            return
          }
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            // 取消订单关闭时
            if (this.popupModule == 2) {
              this.rightList[this.rightNums].ref = 'active'
            }
            this.popupBtnList[this.popupBtnNums].fuc(this.rightList[this.rightNums])
            this.dialogVisible = false
            return
          }
          // 左侧
          if (this.nextNums == -1) {
            this.leftList[this.leftNums].ref = ''
            // this.order_type = 1
            this.selectPopupModule = true
            this.selectBtnList = [
              {
                name: '1件',
                text: '',
                ref: '',
                fuc: ()=>{
                  this.leftList[this.leftNums].payNums = 1
                },
              },
              {
                name: '2件',
                text: '',
                ref: '',
                fuc: ()=>{
                  this.leftList[this.leftNums].payNums = 2
                },
              },
              {
                name: '3件',
                text: '',
                ref: '',
                fuc: ()=>{
                  this.leftList[this.leftNums].payNums = 3
                },
              },
              {
                name: '不需要',
                text: '',
                ref: '',
                fuc: ()=>{
                  this.leftList[this.leftNums].payNums = 0
                },
              },
            ]
            this.selectBtnNums = (this.leftList[this.leftNums].payNums - 1) < 0 ? 0 : this.leftList[this.leftNums].payNums - 1
            this.selectDialogVisible = true
          }
          else {
            if (this.$refs.popupBox) {
              let scrollDom = this.$refs.popupBox.$el.querySelector('.scroll');
              if (scrollDom) {
                scrollDom.remove()
              }
            }
            // 取消订单二次确认弹窗
            if (this.nextNums > -1 && this.rightList[this.rightNums] && this.rightList[this.rightNums].ref) {
              this.supplierInfo.title = "订单详情"
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                }
              ]

              if ([2,5].includes(this.rightList[this.rightNums].status)) {
                this.popupBtnList.push({
                  text: '取消订单',
                  ref: '',
                  fuc: this.cancelOrder,
                })

              }
              this.dialogVisible = true
              this.popupModule = 2
              this.rightList[this.rightNums].ref = ''
              this.popupBtnList[this.popupBtnNums].ref = 'active'
            }
            // 发送订单二次确认弹窗
            else {
              this.sendData = []
              this.leftList.map(item=>{
                if (item.payNums > 0) {
                  this.sendData.push(item)
                }
              })
              this.supplierInfo.title = "请确认订单内容"
              if (!this.canSend) {
                return
              }
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                },
                {
                  text: '确认',
                  ref: '',
                  fuc: this.sendOrder,
                },
              ]
              this.dialogVisible = true
              this.popupModule = 1
              this.leftList[this.leftNums].ref = ''
              this.popupBtnList[this.popupBtnNums].ref = 'active'
            }
          }
      },
      esc: () => {
        if (this.nextNums == -1) {
          if (this.dialogVisible) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.dialogVisible = false
            return
          } else if (this.selectDialogVisible) {
            this.selectBtnList[this.selectBtnNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.selectDialogVisible = false
            return
          }
        } else
        if (this.nextNums > -1) {
          if (this.dialogVisible) {
            if (this.popupModule == 2) {
              this.rightList[this.rightNums].ref = 'active'
            }
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.dialogVisible = false
            return
          } else if (this.selectDialogVisible) {
            this.selectBtnList[this.selectBtnNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.selectDialogVisible = false
            this.nextNums = -1
            return
          }
        }
        // this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      if (this.nextNums == -1) {
        this.popupBtnList[this.popupBtnNums].ref = ''
        this.leftList[this.leftNums].ref = 'active'
      } else if (this.nextNums > -1) {
        this.popupBtnList[this.popupBtnNums].ref = ''
        if (this.rightList[this.rightNums] && this.popupModule == 2) {
          this.rightList[this.rightNums].ref = 'active'
        }
      }
      this.$nextTick(() => {
        if (this.$refs.active[0]) {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        } else {
          this.$store.dispatch('index/setFocusDom',  this.$refs.sendOrder)
        }
        if (this.selectDialogVisible) { // || this.popupModule == 2
          document.getElementById('focus_border').style.borderColor = 'rgb(0,0,0,0)'
        } else {
          document.getElementById('focus_border').style.borderColor = '#fff'
          // document.getElementById('focus_border').style.borderColor = 'transparent'
        }
        // setTimeout(()=>{
          this.popupBtnNums = 0
          this.popupModule = 1
        // },100)

      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },
    selectClose() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        this.selectBtnNums = 0
        this.popupModule = 1
      })
    },
    selectOpend() {
      if (this.selectDialogVisible) {
        this.leftList[this.leftNums].ref = ''
        if (this.rightList[this.rightNums]) {
          this.rightList[this.rightNums].ref = ''
        }
        this.selectBtnList[this.selectBtnNums].ref = 'active'
      }
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState', true)
      //获取商品列表
      getShopList({
        fk_supplier_id: JSON.parse(sessionStorage.getItem('supplierInfo')).fk_supplier_id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
      }).then((res) => {
        this.$store.dispatch('app/setLoadingState', false)
        if (res.code == 200) {
          if (res.data.sale_list) {
            let leftListClone = JSON.parse(JSON.stringify(res.data.sale_list))
            leftListClone.map((item) => {
              item.ref = ''
              item.payNums = 0
              if (item.img) {
                item.img = item.img.indexOf('http') > -1 ? item.img : process.env.VUE_APP_API + item.img
              }
            })
            this.leftList = leftListClone
          }
          if (res.data.unfinish_list) {
            let rightListClone = JSON.parse(JSON.stringify(res.data.unfinish_list))
            rightListClone.map((item) => {
              item.ref = ''
            })
            this.rightList = rightListClone
          }

          if (this.leftList.length > 0) {
            this.nextNums = -1
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          } else {
            this.nextNums = this.rightNums
            this.rightList[this.rightNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      }).catch(err=>{
        this.$store.dispatch('app/setLoadingState', false)
      })


    },
    sendOrder() {
      // this.$store.dispatch('app/setLoadingState', true)
      this.selectPopupModule = false
      this.leftList[this.leftNums].ref = ''
      // this.order_type = 1
      this.selectBtnList = [
        {
          name: '货到付款',
          text: '送货上门',
          ref: '',
          fuc: this.submitInfo,
        },
        {
          name: '在线支付',
          text: '送货上门',
          ref: '',
          fuc: this.submitInfo,
        },
        {
          name: '在线支付',
          text: '到店自提',
          ref: '',
          fuc: this.submitInfo,
        },
      ]
      this.selectBtnNums = 0
      this.selectDialogVisible = true
    },
    submitInfo() {
      this.$store.dispatch('app/setLoadingState', true)
      this.order_type = this.selectBtnNums + 1
      let obj = {
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        home_id: this.$store.getters.getUserInfo.home_id,
        order_type: this.order_type,
        device_type: 1,
        order_detail: [],
      }

      this.leftList.map((item,index)=>{
        if (item.payNums > 0) {
          let itemObj = {
            combination_id: item.combination_id,
            mount: item.payNums
          }
          obj.order_detail.push(itemObj)
        }
      })

      submitPayShop(obj).then(
        (res) => {
          if (res.code == 200) {
            if (this.$refs.rightContent) {
              let scrollDom = this.$refs.rightContent.querySelector('.scroll');
              this.$refs.rightContent.childNodes[0].style.top = "0rem"
              if (scrollDom) {
                scrollDom.remove()
              }
            }
            this.leftList.map(item=>{
              item.payNums = 0
            })
            this.canSend = false

            let rightClone = JSON.parse(JSON.stringify(res.data))
            rightClone.map((item) => {
              item.ref = ''
            })
            this.rightList = rightClone

            if (this.order_type == 1) {
              this.popupModule = 3
              this.leftList[this.leftNums].ref = ''
              this.selectBtnList[this.selectBtnNums].ref = ''
              // this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.popupBtnList = [
                {
                  text: '确认',
                  ref: 'active',
                  fuc: () => {
                    this.dialogVisible = false

                    this.leftList[this.leftNums].ref = 'active'
                    this.rightNums = 0
                    this.nextNums = -1
                    this.$nextTick(()=>{
                      this.fuc.setScroll()
                    })
                  },
                },
              ]
              this.popupMessage = '订购成功！<br/>'
              this.popupBtnNums = 0
              this.dialogVisible = true
            }
            else {
              this.popupModule = 4
              this.leftList[this.leftNums].ref = ''
              this.selectBtnList[this.selectBtnNums].ref = ''
              // this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.popupBtnList = [
                {
                  text: '确认',
                  ref: 'active',
                  fuc: () => {
                    this.dialogVisible = false
                    let rightClone = JSON.parse(JSON.stringify(res.data))
                    rightClone.map((item) => {
                      item.ref = ''
                    })
                    this.rightList = rightClone
                    this.leftList[this.leftNums].ref = 'active'
                    this.rightNums = 0
                    this.nextNums = -1
                    this.$nextTick(() => {
                      this.$store.dispatch('index/setFocusDom', this.$refs.active)
                    })
                  },
                },
              ]
              // this.popupMessage = '订购成功！<br/>'
              this.popupBtnNums = 0
              this.dialogVisible = true
            }
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.leftList[this.leftNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br/>订购失败!<br>'
          if (error.response && error.response.data && error.response.data.msg) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },
    cancelOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      cancelPayShop({
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        // order_id: Number(this.rightList[this.rightNums].order_id),
        order_no: this.rightList[this.rightNums].order_no,
      }).then(
        (res) => {
          if (res.code == 200) {
            let rightClone = JSON.parse(JSON.stringify(res.data))
            if (rightClone) {
              rightClone.map((item) => {
                item.ref = ''
              })
            } else {
              rightClone = []
            }

            this.rightList = rightClone
            if (this.$refs.rightContent) {
              let scrollDom = this.$refs.rightContent.querySelector('.scroll');
              this.$refs.rightContent.childNodes[0].style.top = "0rem"
              if (scrollDom) {
                scrollDom.remove()
              }
            }
            this.popupModule = 3
            this.leftList[this.leftNums].ref = ''
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false

                  // if (this.leftList.length > 0) {
                  //   this.leftList[this.leftNums].ref = 'active'
                  //   this.rightNums = 0
                  //   this.nextNums = -1
                  // } else {
                  //   this.rightNums = 0
                    this.rightList[this.rightNums].ref = 'active'
                  //   this.nextNums = this.rightNums
                  // }
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active)
                  })
                },
              },
            ]
            this.popupMessage = '<br/>退订成功!<br>'
            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.rightList[this.rightNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br/>取消失败!<br>'
          if (error.response && error.response.data && error.response.data.msg) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )

    },
  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = null
  },
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.shopping {
  width: 16.73rem;
  .address {
    display: flex;
    position: absolute;
    top: 1.6rem;
    left: 1.45rem;
    .name {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .name::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .done {
    position: absolute;
    top: 1.6rem;
    right: 4.2rem;
    .item {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .item::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .tel {
    position: absolute;
    top: 1.6rem;
    left: 5rem;
    font-size: 0.36rem;
    font-weight: bold;
    letter-spacing: 0.03rem;
    color: #b5c0ff;
  }
  .container {
    height: 7rem;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    .noData {
      div {
        font-size: 0.5rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -200%);
        letter-spacing: 0.05rem;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
      }
    }
    .aside {
      width: 2rem;
      height: 0.7rem;
      text-align: center;
      line-height: 0.7rem;
      margin-bottom: 0.2rem;
      background: #343d74;
      border-radius: 0.2rem;
      position: relative;
      font-weight: bold;
      transition: all 0.3s;
      letter-spacing: 0.05rem;
      text-indent: 0.05rem;
      font-size: 0.34rem;
      color: #e7e7ef;
      text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
    }
    .left {
      width: 12.75rem;
      height: 7rem;
      margin-left: 0;
      .leftContent {
        width: 11.6rem;
        height: 100%;
        position: relative;
        display: inline-block;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          // left: 0.8rem;
          transition: all 0.2s;
          .messageItem {
            width: 5.5rem;
            height: 3.3603rem;
            //padding: 0.45rem;
            position: relative;
            margin-right: 0.28rem;
            margin-bottom: 0.28rem;
            background-size: 100% 100% !important;
            background: #262954;
            border-radius: 0.3rem;
            overflow: hidden;
            .item_user {
              display: flex;
              align-items: center;
              //width: 100%;
              height: 100%;
              padding: 0 0.35rem;
              .pic {
                width: 1.7rem !important;
                height: 1.75rem !important;
                border-radius: 0.15rem;
                position: relative;
                margin-top: -0.6rem;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .picItem {
                width: 1.12rem !important;
                height: 1.1rem !important;
                border-radius: 0.15rem;
                position: relative;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .contentInfo {
                margin-left: 0.5rem !important;
                height: calc(100% - 0.8rem);
                width: 2.6rem;
                position: relative;
                .title {
                  font-size: 0.36rem !important;
                  font-weight: bold;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                }
                .vegetables_name {
                  font-size: 0.28rem;
                  color: #C7CFFF;
                  margin: 0.1rem 0;
                  overflow: hidden;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  white-space: wrap;
                  font-weight: bold;
                }
                .price {
                  width: 100%;
                  font-size: 0.36rem;
                  color: #F64D23;
                  text-align: right;
                  position: absolute;
                  bottom: 0.6rem;
                  font-weight: bold;
                  text-align: right;
                  span {
                    font-size: 0.28rem;
                  }
                }
                .payNum {
                  width: 1.5rem;
                  height: 0.5rem;
                  background: #DF9C33;
                  border-radius: 0.1rem;
                  position: absolute;
                  right: 0;
                  bottom: -0.1rem;
                  color: #15142D;
                  font-size: 0.24rem;
                  text-align: center;
                  line-height: 0.5rem;
                  font-weight: bold;
                }
              }
            }
            .choose_tip {
              width: 4rem;
              height: 0.37rem;
              position: relative;
              top: 0.13rem;
              right: 0.5rem;
              div {
                width: 100%;
                height: 100%;
                font-size: 0.22rem;
                background: #3cc92d;
                border-radius: 0.05rem;
                position: absolute;
                text-align: center;
                line-height: 0.35rem;
                font-weight: bold;
                color: #fff;
                letter-spacing: 0.03rem;
              }
            }
          }
          // .messageItem:nth-child(3n + 3) {
          //   margin-right: 0;
          // }
        }
      }
      .no_content {
        position: absolute;
        line-height: 7rem;
        left: 6rem;
        // width: 4rem;
        // height: 7rem;
        text-align: center;
        font-size: 0.4rem;
        letter-spacing: 0.05rem;
        line-height: 6rem;
        color: #b5c0ff;
      }
    }
    .right {
      width: 4.75rem;
      height: 7rem;
      //position: relative;
      .rightContent {
        width: 4.75rem;
        height: 5.6rem;
        position: relative !important;
        overflow: hidden;
        display: inline-block;
        .list {
          // margin-right: 0.22rem;
          position: relative;
          transition: all 0.3s;
          .rightList {
            height: 3.3603rem;
            //padding: 0.25rem 0.35rem;
            margin-bottom: 0.28rem;
            margin-right: 0.25rem;
            background: #262954;
            border-radius: 0.24rem;
            font-size: 0.28rem;
            font-weight: bold;
            .rightListInfo {
              padding: 0.25rem 0.35rem;
            }
          }
          .rightListItem {
            height: 3.3603rem;
            //padding: 0.25rem 0.35rem;
            //padding-right: 0.1rem !important;
            margin-bottom: 0.28rem;
            margin-right: 0.25rem;
            background: #262954;
            border-radius: 0.24rem;
            font-size: 0.28rem;
            font-weight: bold;
            .rightListInfo {
              padding: 0.15rem 0.25rem;
              padding-right: 0.15rem;
            }
            .status {
              color: #e77302;
              margin-bottom: 0.1rem;
              letter-spacing: 0.03rem;
            }
            .title {
              width: 1.25rem !important;
              color: #b6c0fd;
            }
            .good,
            .price,
            .pay,
            .time {
              margin-bottom: 0.1rem;
            }
            .good,
            .pay,
            .price {
              display: flex;
            }
          }
        }
      }
      .sendOrder {
        font-size: 0.4rem;
        width: 4.4rem;
        height: 1rem;
        /* background-image: linear-gradient(to right, #617EB6, #4B68A4); */
        font-weight: bold;
        color: #E9EDF6;
        position: absolute;
        right: 1.32rem;
        bottom: 1.6rem;
        text-align: center;
        border-radius: 0.2rem;
        line-height: 1rem;
        letter-spacing: 0.1rem;
        background-image: linear-gradient(to right, #294570, #294570);
        transition: all 0.3s;
        div {
          border-radius: 0.2rem;
        }
  }
}
.no_info {
  //position: absolute;
  line-height: 7rem;
  // left: 6rem;
  //right: 2.3rem;
  // width: 4rem;
  // height: 7rem;
  text-align: center;
  font-size: 0.4rem;
  letter-spacing: 0.05rem;
  text-indent: 0.05rem;
  line-height: 6rem;
  color: #b5c0ff;
}
}
}
</style>
<style lang="less">
.shopping {
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    // background: repeating-linear-gradient(to right, #c98693, #a95361);
    background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 35px;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        .title,
        .detail,
        .price {
          display: flex;
          font-size: 0.35rem;
          font-weight: bold;
          padding-bottom: 0.3rem !important;
          letter-spacing: 0.03rem;
        }
        .title {
          span {
            color: #f64e23;
            margin-left: 0.2rem;
          }
        }
        .price {
          span {
            color: #f64e23;
          }
        }
      }
      .sendCall {
      }
      .cancel,.sendCall {
        width: calc(100% - 0.8rem);
        height: 4.26rem;
        //top: 0.4rem;
        //left: 0.4rem;
        padding: 0;
        margin-top: 0.4rem;
        margin-left: 0.4rem;
        //position: relative;
        overflow: hidden;
        position: relative;
        .message_content {
          width: 100%;
          position: absolute;
          transition: all 0.3s;
          .message_item {
            display: flex;
            flex-direction: row;
            font-size: 0.4rem !important;
            margin-bottom: 0.2rem;
            letter-spacing: 0.03rem;
            .type {
              width: 2.2rem;
              font-size: 0.4rem !important;
            }
          }
          .orderItem {
            letter-spacing: 0.03rem;
            font-size: 0.4rem !important;
            div {
              margin-bottom: 0.2rem;
            }
          }
        }
        .scroll {
          top: 1.8rem !important;
          left: auto !important;
          right: 0.8rem !important;
        }
      }
      .sendCall {
        .message_item {
          flex-direction: column !important;
        }
      }
      .result {
        display: flex;
        justify-content: center !important;
        align-items: center !important;
      }
      .info {
        display: flex;
        justify-content: center !important;
        align-items: center !important;
        margin-top: -0.4rem;
        // background: url(http://192.168.6.212/ptyanglaotv/assets/zc_tan_new.png) no-repeat !important;
        .title {
          display: flex;
          flex-direction: column;
          align-items: center !important;
          text-align: center !important;

          // margin-top: 0.9rem;
          span {
            font-size: 0.5rem;
            color: #464646 !important;
            letter-spacing: 0.14rem;
          }
        }
        .phone {
          width: 7rem;
          text-align: center;
          position: absolute;
          top: 2.3rem;
          // left: 50%;
          // transform: translateX(-50%);
          font-size: 0.3rem;
          // color: yellow;
          color: #464646;
        }
        .tip {
          text-align: center;
          color: #3288dc;
          font-size: 0.4rem;
          margin-top: 1.2rem;
        }
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          // background: repeating-linear-gradient(to right, #c98693, #a95361);
          background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
        }
      }
    }
  }
  .popup_info_box {
    .el-dialog {
      width: 8.6rem;
      height: 5.74rem;
      background: url("../assets/zc_tan_new.png") no-repeat;
      background-size: 100% 100%;
      .el-dialog__header {
        display: none;
      }
      .el-dialog__body {
        background: transparent;
        .info {
          .title {
            span {
              color: #E7F1FA !important;
            }
          }
          .phone {
            top: 1.42rem;
            color: yellow;
          }
          .tip {
            margin-top: 1.1rem;
          }
        }
      }
    }
  }
  .select_pay_box, .select_pay_nums {
    .el-dialog {
      width: 8.4rem;
      height: 5.5rem;
      margin-top: 0 !important;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 0.26rem;
      background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
      // background: repeating-linear-gradient(to right, #c98693, #a95361);
      .el-dialog__header {
        height: 10%;
        display: flex;
        align-items: center;
        padding: 15px 20px !important ;
        .el-dialog__title {
          display: block;
          line-height: normal !important;
          height: 100%;
          font-size: 0.4rem;
          color: #fff;
          background-image: linear-gradient(
            to bottom,
            rgba(255, 255, 255, 1),
            rgba(255, 255, 255, 0.65)
          );
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          font-weight: bold;
          width: 100%;
          text-align: center;
        }
      }
      .el-dialog__body {
        .selectBtnList {
          display: flex;
          flex-direction: row;
          justify-content: space-evenly;
          margin-top: 0.6rem;
          border-radius: 0.3rem;
          .btn {
            position: relative;
            width: 2.2rem;
            height: 3rem;
            line-height: 0.6rem;
            text-align: center;
            border-radius: 0.3rem;
            color: #fff;
            font-size: 0.45rem;
            font-weight: bold;
            letter-spacing: 0.05rem;
            text-indent: 0.05rem;
            // background: repeating-linear-gradient(to right, #c98693, #a95361);
            background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
            .name {
              position: relative;
              width: 1rem;
              margin: 0.7rem auto;
              font-size: 0.4rem;
              text-align: center;
              letter-spacing: 0.06rem;
              color: #fff;
              background-image: linear-gradient(
                to bottom,
                rgba(255, 255, 255, 1),
                rgba(255, 255, 255, 0.65)
              );
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              font-weight: bold;
            }
            .text {
              display: block;
              font-size: 0.26rem;
              position: absolute;
              bottom: 0.3rem;
              width: 100%;
              text-align: center;
              letter-spacing: 0.06rem;
              text-indent: 0.06rem;
              text-shadow: 0.04rem 0.04rem 0.05rem #283c7f;
            }
          }
        }
      }
    }
  }
  .select_pay_nums {
    .el-dialog {
      height: 4.8rem;
    }
    .el-dialog__body {
      width: 100%;
      height: 82%;
      left: 0;
      bottom: 0;
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      .text {
        display: none;
      }
      .selectBtnList {
        width: 92%;
        margin: 0 auto;
        display: grid !important;
        grid-template-columns: repeat(2, 1fr);
        gap: 0;
        transition: all 0.2s;
        margin-top: 0.4rem !important;
        .btn {
          width: 3.6rem !important;
          height: 1.4rem !important;
          margin-bottom: 0.3rem;
          margin-right: 0.3rem;
          font-weight: bold;
          letter-spacing: 0.03rem;
          display: flex;
          align-items: center;
          justify-content: center;
          .name {
            width: 100% !important;
            margin: 0 !important;
            span {
              -webkit-text-fill-color: #fff  !important;
              width: 100% !important;
            }
          }
        }
      }
    }
  }
}
</style>
      