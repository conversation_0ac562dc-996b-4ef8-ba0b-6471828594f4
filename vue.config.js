
 
const TerserPlugin = require('terser-webpack-plugin');
const WebpackObfuscator = require('webpack-obfuscator');
 
const name = process.env.VUE_APP_TITLE || '*****' // 网页标题
module.exports = {
    publicPath: process.env.NODE_ENV === 'production' ? '/tvfrontend/' : './',
    // 将构建好的文件输出到哪里
    outputDir: process.env.NODE_ENV === 'production' ? 'dist_pro' : process.env.NODE_ENV === 'production_inner' ? 'dist_pro_inner' : 'dist_dev',
    lintOnSave: false,   //加入此行 , false为关闭true为开启
    devServer: {
        // host: 'localhost',
        port: 8080,//端口号
        open: false,//是否自动打开
        before: app => { },
    },
    transpileDependencies: [
        'vue-echarts',
        'resize-detector'
    ],
    configureWebpack: config => {
        const plugins = [
          // gzip压缩，无关可忽略
        //   new CompressionPlugin({
        //     cache: false,
        //     test: /\.(js|css|html)?$/i,
        //     filename: '[path].gz[query]',
        //     algorithm: 'gzip',
        //     minRatio: 0.8
        //   })
        ];
     
        if (process.env.NODE_ENV === 'production') {
          // 使用 Terser 进行代码压缩和 source map 生成
          config.optimization = {
            minimizer: [
              new TerserPlugin({
                terserOptions: {
                  compress: {
                    warnings: false,
                    drop_console: true, // 开启console.log压缩
                    drop_debugger: true, // 移除debugger
                  },
                  sourceMap: true, // 启用 source map 生成
                },
                extractComments: false, // 是否将注释提取到单独的文件中
              }),
            ],
          };
     
          // 在 Terser 之后使用 WebpackObfuscator 进行混淆
          plugins.push(
            new WebpackObfuscator(
              {
                // 压缩代码
                compact: true, 
                // 通过用空函数替换它们来禁用console.log，console.info，console.error和console.warn。这使得调试器的使用更加困难。
                disableConsoleOutput: true,
                // 通过固定和随机（在代码混淆时生成）的位置移动数组。
                rotateStringArray: true, 
                // 标识符的混淆方式 hexadecimal(十六进制) mangled(短标识符)
                identifierNamesGenerator: 'hexadecimal',
              },
              []
            )
          );
        }
     
        return {
          name: name,
          devtool: 'source-map', // 确保 devtool 设置为 'source-map' 或类似选项
          plugins: plugins
        };
      },
    
}