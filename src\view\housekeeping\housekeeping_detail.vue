<template>
  <div class="housekeeping_detail">
    <div class="container">
      <div class="left">
        <div class="leftContent scrollParent"  v-if="this.leftList.length > 0">
          <div class="leftList" :style="{ top: '0rem' }">
            <div class="messageItem" :ref="item.ref" v-for="(item, index) in leftList" :key="index">
              <div class="item_user">
                <div :class="item.name ? 'contentInfo' : 'contentInfo'">
                  <div class="title">
                    {{ new Date(item.plan_start_date * 1000).format('hh:mm')  }} - {{ new Date(item.plan_end_date * 1000).format('hh:mm')  }}
                  </div>
                  <div class="vegetables_name">
                    {{ new Date(item.plan_start_date * 1000).format('yyyy-MM-dd')  }}
                  </div>


                </div>
                <div class="payNum" v-if="item.status != 1" :style="{background: item.status == 2 ?  '#00B187' : '#E32E59'}">
                  {{ item.status == 2 ?  '已预约' : '预约已满' }}
                </div>
              </div>
            </div>
          </div>
        </div>
<!--        <div class="no_content" v-else>暂无商品列表</div>-->
      </div>

      <div class="addrStore">
        <img :src="require('@/assets/store.png')" alt="">
        <p>
          商家地址：{{this.supplierInfo.address}}
        </p>
      </div>

      <div class="right">
        <div class="rightContent scrollParent" ref="rightContent" v-if="this.rightList.length > 0">
          <div class="list" :style="{ top: '0rem' }">
            <div
                class="rightList"
                :ref="item.ref"
                v-for="(item, index) in rightList"
                :key="index"
            >
              <div class="listContent">
                <div class="status" :style="{color: statusList[item.status].color}">
                  {{ statusList[item.status].text }}
                </div>
                <div class="time">
                  <div class="title">申请时间:</div>
                  <div class="name">{{ item.created_at }}</div>
                </div>

                <div class="time">
                  <div class="title">预约时间:</div>
                  <div class="name">{{ new Date(item.plan_start_date * 1000).format('yyyy-MM-dd')  }}  {{ new Date(item.plan_start_date * 1000).format('hh:mm')  }} - {{ new Date(item.plan_end_date * 1000).format('hh:mm')  }}</div>
                </div>
              </div>

            </div>
          </div>
        </div>
        <div class="no_info" v-else>暂无近期记录</div>


      </div>

<!--  二次确认弹窗-->
      <el-dialog
        :class="popupModule == 4 ? 'popup_info_box' : 'popup_box'"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        :title="this.supplierInfo.title"
        ref="popupBox"
      >
        <!--发起预约-->
        <div class="message_box sendCall scrollParent" v-if="popupModule == 1">
            <div class="message_content" ref="popupRef" :style="{top: '0rem'}">
              <div class="message_item" v-for="(item,index) in sendData" :key="index" >
                  <div class="itemType">
                    <span class="type">商品{{index + 1}}:</span>
                    <span class="value">{{item.name}}</span>
                  </div>
                  <div class="itemValue">
                    <span>数量:{{item.payNums}}{{item.fk_vegetables_unit_name}} </span>
                    <span>单价:{{item.price}}元</span>
                  </div>
              </div>
            </div>

        </div>

        <!--取消预约-->
        <div class="message_box cancel scrollParent" v-if="popupModule == 2">
          <div class="message_content" ref="popupRef" :style="{top: '0rem'}">
            <div class="message_item">
              <div class="type">
                当前状态:
              </div>
              <div class="value">
                {{ statusList[this.rightList[this.rightNums].status].text }}
              </div>
            </div>
            <div class="message_item">
              <div class="type">申请时间:</div>
              <div class="value">{{ this.rightList[this.rightNums].created_at }}</div>
            </div>
            <div class="message_item">
              <div class="type">预约时间:</div>
              <div class="value">{{ new Date(this.rightList[this.rightNums].plan_start_date * 1000).format('yyyy-MM-dd')  }}  {{ new Date(this.rightList[this.rightNums].plan_start_date * 1000).format('hh:mm')  }} - {{ new Date(this.rightList[this.rightNums].plan_end_date * 1000).format('hh:mm')  }}</div>
            </div>
            <div class="message_item">
              <div class="type">服务项目:</div>
              <div class="value">{{ this.rightList[this.rightNums].service_name }}</div>
            </div>

            <div class="message_item">
              <div class="type">商家:</div>
              <div class="value">{{ this.rightList[this.rightNums].name }}</div>
            </div>

            <div class="message_item">
              <div class="type">地址:</div>
              <div class="value">{{ this.rightList[this.rightNums].addr }}</div>
            </div>


          </div>
        </div>

        <div class="message_box result" style="text-align: center; font-size: 0.37rem; justify-content: center" v-html="popupMessage" v-if="popupModule == 3"></div>

        <div class="message_box info" v-if="popupModule == 4">
          <div class="title"><span>你的预约</span><span>已经发送成功！</span></div>
          <div class="tip">您可以在“服务记录”中查看预约信息。</div>
        </div>

        <div class="popupBtnList">
          <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index"> {{ item.text }}</div>
        </div>
      </el-dialog>

    </div>
    <div class="address">
      <template>
        <div class="name">预约时间段</div>
      </template>
    </div>
    <div class="tel">
      <img :src="require('@/assets/quan.png')" alt="">
      <span>温馨提示:</span>
      {{this.supplierInfo.remark}}
    </div>
    <div class="done"><div class="item">服务记录</div></div>
  </div>
</template>
      
<script>
import {
  getHouseKeepingTimerList,
  getHouseKeepingOrder,
  sendHouseKeepingOrder,
  cancelHouseKeepingOrder,
} from '@/api/index'

export default {
  name:'housekeeping_detail',
  components: {},
  data() {
    return {
      lazyError: require('@/assets/icon_17.png'),
      sendData: [],
      canSend: false,
      supplierInfo: null,
      leftList: [],
      rightList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      order_type: 1,
      order_detail: [],

      firstShow: true,

      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],


      messageList: [],
      statusList:[]
    }
  },
  created() {
    this.supplierInfo =  JSON.parse(sessionStorage.getItem('supplierInfo'))

    //设置页面左上角标题
    this.$nextTick(() => {
        this.$store.dispatch('index/setMainTitle', this.$route.query.title)
    })

  },
  computed: {},
  watch: {
  },
  mounted() {
    if (localStorage.getItem('statusList')) {
      this.statusList = JSON.parse(localStorage.getItem('statusList'))
    }
    if (sessionStorage.getItem('indexFocusService')) {
      this.leftNums = Number(sessionStorage.getItem('indexFocusService'))
      sessionStorage.removeItem('indexFocusService')
    }
    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if ((Math.abs(styleTop) +scrollPageTop)  * fontSize > this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight) {
                this.$refs.popupRef.style.top = styleTop - (scrollTop/fontSize - Math.abs(styleTop)) + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * ((scrollTop/fontSize - Math.abs(styleTop)) * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight)) + 'px'
                return
              }
              if (scrollTop > 0) {
                  this.$refs.popupRef.style.top = styleTop - scrollPageTop + 'rem'
                  scrollBar.style.top = barTop + (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))   + 'px'
              }
            }
          }
          return
        }

        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1 && this.rightList[this.rightNums] && this.rightList[this.rightNums].ref) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums++
            this.rightList[this.rightNums].ref = 'active'

            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }

        }
        //左侧
        else {
            this.leftList[this.leftNums].ref = ''
            if (this.leftList[this.leftNums + 2]) {
              this.leftNums = this.leftNums + 2
            } else {
              if (this.leftNums % 2 == 1) {
                this.leftNums = this.leftList.length - 1
              }
            }
            this.leftList[this.leftNums].ref = 'active'
            // let { name, fk_vegetables_name, price, combination_id } = this.leftList[this.leftNums]
            // this.name = name
            // this.vegetables_name = fk_vegetables_name
            // this.price = price
            // this.combination_id = combination_id
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if (scrollTop > 0) {
                if ((styleTop + scrollPageTop) > 0) {
                  this.$refs.popupRef.style.top = '0rem'
                  scrollBar.style.top = '0px'
                  return
                } else {
                  this.$refs.popupRef.style.top = styleTop + scrollPageTop + 'rem'
                  scrollBar.style.top = barTop - (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))  + 'px'
                }
              }
            }
          }
          return
        }

        // 右侧
        if (this.nextNums > -1) {
          if (this.rightList.length < 1 || !this.rightList[this.rightNums].ref) {
            if (this.rightList.length > 0) {
              this.rightList[this.rightNums].ref = "active"
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (this.rightNums > 0 && this.rightList.length - 1 >= 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums--
            this.rightList[this.rightNums].ref = 'active'


          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 左侧
        else {
          if (this.leftList[this.leftNums - 2]) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums -= 2
            this.leftList[this.leftNums].ref = 'active'
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }

        // 在右侧
        else if (this.nextNums > -1) {
          if (this.leftList.length > 0) {
            if (this.rightList.length > 0) {
              this.rightList[this.rightNums].ref = ''
            }
            this.leftList[this.leftNums].ref = 'active'
            this.nextNums = -1
          }
        }
        else {
          if (this.leftNums % 2 == 1) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
            this.nextNums = -1
          }
        }



        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左侧
        if (this.nextNums == -1) {
          if (this.leftNums % 2 == 1) {
            if (this.rightList.length > 0) {
              this.leftList[this.leftNums].ref = ''
              this.nextNums = this.leftNums
              this.rightList[this.rightNums].ref="active"
            }
          } else {
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            this.leftList[this.leftNums].ref = 'active'
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            // 取消订单关闭时
            if (this.nextNums == -1) {
              this.leftList[this.leftNums].ref = 'active'
            } else {
              this.rightList[this.rightNums].ref = 'active'
            }


            this.popupBtnList[this.popupBtnNums].fuc(this.rightList[this.rightNums])
            this.dialogVisible = false
            return
          }
          // 左侧
          if (this.nextNums == -1) {
            if (!this.leftList[this.leftNums].status) {
              return
            }
            this.leftList[this.leftNums].ref = ''
            this.popupModule = 3
            this.popupBtnNums =  0
            if (this.leftList[this.leftNums].status == 1) {
              this.popupMessage = `<div>请确认预约时间？<br>预约时间:${new Date(this.leftList[this.leftNums].plan_end_date * 1000).format('MM月dd日')}</div>`
              this.popupBtnList = [
                  {
                    text: '关闭',
                    ref: '',
                    fuc: () => {
                      this.dialogVisible = false
                    },
                  },{
                    text: '发起预约',
                    ref: '',
                    fuc: this.sendOrder,
                  },
              ]
            }
            else {
              this.popupMessage = `<div>是否取消当前预约？<br>预约时间:${new Date(this.leftList[this.leftNums].plan_end_date * 1000).format('MM月dd日')}</div>`
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                },{
                  text: '取消预约',
                  ref: '',
                  fuc: this.cancelOrder,
                },
              ]
            }

            this.popupBtnList[this.popupBtnNums].ref="active"
            this.dialogVisible = true

          }
          // 右侧
          else {
            this.rightList[this.rightNums].ref = ""
            this.popupModule = 2
            this.popupBtnNums = 0
            this.popupBtnList=[{
              text: '关闭',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            }]

            if (this.rightList[this.rightNums].status == 2) {
              this.popupBtnList.push({
                text: '取消预约',
                ref: '',
                fuc: this.cancelOrder,
              })

            }

            this.dialogVisible = true



          }
      },
      esc: () => {
        if (this.nextNums == -1) {
          if (this.dialogVisible) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.dialogVisible = false
            return
          }
        } else
        if (this.nextNums > -1) {
          if (this.dialogVisible) {
            if (this.popupModule == 2) {
              this.rightList[this.rightNums].ref = 'active'
            }
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.dialogVisible = false
            return
          }
        }
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      this.popupBtnList[this.popupBtnNums].ref = ''
      if (this.nextNums == -1) {
        this.leftList[this.leftNums].ref = 'active'
      } else {
        if (this.rightList[this.rightNums]) {
          this.rightList[this.rightNums].ref = 'active'
        }
      }

      this.$nextTick(() => {
        if (this.$refs.active[0]) {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        }

        document.getElementById('focus_border').style.borderColor = '#fff'
        // document.getElementById('focus_border').style.borderColor = 'transparent'

        // setTimeout(()=>{
          this.popupBtnNums = 0
          this.popupModule = 1
        // },100)

      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState', true)
      //获取商品列表
      getHouseKeepingTimerList({
        fk_service_id: this.$route.query.id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
      })
      .then((res) => {
        this.$store.dispatch('app/setLoadingState', false)
        if (res.code == 200) {
          if (res.data) {
            let leftListClone = JSON.parse(JSON.stringify(res.data))
            leftListClone.map((item) => {
              item.ref = ''
            })
            this.leftList = leftListClone
          }


          if (this.leftList.length > 0) {
            this.nextNums = -1
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          this.getRightData()
        }
      })
      .catch(err=>{
        this.$store.dispatch('app/setLoadingState', false)
        this.getRightData()
      })




    },
    getRightData() {
      getHouseKeepingOrder({
        fk_service_id: this.$route.query.id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
      })
      .then(res=>{
        if (res.code == 200) {
          if (res.data) {
            let rightListClone = JSON.parse(JSON.stringify(res.data))
            rightListClone = rightListClone.reverse()
            rightListClone.map((item) => {
              item.ref = ''
            })
            this.rightList = rightListClone

            if (this.leftList.length == 0) {
              this.nextNums = this.rightNums
              this.rightList[this.rightNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            this.$nextTick(()=>{
              this.fuc.setScroll()

            })


          }

        }
      })
      .catch(err=>{

      })
    },

    sendOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      this.nextNums == -1
      sendHouseKeepingOrder({
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        fk_service_id: this.$route.query.id,
        fk_jiazheng_plan_id: this.leftList[this.leftNums].id,
        type: this.supplierInfo.fk_supplier_id
      })
      .then(res=>{
        this.$store.dispatch('app/setLoadingState', false)
        this.rightList = []
        this.rightNums = 0
        if (res.code == 200) {
          let newData = res.data.reverse()
          this.leftList[this.leftNums].fk_order_id = newData[0].id
          this.leftList[this.leftNums].status = 2
          this.leftList[this.leftNums].ref = ''
          this.popupModule = 3
          this.popupBtnNums =  0
          this.popupMessage = `<div>您的预约已发送成功！<br>您可以在“服务记录”中查看预约信息。</div>`
          this.popupBtnList = [
            {
              text: '关闭',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            }
          ]
          this.dialogVisible = true
          this.getRightData()
        }

      })
      .catch(err=>{
        this.$store.dispatch('app/setLoadingState', false)

      })

      this.leftList[this.leftNums].ref = ''
      // this.order_type = 1

    },

    cancelOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      let orderID = 0

      if (this.nextNums == -1) {
        orderID = this.leftList[this.leftNums].fk_order_id
      } else {
        orderID = this.rightList[this.rightNums].id
      }

      cancelHouseKeepingOrder({
        type: this.supplierInfo.fk_supplier_id,
        id: orderID,
      }).then(
        (res) => {
          if (res.code == 200) {

            this.leftList.map(item=>{
              if (item.fk_order_id == orderID) {
                item.status = 1
              }
            })

            let rightClone = JSON.parse(JSON.stringify(res.data))
            if (rightClone) {
              rightClone.map((item) => {
                item.ref = ''
              })
            } else {
              rightClone = []
            }

            this.rightList = rightClone
            if (this.$refs.rightContent) {
              let scrollDom = this.$refs.rightContent.querySelector('.scroll');
              this.$refs.rightContent.childNodes[0].style.top = "0rem"
              if (scrollDom) {
                scrollDom.remove()
              }
            }
            this.popupModule = 3
            this.leftList[this.leftNums].ref = ''
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                },
              },
            ]
            this.popupMessage = '<br/>取消预约成功!<br>'
            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.leftList[this.leftNums].ref = ''
          this.rightList[this.rightNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br/>取消失败!<br>'
          if (error.response && error.response.data && error.response.data.msg) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )

    },
  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = null
  },
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.housekeeping_detail {
  width: 16.73rem;
  .address {
    display: flex;
    position: absolute;
    top: 1.6rem;
    left: 1.45rem;
    .name {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .name::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .done {
    position: absolute;
    top: 1.6rem;
    right: 4.2rem;
    .item {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .item::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .tel {
    position: absolute;
    bottom: 0.54rem;
    right: 1rem;
    letter-spacing: 0.03rem;
    font-size: 0.28rem;
    color: #B2BAEF;
    display: flex;
    align-items: center;
    img {
      display: block;
      width: 0.32rem;
      height: 0.32rem;
      margin-right: 0.04rem;
      margin-top: 0.03rem;
    }
  }
  .container {
    height: 7rem;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    .noData {
      div {
        font-size: 0.5rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -200%);
        letter-spacing: 0.05rem;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
      }
    }
    .left {
      width: 12.75rem;
      height: 6rem;
      margin-left: 0;
      overflow: hidden;
      .leftContent {
        width: 11.6rem;
        height: 100%;
        position: relative;
        display: inline-block;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          // left: 0.8rem;
          transition: all 0.2s;
          .messageItem {
            width: 5.5rem;
            height: 2.85rem;
            //padding: 0.45rem;
            position: relative;
            margin-right: 0.28rem;
            margin-bottom: 0.28rem;
            background-size: 100% 100% !important;
            background: #262954;
            border-radius: 0.24rem;
            overflow: hidden;
            .item_user {
              display: flex;
              align-items: center;
              justify-content: center;
              //width: 100%;
              height: 100%;
              padding: 0 0.35rem;
              .contentInfo {
                width: 90%;
                position: relative;
                text-align: center;
                .title {
                  font-size: 0.44rem !important;
                  font-weight: bold;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  letter-spacing: 0.03rem;
                  text-indent: 0.03rem;
                }
                .vegetables_name {
                  font-size: 0.3rem;
                  color: #848BC0;
                  margin: 0.1rem 0;
                  overflow: hidden;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  white-space: wrap;
                  font-weight: bold;
                  margin-bottom: 0;
                }
              }
              .payNum {
                border-radius: 0.1rem;
                border-top-left-radius: 0;
                border-top-right-radius: 0;
                width: 100%;
                height: 0.5rem;
                color: #fff;
                font-size: 0.28rem;
                font-weight: bold;
                text-align: center;
                position: absolute;
                bottom: 0;
                line-height: 0.5rem;
                letter-spacing: 0.03rem;
              }
            }
            .choose_tip {
              width: 4rem;
              height: 0.37rem;
              position: relative;
              top: 0.13rem;
              right: 0.5rem;
              div {
                width: 100%;
                height: 100%;
                font-size: 0.22rem;
                background: #3cc92d;
                border-radius: 0.05rem;
                position: absolute;
                text-align: center;
                line-height: 0.35rem;
                font-weight: bold;
                color: #fff;
                letter-spacing: 0.03rem;
              }
            }
          }
          // .messageItem:nth-child(3n + 3) {
          //   margin-right: 0;
          // }
        }
      }
      .no_content {
        position: absolute;
        line-height: 7rem;
        left: 6rem;
        // width: 4rem;
        // height: 7rem;
        text-align: center;
        font-size: 0.4rem;
        letter-spacing: 0.05rem;
        line-height: 6rem;
        color: #b5c0ff;
      }
    }
    .addrStore {
      height: 0.5rem;
      background: #2C2C5E;
      color: #C0CAFF;
      padding: 0rem 0.3rem;
      border-radius: 0.26rem;
      position: fixed;
      top: 8.8rem;
      left: 1.2rem;
      display: flex;
      align-items: center;
      img {
        display: block;
        width: 0.3rem;
        height: 0.3rem;
        margin-right: 0rem;
        margin-top: 0.03rem;
      }
      p {
        height: 0.5rem;
        font-size: 0.28rem;
        line-height: 0.56rem;
        float: left;
        margin-left: 0.15rem;
      }
    }
    .right {
      width: 4.75rem;
      height: 6rem;
      //position: relative;
      .rightContent {
        width: 4.75rem;
        height: 6rem;
        position: relative !important;
        overflow: hidden;
        display: inline-block;
        .list {
          // margin-right: 0.22rem;
          position: relative;
          transition: all 0.3s;
          .rightList {
            height: 2.85rem;
            margin-bottom: 0.28rem;
            margin-right: 0.25rem;
            background: #262954;
            border-radius: 0.24rem;
            font-size: 0.28rem;
            font-weight: bold;
            .listContent {
              padding: 0.25rem 0.35rem;
            }
            .status {
              color: #e77302;
              margin-bottom: 0.1rem;
              letter-spacing: 0.03rem;
              margin-bottom: 0.2rem;
            }
            .title {
              width: 1.25rem !important;
              color: #b6c0fd;
            }
            .time {
              margin-bottom: 0.16rem;
            }
          }
        }
      }
    }
    .no_info {
      //position: absolute;
      line-height: 7rem;
      // left: 6rem;
      //right: 2.3rem;
      // width: 4rem;
      // height: 7rem;
      text-align: center;
      font-size: 0.4rem;
      letter-spacing: 0.05rem;
      text-indent: 0.05rem;
      line-height: 6rem;
      color: #b5c0ff;
    }
  }
}
</style>
<style lang="less">
.housekeeping_detail {
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    // background: repeating-linear-gradient(to right, #c98693, #a95361);
    background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 35px;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        .title,
        .detail,
        .price {
          display: flex;
          font-size: 0.35rem;
          font-weight: bold;
          padding-bottom: 0.22rem !important;
          letter-spacing: 0.03rem;
        }
        .title {
          span {
            color: #f64e23;
            margin-left: 0.2rem;
          }
        }
        .price {
          span {
            color: #f64e23;
          }
        }
      }
      .sendCall {
      }
      .cancel,.sendCall {
        width: calc(100% - 0.8rem);
        height: 4.26rem;
        //top: 0.4rem;
        //left: 0.4rem;
        padding: 0;
        margin-top: 0.4rem;
        margin-left: 0.4rem;
        //position: relative;
        overflow: hidden;
        position: relative;
        .message_content {
          width: 100%;
          position: absolute;
          transition: all 0.3s;
          .message_item {
            display: flex;
            flex-direction: row;
            font-size: 0.4rem;
            margin-bottom: 0.14rem;
            letter-spacing: 0.03rem;
            .type {
              width: 1.9rem;
              font-size: 0.4rem;
            }
          }
          .orderItem {
            letter-spacing: 0.03rem;
            font-size: 0.4rem !important;
            div {
              margin-bottom: 0.2rem;
            }
          }
        }
        .scroll {
          top: 1.8rem !important;
          left: auto !important;
          right: 0.8rem !important;
        }
      }
      .cancel {
        .message_item {
          font-size: 0.34rem !important;
          .type {
            font-size: 0.34rem !important;
          }
        }
      }
      .sendCall {
        .message_item {
          flex-direction: column !important;
        }
      }
      .result {
        display: flex;
        justify-content: center !important;
        align-items: center !important;
      }
      .info {
        display: flex;
        justify-content: center !important;
        align-items: center !important;
        margin-top: -0.4rem;
        // background: url(http://192.168.6.212/ptyanglaotv/assets/zc_tan_new.png) no-repeat !important;
        .title {
          display: flex;
          flex-direction: column;
          align-items: center !important;
          text-align: center !important;

          // margin-top: 0.9rem;
          span {
            font-size: 0.5rem;
            color: #464646 !important;
            letter-spacing: 0.14rem;
          }
        }
        .phone {
          width: 7rem;
          text-align: center;
          position: absolute;
          top: 2.3rem;
          // left: 50%;
          // transform: translateX(-50%);
          font-size: 0.3rem;
          // color: yellow;
          color: #464646;
        }
        .tip {
          text-align: center;
          color: #3288dc;
          font-size: 0.4rem;
          margin-top: 1.2rem;
        }
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.1rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: -0.05rem;
          // background: repeating-linear-gradient(to right, #c98693, #a95361);
          background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
        }
      }
    }
  }
  .popup_info_box {
    .el-dialog {
      width: 8.6rem;
      height: 5.74rem;
      background: url("../../assets/zc_tan_new.png") no-repeat;
      background-size: 100% 100%;
      .el-dialog__header {
        display: none;
      }
      .el-dialog__body {
        background: transparent;
        .info {
          .title {
            span {
              color: #E7F1FA !important;
            }
          }
          .phone {
            top: 1.2rem;
            color: yellow;
          }
          .tip {
            margin-top: 1.1rem;
          }
        }
      }
    }
  }
}
</style>
      