<template>
  <div class="pointRecord">
    <div class="container">
      <!-- <div class="left">
        <div class="leftList"></div>
      </div> -->
      <div class="left">
        <div
          class="messageCenterList scrollParent"
          v-if="this.leftList.length > 0"
        >
          <div class="leftList" :style="{ top: '0rem' }">
            <div
              class="messageItem"
              :ref="item.ref"
              v-for="(item, index) in leftList"
              :key="index"
            >
              <div class="item_user">
                <div class="content">
                  <div class="time">{{ item.created_at }}</div>
                  <div class="detail">
                    <div class="pic" v-lazy-container="{ selector: 'img' }">
                      <img
                        :data-src="apiBaseUrl + item.fk_point_item_img"
                        :data-error="lazyError"
                        alt=""
                      />
                    </div>
                    <div class="title">{{ item.fk_point_item_title }}</div>
                  </div>
                  <div class="point">
                    <div class="pic">
                      <img src="@/assets/point_icon.png" alt="" />
                    </div>
                    <div class="num">
                      支出<span>{{ item.point }}</span
                      >积分
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_info" v-else>暂无兑换记录</div>
      </div>
      <!-- <div class="no_content" v-else>暂无记录</div> -->
    </div>
  </div>
</template>

<script>
import { getRecord } from '@/api/index'
export default {
  data() {
    return {
      leftList: [],
      lazyError: require('@/assets/customer.png'),
      apiBaseUrl: process.env.VUE_APP_API,
      leftNums: 0,
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '兑换记录')
    this.$store.dispatch('index/setFocusDom', null)
  },
  mounted() {
    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.leftNums < this.leftList.length - 1) {
          if (this.leftList[this.leftNums + 3]) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums += 3
            this.leftList[this.leftNums].ref = 'active'
          } else {
            if (
              this.leftNums <
                this.leftList.length - (this.leftList.length % 3) &&
              this.leftList.length % 3 != 0
            ) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums = this.leftList.length - 1
              this.leftList[this.leftNums].ref = 'active'
            }
          }
        }

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.leftNums > 0 && this.leftNums - 3 >= 0) {
          this.leftList[this.leftNums].ref = ''
          this.leftNums -= 3
          this.leftList[this.leftNums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      left: () => {
        if (this.leftNums % 3 != 0) {
          this.leftList[this.leftNums].ref = ''
          this.leftNums--
          this.leftList[this.leftNums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      right: () => {
        if (this.leftNums < this.leftList.length - 1) {
          if (this.leftNums % 3 != 2) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      enter: () => {},
      esc: () => {
        //   if (this.dialogVisible) {
        //     this.popupBtnList[this.popupBtnNums].ref = ''
        //     this.leftList[this.leftNums].ref = 'active'
        //     this.dialogVisible = false
        //     return
        //   }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    getData() {
      getRecord({
        fk_home_id: this.$store.getters.getUserInfo.home_id,
      }).then((res) => {
        if (res.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data))

          // const copies = 15;
          // let list = JSON.parse(JSON.stringify([].concat(...Array(copies).fill(res.data))))
          list.map((item) => {
            item.ref = ''
          })
          this.leftList = list

          if (this.leftList.length > 0) {
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.pointRecord {
  width: 16.6rem;
  .container {
    width: 100%;
    height: 7rem;
    overflow: hidden;
    display: flex;
    // grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
    // gap: 0.16rem;
    font-size: 0.2rem;
    color: #e7e7ef;
    .left {
      width: 100%;
      height: 7rem;
      margin-left: 0rem;
      //padding-left: 0.1rem;
      //margin-left: 0.05rem;

      .messageCenterList {
        width: 100%;
        height: 100%;
        position: relative;
        display: inline-block;
        overflow: hidden;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          left: 0.4rem;
          transition: all 0.2s;
          .messageItem {
            width: 5.2rem;
            height: 3.33rem;
            // padding: 0.5rem;
            margin-bottom: 0.3rem;
            background-size: 100% 100% !important;
            // background: #262954;
            background: linear-gradient(to bottom, #45427b 75%, #2e3061 25%);

            border-radius: 0.24rem;
            overflow: hidden;
            .item_user {
              display: flex;
              // flex-direction: column;
              // justify-content: center;
              // align-items: center;
              height: 100%;
              padding: 0.15rem 0.35rem;
              .content {
                display: flex;
                flex-direction: column;
                height: initial !important;
                .time {
                  font-size: 0.35rem;
                  color: #fff;
                  margin-bottom: 0.2rem;
                  font-weight: bold;
                }
                .detail {
                  display: flex;
                  // align-items: center;
                  .pic {
                    width: 1.5rem;
                    height: 1.5rem;
                    img {
                      width: 100%;
                      height: 100%;
                      border-radius: 0.1rem;
                    }
                  }
                  .title {
                    width: 3rem;
                    height: 1.65rem;
                    font-size: 0.4rem;
                    font-weight: bold;
                    margin-left: 0.2rem;
                    margin-top: 0.1rem;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    text-overflow: ellipsis;
                    -webkit-line-clamp: 3 !important;
                  }
                }
                .point {
                  display: flex;
                  justify-content: flex-end;
                  align-items: center;
                  font-size: 0.4rem;
                  color: #fff;
                  font-weight: bold;
                  margin-right: 0.1rem;
                  margin-top: 0.1rem;
                  .pic {
                    margin-right: 0.2rem;
                    margin-top: 0.05rem;
                    width: 0.45rem;
                    height: 0.45rem;
                    img {
                      width: 100%;
                      height: 100%;
                    }
                  }

                  .num {
                    letter-spacing: 0.03rem;
                  }
                  span {
                    line-height: 0.35rem;
                    font-size: 0.5rem;
                    color: #ff7a23;
                  }
                }
              }
            }
          }
          .messageItem:not(:nth-child(3n + 3)) {
            margin-right: 0.28rem;
          }
        }
      }
      .no_info {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        line-height: 6rem;
        width: 4rem;
        height: 7rem;
        text-align: center;
        font-size: 0.6rem;
      }
    }
  }
}
</style>
<style lang="less">
.pointRecord {
  .container {
    .left {
      .scroll {
        top: 2.25rem !important;
        left: 18rem !important;
      }
    }
  }
}
</style>