import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);
const router = new Router({
    // mode: "history",        //去除默认的哈希模式（#）后的路径。
    routes: [
        {
            path: "/",
            name: "home",
            component: ()=>import('@/view/home'),
            redirect: '/index',
            children:
            [
                {
                    path: '/index',
                    name: 'index',
                    component: ()=>import('@/view/index'),
                    meta: {
                        backgroundImage: 'bg1.jpg',
                        // backgroundImage: 'bg.jpg',
                        index:2,
                        operationTip: null
                    }
                },{
                    path: '/weather',
                    name: 'weather',
                    component: ()=>import('@/view/weather'),
                    meta: {
                        backgroundImage: 'weatherBg.jpg',
                        index:0,
                    }
                },{
                    path: '/supplier',
                    name: 'supplier',
                    component: ()=>import('@/view/supplierList'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0,
                    }
                },
                {
                    path: '/media_call_message',
                    name: 'media_call_message',
                    component: ()=>import('@/view/media_call_message'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0,
                    }
                },
                {
                    path: '/zegoCall',
                    name: 'zegoCall',
                    component: ()=>import('@/view/zegoCall_index'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    }
                },
                {
                    path: '/zegoLogAll_list',
                    name: 'zegoLogAll_list',
                    component: ()=>import('@/view/zegoLogAll_list'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    }
                },
                {
                    path: '/policy',
                    name: 'policy',
                    component: ()=>import('@/view/policy/policy'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/policyDetail',
                    name: 'policyDetail',
                    component: ()=>import('@/view/policy/policyDetail'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/serviceAgency',
                    name: 'serviceAgency',
                    component: ()=>import('@/view/policy/serviceAgency'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/serviceDetail',
                    name: 'serviceDetail',
                    component: ()=>import('@/view/policy/serviceDetail'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/detailsPage',
                    name: 'detailsPage',
                    component: ()=>import('@/view/policy/detailsPage'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/hospital',
                    name: 'hospital',
                    component: ()=> import( '@/view/hospital/hospital'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/department',
                    name: 'department',
                    component: ()=> import( '@/view/hospital/department'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/doctor',
                    name: 'doctor',
                    component: ()=> import( '@/view/hospital/doctor'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/appointment',
                    name: 'appointment',
                    component: ()=> import( '@/view/hospital/appointment'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/expert',
                    name: 'expert',
                    component: ()=> import( '@/view/expert/expert'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/orderTime',
                    name: 'orderTime',
                    component: ()=> import( '@/view/expert/orderTime'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/homeCare',
                    name: 'homeCare',
                    component: () => import('@/view/homeCare/homeCare'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/reformCreate',
                    name: 'reformCreate',
                    component: () => import('@/view/homeCare/reformCreate'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/lease',
                    name: 'lease',
                    component: ()=> import('@/view/lease/lease'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/leaseDetail',
                    name: 'leaseDetail',
                    component: ()=> import('@/view/lease/leaseDetail'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/leaseIndex',
                    name: 'leaseIndex',
                    component: ()=> import('@/view/leaseIndex/lease_index'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/leaseIndexDetail',
                    name: 'leaseIndexDetail',
                    component: ()=> import('@/view/leaseIndex/lease_index_detail'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/shopping',
                    name: 'shopping',
                    component: ()=> import('@/view/shopping'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/onLineCar',
                    name: 'onLineCar',
                    component: ()=>import('@/view/onLineCar'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    }
                },
                {
                    path: '/tetris',
                    name: 'tetris',
                    component: ()=>import('@/view/games/tetris/index.vue'),
                    meta: {
                        backgroundImage: 'tetris_bg.jpg',
                        index:0,
                        operationTip: 'tetris_tip.png'
                    }
                },
                {
                    path: '/connectFour',
                    name: 'connectFour',
                    component: ()=>import('@/view/games/connectFour/index.vue'),
                    meta: {
                        backgroundImage: 'connectFour_bg.jpg',
                        index:0,
                        operationTip: 'connectFour_tip.png'
                    }
                },
                {
                    path: '/play2048',
                    name: 'play2048',
                    component: ()=>import('@/view/games/play2048/index.vue'),
                    meta: {
                        backgroundImage: 'connectFour_bg.jpg',
                        index:0,
                        operationTip: 'connectFour_tip.png'
                    }
                },
                {
                    path: '/introduce',
                    name: 'introduce',
                    component: () => import('@/view/games/play2048/introduce.vue'),
                    meta: {
                        backgroundImage: 'connectFour_bg.jpg',
                        index: 0,
                        operationTip: 'connectFour_tip.png'
                    }
                },
                {
                    path: '/snake',
                    name: 'snake',
                    component: ()=>import('@/view/games/snake/index.vue'),
                    meta: {
                        backgroundImage: 'connectFour_bg.jpg',
                        index:0,
                        operationTip: 'connectFour_tip.png'
                    }
                },
                {
                    path: '/maze',
                    name: 'maze',
                    component: ()=>import('@/view/games/maze/index.vue'),
                    meta: {
                        backgroundImage: 'connectFour_bg.jpg',
                        index:0,
                        operationTip: 'connectFour_tip.png'
                    }
                },
                {
                    path: '/sokoban',
                    name: 'sokoban',
                    component: ()=>import('@/view/games/sokoban/index.vue'),
                    meta: {
                        backgroundImage: 'connectFour_bg.jpg',
                        index:0,
                        operationTip: 'connectFour_tip.png'
                    }
                },
                {
                    path: '/sokobanLevel',
                    name: 'sokobanLevel',
                    component: ()=>import('@/view/games/sokoban/level_list.vue'),
                    meta: {
                        backgroundImage: 'connectFour_bg.jpg',
                        index:0,
                        operationTip: 'connectFour_tip.png'
                    }
                },
                {
                    path: '/bobble',
                    name: 'bobble',
                    component: ()=>import('@/view/games/bobble/index.vue'),
                    meta: {
                        backgroundImage: 'connectFour_bg.jpg',
                        index:0,
                        operationTip: 'connectFour_tip.png'
                    }
                },
                {
                    path: '/hextrix',
                    name: 'hextrix',
                    component: ()=>import('@/view/games/hextrix/index.vue'),
                    meta: {
                        backgroundImage: 'connectFour_bg.jpg',
                        index:0,
                        operationTip: 'connectFour_tip.png'
                    }
                },
                {
                    path: '/breakOut',
                    name: 'breakOut',
                    component: ()=>import('@/view/games/breakOut/index.vue'),
                    meta: {
                        backgroundImage: 'connectFour_bg.jpg',
                        index:0,
                        operationTip: 'connectFour_tip.png'
                    }
                },
                {
                    path: '/moreServices',
                    name: 'moreServices',
                    component: ()=>import('@/view/moreServices'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    }
                },
                {
                    path: '/online_food_index',
                    name: 'online_food_index',
                    component: ()=>import('@/view/onlineFood/online_food_index'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    }
                },
                {
                    path: '/online_food_aihu',
                    name: 'online_food_aihu',
                    component: ()=>import('@/view/onlineFood/online_food_aihu'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    }
                },
                {
                    path: '/online_food_aihu_confirm',
                    name: 'online_food_aihu_confirm',
                    component: ()=>import('@/view/onlineFood/online_food_aihu_confirm'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    }
                },
                {
                    path: '/handheldCare',
                    name: 'handheldCare',
                    component: ()=> import('@/view/handheldCare'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/elderlyOrientedList',
                    name: 'elderlyOrientedList',
                    component: ()=> import('@/view/elderlyOriented/elderlyOrientedList'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/elderlyOrientedCreate',
                    name: 'elderlyOrientedCreate',
                    component: ()=> import('@/view/elderlyOriented/elderlyOrientedCreate'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/biserialPageList',
                    name: 'biserialPageList',
                    component: ()=> import('@/view/biserialPage/biserialPageList'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/biserialPageInfo',
                    name: 'biserialPageInfo',
                    component: ()=> import('@/view/biserialPage/biserialPageInfo'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/housekeeping',
                    name: 'housekeeping',
                    component: ()=> import('@/view/housekeeping/housekeeping'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/housekeepDetail',
                    name: 'housekeepDetail',
                    component: ()=> import('@/view/housekeeping/housekeeping_detail'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/firmDetails',
                    name: 'firmDetails',
                    component: ()=> import('@/view/drug/firmDetails'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/drugList',
                    name: 'drugList',
                    component: ()=> import('@/view/drug/drugList'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/medicalHome',
                    name: 'medicalHome',
                    component: ()=> import('@/view/drug/medicalHome'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/medicalKit',
                    name: 'medicalKit',
                    component: ()=> import('@/view/drug/medicalKit'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/shoppingCart',
                    name: 'shoppingCart',
                    component: ()=> import('@/view/drug/shoppingCart'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/pointTask',
                    name: 'pointTask',
                    component: ()=> import('@/view/pointExchange/pointTask'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/pointList',
                    name: 'pointList',
                    component: ()=> import('@/view/pointExchange/pointList'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/pointRecord',
                    name: 'pointRecord',
                    component: ()=> import('@/view/pointExchange/pointRecord'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/pointRule',
                    name: 'pointRule',
                    component: ()=> import('@/view/pointExchange/pointRule'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/consultService',
                    name: 'consultService',
                    component: ()=> import('@/view/consultService/consultServiceList'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/consultServiceInfo',
                    name: 'consultServiceInfo',
                    component: ()=> import('@/view/consultService/consultServiceInfo'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/familyAlbum',
                    name: 'familyAlbum',
                    component: ()=> import('@/view/familyAlbum/familyAlbum'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/familyFriends',
                    name: 'familyFriends',
                    component: ()=> import('@/view/familyAlbum/familyFriends'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/hospital_sj',
                    name: 'hospital_sj',
                    component: ()=> import( '@/view/hospital_sj/hospital'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/department_sj',
                    name: 'department_sj',
                    component: ()=> import( '@/view/hospital_sj/department'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/doctor_sj',
                    name: 'doctor_sj',
                    component: ()=> import( '@/view/hospital_sj/doctor'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/chosenType_sj',
                    name: 'chosenType_sj',
                    component: ()=> import( '@/view/hospital_sj/chosenType'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/chosenDept_sj',
                    name: 'chosenDept_sj',
                    component: ()=> import( '@/view/hospital_sj/chosenDept'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/appointment_sj',
                    name: 'appointment_sj',
                    component: ()=> import( '@/view/hospital_sj/appointment'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
                {
                    path: '/time_sj',
                    name: 'time_sj',
                    component: ()=> import( '@/view/hospital_sj/time'),
                    meta: {
                        backgroundImage: 'bg.jpg',
                        index:0
                    },
                },
            ]
        },
        // 404 页面配置
        {
            path: "/:pathMatch(.*)*",  // 捕获所有路径
            name: "404",
            component: () => import('@/view/404'),
        }
    ],
});

const VueRouterPush = Router.prototype.push
Router.prototype.push = function push(to) {
    return VueRouterPush.call(this, to).catch(err => err)
}

export default router;