<template>
  <div class="hospital">
    <div class="left scrollParent">
      <div class="leftList" :style="{ top: '0rem' }">
        <div
          class="leftItem"
          :ref="item.ref"
          v-for="(item, index) in leftList"
          :key="index"
        >
          <div>
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>

    <div class="right">
      <div
        class="messageCenterList scrollParent"
        :key="leftNums"
        v-if="rightList.length > 0"
      >
        <div class="rightList" :style="{ top: '0rem' }">
          <div
            class="messageItem"
            :ref="item.ref"
            v-for="(item, index) in rightList"
            :key="index"
          >
            <div class="itemContent">
              <div class="item_user">
                <img :src="item.img" alt="" />

                <div class="content-detail">
                  <div class="title">
                    {{ item.title }}
                  </div>
                  <div class="sub_title">{{ item.sub_title }}</div>
                  <div class="addr">{{ item.addr }}</div>
                </div>
                <div class="status">
                  <div v-if="item.yy_status === 2">已预约</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="no_content" v-else>
        <div>暂未查询到数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getHospitalList } from '@/api/index'

export default {
  name: 'hospital',
  components: {},
  data() {
    return {
      timer: null,
      leftList: [],
      rightList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      dialogVisible: false,

      messageList: [],
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '预约挂号')
  },
  computed: {},
  watch: {},
  mounted() {
    this.getData()
    if (
      sessionStorage.getItem('indexFocusHOne') &&
      sessionStorage.getItem('indexFocusHTwo')
    ) {
      this.leftNums = Number(sessionStorage.getItem('indexFocusHOne'))
      this.rightNums = Number(sessionStorage.getItem('indexFocusHTwo'))
      sessionStorage.removeItem('indexFocusHOne')
      sessionStorage.removeItem('indexFocusHTwo')
    }
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums++
            this.rightList[this.rightNums].ref = 'active'
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 左侧
        else {
          if (this.leftNums < this.leftList.length - 1) {
            this.rightNums = 0
            this.rightList = []
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            let list = JSON.parse(
              JSON.stringify(this.messageList[this.leftNums].children)
            )
            list.map((item) => {
              if (!item.img.includes('http')) {
                item.img = `${process.env.VUE_APP_API + item.img}`
              }
              item.ref = ''
            })
            this.rightList.push(...list)
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$refs.active[0].classList.add('select')
            })
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums > 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums--
            this.rightList[this.rightNums].ref = 'active'
          }
        }
        // 左侧
        else {
          if (this.leftNums > 0) {
            this.rightNums = 0
            this.rightList = []
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            let list = JSON.parse(
              JSON.stringify(this.messageList[this.leftNums].children)
            )
            list.map((item) => {
              if (!item.img.includes('http')) {
                item.img = `${process.env.VUE_APP_API + item.img}`
              }
              item.ref = ''
            })
            this.rightList.push(...list)
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$refs.active[0].classList.add('select')
            })
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.dialogVisible) {
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          this.rightList[this.rightNums].ref = ''
          this.leftList[this.nextNums].ref = 'active'
          this.nextNums = -1
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (this.dialogVisible) {
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          // console.log(1122)
          return
        }
        // 在左侧
        else if (this.nextNums == -1) {
          if (this.rightList.length > 0) {
            this.leftList[this.leftNums].ref = ''
            this.nextNums = this.leftNums
            this.rightList[this.rightNums].ref = 'active'
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        if (this.nextNums > -1 && this.rightList.length > 0) {
          if (this.leftList[this.leftNums] && this.rightList[this.rightNums]) {
            sessionStorage.setItem('indexFocusHOne', this.leftNums)
            sessionStorage.setItem('indexFocusHTwo', this.rightNums)
            this.$nextTick(() => {
              this.$router.push({
                path: '/department',
                query: {
                  id: this.rightList[this.rightNums].id,
                  title: this.rightList[this.rightNums].title,
                  addr: this.rightList[this.rightNums].addr,
                },
              })
            })
          }
        }
      },
      esc: () => {
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState', true)
      // let leftICon = []
      getHospitalList({
        mac: sessionStorage.getItem('MAC'),
        card_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].card_id_md5,
      })
        .then((res) => {
          this.$store.dispatch('app/setLoadingState', false)
          if (res.code == 200) {
            let leftIConList = JSON.parse(JSON.stringify(res.data.level_list))

            this.leftList = leftIConList.map((item) => ({
              title: item.level_name,
              ref: '',
            }))
            this.leftList[this.leftNums].ref = 'active'
            // this.leftList = leftICon
            this.messageList = JSON.parse(JSON.stringify(res.data.level_list))

            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
              this.$refs.active[0].classList.add('select')
              this.getRightList()
            })
          }
        })
        .catch(() => {
          this.$store.dispatch('app/setLoadingState', false)
        })
    },
    // 右侧数据
    getRightList() {
      let list = JSON.parse(
        JSON.stringify(this.messageList[this.leftNums].children)
      )
      list.map((item) => {
        ;(item.img = `${process.env.VUE_APP_API + item.img}`), (item.ref = '')
      })
      this.rightList = list
      this.hospital_title = this.rightList[this.rightNums].title
      this.hospital_addr = this.rightList[this.rightNums].addr
      // console.log(112233, this.hospital_title)
      if (this.rightList.length > 0) {
        this.nextNums = this.leftNums
        this.leftList[this.leftNums].ref = ''
        this.rightList[this.rightNums].ref = 'active'
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      } else {
        this.nextNums = -1
      }
    },
  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = null
  },
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.hospital {
  width: 16.8rem;
  height: 7rem;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  font-size: 0.2rem;
  color: #e7e7ef;
  position: relative;

  .left {
    width: 2rem;
    border-radius: 0.2rem;
    height: 7rem;
    position: relative;

    .leftList {
      width: 94%;
      text-align: center;
      position: absolute;
      transition: all 0.3s;

      .leftItem {
        height: 0.69rem;
        line-height: 0.77rem;
        margin-bottom: 0.2rem;
        background: #343d74;
        border-radius: 0.2rem;
        position: relative;
        font-weight: bold;
        transition: all 0.3s;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        font-size: 0.34rem;
        color: #e7e7ef;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);

        .badge {
          width: 0.15rem;
          height: 0.15rem;
          border-radius: 50%;
          background: #ff5f5f;
          position: absolute;
          top: 50%;
          right: 0.2rem;
          transform: translateY(-50%);
        }
      }

      .select {
        background: #89a7ff;
        transition: all 0.3s;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(102, 129, 218, 0.6);
      }
    }
  }

  .right {
    width: 15rem;
    height: 7rem;
    margin-left: 0;
    position: relative;

    .messageCenterList {
      width: 14rem;
      height: 100%;
      position: relative;
      margin-left: 0.6rem;

      .rightList {
        width: 100%;
        border-radius: 0.3rem;
        position: absolute;
        //left: 0rem;
        transition: all 0.2s;
        overflow: hidden;

        .messageItem {
          // width: 100%;
          height: 3.3603rem;
          margin-bottom: 0.28rem;
          background-size: 100% 100% !important;
          // background: #ccc !important;
          background: #262954;
          border-radius: 0.3rem;
          overflow: hidden;
          .itemContent {
            padding: 0.5rem;
            .item_user {
              display: flex;
              height: 100%;

              img {
                width: 2.45rem;
                height: 2.45rem;
                border-radius: 50%;
                object-fit: cover;
              }

              .content-detail {
                padding: 0.3rem;
                height: initial !important;

                .title {
                  font-weight: bold;
                  color: #fff;
                  font-size: 0.4rem;
                  margin-bottom: 0.2rem;
                }

                .sub_title {
                  font-size: 0.3rem;
                  color: #b7c0f9;
                  margin-bottom: 0.2rem;
                }

                .addr {
                  font-size: 0.3rem;
                  color: #b7c0f9;
                }
              }

              .status {
                width: 1.2rem;
                height: 0.4rem;
                right: 0rem;
                top: 0rem;
                left: inherit;

                div {
                  position: absolute;
                  top: 0rem;
                  right: -0.05rem;
                  width: 1.2rem;
                  height: 0.4rem;
                  background: #fd8b19;
                  border-radius: 0.05rem;
                  text-align: center;
                  line-height: 0.35rem;
                  font-weight: bold;
                  color: #fff;
                  letter-spacing: 0.03rem;
                }
              }
            }
          }
        }
      }
    }

    .no_content {
      width: 14.1rem;
      height: 6.65rem;
      position: absolute;
      top: 1.2rem;
      //left: 3.4rem;
      overflow: hidden;

      div {
        width: 4rem;
        height: 0.8rem;
        line-height: 0.8rem;
        text-align: center;
        font-size: 0.4rem;
        margin: 2rem auto;
      }
    }
  }
}
</style>
<style lang="less">
.hospital {
  .right {
    .scroll {
      top: 2.25rem !important;
      left: 18.1rem !important;
    }
  }
}
</style>