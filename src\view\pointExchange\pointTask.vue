<template>
  <div class="pointTask">
    <div class="moreList">
      <div
        class="moreItem"
        :ref="item.ref"
        v-for="(item, index) in moreList"
        :key="index"
      >
        <div class="exchange">
          <div class="pic"><img :src="item.pic" alt="" /></div>
          <div class="title">{{ item.title }}</div>
        </div>
      </div>
    </div>
    <div class="point">
      <img src="@/assets/point_rest.png" alt="" />
      <div class="rest">
        我的积分：
        <div class="pic">
          <img src="@/assets/point_icon.png" alt="" />
        </div>
        <span>{{ restPoint }}</span>
      </div>
      <div
        class="sign"
        ref="refSign"
        v-if="isShowSign"
        :style="{ 'background-color': isSign ? '#66ffff' : '#33cc00' }"
      >
        <template v-if="isSign">
          <div class="item">今日签到</div>
          <div class="pic">
            <img src="@/assets/point_icon.png" alt="" />
          </div>
          <div class="num">积分 +{{ signPoint }}</div>
        </template>
        <template v-else>
          <div class="done">已签到</div>
          <div class="pic_sign">
            <img src="@/assets/point_sign.png" alt="" />
          </div>
          <div class="num_sign">+{{ signPoint }}</div>
        </template>
      </div>
    </div>
    <div class="task">积分任务</div>
    <div class="container">
      <div class="left">
        <div
          class="messageCenterList scrollParent"
          v-if="this.leftList.length > 0"
        >
          <div class="leftList" :style="{ top: '0rem' }">
            <div
              class="messageItem"
              :ref="item.ref"
              v-for="(item, index) in leftList"
              :key="index"
            >
              <div
                class="item_user"
                :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }"
              >
                <div class="content">
                  <div class="content-detail">
                    <div class="pic" v-lazy-container="{ selector: 'img' }">
                      <img
                        :data-src="apiBaseUrl + item.img"
                        :data-error="lazyError"
                        alt=""
                      />
                    </div>
                    <div class="title">{{ item.title }}</div>
                  </div>

                  <div class="number">
                    <div class="pic">
                      <img src="@/assets/point_icon.png" alt="" />
                    </div>
                    <div class="num">积分+{{ item.point }}</div>
                  </div>
                </div>
              </div>
              <div class="status" v-if="item.harvest === 1">
                <div>已完成</div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_info" v-else>暂无任务</div>
      </div>
      <el-dialog
        class="popup_box_bg"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        title="日常签到"
      >
        <!--签到-->
        <div class="message_box sendCall" v-if="popupModule == 1">
          <div class="message_content">
            <div class="title">签到成功!</div>
          </div>
        </div>
        <div class="popupBtnList">
          <div
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
          >
            {{ item.text }}
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getPointTask, getRestPoint, insertPoint } from '@/api/index'
export default {
  data() {
    return {
      lazyError: require('@/assets/customer.png'),
      apiBaseUrl: process.env.VUE_APP_API,
      leftList: [],
      leftNums: 0,
      moreList: [
        {
          title: '积分兑换',
          pic: require('@/assets/point_exchange.png'),
          ref: '',
        },
        {
          title: '兑换规则',
          pic: require('@/assets/point_rule.png'),
          ref: '',
        },
        {
          title: '兑换记录',
          pic: require('@/assets/point_record.png'),
          ref: '',
        },
      ],
      moreNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      restPoint: 0,
      firstShow: true,
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      isSign: true,
      isShowSign: true,
      signPoint: 0,
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '用户积分')
  },
  computed: {},
  watch: {},
  mounted() {
    this.getData()
    this.updataTotalPoint()

    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          return
        }
        if (this.nextNums == -2 && this.isShowSign) {
          this.moreList[this.moreNums].ref = ''
          this.$refs.active.splice(0, 1, this.$refs.refSign)
          this.nextNums = -1
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else if (this.nextNums == -1 && this.leftList.length > 0) {
          this.$refs.active = []
          this.nextNums = this.leftNums
          this.leftList[this.leftNums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else if (this.nextNums > -1) {
          if (this.leftNums < this.leftList.length - 1) {
            if (this.leftList[this.leftNums + 3]) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums += 3
              this.leftList[this.leftNums].ref = 'active'
            } else {
              if (
                this.leftNums <
                  this.leftList.length - (this.leftList.length % 3) &&
                this.leftList.length % 3 != 0
              ) {
                this.leftList[this.leftNums].ref = ''
                this.leftNums = this.leftList.length - 1
                this.leftList[this.leftNums].ref = 'active'
              }
            }
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      up: () => {
        if (this.dialogVisible) {
          return
        }
        if (this.nextNums == -1) {
          this.$refs.active = []
          this.moreList[this.moreNums].ref = 'active'
          this.nextNums = -2
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else if (this.nextNums > -1) {
          if (this.leftNums > 0 && this.leftNums - 3 >= 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums -= 3
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          } else {
            this.leftList[this.leftNums].ref = ''
            this.$refs.active.splice(0, 1, this.$refs.refSign)
            this.nextNums = -1
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      left: () => {
        if (this.dialogVisible) {
          return
        }
        if (this.nextNums == -2 && this.moreNums > 0) {
          this.moreList[this.moreNums].ref = ''
          this.moreNums--
          this.moreList[this.moreNums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else if (this.nextNums > -1) {
          if (this.leftNums % 3 != 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      right: () => {
        if (this.dialogVisible) {
          return
        }
        if (this.nextNums == -2 && this.moreNums < this.moreList.length - 1) {
          this.moreList[this.moreNums].ref = ''
          this.moreNums++
          this.moreList[this.moreNums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else if (this.nextNums > -1) {
          if (this.leftNums < this.leftList.length - 1) {
            if (this.leftNums % 3 != 2) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums++
              this.leftList[this.leftNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          }
        }
      },
      enter: () => {
        if (this.nextNums == -2) {
          sessionStorage.setItem('pointIndex', this.moreNums)
          const paths = ['/pointList', '/pointRule', '/pointRecord']
          if (this.moreNums >= 0 && this.moreNums < paths.length) {
            this.$nextTick(() => {
              this.$router.push({
                path: paths[this.moreNums],
              })
            })
          }
        } else if (this.nextNums == -1) {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.$refs.active.push(this.$refs.refSign)
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].fuc(
              this.leftList[this.leftNums]
            )
            return
          }
          if (this.isSign) {
            this.ifSign()
            this.popupBtnList = [
              {
                text: '关闭',
                ref: '',
                fuc: () => {
                  this.dialogVisible = false
                  this.updataTotalPoint()
                  this.getData()
                },
              },
            ]

            this.dialogVisible = true
            this.popupModule = 1
            this.$refs.active = []
            this.popupBtnList[this.popupBtnNums].ref = 'active'
          }
        } else if (this.nextNums > -1) {
          sessionStorage.setItem('taskIndex', this.leftNums)

          if (
            this.leftList[this.leftNums].harvest == 0 &&
            this.leftList[this.leftNums].id != 7
          ) {
            this.$nextTick(() => {
              this.$router.push({
                path: this.leftList[this.leftNums].redirect,
              })
            })
          } else if (this.leftList[this.leftNums].id == 7) {
            if (this.dialogVisible) {
              // 弹窗
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.leftList[this.leftNums].ref = 'active'
              this.dialogVisible = false
              this.popupBtnList[this.popupBtnNums].fuc(
                this.leftList[this.leftNums]
              )
              return
            }
            if (this.isSign) {
              this.ifSign()
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                    this.updataTotalPoint()
                    this.getData()
                  },
                },
              ]
              this.dialogVisible = true
              this.popupModule = 1
              this.leftList[this.leftNums].ref = ''
              this.popupBtnList[this.popupBtnNums].ref = 'active'
            }
          }
        }
      },
      esc: () => {
        if (this.dialogVisible) {
          // this.popupBtnList[this.popupBtnNums].ref = ''
          // this.leftList[this.leftNums].ref = 'active'
          // this.dialogVisible = false
          return
        }
        if (
          sessionStorage.getItem('pointIndex') ||
          sessionStorage.getItem('taskIndex')
        ) {
          sessionStorage.removeItem('pointIndex')
          sessionStorage.removeItem('taskIndex')
        }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        this.popupBtnNums = 0
        this.popupModule = 1
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },
    getData() {
      this.fuc.getPointList((res) => {
        if (res.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data))
          list.map((item) => {
            item.ref = ''
          })
          this.leftList = list
          if (this.leftList.length > 0) {
            this.leftList.find((item) => item.id === 7).harvest == 0
              ? (this.isSign = true)
              : (this.isSign = false)
            this.signPoint = this.leftList.find((item) => item.id === 7).point
            if (sessionStorage.getItem('pointIndex')) {
              this.moreNums = Number(sessionStorage.getItem('pointIndex'))
              this.nextNums = -2
              this.moreList[this.moreNums].ref = 'active'
              sessionStorage.removeItem('pointIndex')
            } else if (sessionStorage.getItem('taskIndex')) {
              this.leftNums = Number(sessionStorage.getItem('taskIndex'))
              this.nextNums = this.leftNums
              this.leftList[this.leftNums].ref = 'active'
              sessionStorage.removeItem('taskIndex')
            } else {
              this.$refs.active = []
              this.nextNums = -1
              this.$refs.active.push(this.$refs.refSign)
            }
          } else {
            this.moreNums = Number(sessionStorage.getItem('pointIndex'))
            this.nextNums = -2
            this.moreList[this.moreNums].ref = 'active'
            sessionStorage.removeItem('pointIndex')
            this.isShowSign = false
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      })
    },

    updataTotalPoint() {
      getRestPoint({
        fk_home_id: this.$store.getters.getUserInfo.home_id,
      }).then((res) => {
        if (res.code == 200) {
          this.restPoint = res.data.point
        }
      })
    },
    ifSign() {
      insertPoint({
        fk_home_id: this.$store.getters.getUserInfo.home_id,
        fk_point_type_id: 7,
      }).then((res) => {
        if (res.code == 200) {
          this.isSign = false
        }
      })
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.pointTask {
  .address {
    display: flex;
    position: absolute;
    top: 1.6rem;
    left: 1.45rem;

    .name {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }

    .name::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }

  .container {
    height: 5.2rem;
    overflow: hidden;
    display: flex;
    margin-top: 0.2rem;
    flex-direction: column;
    // grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
    // gap: 0.16rem;
    font-size: 0.2rem;
    color: #e7e7ef;

    .left {
      width: 16.5rem;
      height: 5.2rem;
      // margin-left: 0;

      .messageCenterList {
        width: 16.5rem;
        height: 100%;
        position: relative;

        .leftList {
          display: flex;
          flex-wrap: wrap;
          // width: 83.6%;
          border-radius: 0.3rem;
          position: absolute;
          // left: 0.8rem;
          transition: all 0.2s;

          .messageItem {
            width: 4.5rem;
            height: 1.51rem;
            display: flex;
            justify-content: center;
            padding: 0.4rem;
            margin-bottom: 0.59rem;
            position: relative;
            margin-right: 0.28rem;
            background-size: 100% 100% !important;
            // background-color: ;
            background: linear-gradient(to bottom, #45427b 65%, #2e3061 35%);
            border-radius: 0.2rem;
            overflow: hidden;

            .item_user {
              display: flex;
              // height: 100%;
              justify-content: center;
              align-items: center;

              .content {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                height: initial !important;
                width: initial !important;

                // margin: initial !important;
                .content-detail {
                  display: flex;
                  // justify-content: center;
                  align-items: center;

                  .pic {
                    width: 1.2rem;
                    height: 1.2rem;

                    img {
                      width: 100%;
                      height: 100%;
                      border-radius: 0.2rem;
                    }
                  }

                  .title {
                    width: 2.7rem;
                    font-size: 0.3rem;
                    font-weight: bold;
                    line-height: 0.55rem;
                    margin-left: 0.3rem;
                  }
                }

                .number {
                  display: flex;
                  margin-top: 0.25rem;

                  .pic {
                    width: 0.45rem;
                    height: 0.45rem;

                    img {
                      width: 100%;
                      height: 100%;
                    }
                  }

                  .num {
                    margin-left: 0.1rem;
                    font-size: 0.3rem;
                    font-weight: bold;
                    line-height: 0.4rem;
                    color: #fff;
                  }
                }
              }

              .introduce {
                height: 0.9rem;
                margin-top: 0.1rem !important;
                margin-bottom: 0 !important;
                font-size: 0.22rem !important;
                font-weight: 500 !important;
                line-height: 0.3rem;
                letter-spacing: 0.03rem;
                -webkit-line-clamp: 3 !important;
                background-image: initial !important;
                -webkit-text-fill-color: #bddb6c !important;
              }
            }

            .status {
              right: 0rem;
              top: 0rem;
              left: inherit;

              div {
                position: absolute;
                top: 0rem;
                right: 0rem;
                width: 1.2rem;
                height: 0.35rem;
                background: #00ff03;
                border-radius: 0.02rem;
                text-align: center;
                line-height: 0.35rem;
                font-weight: bold;
                font-size: 0.25rem;
                color: #000;
                letter-spacing: 0.03rem;
              }
            }
          }

          .messageItem:nth-child(3n + 3) {
            margin-right: 0;
          }
        }
      }

      .no_info {
        position: absolute;
        line-height: 4rem;
        left: 50%;
        transform: translateX(-50%);
        // width: 4rem;
        // height: 7rem;
        text-align: center;
        font-size: 0.6rem;
      }
    }
  }

  .point {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    // padding: 0 0.5rem;
    img {
      width: 100%;
      // width: 11rem;
      height: 1rem;
      border-radius: 0.3rem;
      object-fit: cover;
    }

    .rest {
      position: absolute;
      left: 0.35rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.35rem;
      // font-weight: bold;
      line-height: 0.5rem;
      color: #fff;
      // color: #2074f2;
      letter-spacing: 0.05rem;

      // text-shadow: 0.01rem 0 0 #fff;
      .pic {
        width: 0.45rem;
        height: 0.45rem;

        img {
          width: 100%;
          height: 100%;
        }
      }

      span {
        margin-left: 0.1rem;
        font-size: 0.5rem;
      }
    }

    .sign {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 0.3rem;
      margin-left: 0.5rem;
      width: 8rem;
      height: 0.8rem;
      padding: 0.1rem;
      border-radius: 0.2rem;
      background-color: #66ffff;

      .item {
        width: 1.2rem;
        font-size: 0.3rem;
        font-weight: bold;
        color: #000;
      }

      .pic {
        margin-left: 0.2rem;
        width: 0.45rem;
        height: 0.45rem;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .num {
        margin-left: 0.2rem;
        font-size: 0.3rem;
        font-weight: bold;
        line-height: 0.55rem;
        color: #eb3d55;
      }

      .done {
        width: 1.2rem;
        margin-right: 1.5rem;
        font-size: 0.35rem;
        font-weight: bold;
        color: #fff;
        text-align: center;
      }

      .pic_sign {
        width: 0.45rem;
        height: 0.4rem;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .num_sign {
        margin-left: 0.1rem;
        font-size: 0.35rem;
        font-weight: bold;
        line-height: 0.55rem;
        color: #fff;
      }
    }
  }

  .moreList {
    position: absolute;
    top: 0.92rem;
    left: 7rem;
    display: flex;
    width: 100%;
    .moreItem {
      width: 2rem;
      height: 0.5rem;
      border-radius: 0.15rem;
      margin-left: 0.5rem;

      .exchange {
        display: flex;
        .pic {
          width: 0.55rem;
          height: 0.5rem;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .title {
          width: 1.5rem;
          height: 0.5rem;
          margin-left: 0.05rem;
          font-size: 0.3rem;
          font-weight: bold;
          line-height: 0.5rem;
          color: #fff;
          letter-spacing: 0.05rem;
        }
      }
    }
  }

  .task {
    font-size: 0.35rem;
    // color: #2074f2;
    color: #fff;
    font-weight: bold;
    line-height: 0.55rem;
    margin: 0.5rem 0 0.2rem 0.35rem;
    // text-shadow: 0.02rem 0 0 #fff;
  }
}
</style>
<style lang="less">
.pointTask {
  // position: relative;

  .el-dialog {
    width: 10rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;

      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }

    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;

      .message_box {
        padding: 0.4rem;
        color: #464646;
        font-size: 0.6rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        align-items: center;
        justify-content: center;

        .message_content {
          margin-top: 0.3rem;
        }
      }

      .cancel {
        flex-direction: column;

        div {
          text-align: center;
        }

        div:nth-child(2) {
          margin-top: 0.1rem;
          color: red;
          line-height: 0.48rem;
        }

        //align-items: normal !important;
      }

      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;

        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
        }
      }
    }
  }

  .container {
    .left {
      .scroll {
        top: 4.5rem !important;
        left: 18rem !important;
      }
    }
  }

  .popup_box_bg {
    .el-dialog {
      width: 10rem;
      height: 7rem;
      background: url('../../assets/point_tip.png') no-repeat;
      background-size: 100% 100%;

      .el-dialog__header {
        display: none;
      }

      .el-dialog__body {
        background: transparent;

        .info {
          display: flex;
          justify-content: center !important;
          align-items: center !important;
          margin-top: -0.4rem;

          .title {
            display: flex;
            flex-direction: column;
            align-items: center !important;
            text-align: center !important;
            margin-top: -0.3rem;

            span {
              text-align: center !important;
              font-size: 0.5rem !important;
              color: #e7f1fa !important;
              letter-spacing: 0.14rem !important;
            }
          }

          .phone {
            width: 7rem;
            text-align: center;
            position: absolute;
            top: 1.3rem;
            font-size: 0.3rem !important;
            color: yellow !important;
          }

          .tip {
            color: #3288dc;
            font-size: 0.4rem;
            margin-top: 1.2rem;
          }
        }
      }
    }
  }
}
</style>