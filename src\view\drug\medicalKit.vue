<!--我的药箱-->
<template>
  <div class="medicalKit">
    <div class="container">
      <div class="right">
        <div class="messageCenterList scrollParent" :key="removeDrugNums" v-if="rightList.length > 0">
          <div class="rightList" :style="{ top: '0rem' }" v-if="rightList.length > 0">
            <div class="messageItem" :ref="item.ref" v-for="(item, index) in rightList" :key="index">
              <div class="item_user" :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }">
                <div class="contentInfo">
                  <div  class="pic" v-lazy-container="{ selector: 'img' }">
                    <img :data-src="item.img"  :data-error="lazyError" alt="">
                    <div class="choose_tip" v-if="item.choose_tip">
                      已选
                    </div>
                  </div>

                  <div class="info">
                    <div class="title">{{ item.title }}</div>
                    <div class="infoItem">规格: {{item.model}}</div>
                    <div class="infoItem">生产商: {{item.factory}}</div>
                  </div>

                </div>
                <div class="price">
                  <span>￥</span>
                  <span>{{item.price.toFixed(2)}}</span>
                </div>

              </div>
            </div>
          </div>
        </div>
        <div class="no_content" style="left: 40%;transform: translateX(-50%)" v-else>暂无药品</div>
      </div>

      <div class="tab_list scrollParent" :key="this.unfinishList.length" v-if="this.unfinishList.length > 0">
        <div class="list" :style="{ top: '0rem',width: this.unfinishList.length > 1 ? '94.6%' : '100%' }">
          <div
            class="unfinishList"
            :ref="item.ref"
            v-for="(item, index) in unfinishList"
            :key="index"
          >
            <div class="unfinishListInfo">
              <div class="status" :style="{color:statusList[item.status].color}">{{ statusList[item.status].text }}</div>
              <div class="orderNums">数量:{{JSON.parse(item.dispensing_list).length}}</div>
              <div class="apply">
                <div class="title">申请时间</div>
                <div class="time">{{ item.created_at }}</div>
              </div>
              <div class="appoint">
                <div class="title">订单详情</div>
                <div class="time" style="max-height: 1.2rem;overflow: hidden;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 3;text-overflow: ellipsis;">
                  <div v-for="(items,keys) in JSON.parse(item.dispensing_list)" :key="keys">
                    {{keys + 1}}:{{ items.title }}
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <div class="no_content" v-else>暂无近期记录</div>

      <div class="buy_btn" ref="buy_btn" :style="{opacity: selectMedical.length > 0 ? 1 : 0.6}">
        <span>配药申请</span>
        <span class="shop_cont" v-if="selectMedical.length > 0">{{selectMedical.length}}</span>
      </div>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      :show-close="false"
      :close-on-click-modal="false"
      @opened="popupOpend"
      @close="popupClose"
      :title="popupModule == 4 ? '操作' : '订单详情'"
      :custom-class="popupModule == 4 ? 'btnListPopup' : ''"
    >
      <!--发起预约-->
      <div class="message_box sendCall scrollParent" v-if="popupModule == 1">
        <div class="message_content" ref="popupRef" :style="{top: '0rem'}">
          <div class="messageTitle">您即将发起下列药品的配药申请：</div>
          <div class="message_item" v-for="(item,index) in selectMedical" :key="index" >
            <div class="itemType">
              <span class="type">药品{{numberChinese[index]}}:</span>
              <span class="value">{{item.title}}</span>
            </div>
            <div class="itemType">
              <span class="type">生产商:</span>
              <span class="value">{{item.factory}}</span>
            </div>
            <div class="price">
              <span>￥</span>
              <span>{{item.price.toFixed(2)}}</span>
            </div>
          </div>
        </div>

      </div>

      <!--取消预约-->
      <div class="message_box sendCall scrollParent" v-if="popupModule == 2">
        <div class="message_content"  ref="popupRef" :style="{top: '0rem'}">
          <div class="status" style="margin-bottom: 0.1rem">
            当前状态：{{ unfinishList[unfinishNums] && statusList[unfinishList[unfinishNums].status].text  }}
          </div>
          <div class="apply"  style="margin-bottom: 0.1rem">
            申请时间：{{ unfinishList[unfinishNums] && unfinishList[unfinishNums].created_at }}
          </div>
          <div class="apply">
            商家：{{ unfinishList[unfinishNums] && unfinishList[unfinishNums].fk_dispensing_pharmacy_title }}
          </div>
          <div class="orderInfo" v-if="unfinishList[unfinishNums]" style="margin-top: 0.1rem">
            <div class="message_item" v-for="(item,index) in JSON.parse(unfinishList[unfinishNums].dispensing_list)">
              <div class="itemType">
                <span class="type">药品{{numberChinese[index]}}:</span>
                <span class="value">{{ item.title }}</span>
              </div>
              <div class="itemType">
                <span class="type">生产商:</span>
                <span class="value">{{ item.factory }}</span>
              </div>
              <div class="price">
                <span>￥</span>
                <span>{{item.price.toFixed(2)}}</span>
              </div>
            </div>

          </div>
        </div>
      </div>

      <div class="message_box" v-if="popupModule == 3"  style="text-align: center; font-size: 0.37rem; justify-content: center !important;align-items: center !important;" v-html="popupMessage"></div>


      <div class="popupBtnList">
        <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index" :style="{opacity: item.opacity ? 0.6 : 1}">
          {{ item.text }}
        </div>
      </div>
    </el-dialog>

    <div class="address">
      <div class="name">{{ this.supplierInfo.title }}</div>
<!--      <div class="names" style="position: absolute;width:max-content;left: 50%;transform: translateX(-50%);">客服热线:{{ this.supplierInfo.telephone }}</div>-->
    </div>
    <div class="tel" v-if="this.supplierInfo.remark">
      <img :src="require('@/assets/quan.png')" alt="">
      {{this.supplierInfo.remark}}
    </div>
    <div class="done"><div class="item">服务记录</div></div>
  </div>
</template>
  
<script>
import { getMedicalKitList, getMedicalHomeOrderList, cancelMedicalHomeOrder, removeMedicalKitDrug, createMedicalHomeOrder } from '@/api/index'

export default {
  name: 'medicalKit',
  components: {},
  inject: ['reload'],
  data() {
    return {
      numberChinese:['一','二','三','四','五','六','七','八','九','十','十一','十二','十三','十四','十五','十六','十七','十八','十九','二十'],
      lazyError:  require('@/assets/fubao_logo.png'),
      supplierInfo: null,
      firstSend: true,
      selectMedical:[],  // 选中的药品

      hospital_addr: '', //医院地址
      patient_name: '',
      patient: '',

      rightList: [],
      unfinishList: [],
      statusList: [],

      nextNums: -1, // -1  说明焦点在左侧   > -1  在中间侧  -2在右侧上方  -3 购物车按钮
      rightNums: 0,
      unfinishNums: 0,
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      messageList: [],

      removeDrugNums: 0
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '我的药箱')
    this.supplierInfo =  JSON.parse(sessionStorage.getItem('supplierInfo'))
    this.$store.dispatch('index/setFocusDom', null);
  },
  computed: {},
  watch: {},
  mounted() {
    this.statusList = JSON.parse(localStorage.getItem('statusList'))

    this.hospital_addr = this.$route.query.addr
    this.patient = this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].name
    this.getData()
    if (sessionStorage.getItem('indexFocusDOne') && sessionStorage.getItem('indexFocusDTwo')) {
      this.rightNums = Number(sessionStorage.getItem('indexFocusDTwo'))
      sessionStorage.removeItem('indexFocusDOne')
      sessionStorage.removeItem('indexFocusDTwo')
    }
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if ((Math.abs(styleTop) +scrollPageTop)  * fontSize > this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight) {
                this.$refs.popupRef.style.top = styleTop - (scrollTop/fontSize - Math.abs(styleTop)) + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * ((scrollTop/fontSize - Math.abs(styleTop)) * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight)) + 'px'
                return
              }
              if (scrollTop > 0) {
                this.$refs.popupRef.style.top = styleTop - scrollPageTop + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))   + 'px'
              }
            }
          } else
          if (this.popupModule == 4 && this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 左侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1) {
            if (this.rightList[this.rightNums + 2]) {
              this.rightList[this.rightNums].ref = ''
              this.rightNums += 2
              this.rightList[this.rightNums].ref = 'active'
            } else {
              if (
                this.rightNums < this.rightList.length - (this.rightList.length % 2) &&
                this.rightList.length % 2 != 0
              ) {
                this.rightList[this.rightNums].ref = ''
                this.rightNums = this.rightList.length - 1
                this.rightList[this.rightNums].ref = 'active'
              }
            }
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 记录
        else if (this.nextNums == -2) {
          if (this.unfinishNums < this.unfinishList.length - 1) {
            this.unfinishList[this.unfinishNums].ref = ''
            this.unfinishNums++
            this.unfinishList[this.unfinishNums].ref = 'active'

            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          } else {
            if (this.selectMedical.length > 0) {
              this.unfinishList[this.unfinishNums].ref = ''
              this.nextNums = -3
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.buy_btn)
              })
            }
            return
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if (scrollTop > 0) {
                if ((styleTop + scrollPageTop) > 0) {
                  this.$refs.popupRef.style.top = '0rem'
                  scrollBar.style.top = '0px'
                  return
                } else {
                  this.$refs.popupRef.style.top = styleTop + scrollPageTop + 'rem'
                  scrollBar.style.top = barTop - (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))  + 'px'
                }
              }
            }
          } else
          if (this.popupModule == 4 && this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 左侧
        if (this.nextNums > -1) {
          if (this.rightNums > 0 && this.rightNums - 2 >= 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums -= 2
            this.rightList[this.rightNums].ref = 'active'
          }
          // console.log(1212,this.rightList[this.rightNums]);
        }
        // 记录
        else if (this.nextNums == -2) {
          if (this.unfinishNums > 0 && this.unfinishNums - 1 >= 0) {
            this.unfinishList[this.unfinishNums].ref = ''
            this.unfinishNums--
            this.unfinishList[this.unfinishNums].ref = 'active'
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 在购物车按钮上
        else if (this.nextNums == -3) {
          if (this.unfinishList.length > 0 ) {
            this.unfinishList[this.unfinishNums].ref = 'active'
            this.nextNums = -2
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupModule != 4 && this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 左侧
        if (this.nextNums > -1) {
          if (this.rightNums % 2 != 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums--
            this.rightList[this.rightNums].ref = 'active'
          }
        }
        // 记录
        else if (this.nextNums == -2 && this.rightList.length > 0) {
          this.unfinishList[this.unfinishNums].ref = ''
          this.rightList[this.rightNums].ref = 'active'
          this.nextNums = this.rightNums
        }
        // 按钮
        else if (this.nextNums == -3) {
          this.rightList[this.rightNums].ref = 'active'
          this.nextNums = this.rightNums
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupModule != 4 && this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 左侧
        if (this.nextNums > -1) {
          if (this.rightNums >= 0) {
            if (this.rightNums % 2 != 1 && this.rightNums != this.rightList.length - 1) {
              this.rightList[this.rightNums].ref = ''
              this.rightNums++
              this.rightList[this.rightNums].ref = 'active'
            } else {
              if (this.selectMedical.length > 0) {
                this.rightList[this.rightNums].ref = ''
                this.nextNums = -3
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.buy_btn)
                })
                return
              } else {
                if (this.unfinishList.length > 0) {
                  this.rightList[this.rightNums].ref = ''
                  this.unfinishList[this.unfinishNums].ref = 'active'
                  this.nextNums = -2
                }
              }

            }
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        if (this.dialogVisible) {
          if (this.popupBtnList[this.popupBtnNums].opacity) {
            return
          }
          // 弹窗
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.popupBtnList[this.popupBtnNums].fuc(this.rightList[this.rightNums])
          this.dialogVisible = false

          // 取消订单关闭时
          if (this.nextNums > -1 && this.rightList[this.rightNums]) {
            this.rightList[this.rightNums].ref = 'active'
          } else
          if (this.nextNums != -3 && this.unfinishList[this.unfinishNums]) {
            this.nextNums = -2
            this.unfinishList[this.unfinishNums].ref = "active"
          }
          return
        }
        // 左侧
        if (this.nextNums > -1 && this.rightList.length > 0) {
          this.popupBtnList = [
            {
              text: '选择',
              ref: '',
              fuc: this.selectThisDrug,
            },
            {
              text: '移除',
              ref: '',
              fuc: this.removeThisDrug,
            },
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          if (this.rightList[this.rightNums].choose_tip) {
            this.popupBtnList[0].text = "取消选择"
            this.popupBtnList[1].opacity = true
          }

          this.dialogVisible = true
          this.popupModule = 4
          this.rightList[this.rightNums].ref = ''
          this.popupBtnList[this.popupBtnNums].ref = 'active'

          return
        } else
        if (this.nextNums == -2 && this.unfinishList.length > 0) {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.unfinishList[this.unfinishNums].ref = 'active'
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].fuc(this.unfinishList[this.unfinishNums])
            return
          }
          if (this.unfinishList[this.unfinishNums]) {
            if(this.unfinishList[this.unfinishNums].status == 2){

              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                },
                {
                  text: '取消订单',
                  ref: '',
                  fuc: this.cancelOrder,
                },
              ]
            }else{
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                },
              ]
            }
            this.dialogVisible = true
            this.popupModule = 2
            this.unfinishList[this.unfinishNums].ref = ''
            this.popupBtnList[this.popupBtnNums].ref = 'active'
          }
        } else
        if (this.nextNums == -3 && this.selectMedical.length > 0) {
          this.rightList[this.rightNums].ref = ''
          this.popupModule = 1
          this.popupBtnNums =  0
          this.popupBtnList = [
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            },{
              text: '确认',
              ref: '',
              fuc: this.sendOrder,
            },
          ]
          this.popupBtnList[this.popupBtnNums].ref="active"
          this.dialogVisible = true
        }
      },
      esc: () => {
        if (this.dialogVisible) {
          // this.popupBtnList[this.popupBtnNums].ref = ''
          // this.unfinishList[this.unfinishNums].ref = 'active'
          // this.dialogVisible = false
          return
        }
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      this.$nextTick(() => {
        if (this.$refs.active.length > 0) {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        } else {
          this.$store.dispatch('index/setFocusDom', this.$refs.buy_btn)
        }
        document.getElementById('focus_border').style.borderColor = '#fff'
        setTimeout(()=>{
          this.popupBtnNums = 0
          this.popupModule = 0
        },5)

      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },
    arrFilter(fk_id) {
      let num = 0
      this.selectMedical.map(item=>{
        if (item.fk_dispensing_class_id == fk_id) {
          num++
        }
      })
      return num
    },
    selectThisDrug() {
      if (this.rightList[this.rightNums].choose_tip) {
        this.selectMedical.map((item,index)=>{
          if (item.id == this.rightList[this.rightNums].id && item.number == this.rightList[this.rightNums].number) {
            this.selectMedical.splice(index, 1);
          }
        })
      } else {
        this.selectMedical.push(this.rightList[this.rightNums])
      }

      this.rightList[this.rightNums].choose_tip = !this.rightList[this.rightNums].choose_tip
    },
    getData() {
      this.$store.dispatch('app/setLoadingState',true)
      getMedicalKitList({
        fk_dispensing_pharmacy_id: this.supplierInfo.fk_supplier_id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
      })
      .then((res) => {
        this.$store.dispatch('app/setLoadingState',false)
        if (res.code == 200) {
            let list = JSON.parse(JSON.stringify(res.data))
            list.map((item) => {
              item.ref = ''
              item.choose_tip = false
              this.selectMedical.map((items, index) => {
                if (items.id == item.id && items.number == item.number) {
                  item.choose_tip = true
                }
              })
              if (item.img) {
                item.img = item.img.indexOf('http') > -1 ? item.img : process.env.VUE_APP_API + item.img
              }
            })
            this.rightList = list

            this.nextNums = 0
            this.rightList[this.rightNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
        }
        this.getOrderList()
      }).catch(()=>{
        this.$store.dispatch('app/setLoadingState', false)
        this.getOrderList()
      })
    },
    // 获取近期订单
    getOrderList() {
      getMedicalHomeOrderList({
        fk_dispensing_pharmacy_id: this.supplierInfo.fk_supplier_id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
      })
      .then(res=>{
        this.unfinishList = []
        this.unfinishNums = 0
        this.removeDrugNums++
        if (res.code == 200 && res.data.length > 0) {
            let unfinishListClone = JSON.parse(JSON.stringify(res.data))
            unfinishListClone.map((item) => {
              item.ref = ''
            })
            this.unfinishList = unfinishListClone

            if (this.rightList.length == 0) {
              this.nextNums = -2
              this.unfinishList[this.unfinishNums].ref = "active"

              this.$nextTick(()=>{

                this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                this.fuc.setScroll()
              })
            }
          this.$nextTick(()=>{
            this.fuc.setScroll()
          })

        }
      })
      .catch(error=>{

      })
    },
    sendOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      createMedicalHomeOrder({
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        dispensing_list: JSON.stringify(this.selectMedical)
      })
      .then(res=>{
        this.$store.dispatch('app/setLoadingState', false)
        if (res.code == 200) {
          this.selectMedical = []
          this.getOrderList()
          this.rightList.map(item=>{
            item.choose_tip = false
          })

          this.popupModule = 3
          this.$refs.active = ''

          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
                this.rightList[this.rightNums].ref = "active"
                this.nextNums = this.rightNums
              },
            },
          ]
          this.popupMessage = '<br>配药申请发送成功!</br>'
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      })
      .catch(error=>{
        this.$store.dispatch('app/setLoadingState', false)
        this.popupModule = 3
        this.$refs.active = ''
        this.popupBtnList = [
          {
            text: '确认',
            ref: 'active',
            fuc: () => {
              this.dialogVisible = false
            },
          },
        ]
        this.popupMessage = '<br>申请配药失败</br>'
        if (error.response && error.response.data && error.response.data.msg) {
          this.popupMessage += error.response.data.msg
        }
        this.popupBtnNums = 0
        this.dialogVisible = true

      })

    },
    cancelOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      cancelMedicalHomeOrder({
        id: this.unfinishList[this.unfinishNums].id
      }).then(
        (res) => {
          if (res.code == 200) {
            this.popupModule = 3
            this.unfinishList[this.unfinishNums].ref = ''
            // this.unfinishList = []

            // this.$nextTick(()=>{
            // this.unfinishList = res.data
            this.unfinishList[this.unfinishNums].status = 3
            // })

            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false

                  this.unfinishList[this.unfinishNums].ref = 'active'
                  this.nextNums = -2
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active)
                  })
                },
              },
            ]
            this.popupMessage = '<div style="margin: auto;">订单取消成功!</div>'

            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.unfinishList[this.unfinishNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
                this.unfinishList[this.unfinishNums].ref = 'active'
                this.nextNums = -2
              },
            },
          ]
          this.popupMessage = '<br/>取消失败!<br>'
          if (error.response && error.response.data && error.response.data.msg) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },

    removeThisDrug() {
      this.$store.dispatch('app/setLoadingState',true)
      removeMedicalKitDrug({
        id: this.rightList[this.rightNums].id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
      })
      .then(res=>{
        this.$store.dispatch('app/setLoadingState',false)
        if (res.code == 200) {
          this.rightList = []
          this.removeDrugNums++
          let list = JSON.parse(JSON.stringify(res.data))
          list.map((item) => {
            item.ref = ''
            item.choose_tip = false
            this.selectMedical.map((items, index) => {
              if (items.id == item.id && items.number == item.number) {
                item.choose_tip = true
              }
            })
            if (item.img) {
              item.img = item.img.indexOf('http') > -1 ? item.img : process.env.VUE_APP_API + item.img
            }
          })
          this.rightList = list

          this.nextNums = 0
          if (this.rightList[this.rightNums]) {
            this.rightList[this.rightNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
              this.fuc.setScroll()

            })
          } else {
            if (this.rightList[this.rightNums-1]) {
              this.rightNums = this.rightList.length - 1
              this.rightList[this.rightNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
                this.fuc.setScroll()
              })
            } else {
              this.$store.dispatch('index/setFocusDom', null)
            }
          }

        }

      })
      .catch(err=>{
        this.$store.dispatch('app/setLoadingState',false)
      })
    }
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
  <style lang='less' scoped>
.medicalKit {
  .tel {
    position: absolute;
    bottom: 0.54rem;
    right: 1rem;
    letter-spacing: 0.03rem;
    font-size: 0.28rem;
    color: #B2BAEF;
    display: flex;
    align-items: center;
    img {
      display: block;
      width: 0.32rem;
      height: 0.32rem;
      margin-right: 0.04rem;
      margin-top: 0.03rem;
    }
  }
  .address {
    width: 60%;
    display: flex;
    // width: 7.8rem;
    position: absolute;
    top: 1.6rem;
    left: 1.45rem;
    .name,.names {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .name::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .done {
    position: absolute;
    top: 1.6rem;
    right: 3.65rem;
    .item {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .item::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .container {
    height: 7rem;
    overflow: hidden;
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 每行显示两个元素 */
    gap: 0.16rem;
    font-size: 0.2rem;
    color: #e7e7ef;
    .noData {
      div {
        font-size: 0.5rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -200%);
        letter-spacing: 0.05rem;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
      }
    }
    .right {
      width: 16.8rem;
      height: 7rem;
      margin-left: 0;
      //padding-left: 0.1rem;
      //margin-left: 0.05rem;
      .item_title {
        height: 0.6rem;
        line-height: 0.6rem;
        background: #ccc;
        font-size: 0.28rem;
        font-weight: bold;
        color: #000;
        padding-left: 0.2rem;
      }
      .item_list {
        //height: calc(100% - 0.76rem);
        height: 100%;
        //background: #ccc;
        border-radius: 0.26rem;

        overflow: hidden;

        position: relative;
        .friendsList {
          width: 100%;
          display: grid;
          grid-template-columns: repeat(3, 1fr); /* 每行显示两个元素 */
          //gap: 0.16rem;
          gap: 0;
          position: absolute;
          top: 0;
          transition: all 0.2s;
          //.friendsItem:nth-child(3n + 3) {
          //  margin-right: 0;
          //}
        }
      }

      .messageCenterList {
        width: 12.12rem;
        height: 7rem;
        position: relative;
        .rightList {
          display: flex;
          flex-wrap: wrap;
          border-radius: 0.3rem;
          position: absolute;
          transition: all 0.2s;
          .messageItem {
            width: 5.74rem;
            height: 3.3603rem;
            margin-bottom: 0.28rem;
            // margin-right: 0.28rem;
            background-size: 100% 100% !important;
            // background: #ccc !important;
            background: #262954;
            border-radius: 0.3rem;
            overflow: hidden;
            .item_user {
              display: flex;
              flex-direction: column;
              height: 100%;
              position: relative;
              img {
                width: 1.62rem;
                height: 2.2rem;
                margin: 0.26rem 0.16rem;
                border-radius: 0.06rem;
                object-fit: cover;
                //object-fit: contain;
                //background: #fff;
              }

              .contentInfo {
                height: initial !important;
                display: flex;
                flex-direction: row;
                text-align: left;
                font-size: 0.26rem;
                .pic {
                  .choose_tip {
                    position: absolute;
                    top: 0.26rem;
                    left: 0.16rem;
                    width: 0.7rem;
                    height: 0.35rem;
                    background: #FD8B19;
                    border-radius: 0.05rem;
                    font-size: 0.22rem;
                    text-align: center;
                    line-height: 0.35rem;
                    font-weight: bold;
                    color: #fff;
                    letter-spacing: 0.03rem;
                  }
                }
                .info {
                  width: 3.6rem;
                  .title {
                    margin-top: 0.24rem;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    font-weight: bold;
                    color: #fff;
                  }
                  .infoItem {
                    font-size: 0.26rem;
                    color: #B7C0F9;
                    margin-top: 0.2rem;
                  }
                }
              }
              .price {
                width: 1.8rem;
                font-size: 0.36rem;
                color: #F64D23;
                text-align: left;
                position: absolute;
                left: 0.15rem;
                bottom: 0.2rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-weight: bold;
                span:nth-child(1) {
                  font-size: 0.28rem;
                }
              }
            }
          }
          .messageItem:nth-child(2n + 1) {
            margin-right: 0.28rem;
          }
        }
      }
    }
    .tab_list {
      width: 4.22rem;
      height: 5.6rem;
      position: relative;
      right: 4.38rem;
      overflow: hidden;
      .list {
        margin-right: 0.22rem;
        position: absolute;
        transition: all 0.3s;
        .unfinishList {
          height: 3.3603rem;
          margin-bottom: 0.28rem;
          background: #262954;
          border-radius: 0.24rem;
          font-size: 0.28rem;
          font-weight: bold;
          position: relative;
          .unfinishListInfo {
            padding: 0.15rem 0.35rem;
          }
          .status {
            color: #3ce1ae;
            margin-bottom: 0.1rem;
            letter-spacing: 0.03rem;
          }
          .orderNums {
            position: absolute;
            right: 0.35rem;
            top: 0.25rem;
            color: red;
          }
          .apply,
          .appoint,
          .depart,
          .doctor {
            margin-bottom: 0.1rem;
            .title {
              color: #b6c0fd;
              margin-right: 0.06rem;
            }
          }
          .depart,
          .doctor {
            display: flex;
          }
        }
      }
    }
    .buy_btn {
      font-size: 0.4rem;
      width: 4.22rem;
      height: 1rem;
      /* background-image: linear-gradient(to right, #617EB6, #4B68A4); */
      font-weight: bold;
      color: #E9EDF6;
      position: absolute;
      right: 1.2rem;
      bottom: 1.6rem;
      text-align: center;
      border-radius: 0.2rem;
      line-height: 1rem;
      letter-spacing: 0.1rem;
      background-image: linear-gradient(to right, #617EB6, #4967A5);
      transition: all 0.3s !important;
      img {
        display: block;
        width: 0.6rem;
        height: 0.6rem;
        position: absolute;
        top: 50%;
        left: 15%;
        transform: translateY(-50%);
      }
      span {
        display: block;
        text-indent: 0.4rem;
      }
      .shop_cont {
        text-indent: 0;
        padding: 0.03rem 0.1rem;
        position: absolute;
        right: 0.2rem;
        top: 0.1rem;
        background: #FB291A;
        font-size: 0.22rem;
        line-height: initial;
        letter-spacing: 0;
        border-radius: 50%;
      }
    }
    .no_content {
      position: absolute;
      right: 1.5rem;
      line-height: 5.3rem;
      width: 4rem;
      height: 5.3rem;
      text-align: center;
      font-size: 0.4rem;
    }
  }
}
</style>
<style lang="less">
.medicalKit {
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    // background: repeating-linear-gradient(to right, #c98693, #a95361);
    background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 35px;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
            to bottom,
            rgba(255, 255, 255, 1),
            rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        .title,
        .detail,
        .price {
          display: flex;
          font-size: 0.35rem;
          font-weight: bold;
          //padding-bottom: 0.22rem !important;
          letter-spacing: 0.03rem;
        }
        .title {
          span {
            color: #f64e23;
            margin-left: 0.2rem;
          }
        }
        .price {
          span {
            color: #f64e23;
          }
        }
      }

      .cancel,.sendCall {
        width: calc(100% - 0.8rem);
        height: 4.26rem;
        //top: 0.4rem;
        //left: 0.4rem;
        padding: 0;
        margin-top: 0.4rem;
        margin-left: 0.4rem;
        //position: relative;
        overflow: hidden;
        position: relative;
        .message_content {
          width: 100%;
          position: absolute;
          transition: all 0.3s;
          .message_item {
            display: flex;
            flex-direction: row;
            font-size: 0.4rem;
            margin-bottom: 0.14rem;
            letter-spacing: 0.03rem;
            .type {
              width: 1.9rem;
              font-size: 0.4rem;
            }
          }
          .orderItem {
            letter-spacing: 0.03rem;
            font-size: 0.4rem !important;
            div {
              margin-bottom: 0.2rem;
            }
          }
        }
        .scroll {
          top: 1.8rem !important;
          left: auto !important;
          right: 0.8rem !important;
        }
      }
      .cancel {
        .message_item {
          font-size: 0.34rem !important;
          .type {
            font-size: 0.34rem !important;
          }
        }
      }
      .sendCall {
        font-size: 0.36rem !important;
        color: #464646;
        font-weight: bold;
        letter-spacing: 0.03rem;
        .messageTitle {
          padding-bottom: 0.22rem;
          letter-spacing: 0.03rem;
        }
        .message_item {
          flex-direction: column !important;
          font-size: 0.36rem !important;
          .type,.value {
            display: inline-block;
            font-size: 0.3rem !important;
          }
          .type {
            width: 1.2rem !important;
          }
        }
      }
      .result {
        display: flex;
        justify-content: center !important;
        align-items: center !important;
      }
      .info {
        display: flex;
        justify-content: center !important;
        align-items: center !important;
        margin-top: -0.4rem;
        // background: url(http://192.168.6.212/ptyanglaotv/assets/zc_tan_new.png) no-repeat !important;
        .title {
          display: flex;
          flex-direction: column;
          align-items: center !important;
          text-align: center !important;

          // margin-top: 0.9rem;
          span {
            font-size: 0.5rem;
            color: #464646 !important;
            letter-spacing: 0.14rem;
          }
        }
        .phone {
          width: 7rem;
          text-align: center;
          position: absolute;
          top: 2.3rem;
          // left: 50%;
          // transform: translateX(-50%);
          font-size: 0.3rem;
          // color: yellow;
          color: #464646;
        }
        .tip {
          text-align: center;
          color: #3288dc;
          font-size: 0.4rem;
          margin-top: 1.2rem;
        }
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.1rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: -0.05rem;
          // background: repeating-linear-gradient(to right, #c98693, #a95361);
          background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
        }
      }
    }
  }
  .btnListPopup {
    height: max-content !important;
    .el-dialog__header {
      height: 1.1rem !important;
      padding: 0 !important;
      .el-dialog__title {
        line-height: 1.1rem !important;
      }
    }
    .el-dialog__body {
      position: relative !important;
      width: 100% !important;
      left: 0 !important;
      border-top-left-radius: 0 !important;
      border-top-right-radius: 0 !important;
      height: max-content !important;
      .popupBtnList {
        flex-direction: column !important;
        div {
          width: 8.1rem;
          margin: auto;
          margin-bottom: 0.3rem;
        }
      }
    }
  }
}
</style>