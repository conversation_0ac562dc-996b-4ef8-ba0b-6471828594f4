<template>
  <div class="appointment">
    <div class="container">
      <div class="left">
        <div
          class="messageCenterList scrollParent"
          v-if="this.leftList.length > 0"
        >
          <div class="leftList" :style="{ top: '0rem' }">
            <div
              class="messageItem"
              :ref="item.ref"
              v-for="(item, index) in leftList"
              :key="index"
            >
              <div class="item_user">
                <div class="content">
                  <div class="date">{{ item.date }}</div>
                  <div class="week">{{ item.dayOfWeek }}</div>
                  <div class="info" v-if="item.has_fq == 0">无可预约时间</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_info" v-else>暂无内容</div>
      </div>
      <!-- <div class="no_content" v-else>暂无记录</div> -->
    </div>
    <div class="btnList_bottom">
      <div class="btnList">
        <div
          class="btnItem"
          v-for="(item, index) in btnList"
          :key="index"
          :ref="item.ref"
        >
          <div class="text">{{ item.text }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
    
    <script>
import { getSJDateList, cancelAppointment } from '@/api/index'

export default {
  components: {},
  data() {
    return {
      unfinishList: [],
      leftList: [],
      rightList: [],
      statusList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧  -2在最右边
      rightNums: 0,
      unfinishNums: 0,
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      btnList: [
        {
          text: '重新开始',
          ref: '',
        },
        {
          text: '退出',
          ref: '',
        },
      ],
      btnNums: 0,
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '预约挂号-选择日期')
    this.$store.dispatch('index/setFocusDom', null)
  },
  computed: {},
  watch: {},
  mounted() {
    this.statusList = JSON.parse(localStorage.getItem('statusList'))
    if (sessionStorage.getItem('indexFocusAppment')) {
      this.leftNums = Number(sessionStorage.getItem('indexFocusAppment'))
      sessionStorage.removeItem('indexFocusAppment')
    }
    this.getData()

    this.fuc.KeyboardEvents({
      down: () => {
        if (this.nextNums > -1) {
          if (
            this.leftNums < this.leftList.length - 1 &&
            this.leftList.length > 0
          ) {
            if (this.leftList[this.leftNums + 4]) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums += 4
              this.leftList[this.leftNums].ref = 'active'
            } else {
              if (
                this.leftNums <
                  this.leftList.length - (this.leftList.length % 4) &&
                this.leftList.length % 4 != 0
              ) {
                this.leftList[this.leftNums].ref = ''
                this.leftNums = this.leftList.length - 1
                this.leftList[this.leftNums].ref = 'active'
              } else {
                this.leftList[this.leftNums].ref = ''
                this.nextNums = -1
                this.btnList[this.btnNums].ref = 'active'
              }
            }
          } else {
            this.leftList[this.leftNums].ref = ''
            this.nextNums = -1
            this.btnList[this.btnNums].ref = 'active'
          }
        }

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.nextNums == -1 && this.leftList.length > 0) {
          this.btnList[this.btnNums].ref = ''
          this.nextNums = this.leftNums
          this.leftList[this.leftNums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else if (this.nextNums > -1) {
          if (this.leftNums > 0 && this.leftNums - 4 >= 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums -= 4
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      left: () => {
        if (this.nextNums > -1) {
          if (this.leftNums % 4 != 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        } else if (this.nextNums == -1) {
          if (this.btnNums > 0) {
            this.btnList[this.btnNums].ref = ''
            this.btnNums--
            this.btnList[this.btnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      right: () => {
        if (this.nextNums > -1) {
          if (this.leftNums < this.leftList.length - 1) {
            if (this.leftNums % 4 != 3) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums++
              this.leftList[this.leftNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          }
        } else if (this.nextNums == -1) {
          if (this.btnNums < this.btnList.length - 1) {
            this.btnList[this.btnNums].ref = ''
            this.btnNums++
            this.btnList[this.btnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      enter: () => {
        if (this.nextNums > -1 && this.leftList.length > 0) {
          if (this.leftList[this.leftNums].has_fq == 1) {
            sessionStorage.setItem('indexFocusAppment', this.leftNums)
            this.$nextTick(() => {
              this.$router.push({
                path: '/time_sj',
                query: {
                  hos_name: this.$route.query.hos_name,
                  dept_name: this.$route.query.dept_name,
                  dept_code: this.$route.query.dept_code,
                  one_dept_code: this.$route.query.one_dept_code,
                  hos_org_code: this.$route.query.hos_org_code,
                  order_type: this.$route.query.order_type,
                  resource_code: this.$route.query.resource_code,
                  resource_name: this.$route.query.resource_name,
                  title: this.$route.query.title,
                  date: this.leftList[this.leftNums].date,
                  start_time: this.leftList[this.leftNums].date,
                  end_time: this.leftList[this.leftNums].date,
                },
              })
            })
          }
        } else if (this.nextNums == -1) {
          if (this.btnNums === 0 || this.btnNums === 1) {
            const sessionKeys = [
              'indexFocusDOne',
              'indexFocusDTwo',
              'chosenIndex',
              'indexDocOrDept',
              'indexChosenDept',
            ]

            if (this.btnNums === 1) {
              sessionKeys.push('indexFocusHos')
            }

            sessionKeys.forEach((key) => sessionStorage.removeItem(key))

            // this.$router.push({
            //   path: this.btnNums === 0 ? './hospital_sj' : './supplier',
            // })
            if (this.$route.query.order_type == 1) {
              if (this.btnNums === 0) {
                history.go(-4)
              } else {
                history.go(-5)
              }
            } else if (this.$route.query.order_type == 2) {
              if (this.btnNums === 0) {
                history.go(-5)
              } else {
                history.go(-6)
              }
            }
          }
        }
      },
      esc: () => {
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.unfinishList[this.unfinishNums].ref = 'active'
          this.dialogVisible = false
          return
        }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState', true)
      // let leftICon = []
      getSJDateList({
        hos_org_code: this.$route.query.hos_org_code,
        one_dept_code: this.$route.query.one_dept_code,
        dept_code: this.$route.query.dept_code,
        order_type: this.$route.query.order_type,
        page: 1,
        limit: 50,
        resource_code: this.$route.query.resource_code,
        order_num_type: this.$route.query.order_num_type,
      })
        .then(
          (res) => {
            this.$store.dispatch('app/setLoadingState', false)
            if (res.code == 200) {
              let list = JSON.parse(JSON.stringify(res.data))
              list.sort((a, b) => new Date(a.date) - new Date(b.date))
              list.map((item) => {
                item.ref = ''
                const date = new Date(item.date)
                const daysOfWeek = [
                  '星期日',
                  '星期一',
                  '星期二',
                  '星期三',
                  '星期四',
                  '星期五',
                  '星期六',
                ]
                item.dayOfWeek = daysOfWeek[date.getDay()]
              })
              this.leftList = list
              if (this.leftList.length > 0) {
                this.nextNums = this.leftNums
                this.leftList[this.leftNums].ref = 'active'
                // this.$nextTick(() => {
                //   this.$store.dispatch('index/setFocusDom', this.$refs.active)
                // })
              } else {
                this.nextNums = -1
                this.btnList[this.btnNums].ref = 'active'
              }
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          },
          (err) => {
            this.$store.dispatch('app/setLoadingState', false)
            if (err.response) {
              this.nextNums = -1
              this.btnList[this.btnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          }
        )
        .catch(() => {
          this.$store.dispatch('app/setLoadingState', false)
        })
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
<style lang="less" scoped>
.appointment {
  width: 16.6rem;
  position: relative;
  .container {
    width: 100%;
    height: 6rem;
    overflow: hidden;
    display: flex;
    // grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
    // gap: 0.16rem;
    font-size: 0.2rem;
    color: #e7e7ef;
    .left {
      width: 100%;
      height: 6rem;
      margin-left: 0rem;
      //padding-left: 0.1rem;
      //margin-left: 0.05rem;

      .messageCenterList {
        width: 100%;
        height: 100%;
        position: relative;
        display: inline-block;
        overflow: hidden;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          left: 0.4rem;
          transition: all 0.2s;
          .messageItem {
            width: 3.8rem;
            height: 1.8rem;
            // padding: 0.5rem;
            margin-bottom: 0.3rem;
            background-size: 100% 100% !important;
            // background: #262954;
            background: url('../../assets/hospital_btn_bg.png') no-repeat;

            border-radius: 0.24rem;
            overflow: hidden;
            .item_user {
              display: flex;
              height: 100%;
              // padding: 0.15rem;
              .content {
                display: flex;
                padding: 0 0.1rem;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                height: initial !important;

                .date,
                .week {
                  // width: 100%;
                  // height: 1.8rem;
                  text-align: center;
                  font-size: 0.35rem;
                  font-weight: bold;
                }
                .info {
                  text-align: center;
                  color: yellow;
                  font-size: 0.25rem;
                  margin-top: 0.05rem;
                }
              }
            }
          }
          .messageItem:not(:nth-child(4n + 4)) {
            margin-right: 0.28rem;
          }
        }
      }
      .no_info {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        line-height: 6rem;
        width: 4rem;
        height: 7rem;
        text-align: center;
        font-size: 0.6rem;
      }
    }
  }
  .btnList_bottom {
    position: absolute;
    bottom: -1.36rem;
    left: 50%;
    transform: translateX(-50%);
    height: 1rem;
    .btnList {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 100%;
      .btnItem {
        text-align: center;
        width: 3.5rem;
        height: 1rem;
        margin-left: 0.3rem;
        font-size: 0.4rem;
        font-weight: bold;
        border-radius: 0.24rem;
        background: url('../../assets/btn_gh_click_bg.png') no-repeat;
        background-size: 100% 100% !important;
        .text {
          line-height: 1.08rem;
        }
      }
    }
  }
}
</style>
<style lang="less">
.appointment {
  .container {
    .left {
      .scroll {
        top: 2.25rem !important;
        left: 18rem !important;
      }
    }
  }
}
</style>