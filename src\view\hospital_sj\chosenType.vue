<template>
  <div class="chosenType">
    <div class="container">
      <div
        class="list"
        v-for="(item, index) in list"
        :key="index"
        :ref="item.ref"
      >
        <div class="pic"><img :src="item.imgUrl" alt="" /></div>
      </div>
    </div>
    <div class="btnList_bottom">
      <div class="btnList">
        <div
          class="btnItem"
          v-for="(item, index) in btnList"
          :key="index"
          :ref="item.ref"
        >
          <div class="text">{{ item.text }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
import { getExchangeRule } from '@/api/index'
export default {
  data() {
    return {
      list: [
        {
          title: '医生',
          order_type: 1,
          ref: '',
          imgUrl: require('@/assets/select_1_btnBg.png'),
        },
        {
          title: '门诊',
          order_type: 2,
          ref: '',
          imgUrl: require('@/assets/select_2_btnBg.png'),
        },
      ],
      nums: 0,
      btnList: [
        {
          text: '重新开始',
          ref: '',
        },
        {
          text: '退出',
          ref: '',
        },
      ],
      btnNums: 0,
      nextNums: -1,
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '预约挂号-选择医生/门诊')
    this.$store.dispatch('index/setFocusDom', null)
  },
  mounted() {
    if (sessionStorage.getItem('chosenIndex')) {
      this.nums = Number(sessionStorage.getItem('chosenIndex'))
      sessionStorage.removeItem('chosenIndex')
    }
    this.getData()

    this.fuc.KeyboardEvents({
      down: () => {
        if (this.nextNums > -1) {
          this.list[this.nums].ref = ''
          this.nextNums = -1
          this.btnList[this.btnNums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      up: () => {
        if (this.nextNums == -1) {
          this.btnList[this.btnNums].ref = ''
          this.nextNums = this.nums
          this.list[this.nums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      left: () => {
        if (this.nextNums > -1) {
          if (this.nums > 0) {
            this.list[this.nums].ref = ''
            this.nums--
            this.list[this.nums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        } else if (this.nextNums == -1) {
          if (this.btnNums > 0) {
            this.btnList[this.btnNums].ref = ''
            this.btnNums--
            this.btnList[this.btnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      right: () => {
        if (this.nextNums > -1) {
          if (this.nums < this.list.length - 1) {
            this.list[this.nums].ref = ''
            this.nums++
            this.list[this.nums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        } else if (this.nextNums == -1) {
          if (this.btnNums < this.btnList.length - 1) {
            this.btnList[this.btnNums].ref = ''
            this.btnNums++
            this.btnList[this.btnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      enter: () => {
        if (this.nextNums > -1) {
          sessionStorage.setItem('chosenIndex', this.nums)
          this.$router.push({
            path: './doctor_sj',
            query: {
              hos_name: this.$route.query.hos_name,
              dept_name: this.$route.query.dept_name,
              dept_code: this.$route.query.dept_code,
              one_dept_code: this.$route.query.one_dept_code,
              hos_org_code: this.$route.query.hos_org_code,
              order_type: this.list[this.nums].order_type,
            },
          })
        } else if (this.nextNums == -1) {
          if (this.btnNums === 0 || this.btnNums === 1) {
            const sessionKeys = ['indexFocusDOne', 'indexFocusDTwo']

            if (this.btnNums === 1) {
              sessionKeys.push('indexFocusHos')
            }

            sessionKeys.forEach((key) => sessionStorage.removeItem(key))

            // this.$router.push({
            //   path: this.btnNums === 0 ? './hospital_sj' : './supplier',
            // })

            if (this.btnNums === 0) {
              history.go(-2)
            } else {
              history.go(-3)
            }
          }
        }
      },
      esc: () => {
        //   if (this.dialogVisible) {
        //     this.popupBtnList[this.popupBtnNums].ref = ''
        //     this.leftList[this.leftNums].ref = 'active'
        //     this.dialogVisible = false
        //     return
        //   }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    getData() {
      this.list[this.nums].ref = 'active'
      this.nextNums = this.nums
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active)
      })
    },
  },
}
</script>
  
  <style lang='less' scoped>
.chosenType {
  display: flex;
  position: relative;

  .container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 5.6rem;
    .list {
      width: 4.7rem;
      height: 100%;
      border-radius: 0.3rem;
      margin: 0 0.4rem;
      .pic {
        height: 100%;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .btnList_bottom {
    position: absolute;
    bottom: -1.76rem;
    left: calc(50% - 0.1rem);
    transform: translateX(-50%);
    height: 1rem;
    .btnList {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 100%;
      .btnItem {
        text-align: center;
        width: 3.5rem;
        height: 1rem;
        margin-left: 0.3rem;
        font-size: 0.4rem;
        font-weight: bold;
        border-radius: 0.24rem;
        background: url('../../assets/btn_gh_click_bg.png') no-repeat;
        background-size: 100% 100% !important;
        .text {
          line-height: 1.08rem;
        }
      }
    }
  }
}
</style>