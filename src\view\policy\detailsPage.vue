<template>
  <div class="detailsPage">
    <div class="container">
      <div class="right scrollParent" ref="refRight">
        <div class="list" ref="refList" :style="{ top: '0rem' }">
          <div class="content" v-html="content"></div>
        </div>
      </div>
    </div>
    <audio :src="audioSrc" v-if="audioSrc" autoplay></audio>
    <div class="aiPersionBtn" v-if="showBtn" ref="aiPersionBtn">
      AI播报
    </div>
  </div>
</template>

<script>
import { getNewsDetail } from '@/api/index'
import media from '@/utils/mediaPlayer.js'
import store from "@/store";

export default {
  name: 'detailsPage',
  components: {},
  data() {
    return {
      title: '',
      contentData: null,
      content: '',
      video_url: '',
      isReady: false,
      audioSrc: null,
      showBtn: false,
      videoTimer: null
    }
  },
  created() {
    //设置页面左上角标题
    this.$nextTick(() => {
      this.$store.dispatch('index/setMainTitle', this.title)
    })
  },
  computed: {},
  watch: {},
  mounted() {
    this.video_url = null
    this.title = this.$route.query.title
    this.$store.dispatch('app/setLoadingState', true)

    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.$store.getters.loadingState) {
          return
        }
        const listEl = this.$refs.refList
        const scrollBar =
          this.$refs.refList.parentNode.querySelector('.scrollBar')

        // console.log('可视区域', listEl.parentNode.clientHeight)
        // console.log('元素距离顶部', listEl.offsetTop)
        // console.log('元素高度', listEl.clientHeight)
        // console.log('下拉高度', listEl.parentNode.offsetTop)
        if (this.$refs.aiPersionBtn && this.$store.getters.focus_dom && this.$store.getters.focus_dom.classList[0] == "aiPersionBtn") {
          this.$store.dispatch('index/setFocusDom', null);
          return
        }

        if (listEl && scrollBar) {
          let currentTop = parseInt(listEl.style.top, 10)
          let currentScrollTop = parseInt(scrollBar.style.top, 10)
          let listVisHeight = listEl.parentNode.clientHeight
          let listHeight = listEl.clientHeight

          const radio = listHeight / listVisHeight

          const potentialNewTop = currentTop - 150 // 预计的新top值
          const scrollNewTop = currentScrollTop + 150 / radio
          const maxScrollableHeight =
            listEl.clientHeight - listEl.parentNode.clientHeight // 最大可滚动高度
          const maxSrcollBarHeight =
            scrollBar.parentNode.clientHeight - scrollBar.clientHeight
          if (listVisHeight < listHeight) {
            // 将元素的top值与最大可滚动高度进行比较，以确定是否已经到达或超过了底部
            if (-potentialNewTop < maxScrollableHeight) {
              listEl.style.top = `${potentialNewTop}px`
            } else {
              // 已经到达或超过底部，调整top以确保内容底部和容器底部对齐
              listEl.style.top = `${-maxScrollableHeight - 50}px`
            }
          } else {
            return
          }

          if (scrollNewTop < maxSrcollBarHeight) {
            scrollBar.style.top = `${scrollNewTop}px`
          } else {
            scrollBar.style.top = `${maxSrcollBarHeight}px`
          }
        }
      },
      up: () => {
        if (this.$store.getters.loadingState) {
          return
        }
        const listEl = this.$refs.refList
        const scrollBar =
          this.$refs.refList.parentNode.querySelector('.scrollBar')

        if (listEl && scrollBar) {
          let currentTop = parseInt(listEl.style.top, 10)
          let currentScrollTop = parseInt(scrollBar.style.top, 10)
          let listVisHeight = listEl.parentNode.clientHeight
          let listHeight = listEl.clientHeight
          const radio = listHeight / listVisHeight
          const potentialNewTop = currentTop + 150 // 预计的新top值
          const scrollNewTop = currentScrollTop - 135 / radio

          if (this.$refs.aiPersionBtn && potentialNewTop == 150) {
            this.$store.dispatch('index/setFocusDom', this.$refs.aiPersionBtn);
            return
          }

          // 检查是否已经到达最顶部
          if (potentialNewTop >= 0) {
            listEl.style.top = `0px` // 直接设置为0，确保不会超过顶部
            scrollBar.style.top = `0px`
          } else {
            listEl.style.top = `${potentialNewTop}px`
            scrollBar.style.top = `${scrollNewTop}px`
          }
        }
      },
      left: () => {},
      right: () => {},
      enter: () => {
        if (this.$store.getters.loadingState) {
          return
        }
        if (this.$refs.aiPersionBtn && this.$store.getters.focus_dom && this.$store.getters.focus_dom.classList[0] == "aiPersionBtn") {
          this.$store.dispatch('app/setLoadingState', true)
          this.audioSrc = null
          this.video_url = this.contentData.video_url
          clearTimeout(this.videoTimer)
          this.videoTimer = null
          this.videoTimer = setTimeout(()=>{
            this.playVideo(()=>{
              this.$store.dispatch('app/setLoadingState', false)
              this.$store.dispatch('index/setFocusDom', null);
              setTimeout(()=>{
                this.isReady = false
              },800)
            })
          },1000)

        }
      },
      esc: () => {
        if (this.$store.getters.loadingState) {
          return
        }
        if (this.$refs.aiPersionBtn) {
          if (this.$store.state.app.media) {
            this.$store.state.app.media.stop(()=>{
                this.isReady = false
                this.$store.dispatch('index/setFocusDom', this.$refs.aiPersionBtn);
            })
          } else {
            this.$store.dispatch('index/setFocusDom', null);
            history.go(-1)
          }
          return
        }

        sessionStorage.removeItem('privacy_policy')
        if(!this.content || this.content == '<p><br></p>'){
          if (this.$store.state.app.media) {
            this.$store.state.app.media.stop(()=>{
              this.$store.dispatch('index/setFocusDom', null);
              history.go(-1)
            })
            return
          }
        }
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    playVideo(callback) {
      this.$store.dispatch('app/setLoadingState', true)
      this.isReady = true
      this.$nextTick(() => {
        var obj = {
          x: 0,
          y: 0,
          w: 1920,
          h: 1080,
          r: 0,
        }
        let fontSize =
          Number(
            document
              .getElementsByTagName('html')[0]
              .style.fontSize.split('px')[0]
          ) / 100
        let video_url = process.env.VUE_APP_API + `${this.video_url}`
        let videoList = [video_url]
        this.$store.state.app.media = media.DetermineKernel(
          {
            videoList: videoList,
            loop: 1,
            windowPos: obj,
            fontSize: fontSize,
          },
          () => {
            this.isReady = true
            this.$store.dispatch('app/setLoadingState', false)
            if (callback) {
              callback()
            }
          }
        )
      })
    },
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      if (!this.$route.query.id) {
        if (sessionStorage.getItem('privacy_policy')) {
          let policyData = JSON.parse(
            decodeURI(sessionStorage.getItem('privacy_policy'))
          )
          this.content = policyData.content
          this.content +=
            `<br><br><div style="text-align: right"><span>更新时间:</span><span>` +
            policyData.updated_at +
            `</span></div>`
          if (this.content.indexOf('<img') > -1) {
            let reg = new RegExp('/public/storage/', 'g')
            this.content = this.content.replace(
              reg,
              process.env.VUE_APP_API + '/public/storage/'
            )
          }
          this.content =  this.content.replace(
              new RegExp('<p', 'g'),
              '<p style="word-break:break-all;" '
          )
          setTimeout(()=>{
            this.$store.dispatch('app/setLoadingState', false)
          },1000)
        }

        return
      }
      this.$store.dispatch('app/setLoadingState', true)
      getNewsDetail({
        policy_id: this.$route.query.id,
        user_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].id,
      }).then((res) => {
        this.$store.dispatch('app/setLoadingState', false)
        if (res.code == 200) {
          this.contentData = res.data
          if (res.data.url && res.data.video_url) {
            this.showBtn = true
          }
          if (res.data.url && res.data.url != "") {
            this.audioSrc = res.data.url.indexOf('http') == -1 ? process.env.VUE_APP_API + res.data.url : res.data.url
          }
          if (!res.data.content || res.data.content == '<p><br></p>') {
            this.video_url = res.data.video_url

            clearTimeout(this.videoTimer)
            this.videoTimer = null
            this.$store.dispatch('app/setLoadingState', true)
            this.videoTimer = setTimeout(()=>{
              this.playVideo(()=>{
                this.$nextTick(() => {
                  this.$store.state.app.media.fullscreen('t')
                  this.$store.dispatch('index/setFocusDom', document.body)
                })
                setTimeout(()=>{
                  this.isReady = false
                },800)
              })
            },1000)



          } else {
            this.content = res.data.content
            if (this.content.indexOf('<img') > -1) {
              let reg = new RegExp('/public/storage/', 'g')
              this.content =  this.content.replace(
                reg,
                process.env.VUE_APP_API + '/public/storage/'
              )
            }
            this.content =  this.content.replace(
                new RegExp('<p', 'g'),
                '<p style="word-break:break-all;" '
            )
          }
          this.$nextTick(()=>{
            this.fuc.setScroll()
          })
        }
      }).catch(err=>{
        this.$store.dispatch('app/setLoadingState', false)
      })
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.detailsPage {
  display: flex;
  position: relative;
  .aiPersionBtn {
    position: fixed;
    top: 0.8rem;
    right: 4.2rem;
    border-radius: 0.14rem;
    background: #89A7FF;
    padding: 0.1rem 0.36rem;

    font-weight: bold;
    transition: all 0.3s;
    letter-spacing: 0.02rem;
    text-indent: 0.02rem;
    font-size: 0.28rem;
    color: #E7E7EF;
    //text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
  }
  .container {
    position: relative;

    .right {
      width: 16.3rem;
      height: 7rem;
      position: absolute;
      font-size: 0.21rem;
      overflow: hidden;
      border-radius: 0.24rem;

      .list {
        width: 100%;
        overflow-y: auto;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;

        .content {
          font-size: 0.4rem !important;
          padding: 0.15rem;
          line-height: 0.55rem;
          position: relative;
          width: 16rem !important;
          height: initial !important;
          //   padding: 0.15rem;
        }
      }
    }
  }
}
</style>
<style lang="less">
.detailsPage {
  .container {
    .right {
      .scroll {
        top: 2.25rem !important;
        left: 17.8rem !important;
      }
    }
  }
}
</style>