function setupWebViewJavascriptBridge (callback) {
    if (window.WebViewJavascriptBridge) {
      callback(window.WebViewJavascriptBridge)
    } else {
      document.addEventListener(
        'WebViewJavascriptBridgeReady',
        function () {
          callback(window.WebViewJavascriptBridge)
        },
        false
      )
    }
}
//注册回调函数，第一次连接时调用 初始化函数(android需要初始化,ios不用)
setupWebViewJavascriptBridge((bridge) => {
    //初始化
    bridge.init((message, responseCallback) => {
      var data = {
        'Javascript Responds': 'Wee!'
      };
      responseCallback(data);
    });
});
export default {
  callhandler (name, data, callback) {
      setupWebViewJavascriptBridge( (bridge)=> {
        if (bridge) {
            bridge.callHandler(name, data ,(res)=> {
                callback(res)
            })
        }
    })
  },
  registerhandler (name, callback) {
    setupWebViewJavascriptBridge( (bridge)=> {
      if (bridge) {
        bridge.registerHandler(name,  (data, responseCallback)=> {
          callback(data, responseCallback)
        })
      }
    })
  }
}
