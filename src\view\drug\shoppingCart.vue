<!--购物车-->
<template>
  <div class="shoppingCart">
    <div class="container">
      <div class="left_List"  ref="send_btn" :key="leftNums">
          <img :src="require('@/assets/shoppingCart_send.png')" alt="">
      </div>
      <div class="left">
        <div class="leftContent scrollParent"  v-if="this.leftList.length > 0" :style="{width: leftList.length> 4 ? '12.4rem' : '12.8rem'}">
          <div class="leftList" :style="{ top: '0rem'}">
            <div class="messageItem" :ref="item.ref" v-for="(item, index) in leftList" :key="index" :style="{width: leftList.length> 4 ? '5.96rem' : '6.12rem'}">
              <div class="item_user" :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }">
                <div class="contentInfo">
                  <div  class="pic" v-lazy-container="{ selector: 'img' }">
                    <img :data-src="item.img"  :data-error="lazyError" alt="">
                    <div class="choose_tip" v-if="item.choose_tip">
                      已选
                    </div>
                  </div>

                  <div class="info">
                    <div class="title">{{ item.title }}</div>
                    <div class="infoItem">规格: {{item.model}}</div>
                    <div class="infoItem">生产商: {{item.factory}}</div>
                  </div>

                </div>
                <div class="price">
                  <span>￥</span>
                  <span>{{item.price.toFixed(2)}}</span>
                </div>

              </div>
            </div>
          </div>
        </div>
<!--        <div class="no_content" v-else>暂无商品列表</div>-->
      </div>

    </div>

    <div class="tel" v-if="this.supplierInfo.remark">
      <img :src="require('@/assets/quan.png')" alt="">
      {{this.supplierInfo.remark}}
    </div>

    <el-dialog
        :class="popupModule == 4 ? 'popup_info_box' : 'popup_box'"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        :title="this.supplierInfo.title"
        ref="popupBox"
    >
      <!--发起预约-->
      <div class="message_box sendCall scrollParent" v-if="popupModule == 1">
        <div class="message_content" ref="popupRef" :style="{top: '0rem'}">
          <div class="messageTitle">您即将发起下列药品的配药申请：</div>
          <div class="message_item" v-for="(item,index) in leftList" :key="index" >
            <div class="itemType">
              <span class="type">药品{{numberChinese[index]}}:</span>
              <span class="value">{{item.title}}</span>
            </div>
            <div class="itemType">
              <span class="type">生产商:</span>
              <span class="value">{{item.factory}}</span>
            </div>
            <div class="price">
              <span>￥</span>
              <span>{{item.price.toFixed(2)}}</span>
            </div>
          </div>
        </div>

      </div>

      <!--取消预约-->
      <div class="message_box cancel scrollParent" v-if="popupModule == 2">
        <div class="message_content" ref="popupRef" :style="{top: '0rem'}">
          <div class="message_item">
            <div class="type">
              当前状态:
            </div>
            <div class="value">
              {{ statusList[this.rightList[this.rightNums].status].text }}
            </div>
          </div>
          <div class="message_item">
            <div class="type">申请时间:</div>
            <div class="value">{{ this.rightList[this.rightNums].created_at }}</div>
          </div>
          <div class="message_item">
            <div class="type">预约时间:</div>
            <div class="value">{{ new Date(this.rightList[this.rightNums].plan_start_date * 1000).format('yyyy-MM-dd')  }}  {{ new Date(this.rightList[this.rightNums].plan_start_date * 1000).format('hh:mm')  }} - {{ new Date(this.rightList[this.rightNums].plan_end_date * 1000).format('hh:mm')  }}</div>
          </div>
          <div class="message_item">
            <div class="type">服务项目:</div>
            <div class="value">{{ this.rightList[this.rightNums].service_name }}</div>
          </div>

          <div class="message_item">
            <div class="type">商家:</div>
            <div class="value">{{ this.rightList[this.rightNums].name }}</div>
          </div>

          <div class="message_item">
            <div class="type">地址:</div>
            <div class="value">{{ this.rightList[this.rightNums].addr }}</div>
          </div>


        </div>
      </div>

      <div class="message_box result" style="text-align: center; font-size: 0.37rem; justify-content: center" v-html="popupMessage" v-if="popupModule == 3"></div>

      <div class="message_box info" v-if="popupModule == 4">
        <div class="title"><span>你的预约</span><span>已经发送成功！</span></div>
        <div class="tip">您可以在“服务记录”中查看预约信息。</div>
      </div>

      <div class="popupBtnList">
        <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index"> {{ item.text }}</div>
      </div>
    </el-dialog>


  </div>
</template>
      
<script>
import { createMedicalHomeOrder } from '@/api/index'

export default {
  name:'shoppingCart',
  components: {},
  data() {
    return {
      lazyError: require('@/assets/zhujie.png'),
      numberChinese:['一','二','三','四','五','六','七','八','九','十','十一','十二','十三','十四','十五','十六','十七','十八','十九','二十'],
      canSend: false,
      supplierInfo: null,

      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],

      leftList: [],
      rightList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      order_type: 1,
      order_detail: [],

      firstShow: true,


      messageList: [],
      statusList:[]
    }
  },
  created() {
    this.supplierInfo =  JSON.parse(sessionStorage.getItem('supplierInfo'))

    //设置页面左上角标题
    this.$nextTick(() => {
        this.$store.dispatch('index/setMainTitle', '购物车')
    })

  },
  computed: {},
  watch: {},
  mounted() {
    if (localStorage.getItem('statusList')) {
      this.statusList = JSON.parse(localStorage.getItem('statusList'))
    }

    if (sessionStorage.getItem('shoppingCart_focus_index')) {
      this.leftNums = Number(sessionStorage.getItem('shoppingCart_focus_index'))
      sessionStorage.removeItem('shoppingCart_focus_index')
    }

    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if ((Math.abs(styleTop) +scrollPageTop)  * fontSize > this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight) {
                this.$refs.popupRef.style.top = styleTop - (scrollTop/fontSize - Math.abs(styleTop)) + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * ((scrollTop/fontSize - Math.abs(styleTop)) * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight)) + 'px'
                return
              }
              if (scrollTop > 0) {
                this.$refs.popupRef.style.top = styleTop - scrollPageTop + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))   + 'px'
              }
            }
          }
          return
        }
        if (this.nextNums == -1) {

          return
        }
        this.leftList[this.leftNums].ref = ''
        if (this.leftList[this.leftNums + 2]) {
          this.leftNums = this.leftNums + 2
        } else {
          if (this.leftNums % 2 != 0) {
            this.leftNums = this.leftList.length - 1
          }
        }
        this.leftList[this.leftNums].ref = 'active'

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if (scrollTop > 0) {
                if ((styleTop + scrollPageTop) > 0) {
                  this.$refs.popupRef.style.top = '0rem'
                  scrollBar.style.top = '0px'
                  return
                } else {
                  this.$refs.popupRef.style.top = styleTop + scrollPageTop + 'rem'
                  scrollBar.style.top = barTop - (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))  + 'px'
                }
              }
            }
          }
          return
        }
        if (this.nextNums == -1) {

          return
        }
        if (this.leftList[this.leftNums - 2]) {
          this.leftList[this.leftNums].ref = ''
          this.leftNums -= 2
          this.leftList[this.leftNums].ref = 'active'
        }

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        if (this.nextNums == -1) {

          return
        }
        if (this.leftNums % 2 != 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
        } else {
          this.leftList[this.leftNums].ref = ''
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.send_btn)
            this.nextNums = -1
          })
        }


      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        if (this.nextNums == -1) {
          this.leftList[this.leftNums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
            this.nextNums = this.leftNums
          })
          return
        }
        // 在右侧
        if (this.leftList.length > 1 && this.leftNums % 2 != 1) {
          this.leftList[this.leftNums].ref = ''
          this.leftNums++
          this.leftList[this.leftNums].ref = 'active'
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      enter: () => {
        if (this.dialogVisible) {
          // 弹窗
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.popupBtnList[this.popupBtnNums].fuc(this.rightList[this.rightNums])
          this.dialogVisible = false

          // 取消订单关闭时
          if (this.nextNums > -1 && this.leftList[this.leftNums]) {
            this.leftList[this.leftNums].ref = 'active'
          }
          return
        }
        if (this.nextNums == -1) {

          this.leftList[this.leftNums].ref = ''
          this.popupModule = 1
          this.popupBtnNums =  0
          this.popupBtnList = [
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            },{
              text: '确认',
              ref: '',
              fuc: this.sendOrder,
            },
          ]
          this.popupBtnList[this.popupBtnNums].ref="active"
          this.dialogVisible = true

          return
        }
        else {
          this.leftList[this.leftNums].ref = ''
          this.popupModule = 3
          this.popupBtnNums =  0
          this.popupMessage = `<div>从购物车移除<br>${this.leftList[this.leftNums].title}?</div>`
          this.popupBtnList = [
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            },{
              text: '确认',
              ref: '',
              fuc: this.removeMedical,
            },
          ]

          this.popupBtnList[this.popupBtnNums].ref="active"
          this.dialogVisible = true


        }
        // sessionStorage.setItem('shoppingCart_focus_index',this.leftNums)

      },
      esc: () => {
        if (this.dialogVisible) {

          return
        }
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      this.popupBtnList[this.popupBtnNums].ref = ''
      if (this.nextNums > -1 && this.leftList[this.leftNums]) {
        this.leftList[this.leftNums].ref = 'active'
      } else {
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.send_btn)
          document.getElementById('focus_border').style.borderColor = '#fff'
        })
        return
      }

      this.$nextTick(() => {
        if (this.$refs.active[0]) {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        }

        document.getElementById('focus_border').style.borderColor = '#fff'
        // document.getElementById('focus_border').style.borderColor = 'transparent'

        // setTimeout(()=>{
        this.popupBtnNums = 0
        this.popupModule = 1
        // },100)

      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState', true)


      let leftListClone = JSON.parse(sessionStorage.getItem('selectMedical'))

      leftListClone.map((item) => {
        item.ref = ''
        if (item.img) {
          item.img = item.img.indexOf('http') > -1 ? item.img : process.env.VUE_APP_API + item.img
        }
      })
      this.leftList = leftListClone

      this.$store.dispatch('app/setLoadingState', false)


      this.nextNums = this.leftNums
      if (this.leftList.length > 0) {
        this.leftList[this.leftNums].ref = 'active'
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      } else {
        history.go(-1)
      }




    },
    removeMedical() {
      this.leftList.map((item,index)=>{
        if (item.id == this.leftList[this.leftNums].id) {
          this.leftList.splice(index, 1);
        }
      })
      sessionStorage.setItem('selectMedical',JSON.stringify(this.leftList))
      if (this.leftList.length == 0) {
        history.go(-1)
        return
      }
      this.leftList = []
      this.leftNums = 0
      this.getData()
    },
    sendOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      createMedicalHomeOrder({
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        dispensing_list: JSON.stringify(this.leftList)
      })
      .then(res=>{
        this.$store.dispatch('app/setLoadingState', false)
        this.popupModule = 3
        this.$refs.active = ''
        this.popupBtnList = [
          {
            text: '确认',
            ref: 'active',
            fuc: () => {
              this.dialogVisible = false
              sessionStorage.removeItem('selectMedical')
              history.go(-1)
            },
          },
        ]
        this.popupMessage = '<br>配药申请发送成功!</br>'
        this.popupBtnNums = 0
        this.dialogVisible = true
      })
      .catch(error=>{
        this.$store.dispatch('app/setLoadingState', false)
        this.popupModule = 3
        this.$refs.active = ''
        this.popupBtnList = [
          {
            text: '确认',
            ref: 'active',
            fuc: () => {
              this.dialogVisible = false
            },
          },
        ]
        this.popupMessage = '<br>申请配药失败</br>'
        if (error.response && error.response.data && error.response.data.msg) {
          this.popupMessage += error.response.data.msg
        }
        this.popupBtnNums = 0
        this.dialogVisible = true

      })

    }

  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = null
  },
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.shoppingCart {
  width: 16.73rem;
  .tel {
    position: absolute;
    bottom: 0.54rem;
    right: 1rem;
    letter-spacing: 0.03rem;
    font-size: 0.28rem;
    color: #B2BAEF;
    display: flex;
    align-items: center;
    img {
      display: block;
      width: 0.32rem;
      height: 0.32rem;
      margin-right: 0.04rem;
      margin-top: 0.03rem;
    }
  }
  .container {
    width: 16.73rem;
    height: 7rem;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    .noData {
      div {
        font-size: 0.5rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -200%);
        letter-spacing: 0.05rem;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
      }
    }
    .left_List {
      width: 3.2rem;
      height: 2.15rem;
      border-radius: 0.24rem;
      margin-right: 1rem;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
    .left {
      width: 12.5rem;
      height: 7rem;
      margin-left: 0;
      .leftContent {
        width: 100%;
        height: 100%;
        position: relative;
        display: inline-block;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          // left: 0.8rem;
          transition: all 0.2s;
           .messageItem {
            height: 3.3603rem;
            //padding: 0.45rem;
            position: relative;
            margin-right: 0.28rem;
            margin-bottom: 0.28rem;
            background-size: 100% 100% !important;
            background: #262954;
            border-radius: 0.3rem;
            overflow: hidden;
            .item_user {
              display: flex;
              flex-direction: column;
              height: 100%;
              position: relative;
              img {
                width: 1.62rem;
                height: 2.2rem;
                margin: 0.26rem 0.16rem;
                border-radius: 0.06rem;
                object-fit: cover;
                //object-fit: contain;
                //background: #fff;
              }

              .contentInfo {
                height: initial !important;
                display: flex;
                flex-direction: row;
                text-align: left;
                font-size: 0.26rem;
                .pic {
                  .choose_tip {
                    position: absolute;
                    top: 0.26rem;
                    left: 0.16rem;
                    width: 0.7rem;
                    height: 0.35rem;
                    background: #FD8B19;
                    border-radius: 0.05rem;
                    font-size: 0.22rem;
                    text-align: center;
                    line-height: 0.35rem;
                    font-weight: bold;
                    color: #fff;
                    letter-spacing: 0.03rem;
                  }
                }
                .info {
                  width: 64%;
                  .title {
                    margin-top: 0.24rem;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    font-weight: bold;
                    color: #fff;
                  }
                  .infoItem {
                    font-size: 0.26rem;
                    color: #B7C0F9;
                    margin-top: 0.2rem;
                  }
                }
              }
              .price {
                width: 1.8rem;
                font-size: 0.36rem;
                color: #F64D23;
                text-align: left;
                position: absolute;
                left: 0.15rem;
                bottom: 0.2rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-weight: bold;
                span:nth-child(1) {
                  font-size: 0.28rem;
                }
              }
            }
            .choose_tip {
              width: 4rem;
              height: 0.37rem;
              position: relative;
              top: 0.13rem;
              right: 0.5rem;
              div {
                width: 100%;
                height: 100%;
                font-size: 0.22rem;
                background: #3cc92d;
                border-radius: 0.05rem;
                position: absolute;
                text-align: center;
                line-height: 0.35rem;
                font-weight: bold;
                color: #fff;
                letter-spacing: 0.03rem;
              }
            }
          }
           .messageItem:nth-child(2n + 2) {
             margin-right: 0;
           }
        }
      }
      .no_content {
        position: absolute;
        line-height: 7rem;
        left: 6rem;
        // width: 4rem;
        // height: 7rem;
        text-align: center;
        font-size: 0.4rem;
        letter-spacing: 0.05rem;
        line-height: 6rem;
        color: #b5c0ff;
      }
    }
    .no_info {
      //position: absolute;
      line-height: 7rem;
      // left: 6rem;
      //right: 2.3rem;
      // width: 4rem;
      // height: 7rem;
      text-align: center;
      font-size: 0.4rem;
      letter-spacing: 0.05rem;
      text-indent: 0.05rem;
      line-height: 6rem;
      color: #b5c0ff;
    }
  }
}
</style>
<style lang="less">
  .shoppingCart {
    .el-dialog {
      width: 9.4rem;
      height: 8rem;
      margin-top: 0 !important;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 0.26rem;
      // background: repeating-linear-gradient(to right, #c98693, #a95361);
      background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

      .el-dialog__header {
        height: 10%;
        display: flex;
        align-items: center;
        padding-top: 35px;
        .el-dialog__title {
          display: block;
          line-height: normal !important;
          height: 100%;
          font-size: 0.4rem;
          color: #fff;
          background-image: linear-gradient(
              to bottom,
              rgba(255, 255, 255, 1),
              rgba(255, 255, 255, 0.65)
          );
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          font-weight: bold;
          width: 100%;
          text-align: center;
        }
      }
      .el-dialog__body {
        width: 92%;
        height: 80%;
        position: absolute;
        bottom: 4%;
        left: 4%;
        border-radius: 0.2rem;
        background: #fff;
        padding: 0 !important;
        .message_box {
          padding: 0.4rem;
          color: #464646;
          font-size: 0.32rem;
          font-weight: bold;
          height: 60%;
          display: flex;
          align-items: flex-start;
          flex-direction: column;
          .title,
          .detail,
          .price {
            display: flex;
            font-size: 0.35rem;
            font-weight: bold;
            //padding-bottom: 0.22rem !important;
            letter-spacing: 0.03rem;
          }
          .title {
            span {
              color: #f64e23;
              margin-left: 0.2rem;
            }
          }
          .price {
            span {
              color: #f64e23;
            }
          }
        }

        .cancel,.sendCall {
          width: calc(100% - 0.8rem);
          height: 4.26rem;
          //top: 0.4rem;
          //left: 0.4rem;
          padding: 0;
          margin-top: 0.4rem;
          margin-left: 0.4rem;
          //position: relative;
          overflow: hidden;
          position: relative;
          .message_content {
            width: 100%;
            position: absolute;
            transition: all 0.3s;
            .message_item {
              display: flex;
              flex-direction: row;
              font-size: 0.4rem;
              margin-bottom: 0.14rem;
              letter-spacing: 0.03rem;
              .type {
                width: 1.9rem;
                font-size: 0.4rem;
              }
            }
            .orderItem {
              letter-spacing: 0.03rem;
              font-size: 0.4rem !important;
              div {
                margin-bottom: 0.2rem;
              }
            }
          }
          .scroll {
            top: 1.8rem !important;
            left: auto !important;
            right: 0.8rem !important;
          }
        }
        .cancel {
          .message_item {
            font-size: 0.34rem !important;
            .type {
              font-size: 0.34rem !important;
            }
          }
        }
        .sendCall {
          font-size: 0.36rem !important;
          color: #464646;
          font-weight: bold;
          letter-spacing: 0.03rem;
          .messageTitle {
            padding-bottom: 0.22rem;
            letter-spacing: 0.03rem;
          }
          .message_item {
            flex-direction: column !important;
            font-size: 0.36rem !important;
            .type,.value {
              display: inline-block;
              font-size: 0.3rem !important;
            }
            .type {
              width: 1.2rem !important;
            }
          }
        }
        .result {
          display: flex;
          justify-content: center !important;
          align-items: center !important;
        }
        .info {
          display: flex;
          justify-content: center !important;
          align-items: center !important;
          margin-top: -0.4rem;
          // background: url(http://192.168.6.212/ptyanglaotv/assets/zc_tan_new.png) no-repeat !important;
          .title {
            display: flex;
            flex-direction: column;
            align-items: center !important;
            text-align: center !important;

            // margin-top: 0.9rem;
            span {
              font-size: 0.5rem;
              color: #464646 !important;
              letter-spacing: 0.14rem;
            }
          }
          .phone {
            width: 7rem;
            text-align: center;
            position: absolute;
            top: 2.3rem;
            // left: 50%;
            // transform: translateX(-50%);
            font-size: 0.3rem;
            // color: yellow;
            color: #464646;
          }
          .tip {
            text-align: center;
            color: #3288dc;
            font-size: 0.4rem;
            margin-top: 1.2rem;
          }
        }
        .popupBtnList {
          display: flex;
          flex-direction: row;
          justify-content: space-evenly;
          margin-top: 0.25rem;
          div {
            width: 3.84rem;
            height: 1rem;
            line-height: 1.1rem;
            text-align: center;
            border-radius: 0.3rem;
            color: #fff;
            font-size: 0.45rem;
            font-weight: bold;
            letter-spacing: 0.05rem;
            text-indent: -0.05rem;
            // background: repeating-linear-gradient(to right, #c98693, #a95361);
            background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
          }
        }
      }
    }
  }
</style>
      