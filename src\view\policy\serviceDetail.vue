<template>
  <div class="serviceDetail">
    <div class="header">
      <div class="name item">名称</div>
      <div class="adress item">地址</div>
      <div class="phone item">电话</div>
    </div>
    <div class="detail">
      <div class="container">
        <div class="left">
          <div class="messageCenterList scrollParent">
            <div class="leftList" :style="{ top: '0rem' }">
              <div
                class="messageItem"
                :ref="item.ref"
                v-for="(item, index) in leftList"
                :key="index"
              >
                <div class="title item">{{ item.title }}</div>
                <div class="adress item">{{ item.addr }}</div>
                <div class="phone item">{{ item.phone }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
      
<script>
import { getServiceDetail } from '@/api/index'
export default {
  name:'serviceDetail',
  components: {},
  data() {
    return {
      leftList: [],
      leftNums: 0,
    }
  },
  created() {
    //设置页面左上角标题
    this.$nextTick(() => {
      this.$store.dispatch('index/setMainTitle', this.$route.query.title)
    })
  },
  computed: {},
  watch: {},
  mounted() {
    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.leftNums < this.leftList.length - 1) {
          this.leftList[this.leftNums].ref = ''
          this.leftNums++
          this.leftList[this.leftNums].ref = 'active'
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.leftNums > 0 && this.leftList.length - 1 >= 0) {
          this.leftList[this.leftNums].ref = ''
          this.leftNums--
          this.leftList[this.leftNums].ref = 'active'
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {},
      right: () => {},
      enter: () => {},
      esc: () => {
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)

      getServiceDetail({
        // user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        org_class_id: this.$route.query.id,
        user_id: this.$store.getters.getUserInfo.home_id,
      }).then((res) => {
        if (res.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data))
          list.map((item) => {
            item.ref = ''
          })
          this.leftList = list

          if (this.leftList.length > 0) {
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      })
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
      <style lang='less' scoped>
.serviceDetail {
  width: 16.8rem;
  height: 8.26rem;
  .header {
    width: 100%;
    height: 1rem;
    display: flex;
    .item {
      text-align: center;
      line-height: 1rem;
      color: #fff;
      font-weight: bold;
      font-size: 0.38rem;
    }
    .name {
      width: 6.6rem;
    }
    .adress {
      width: 5.9rem;
    }
    .phone {
      width: 3.8rem;
    }
  }
  .detail {
    width: 100%;
    height: 6rem;
    .container {
      width: 100%;
      height: 6rem;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      font-size: 0.2rem;
      color: #e7e7ef;

      .left {
        width: 100%;
        height: 6rem;
        .messageCenterList {
          width: 100%;
          height: 100%;
          position: relative;
          .leftList {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
            border-radius: 0.3rem;
            position: absolute;
            // left: 0.8rem;
            transition: all 0.2s;
            .messageItem {
              display: flex;
              width: 100%;
              height: 0.8rem;
              padding: 0.5rem;
              margin-bottom: 0.3rem;
              position: relative;
              margin-right: 0.2rem !important;
              background-size: 100% 100% !important;
              // background: #ccc !important;
              background: #262954;
              border-radius: 0.3rem;
              overflow: hidden;
              .item {
                text-align: center;
                line-height: 1rem;
                color: #fff;
                font-weight: bold;
                font-size: 0.38rem;
              }
              .title {
                width: 6rem;
              }
              .adress {
                width: 6.5rem;
              }
              .phone {
                width: 3.5rem;
              }
            }
            .messageItem:nth-child(3n + 3) {
              margin-right: 0;
            }
          }
        }
      }
    }
  }
}
</style>