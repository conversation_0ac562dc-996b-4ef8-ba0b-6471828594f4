<template>
    <div class="handheldCare">
      <div class="container">
        <div class="left">
          <div class="qrcode">
            <img :src="qrCode" alt="" />
            <div class="name">{{ $store.getters.getUserInfo.title }}</div>
            <div class="id">ID:{{ $store.getters.getUserInfo.home_id }}</div>
          </div>
          <div class="content">
            提示:
            <br />进入微信小程序“老有数”家庭相册栏目，完成与当前设备的绑定，实现照片分享。
          </div>
        </div>
        <div class="right">
          <div
            class="messageCenterList scrollParent"
            v-if="this.rightList.length > 0"
          >
            <div class="rightList" :style="{ top: '0rem' }">
              <div
                class="messageItem"
                :ref="item.ref"
                v-for="(item, index) in rightList"
                :key="index"
              >
                <div class="item_user">
                  <div class="detail">
                    <div class="pic" v-lazy-container="{ selector: 'img' }">
                      <img
                        :data-src="apiBaseUrl + item.fk_home_avatar"
                        class="avatar"
                        :data-error="lazyError"
                        alt=""
                      />
                    </div>
                    <div class="name">{{ item.fk_home_title }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="no_content" v-else>{{ '无绑定用户' }}</div>
        </div>
        <el-dialog
          :visible.sync="dialogVisible"
          :show-close="false"
          :close-on-click-modal="false"
          @opened="popupOpend"
          @close="popupClose"
          title="操作"
        >
          <div class="message_box cancel" v-if="popupModule == 2">
            <div class="title">
              是否解除与<span>{{
                rightList[rightNums] && rightList[rightNums].fk_home_title
              }}</span
              >的绑定？
            </div>
          </div>
  
          <div
            class="message_box result"
            v-html="popupMessage"
            v-if="popupModule == 3"
          ></div>
  
          <div class="popupBtnList">
            <div
              :ref="item.ref"
              v-for="(item, index) in popupBtnList"
              :key="index"
            >
              {{ item.text }}
            </div>
          </div>
        </el-dialog>
      </div>
    </div>
  </template>
  
  <script>
  import {
    getFamilyAlbumQrcode,
    getFamilyAlbumFriends,
    UnbindFriends,
  } from '@/api/index'
  export default {
    name: 'handheldCare',
    data() {
      return {
        title: '',
        qrCode: '',
        lazyError: require('@/assets/care_avatar.png'),
        rightNums: 0,
        rightList: [],
        apiBaseUrl: process.env.VUE_APP_API,
        dialogVisible: false,
        popupModule: 1,
        popupMessage: '',
        popupBtnNums: 0,
        popupBtnList: [],
        timer: null,
      }
    },
    created() {
      //设置页面左上角标题
      this.$store.dispatch('index/setMainTitle', '我的好友')
    },
    computed: {},
    watch: {
      '$store.state.app.webscoketMessage': {
      handler(res, val) {
        if (res.code == 264) {
          this.getData()
        }
      },
    },
    },
    mounted() {
      this.getData()
      this.getQrCode()
      this.fuc.KeyboardEvents({
        down: () => {
          if (this.dialogVisible) {
            return
          }
          if (this.rightNums < this.rightList.length - 1) {
            if (this.rightList[this.rightNums + 3]) {
              this.rightList[this.rightNums].ref = ''
              this.rightNums += 3
              this.rightList[this.rightNums].ref = 'active'
            } else {
              if (
                this.rightNums <
                  this.rightList.length - (this.rightList.length % 3) &&
                this.rightList.length % 3 != 0
              ) {
                this.rightList[this.rightNums].ref = ''
                this.rightNums = this.rightList.length - 1
                this.rightList[this.rightNums].ref = 'active'
              }
            }
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        },
        up: () => {
          if (this.dialogVisible) {
            return
          }
          if (this.rightNums > 0 && this.rightNums - 3 >= 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums -= 3
            this.rightList[this.rightNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        },
        left: () => {
          if (this.dialogVisible) {
            if (this.popupBtnNums > 0) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums--
              this.popupBtnList[this.popupBtnNums].ref = 'active'
  
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (this.rightNums % 3 != 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums--
            this.rightList[this.rightNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        },
        right: () => {
          if (this.dialogVisible) {
            if (this.popupBtnNums < this.popupBtnList.length - 1) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums++
              this.popupBtnList[this.popupBtnNums].ref = 'active'
  
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (this.rightNums < this.rightList.length - 1) {
            if (this.rightNums % 3 != 2) {
              this.rightList[this.rightNums].ref = ''
              this.rightNums++
              this.rightList[this.rightNums].ref = 'active'
            }
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        },
        enter: () => {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.rightList[this.rightNums].ref = 'active'
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].fuc(
              this.rightList[this.rightNums]
            )
            return
          }
          if (this.rightList[this.rightNums]) {
            
              this.popupBtnList = [
                {
                  text: '取消',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                },
                {
                  text: '确认',
                  ref: '',
                  fuc: this.cancleCare,
                },
              ]
              this.dialogVisible = true
              this.popupModule = 2
              this.rightList[this.rightNums].ref = ''
              this.popupBtnList[this.popupBtnNums].ref = 'active'
           
          }
        },
        esc: () => {
          if (this.dialogVisible) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.rightList[this.rightNums].ref = 'active'
            this.dialogVisible = false
            return
          } 
          this.$store.dispatch('index/setFocusDom', null)
          history.go(-1)
        },
      })
    },
    methods: {
      popupClose() {
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
          document.getElementById('focus_border').style.borderColor = '#fff'
          // setTimeout(() => {
          this.popupBtnNums = 0
          this.popupModule = 1
          // }, 20)
        })
      },
      popupOpend() {
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
          document.getElementById('focus_border').style.borderColor = '#5472B0'
        })
      },
      getData() {
        this.$store.dispatch('index/setFocusDom', this.$refs.active)
  
        // 获取列表
        getFamilyAlbumFriends({
          // user_id:
          //   this.$store.getters.getUserInfo.oldsters[
          //     this.$store.state.app.selectUserIndex
          //   ].id,
          bind_home_id: this.$store.getters.getUserInfo.home_id,
        }).then(
          (res) => {
            if (res.code == 200) {
              if (res.data) {
                let list = JSON.parse(JSON.stringify(res.data))
                
                list.map((item) => {
                  item.ref = ''
                })
                this.rightList = list
              } else {
                this.rightList = []
              }
              if (this.rightList.length > 0) {
                if (this.rightNums >= this.rightList.length) {
                  this.rightNums = this.rightList.length - 1
                }
                this.rightList[this.rightNums].ref = 'active'
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active)
                })
              } else {
                this.$store.dispatch('index/setFocusDom', null)
              }
            }
          },
          (err) => {
            if (err.response) {
              this.rightList = []
              this.$store.dispatch('index/setFocusDom', null)
            }
          }
        )
      },
      // 获取二维码
      getQrCode() {
        clearInterval(this.timer)
        this.timer = null
        getFamilyAlbumQrcode({
          home_id: this.$store.getters.getUserInfo.home_id,
        }).then((res) => {
          if (res.code == 200) {
            this.qrCode = this.apiBaseUrl + res.data.path
            this.timer = setInterval(() => {
              this.getQrCode()
            }, (res.data.times * 60 - 10) * 1000)
          }
        })
      },
  
      // 取消绑定
      cancleCare() {
        UnbindFriends({
          // user_id:
          //   this.$store.getters.getUserInfo.oldsters[
          //     this.$store.state.app.selectUserIndex
          //   ].id,
          bind_home_id: this.$store.getters.getUserInfo.home_id,
          fk_home_id: this.rightList[this.rightNums].fk_home_id,
        }).then(
          (res) => this.successResult(res),
  
          (error) => this.errorResult(error)
        )
      },
      successResult(res) {
        this.popupModule = 3
        this.rightList[this.rightNums].ref = ''
        this.popupBtnList = [
          {
            text: '确认',
            ref: 'active',
            fuc: () => {
              this.dialogVisible = false
              this.getData()
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            },
          },
        ]
        this.popupMessage = '<br/>操作成功!<br>'
        this.popupBtnNums = 0
        this.dialogVisible = true
        this.$store.dispatch('app/setLoadingState', false)
      },
      errorResult(error) {
        this.$store.dispatch('app/setLoadingState', false)
        this.popupModule = 3
        this.rightList[this.rightNums].ref = ''
        this.popupBtnList = [
          {
            text: '确认',
            ref: 'active',
            fuc: () => {
              this.dialogVisible = false
            },
          },
        ]
        this.popupMessage = '<br/>操作失败!<br>'
        // if (error.response && error.response.data && error.response.data.msg) {
        //   this.popupMessage += error.response.data.msg
        // }
        this.popupBtnNums = 0
        this.dialogVisible = true
      },
    },
    destroyed() {
      clearInterval(this.timer)
      this.timer = null
    },
    beforeDestory() {},
  }
  </script>
  
  <style lang="less" scoped>
  .handheldCare {
    .container {
      height: 7rem;
      overflow: hidden;
      display: flex;
  
      .left {
        width: 3.5rem;
        // height: 7.4rem;
        display: flex;
        flex-direction: column;
        align-items: center;
  
        .qrcode {
          width: 3.5rem;
          // height: 4.5rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 0.3rem 0;
          border-radius: 0.3rem;
          background: #393f79;
          margin-bottom: 0.35rem;
          img {
            width: 2.5rem;
            height: 2.5rem;
            object-fit: cover;
          }
  
          .name,
          .id {
            font-size: 0.32rem;
            font-weight: bold;
            line-height: 0.45rem;
            text-align: center;
            letter-spacing: 0.05rem;
          }
          .name {
            margin-top: 0.2rem;
          }
          .id {
            margin-top: 0.1rem;
          }
        }
        .content {
          margin: initial !important;
          width: 3.1rem !important;
          height: initial !important;
          font-size: 0.3rem;
          font-weight: bold;
          line-height: 0.4rem;
          letter-spacing: 0.04rem;
          color: #e8e8eb;
        }
      }
  
      .right {
        width: 12.4rem;
        height: 7rem;
        margin-left: 0.7rem;
        .messageCenterList {
          width: 100%;
          height: 100%;
          margin-left: 0.17rem;
          position: relative;
          .rightList {
            display: flex;
            flex-wrap: wrap;
            border-radius: 0.3rem;
            position: absolute;
            transition: all 0.2s;
            .messageItem {
              width: 3rem;
              height: 2.5rem;
              padding: 0.4rem;
              margin-bottom: 0.4rem;
              background-size: 100% 100% !important;
              background: repeating-linear-gradient(to right, #b2acf4, #837beb);
              border-radius: 0.3rem;
              overflow: hidden;
              .item_user {
                position: relative;
                height: 2.25rem;
                .detail {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  .pic {
                    width: 1.5rem;
                    height: 1.5rem;
                    margin-top: 0.1rem;
  
                    img {
                      width: 100%;
                      height: 100%;
                      border-radius: 50%;
                    }
                  }
                  .name {
                    margin-top: 0.1rem;
                    text-align: center;
                    font-size: 0.45rem;
                    font-weight: bold;
                    line-height: 0.45rem;
                  }
                }
              }
              .choose_tip {
                position: relative;
                height: 0.5rem;
                margin-bottom: 0.3rem;
                .status {
                  text-align: center;
                  border-radius: 0.2rem;
                  font-size: 0.3rem;
                  font-weight: bold;
                  line-height: 0.5rem;
                  background-color: #e65051;
                }
              }
            }
            .messageItem:not(:nth-child(3n)) {
              margin-right: 0.28rem;
            }
          }
        }
        .no_content {
          position: absolute;
          line-height: 7rem;
          left: 10rem;
          text-align: center;
          font-size: 0.4rem;
          letter-spacing: 0.05rem;
          color: #b5c0ff;
        }
      }
    }
  }
  </style>
  <style lang="less">
  .handheldCare {
    .container {
      .el-dialog {
        width: 8.75rem;
        margin-top: 0 !important;
        top: 50%;
        transform: translateY(-50%);
        border-radius: 0.2rem;
        background: #5472b0;
        .el-dialog__header {
          text-align: center;
          height: 0.65rem;
          line-height: 0.75rem;
          font-weight: bold;
          .el-dialog__title {
            color: #fff;
            font-size: 0.5rem !important;
            background: linear-gradient(to bottom, #fdfeff 30%, #bed1fb 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        .el-dialog__body {
          background: #fff;
          border-radius: 0 0 0.16rem 0.16rem;
  
          .agree {
            border-radius: 0.16rem;
            padding: 0.2rem 0;
          }
          .message_box {
            text-align: center;
            .title {
              font-size: 0.5rem;
              font-weight: bold;
              margin-bottom: 0.22rem !important;
              letter-spacing: 0.03rem;
              span {
                color: #ff3043;
              }
            }
          }
          .result {
            font-size: 0.5rem;
            font-weight: bold;
            letter-spacing: 0.03rem;
          }
          .popupBtnList {
            display: flex;
            flex-direction: row;
            justify-content: space-evenly;
            margin-top: 0.8rem;
            div {
              width: 2rem;
              height: 1rem;
              line-height: 1.08rem;
              background: #5472b0;
              color: #fff;
              border-radius: 0.16rem;
              margin-bottom: 0.2rem;
              text-align: center;
              font-weight: bold;
              font-size: 0.5rem;
              letter-spacing: 0.05rem;
              text-indent: 0.05rem;
            }
          }
          .popupMessage {
            padding: 0.2rem 0;
            .popupMessage {
              line-height: 0.4rem;
              text-align: center;
              font-size: 0.5rem;
              margin-bottom: 0.3rem;
            }
            .popupMessageBtn {
              width: 20vw;
              height: 0.6rem;
              line-height: 0.68rem;
              background: #5472b0;
              font-weight: bold;
              text-align: center;
              font-size: 0.5rem;
              color: #fff;
              border-radius: 0.16rem;
              margin: 0 auto;
              margin-top: 0.2rem !important;
            }
            .popupMessageBtn:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
  </style>