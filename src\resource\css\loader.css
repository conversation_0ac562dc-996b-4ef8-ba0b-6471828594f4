body{
    background: #009688;
    padding: 0;
    margin: 0;
}
.load{
    width:240px;
    height:240px;
    position:absolute;
    top:50%;
    left:50%;
    margin:-120px 0 0 -120px;
    color:#efcc19;
    -webkit-animation:fadeIn 2s infinite ease-in-out;
    animation:fadeIn 2s infinite ease-in-out;
    -webkit-animation-delay:2s;
    animation-delay:2s;
    opacity:0;
}
.load .loader,.load .loader:before,.load .loader:after{
    background:#efcc19;
    -webkit-animation:load 1s infinite ease-in-out;
    animation:load 1s infinite ease-in-out;
    width:1em;
    height:4em
}
.load .loader:before,.load .loader:after{
    position:absolute;
    top:0;
    content:''
}
.load .loader:before{
    left:-1.5em;
    -webkit-animation-delay:-0.32s;
    animation-delay:-0.32s
}
.load .loader{
    text-indent:-9999em;
    margin:8em auto;
    position:relative;
    font-size:11px;
    -webkit-animation-delay:-0.16s;
    animation-delay:-0.16s
}
.load .loader:after{
    left:1.5em
}
@-webkit-keyframes load{
    0%,80%,100%{
        box-shadow:0 0 #efcc19;
        height:4em
    }
    40%{
        box-shadow:0 -2em #efcc19;height:5em
    }
}

@keyframes load{
    0%,80%,100%{
        box-shadow:0 0 #efcc19;
        height:4em
    }
    40%{
        box-shadow:0 -2em #efcc19;
        height:5em
    }
}

@-webkit-keyframes fadeIn{
    0%{
        opacity:0;
    }
    100%{
        opacity:1;
    }
}
@keyframes fadeIn{
    0%{
        opacity:0;
    }
    100%{
        opacity:1;
    }
}