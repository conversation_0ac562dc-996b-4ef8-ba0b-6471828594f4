<template>
  <div class="introduce">
    <div class="container">
      <div class="right scrollParent" ref="refRight">
        <div class="list" ref="refList" :style="{ top: '0rem' }">
          <div class="content">
            <h1>玩法说明</h1>
            <h2>游戏目标：</h2>
            <p>
              • 合并方块以达到2048这个数字。玩家可以继续游戏，以争取更高的分数。
            </p>
            <h2>操作方式：</h2>
            <p>• 遥控器左按键：方块向左移动。</p>
            <p>• 遥控器右按键：方块向右移动。</p>
            <p>• 遥控器上按键：方块向上移动。</p>
            <p>• 遥控器下按键：方块向下移动。</p>
            <p>• 遥控器“返回”按键实现游戏暂停。</p>
            <p>• 遥控器“中心键”为确认按键。</p>
            <h2>游戏规则：</h2>
            <p>• 游戏使用一个4x4的网格。</p>
            <p>• 游戏开始时，棋盘上会随机生成两个方块，数字为2或4。</p>
            <p>
              •
              玩家通过上下左右方向键控制方块的移动方向。所有的方块会沿着指定的方向滑动，直到遇到其他方块或棋盘边缘。
            </p>
            <p>
              •
              当两个相同数字的方块碰到一起时，它们会合并成一个数字是这两个方块和的方块（如两个2合并成一个4）。每次合并方块都会产生新的方块（通常是2或4）。
            </p>
            <p>• 每次有效的移动后，棋盘上会随机生成一个新的2或4的方块。</p>
            <p>• 如果棋盘上没有空格并且无法再进行任何合并，则游戏结束。</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  created() {
    this.$store.dispatch('index/setMainTitle', '2048')
  },
  mounted() {
    this.$store.dispatch('index/setFocusDom', null)
    this.$nextTick(() => {
          this.fuc.setScroll()
        })
    this.fuc.KeyboardEvents({
      down: () => {
        const listEl = this.$refs.refList
        const scrollBar =
          this.$refs.refList.parentNode.querySelector('.scrollBar')

        // console.log('可视区域', listEl.parentNode.clientHeight)
        // console.log('元素距离顶部', listEl.offsetTop)
        // console.log('元素高度', listEl.clientHeight)
        // console.log('下拉高度', listEl.parentNode.offsetTop)

        if (listEl && scrollBar) {
          let currentTop = parseInt(listEl.style.top, 10)
          let currentScrollTop = parseInt(scrollBar.style.top, 10)
          let listVisHeight = listEl.parentNode.clientHeight
          let listHeight = listEl.clientHeight

          const radio = listHeight / listVisHeight

          const potentialNewTop = currentTop - 150 // 预计的新top值
          const scrollNewTop = currentScrollTop + 150 / radio
          const maxScrollableHeight =
            listEl.clientHeight - listEl.parentNode.clientHeight // 最大可滚动高度
          const maxSrcollBarHeight =
            scrollBar.parentNode.clientHeight - scrollBar.clientHeight
          if (listVisHeight < listHeight) {
            // 将元素的top值与最大可滚动高度进行比较，以确定是否已经到达或超过了底部
            if (-potentialNewTop < maxScrollableHeight) {
              listEl.style.top = `${potentialNewTop}px`
            } else {
              // 已经到达或超过底部，调整top以确保内容底部和容器底部对齐
              listEl.style.top = `${-maxScrollableHeight - 50}px`
            }
          } else {
            return
          }

          if (scrollNewTop < maxSrcollBarHeight) {
            scrollBar.style.top = `${scrollNewTop}px`
          } else {
            scrollBar.style.top = `${maxSrcollBarHeight}px`
          }
        }
      },
      up: () => {
        const listEl = this.$refs.refList
        const scrollBar =
          this.$refs.refList.parentNode.querySelector('.scrollBar')

        if (listEl && scrollBar) {
          let currentTop = parseInt(listEl.style.top, 10)
          let currentScrollTop = parseInt(scrollBar.style.top, 10)
          let listVisHeight = listEl.parentNode.clientHeight
          let listHeight = listEl.clientHeight
          const radio = listHeight / listVisHeight
          const potentialNewTop = currentTop + 150 // 预计的新top值
          const scrollNewTop = currentScrollTop - 135 / radio

          // 检查是否已经到达最顶部
          if (potentialNewTop >= 0) {
            listEl.style.top = `0px` // 直接设置为0，确保不会超过顶部
            scrollBar.style.top = `0px`
          } else {
            listEl.style.top = `${potentialNewTop}px`
            scrollBar.style.top = `${scrollNewTop}px`
          }
        }
      },
      left: () => {},
      right: () => {},
      enter: () => {},
      esc: () => {
        this.$store.dispatch('index/setFocusDom', null)
        this.$router.go(-1)
      },
    })
  },
}
</script>

<style lang="less" scoped>
.introduce {
  display: flex;
  position: relative;
  .container {
    position: relative;

    .right {
      width: 16.3rem;
      height: 7rem;
      position: absolute;
      font-size: 0.21rem;
      overflow: hidden;
      border-radius: 0.24rem;

      .list {
        width: 100%;
        overflow-y: auto;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;

        .content {
          padding: 0.15rem;
          position: relative;
          width: 16rem !important;
          height: initial !important;
          h1{
            margin-bottom: 0.4rem;
          }
          h2 {
            margin-top: 0.2rem;
          }
          p{
            margin-top: 0.1rem;
            font-size: 0.3rem;
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.introduce {
  .container {
    .right {
      .scroll {
        top: 2.25rem !important;
        left: 17.8rem !important;
      }
    }
  }
}
</style>