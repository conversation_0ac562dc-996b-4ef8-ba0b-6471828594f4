<!--活动预约&&咨询服务详情-->
<template>
  <div class="consultServiceInfo">
    <div class="container">
      <div class="left scrollParent" v-if="leftList.length > 0">
        <div class="leftList"  style="top: 0rem">
          <div class="leftItem" v-for="(item,index) in leftList" :key="index" :ref="item.ref" :style="{fontSize: item.titleBig ? '0.36rem' : '0.3rem'}">
            <div>{{item.dataTimeValue.topTime}}</div>
            <div v-if="!item.titleBig">至</div>
            <div>{{item.dataTimeValue.bottomTime}}</div>

            <div class="status" v-if="item.state" :style="{background: item.state == 1 ? '#00B187' : '#E32E59'}">
              <div>{{item.state == 1 ? '已预约' : '预约已满'}}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="right scrollParent" ref="refRight" :style="{ width: leftList.length > 0 ?'12rem' : '16rem'}" v-if="showPage">
        <div class="list" ref="refList" :style="{ top: '0rem' }">
          <div class="content" v-if="dataPromise">
            <div class="subject_title">
              <div class="subject_top">
                <div class="pic" v-lazy-container="{ selector: 'img' }">
                  <img :data-src="dataPromise.img" :data-error="lazyError" :key="rightNums" alt=""  />
                </div>
                <div class="subject_title">
                  <div class="name">{{dataPromise.title}}</div>
                  <div class="max_people">最多可预约人数: {{dataPromise.max_people}} 人</div>
                </div>
              </div>
            </div>
            <div class="contentInfo">
              <div class="contentTitle">详细介绍</div>
              <div class="info" v-html="content"></div>
            </div>
          </div>
        </div>
      </div>

    </div>
    <div class="notice" v-if="supplierInfo && supplierInfo.telephone">
      <img :src="require('../../assets/phone.png')" alt="">
      客服热线: {{supplierInfo.telephone}}
    </div>

    <el-dialog
        :class="popupModule == 4 ? 'popup_info_box' : 'popup_box'"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        :title="this.supplierInfo.title"
        ref="popupBox"
    >

      <!--取消预约-->
      <div class="message_box cancel scrollParent" v-if="popupModule == 2">
        <div class="message_content" ref="popupRef" :style="{top: '0rem'}">
          <div class="message_item">
            <div class="type">
              当前状态:
            </div>
            <div class="value">
              {{ statusList[this.orderList[this.orderNums].status].text }}
            </div>
          </div>

          <div class="message_item" v-for="[key, value] in Object.entries(sendPostInfo[$route.query.type].orderObj)" :key="key">
            <template  v-if="key.indexOf('time') > -1">
              <div class="type">{{value}}：</div>
              <div class="value">{{ new Date(orderList[orderNums][key]*1000).format('yyyy/MM/dd hh:mm') }}</div>
            </template>
            <template  v-else-if="key.indexOf('dataTimeValue') > -1">
              <div class="type">{{value}}：</div>
              <div class="value" style="max-width: 74%">
                <template v-if="!isSameDay(orderList[orderNums].start_time*1000,orderList[orderNums].end_time*1000)">
                  <span>{{ new Date(orderList[orderNums].start_time * 1000).format('yyyy/MM/dd hh:mm') }} 至 <br/></span>
                  <span>{{ new Date(orderList[orderNums].end_time * 1000).format('yyyy/MM/dd hh:mm') }}</span>
                </template>
                <template v-else>
                  <span>{{ orderList[orderNums].dataTimeValue.topTime }}</span>
                  <span>{{ orderList[orderNums].dataTimeValue.bottomTime }}</span>
                </template>

              </div>
            </template>
            <template  style="display: flex" v-else>
              <div class="type">{{value}}：</div>
              <div class="value">{{orderList[orderNums][key]}}</div>
            </template>

          </div>

          <div class="message_item">
            <div class="type">商家:</div>
            <div class="value">{{ this.supplierInfo.title }}</div>
          </div>

          <div class="message_item">
            <div class="type">地址:</div>
            <div class="value">{{ this.supplierInfo.address }}</div>
          </div>

        </div>
      </div>

      <div class="message_box result" style="text-align: center; font-size: 0.37rem; justify-content: center" v-html="popupMessage" v-if="popupModule == 3"></div>

      <div class="message_box info" v-if="popupModule == 4">
        <div class="title"><span>你的预约</span><span>已经发送成功！</span></div>
        <div class="tip">您可以在“服务记录”中查看预约信息。</div>
      </div>

      <div class="popupBtnList">
        <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index"> {{ item.text }}</div>
      </div>
    </el-dialog>

  </div>
</template>
      
<script>
import {
  getActiveOrgTime,
  creatActiveOrder,
  cancelActiveOrder,
  getZiXunOrgTime,
  creatZiXunOrder,
  cancelZiXunOrder
} from '@/api/index'
import store from "@/store";

export default {
  name: 'consultServiceInfo',
  components: {},
  data() {
    return {
      showPage: true,
      sendPostInfo:{
        51: {
          leftListFuc: getZiXunOrgTime,
          leftSendPromise:{
            fk_zixun_zixunshi_id: 0,
            user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id
          },
          creadOrderFuc: creatZiXunOrder,
          creadPromise:{
            fk_zixun_time_id: 0,
            fk_supplier_id: 0,
            user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id
          },
          cancelOrderFuc: cancelZiXunOrder,
          orderListPromise: {
            user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
            fk_supplier_id: 0
          },


        },
        54: {
          leftListFuc: getActiveOrgTime,
          leftSendPromise:{
            fk_huodongyuyue_huodong_id: 0,
            user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id
          },
          creadOrderFuc: creatActiveOrder,
          creadPromise:{
            fk_huodongyuyue_time_id: 0,
            fk_supplier_id: 0,
            user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id
          },
          cancelOrderFuc: cancelActiveOrder,
          orderListPromise: {
            user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
            fk_supplier_id: 0
          },
        }
      },
      lazyError: require("@/assets/yaowu.png"),

      thisOrder: null,
      statusList:[],
      leftList: [],
      rightList: [],

      nextNums: 0, // -1  说明焦点在左侧   > -1  在右侧

      leftNums: 0,
      rightNums: 0,
      orderNums: 0,

      title: '',
      content: '',
      dataPromise: null,
      supplierInfo: null,
      playTimer: null,

      dialogVisible: false,
      popupModule: 1,   // 1、好友弹窗  2、消息提示
      popupMessage:'',
      popupBtnNums: 0,
      popupBtnList:[
        {
          text: '呼叫好友',
          ref:'',
          fuc: this.callMyfriend
        },
        {
          text: '删除好友',
          ref:'',
          fuc: this.delectMyfriend
        },
      ],
    }
  },
  created() {
    this.supplierInfo = JSON.parse(sessionStorage.getItem('supplierInfo'))
    if (localStorage.getItem('statusList')) {
      this.statusList = JSON.parse(localStorage.getItem('statusList'))
    }
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', JSON.parse(sessionStorage.getItem('redirectInfo')).title)
  },
  computed: {},
  watch: {

  },
  mounted() {
    if (sessionStorage.getItem('consultData')) {
      this.dataPromise = JSON.parse(sessionStorage.getItem('consultData'))
    } else {
      return
    }

    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if ((Math.abs(styleTop) +scrollPageTop)  * fontSize > this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight) {
                this.$refs.popupRef.style.top = styleTop - (scrollTop/fontSize - Math.abs(styleTop)) + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * ((scrollTop/fontSize - Math.abs(styleTop)) * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight)) + 'px'
                return
              }
              if (scrollTop > 0) {
                this.$refs.popupRef.style.top = styleTop - scrollPageTop + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))   + 'px'
              }
            }
          }
          return
        }
        if (this.nextNums > -1) {
          const listEl = this.$refs.refList
          const scrollBar = this.$refs.refList.parentNode.querySelector('.scrollBar')

          if (listEl && scrollBar) {
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight

            const radio = listHeight / listVisHeight

            const potentialNewTop = currentTop - 150 // 预计的新top值

            const scrollNewTop = currentScrollTop + 150 / radio
            const maxScrollableHeight = listEl.clientHeight - listEl.parentNode.clientHeight // 最大可滚动高度
            const maxSrcollBarHeight = scrollBar.parentNode.clientHeight - scrollBar.clientHeight
            if (listVisHeight < listHeight) {
              // 将元素的top值与最大可滚动高度进行比较，以确定是否已经到达或超过了底部
              if (-potentialNewTop < maxScrollableHeight) {
                listEl.style.top = `${potentialNewTop}px`
              } else {
                // 已经到达或超过底部，调整top以确保内容底部和容器底部对齐
                listEl.style.top = `${-maxScrollableHeight - 50}px`
              }
            } else {
              return
            }

            if (scrollNewTop < maxSrcollBarHeight) {
              scrollBar.style.top = `${scrollNewTop}px`
            } else {
              scrollBar.style.top = `${maxSrcollBarHeight}px`
            }
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }else
        if (this.nextNums == -1 &&  this.leftNums < this.leftList.length - 1) {
          this.leftList[this.leftNums].ref = ""
          this.leftNums ++
          this.leftList[this.leftNums].ref = "active"
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }

      },
      up: () => {
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if (scrollTop > 0) {
                if ((styleTop + scrollPageTop) > 0) {
                  this.$refs.popupRef.style.top = '0rem'
                  scrollBar.style.top = '0px'
                  return
                } else {
                  this.$refs.popupRef.style.top = styleTop + scrollPageTop + 'rem'
                  scrollBar.style.top = barTop - (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))  + 'px'
                }
              }
            }
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          const listEl = this.$refs.refList
          // const scrollBar = this.$refs.refScroll
          const scrollBar = this.$refs.refList.parentNode.querySelector('.scrollBar')

          if (listEl && scrollBar) {
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight
            const radio = listHeight / listVisHeight
            const potentialNewTop = currentTop + 150 // 预计的新top值
            const scrollNewTop = currentScrollTop - 135 / radio

            // 检查是否已经到达最顶部
            if (potentialNewTop >= 0) {
              listEl.style.top = `0px` // 直接设置为0，确保不会超过顶部
              scrollBar.style.top = `0px`
            } else {
              listEl.style.top = `${potentialNewTop}px`
              scrollBar.style.top = `${scrollNewTop}px`
            }
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }else
        if (this.nextNums == -1 &&  this.leftNums > 0) {
          this.leftList[this.leftNums].ref = ""
          this.leftNums --
          this.leftList[this.leftNums].ref = "active"
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }

      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1 && this.leftList.length > 0) {
          this.$refs.active = []
          this.nextNums = -1
          this.leftList[this.leftNums].ref = "active"
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }

        // 在左
        if (this.nextNums == -1) {
          this.leftList[this.leftNums].ref = ""
          this.$refs.active.push(this.$refs.refRight)
          this.nextNums = 0
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        if (this.dialogVisible) {
          // 弹窗
          this.popupBtnList[this.popupBtnNums].ref = ''
          // 取消订单关闭时
          if (this.nextNums == -1) {
            this.leftList[this.leftNums].ref = 'active'
          } else if(this.nextNums == -2 && this.orderList[this.orderNums]) {
            this.orderList[this.orderNums].ref = 'active'
          } else {
            this.rightList[this.rightNums].ref = 'active'
          }


          this.popupBtnList[this.popupBtnNums].fuc(this.rightList[this.rightNums])
          this.dialogVisible = false
          return
        }
        // 在左侧
        if (this.nextNums == -1) {
          this.leftList[this.leftNums].ref = ''
          this.popupModule = 3

          this.popupMessage = `<div>请确认预约时间<br>${this.leftList[this.leftNums].dataTimeValue.topTime} ${this.leftList[this.leftNums].dataTimeValue.bottomTime}</div>`
          if (!this.leftList[this.leftNums].titleBig) {
            this.popupMessage = `<div>请确认预约时间<br>${this.leftList[this.leftNums].dataTimeValue.topTime}<br>至<br>${this.leftList[this.leftNums].dataTimeValue.bottomTime}</div>`
          }
          this.popupBtnNums =  0
          this.popupBtnList = [
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            },{
              text: '发起预约',
              ref: '',
              fuc: this.sendOrder,
            },
          ]
          if (this.leftList[this.leftNums].state == 1) {
            this.popupMessage = `<div>是否取消当前预约<br>${this.leftList[this.leftNums].dataTimeValue.topTime} ${this.leftList[this.leftNums].dataTimeValue.bottomTime}</div>`
            if (!this.leftList[this.leftNums].titleBig) {
              this.popupMessage = `<div>是否取消当前预约<br>${this.leftList[this.leftNums].dataTimeValue.topTime}<br>至<br>${this.leftList[this.leftNums].dataTimeValue.bottomTime}</div>`
            }
            this.popupBtnList[1] = {
              text: '取消预约',
              ref: '',
              fuc: this.cancelOrder,
            }
          }



          this.popupBtnList[this.popupBtnNums].ref="active"
          this.dialogVisible = true
          // this.sendOrder()
        }
      },
      esc: () => {
        if (this.$store.getters.loadingState) {
          clearTimeout(this.playTimer)
          this.playTimer = null
          this.$store.dispatch('app/setLoadingState', false)
          return
        }

        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      if (this.leftNums == 0) {
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0]);
          document.getElementById('focus_border').style.borderColor = "#fff"
          this.popupBtnNums = 0
          this.popupModule = 1
        })
      }
    },
    popupOpend() {
      this.$nextTick(()=>{
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0]);
        document.getElementById('focus_border').style.borderColor = "#4b68a7"
      })
    },
    isSameDay(date1, date2) {
      return new Date(date1).toDateString() === new Date(date2).toDateString();
    },
    getData(infoType) {
      this.content = this.dataPromise.content
      if (this.content && this.content.indexOf('<img') > -1) {
        let reg = new RegExp('/public/storage/', 'g')
        let regImg = new RegExp('<img', 'g')
        this.content = this.content.replace(reg, process.env.VUE_APP_API + '/public/storage/')
        //this.content = this.content.replace(regImg, '<img style="width: 10rem" ')

        this.content = this.content
            // 匹配包含 style 的 img 标签，并在 style 内增加 width:10rem
            .replace(/(<img[^>]*style="[^"]*)"/g, '$1; width:90%"')
            // 匹配没有 style 的 img 标签，并增加 style="width:10rem"
            .replace(/(<img(?![^>]*style)[^>]*)(>)/g, '$1 style="width:90%"$3');

        this.content = this.content
            .replace(/(<p[^>]*style="[^"]*)"/g, '$1; word-break:break-all;"')
            .replace(/(<p(?![^>]*style)[^>]*)(>)/g, '$1 style="word-break:break-all;"$3');
      }

      Object.keys(this.sendPostInfo[this.$route.query.type].leftSendPromise).forEach(key => {
        if (key.indexOf('user_id') == -1) {
          this.sendPostInfo[this.$route.query.type].leftSendPromise[key] = this.dataPromise.id
        }
      });

      this.sendPostInfo[this.$route.query.type].leftListFuc(this.sendPostInfo[this.$route.query.type].leftSendPromise)
      .then(res=>{
        if (res.code == 200) {
          this.showPage = true
          let dataInfo = JSON.parse(JSON.stringify(res.data))

          // dataInfo = dataInfo.concat(JSON.parse(JSON.stringify(res.data)))
          // dataInfo = dataInfo.concat(JSON.parse(JSON.stringify(res.data)))
          // dataInfo = dataInfo.concat(JSON.parse(JSON.stringify(res.data)))
          // dataInfo = dataInfo.concat(JSON.parse(JSON.stringify(res.data)))
          // dataInfo = dataInfo.concat(JSON.parse(JSON.stringify(res.data)))
          // dataInfo = dataInfo.concat(JSON.parse(JSON.stringify(res.data)))
          // dataInfo = dataInfo.concat(JSON.parse(JSON.stringify(res.data)))
          // dataInfo = dataInfo.concat(JSON.parse(JSON.stringify(res.data)))
          // dataInfo = dataInfo.concat(JSON.parse(JSON.stringify(res.data)))


          dataInfo.map(item=>{
              item.ref = ""
              item.titleBig = true
              if (!this.isSameDay(item.start_time*1000,item.end_time*1000)) {
                item.titleBig = false
                item.dataTimeValue = {
                  topTime: new Date(item.start_time * 1000).format('MM月dd日 hh:mm'),
                  bottomTime: new Date(item.end_time * 1000).format('MM月dd日 hh:mm')
                }
              } else {
                item.dataTimeValue = {
                  topTime: new Date(item.start_time * 1000).format('MM月dd日'),
                  bottomTime: new Date(item.start_time * 1000).format('hh:mm') + '-' + new Date(item.end_time * 1000).format('hh:mm')
                }
              }
          })
          this.leftList = dataInfo

          console.log(this.$refs.refRight)
          if (!infoType) {
            this.$refs.active = []
            this.$refs.active.push(this.$refs.refRight)
            this.nextNums = 0
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
              this.fuc.setScroll()
            })
            setTimeout(() => {
              this.fuc.setScroll()
            }, 100)
          }

        }
      })
    },
    sendOrder() {
      Object.keys(this.sendPostInfo[this.$route.query.type].creadPromise).forEach(key => {
        if (key.indexOf('time_id') > -1) {
          this.sendPostInfo[this.$route.query.type].creadPromise[key] = this.leftList[this.leftNums].id
        }
        if (key.indexOf('supplier_id') > -1) {
          this.sendPostInfo[this.$route.query.type].creadPromise[key] = this.supplierInfo.fk_supplier_id
        }

      });
      this.sendPostInfo[this.$route.query.type].creadOrderFuc(this.sendPostInfo[this.$route.query.type].creadPromise)
      .then(res=>{
        if (res.code == 200) {
          this.leftList[this.leftNums].ref = ""
          this.leftList[this.leftNums].state = 1

          this.popupModule = 3
          this.popupBtnNums =  0
          this.popupMessage = `<div>您的预约已发送成功！<br>您可以在“服务记录”中查看预约信息。</div>`
          this.popupBtnList = [
            {
              text: '关闭',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            }
          ]
          this.dialogVisible = true
          this.getData(true)
        }
      })
      .catch(error => {
        this.leftList[this.leftNums].ref = ""
        this.$store.dispatch('app/setLoadingState', false)
        this.popupModule = 3
        this.popupBtnList = [
          {
            text: '确认',
            ref: 'active',
            fuc: () => {
              this.dialogVisible = false
            },
          },
        ]
        this.popupMessage = '<br/>预约失败!<br>'
        if (error.response && error.response.data && error.response.data.msg) {
          this.popupMessage += error.response.data.msg
        }
        this.popupBtnNums = 0
        this.dialogVisible = true
      })
    },
    cancelOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      Object.keys(this.sendPostInfo[this.$route.query.type].orderListPromise).forEach(key => {
        if (key.indexOf('supplier_id') > -1) {
          this.sendPostInfo[this.$route.query.type].orderListPromise[key] = this.supplierInfo.fk_supplier_id
        }
      });
      let orderID = this.leftList[this.leftNums].order_id
      this.sendPostInfo[this.$route.query.type].orderListPromise.order_id = orderID
      this.sendPostInfo[this.$route.query.type].cancelOrderFuc(this.sendPostInfo[this.$route.query.type].orderListPromise)
      .then(res => {
        if (res.code == 200) {

          this.leftList[this.leftNums].state = 0

          this.popupModule = 3
          this.leftList[this.leftNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br/>取消预约成功!<br>'
          this.popupBtnNums = 0
          this.dialogVisible = true
          this.getData(true)
        }
        this.$store.dispatch('app/setLoadingState', false)
      })
      .catch(error => {
        this.$store.dispatch('app/setLoadingState', false)
        this.popupModule = 3
        this.leftList[this.leftNums].ref = ''
        this.popupBtnList = [
          {
            text: '确认',
            ref: 'active',
            fuc: () => {
              this.dialogVisible = false
            },
          },
        ]
        this.popupMessage = '<br/>取消失败!<br>'
        if (error.response && error.response.data && error.response.data.msg) {
          this.popupMessage += error.response.data.msg
        }
        this.popupBtnNums = 0
        this.dialogVisible = true
      })
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.consultServiceInfo {
  display: flex;
  position: relative;
  width: 100%;
  height: 7rem;
  .notice {
    position: fixed;
    font-size: 0.28rem;
    right: 1.2rem;
    bottom: 0.6rem;
    color: #B2BAEF;
    display: flex;
    flex-direction: row;
    align-items: center;
    img {
      position: relative;
      top: 0.02rem;
      left: -0.05rem;
      width: 0.32rem;
      height: 0.32rem;
    }
  }
  .container {
    position: relative;
    width: 100%;
    height: 100%;
    .left {
      width: 3.2rem;
      height: 7rem;
      position: absolute;
      top: 0 !important;
      overflow: hidden;
      .leftList {
        background-size: 100% 100% !important;
        cursor: pointer;
        overflow: hidden;
        position: relative;
        .leftItem {
          width: 100%;
          height: 1.2rem;
          padding: 0.45rem 0;
          font-size: 0.36rem;
          border-radius: 0.24rem;
          background: #5472B0;
          margin-bottom: 0.35rem;
          font-weight: bold;
          margin-right: 0.28rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          position: relative;
          overflow: hidden;
          .status {
            width: 100%;
            height: 0.4rem;
            color: #fff;
            font-size: 0.22rem;
            font-weight: bold;
            text-align: center;
            position: absolute;
            bottom: 0;
            line-height: 0.4rem;
            letter-spacing: 0.03rem;

          }
        }
      }

    }

    .right {
      height: 6.8rem;
      position: absolute;
      right: 0.4rem;
      background: #25294f;
      font-size: 0.21rem;
      overflow: hidden;
      border-radius: 0.24rem;
      padding: 0.2rem;
      padding-bottom: 0;
      .list {
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        .content {
          position: relative;
          width: calc(100% - 1rem) !important;
          height: initial !important;
          padding: 0.5rem;
          .subject_title {
            .subject_top {
              display: flex;
              align-items: center;
              .pic {
                width: 2.4rem;
                height: 2.8rem;
                border-radius: 0.2rem;
                overflow: hidden;
                margin-right: 0.4rem;
                border: 0.02rem solid #BCBED2;
                background: #fff;
                img {
                  display: block;
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }
              .name {
                color: #fff;
                font-size: 0.38rem;
                font-weight: bold;
                margin: 0.1rem 0;
              }
              .max_people {
                color: #B5C0FD;
                font-size: 0.26rem;
              }
            }
          }
          .contentInfo {
            .contentTitle {
              width: 1.8rem;
              height: 0.45rem;
              background: #4991FF;
              border-radius: 0.3rem;
              text-align: center;
              color: #fff;
              font-size: 0.26rem;
              font-weight: bold;
              line-height: 0.55rem;
              margin: 0.2rem 0 0.3rem 0;
            }
            .info {
              width: 100%;
              color: #B5C0FD;
              font-size: 0.24rem;
              text-indent: 0.5rem;
              text-align: justify;
            }
          }
        }
      }
    }

    .done {
      width: 3.9rem;
      position: fixed;
      top: 1.6rem;
      right: 0.9rem;
      .item {
        font-size: 0.36rem;
        font-weight: bold;
        letter-spacing: 0.03rem;
        color: #b5c0ff;
      }
      .item::before {
        content: '';
        width: 0.1rem;
        height: 0.1rem;
        position: absolute;
        background: #b5c0ff;
        border-radius: 50%;
        top: 50%;
        left: -0.25rem;
      }
    }
  }
}
</style>
<style lang="less">
.consultServiceInfo {
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    // background: repeating-linear-gradient(to right, #c98693, #a95361);
    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        letter-spacing: 0.03rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        justify-content: center !important;
        align-items: center !important;
        .title {
          font-size: 0.48rem;
          font-weight: bold;
          // padding-bottom: 0.12rem !important;
          p {
            margin-bottom: 0.22rem;
          }
        }
      }
      .result {
        font-size: 0.48rem !important;
        font-weight: bold !important;
        letter-spacing: 0.03rem !important;
      }
      .cancel {
        flex-direction: column;
        div {
          text-align: center;
        }
        div:nth-child(2) {
          margin-top: 0.1rem;
          color: red;
          line-height: 0.48rem;
        }
        //align-items: normal !important;
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

          // background: repeating-linear-gradient(to right, #c98693, #a95361);
        }
      }
    }
  }
  .container {
    .left {
      .scroll {
        left: 4.74rem !important;
        top: 2.26rem !important;
      }
    }
    .right {
      .scroll {
        top: 2.25rem !important;
        left: 17.8rem !important;
      }
    }
  }
}
</style>
      