.tetri_index {
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    //display: flex;
    //flex-direction: row;

}

.r {
    float: right;
}
.l {
    float: left;
}
.clear {
    clear: both;
}
b {
    display: block;
    width: 0.28rem;
    height: 0.28rem;
    padding: 0.02rem;
    border: 0.02rem solid #879372;
    margin: 0 0.02rem 0.02rem 0;
    float: left;
    &:after {
        content: '';
        display: block;
        width: 0.28rem;
        height: 0.28rem;
        background: #879372;
        overflow: hidden;
    }
    &.c {
        border-color: #000;
        &:after {
            background: #000;
        }
    }
    &.d {
        border-color: #560000;
        &:after {
            background: #560000;
        }
    }
}
.bg {
    background: url('//img.alicdn.com/tps/TB1qq7kNXXXXXacXFXXXXXXXXXX-400-186.png') no-repeat;
    overflow: hidden;
}



.rect {
    width: 4rem;
    height: 100%;
    //margin: 0 auto;
    position: relative;
    &.drop {
        -webkit-transform: translateY(5px);
        transform: translateY(5px);
    }
}

.screen {
    width: 100%;
    height: 101%;
    //border: solid 0.05rem;
    border-color: #987f0f #fae36c #fae36c #987f0f;
    margin: 0 auto;
    position: relative;
    position: absolute;
    top: -0.6rem;
    .panel {
        width: 100%;
        height: 100%;
        margin: 0 auto;
        background: #9ead86;
        padding: 8px;
        //border: 2px solid #494536;
    }
}

.state {
    width: 1.5rem;

    p {
        line-height: 47px;
        height: 57px;
        padding: 10px 0 0;
        white-space: nowrap;
        clear: both;
        color: #000 !important;
    }
    .bottom {
        position: absolute;
        width: 114px;
        top: 426px;
        left: 0;
    }
}

.logo {
    left: 0.15rem;
    top: 3rem;

}
