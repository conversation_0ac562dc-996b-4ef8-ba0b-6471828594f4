<template>
  <div id="container"></div>
</template>
<script>
import AMapLoader from "@amap/amap-jsapi-loader";

export default {
  name: "map-view",
  props:{
    mapKey: String,
    center:Array,
    markers: Array,
    polyline: Object,
    mapLoad: Function
  },
  data(){
    return {
      map: null,
      driverPosition: null,
      polylineMarkers: null,
      AMap: null
    }
  },
  watch:{
    "center": {
      handler(){
        this.initAMap()
      },
      deep: true,
      immediate: true
    }
  },
  created(){
    this.map?.destroy();
    this.map = null
  },
  mounted() {
    // this.initAMap();
  },
  destroyed() {
    this.map?.destroy();
  },
  unmounted() {
    this.map?.destroy();
  },
  methods: {
    initAMap() {
      if (!this.map) {
        AMapLoader.load({
          key: this.mapKey, // 申请好的Web端开发者Key，首次调用 load 时必填
          // version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
          plugins: ["AMap.ElasticMarker",], //2.0  "AMap.MoveAnimation" 需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
        })
        .then((AMap) => {
          this.AMap = AMap
          this.map = new AMap.Map("container", {
            // 设置地图容器id
            viewMode: "2D", // 是否为3D地图模式
            zoom: 18, // 初始化地图级别
            resizeEnable: true,
            scrollWheel: true,
            center: this.center, // 初始化地图中心点位置
          });
          this.setMarkers()
          this.setPolyline()

          this.map.on('complete',()=>{
            if (this.mapLoad) {
              this.mapLoad(true)
            }
          })



        })
        .catch((e) => {
          console.log(e);
        });
      } else {
        this.map.setCenter(this.center,false,300)
        this.setPolyline()
      }
      if (this.driverPosition) {
        this.driverPosition.moveTo(this.center,1000);

        // setAngle
        // version 2.0
        // this.driverPosition.moveTo(this.center,{
        //   duration: 1000,
        //   autoRotation: true
        // });
      }
    },
    setMarkers() {
          let markerCopy = JSON.parse(JSON.stringify(this.markers))
          markerCopy[0].map = this.map
          markerCopy[1].map = this.map
          let myPosition = new AMap.Marker(markerCopy[1])
          this.map.add(myPosition);

          this.driverPosition = new AMap.Marker(markerCopy[0])
          this.map.add(this.driverPosition);
    },
    setPolyline () {
        let polylineList = JSON.parse(JSON.stringify(this.polyline))
        if (this.polylineMarkers) {
          this.map.remove(this.polylineMarkers);
        }
        let moveLong = []
        polylineList.path.map(item=>{
          item.polyline.split(';').map(items=>{
            let obj = [Number(items.split(',')[0]), Number(items.split(',')[1])]
            moveLong.push(obj)
          })
        })
      if (moveLong.length > 0) {
        polylineList.path = moveLong
        this.polylineMarkers = new AMap.Polyline(polylineList)
        this.map.add(this.polylineMarkers);
      }

    }
  },
};
</script>
<style scoped>
#container {
  width: 100%;
  height: 100%;

}

</style>

<style lang="less">
  #container {
    .amap-marker-label {
      color: #000 !important;
    }
  }
</style>