<template>
  <div class="leaseDetail">
    <div class="container">
      <div class="left">
        <div class="left-up">
          <div class="videoList" ref="refVideo">
            <!-- <video :src="rela_video_url" controls autoplay loop></video> -->
          </div>
        </div>
        <div class="left-down">
          <div class="lease" ref="refLease">
            <div>租借申请</div>
          </div>
        </div>
      </div>

      <div class="right scrollParent" ref="refRight">
        <div class="list" ref="refList" :style="{ top: '0rem' }">
          <div class="price">
            押金：<span>{{ list.deposit_price }}元</span>
          </div>
          <div class="info">
            单次租赁价格：<span>{{ list.price }}元</span>（为期{{ list.month }}个月）
          </div>
          <div class="subsidy">
            补贴后的租赁价格：<span>{{ list.subsidy }}元</span>（{{ list.month }}个月）<span
              >{{ list.subsidy / list.month }}元/月</span
            >
          </div>
          <div class="pic">
            <img :src="apiUrl+list.detail_pic" alt="" />
          </div>
        </div>
      </div>
      <el-dialog
        class="popup_box"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        title="辅具租赁"
      >
        <!--发起预约-->
        <div class="message_box sendCall" v-if="popupModule == 1">
          <div class="title">
            <p>请确认需要租借的辅具。</p>
            <span>租借辅具：</span><span>{{ list.name }}</span>
          </div>
        </div>
        <div
          class="message_box result"
          style="text-align: center; font-size: 0.37rem; justify-content: center"
          v-html="popupMessage"
          v-if="popupModule == 2"
        ></div>
        <div class="popupBtnList">
          <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index">
            {{ item.text }}
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
      
<script>
import { submitLeaseList } from '@/api/index'
import media from '@/utils/mediaPlayer.js'

export default {
  name:'leaseDetail',
  components: {},
  data() {
    return {
      timeNum: new Date().getTime(),
      leftList: [],
      rightList: [],
      apiUrl:process.env.VUE_APP_API,
      list: {},
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      isReady: false,
      playTimer: null
    }
  },
  created() {
    this.$store.state.app.media = null
    //设置页面左上角标题
    this.$nextTick(() => {
      this.$store.dispatch('index/setMainTitle', this.list.name)
    })
  },
  computed: {},
  watch: {},
  mounted() {
    this.list = JSON.parse(this.$route.query.list)

    setTimeout(() => {
      this.fuc.setScroll()
    }, 100)
    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
          return
        }
        if (this.dialogVisible) {
          return
        }
        // 在左上
        if (this.nextNums == -2) {
          this.$refs.active = []
          this.$refs.active.push(this.$refs.refLease)
          this.nextNums = -1
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        //右侧
        if (this.nextNums > -1) {
          const listEl = this.$refs.refList
          const scrollBar = this.$refs.refList.parentNode.querySelector('.scrollBar')

          // console.log('可视区域', listEl.parentNode.clientHeight)
          // console.log('元素距离顶部', listEl.offsetTop)
          // console.log('元素高度', listEl.clientHeight)
          // console.log('下拉高度', listEl.parentNode.offsetTop)

          if (listEl && scrollBar) {
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight
            const radio = listHeight / listVisHeight

            const potentialNewTop = currentTop - 150 // 预计的新top值
            const scrollNewTop = currentScrollTop + 150 / radio
            const maxScrollableHeight = listEl.clientHeight - listEl.parentNode.clientHeight // 最大可滚动高度
            const maxSrcollBarHeight = scrollBar.parentNode.clientHeight - scrollBar.clientHeight
            // 将元素的top值与最大可滚动高度进行比较，以确定是否已经到达或超过了底部
            if (-potentialNewTop < maxScrollableHeight) {
              listEl.style.top = `${potentialNewTop}px`
            } else {
              // 已经到达或超过底部，调整top以确保内容底部和容器底部对齐
              listEl.style.top = `${-maxScrollableHeight - 50}px`
            }

            if (scrollNewTop < maxSrcollBarHeight) {
              scrollBar.style.top = `${scrollNewTop}px`
            } else {
              scrollBar.style.top = `${maxSrcollBarHeight}px`
            }
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      up: () => {
        if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
          return
        }
        if (this.dialogVisible) {
          return
        }
        // 在左下
        if (this.nextNums == -1) {
          this.$refs.active = []
          this.$refs.active.push(this.$refs.refVideo)
          this.nextNums = -2
        }
        // 右侧
        if (this.nextNums > -1) {
          const listEl = this.$refs.refList
          const scrollBar = this.$refs.refList.parentNode.querySelector('.scrollBar')
          if (listEl && scrollBar) {
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight
            const radio = listHeight / listVisHeight
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            const potentialNewTop = currentTop + 150 // 预计的新top值
            const scrollNewTop = currentScrollTop - 135 / radio

            // 检查是否已经到达最顶部
            if (potentialNewTop >= 0) {
              listEl.style.top = `0px` // 直接设置为0，确保不会超过顶部
              scrollBar.style.top = `0px`
            } else {
              listEl.style.top = `${potentialNewTop}px`
              scrollBar.style.top = `${scrollNewTop}px`
            }
          }
        }

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
          return
        }
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          this.$refs.active = []
          this.$refs.active.push(this.$refs.refLease)
          this.nextNums = -1
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
          return
        }
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左下
        if (this.nextNums == -1) {
          this.$refs.active = []
          this.$refs.active.push(this.$refs.refRight)
          this.nextNums = 0
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 在左上
        else if (this.nextNums == -2) {
          this.$refs.active = []
          this.$refs.active.push(this.$refs.refRight)
          this.nextNums = 0
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        if (this.nextNums == -2) {
          if (this.$store.state.app.media) {
            if (this.$store.state.app.media.isFullscreen) {
              return
            }
            // 全屏切换
            this.$store.state.app.media.fullscreen('t')
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', document.body)
            })
          }
        }
        if (this.$refs.active && this.nextNums == -1) {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.$refs.active.push(this.$refs.refLease)
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].fuc(this.leftList[this.leftNums])
            return
          }
          if (this.$store.state.app.media) {
            let time = new Date().getTime()
            if (time - this.timeNum < 600) {
              return
            }
            this.$store.state.app.media.stop(()=>{
              this.$store.state.app.media = null
              this.isReady = false
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                    this.playVideo()
                  },
                },
                {
                  text: '确认',
                  ref: '',
                  fuc: this.submitInfo,
                },
              ]
              this.dialogVisible = true
              this.popupModule = 1
              this.$refs.active = ''
              this.popupBtnList[this.popupBtnNums].ref = 'active'
            })
            return
          }
          clearTimeout(this.playTimer)
          this.playTimer = null
          this.$store.state.app.media = null
          this.isReady = false
          this.popupBtnList = [
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
                this.playVideo()
              },
            },
            {
              text: '确认',
              ref: '',
              fuc: this.submitInfo,
            },
          ]
          this.dialogVisible = true
          this.popupModule = 1
          this.$refs.active = ''
          this.popupBtnList[this.popupBtnNums].ref = 'active'
        }
      },
      esc: () => {
        if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
          this.$store.state.app.media.fullscreen('t')
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
          return
        }
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.$refs.active.push(this.$refs.refLease)
          this.dialogVisible = false
          this.playVideo()
          return
        }

        let time = new Date().getTime()
        if (time - this.timeNum < 0) {
          return
        }
        if (this.$store.state.app.media) {
          if (!this.isReady) {
            return
          }
          this.$store.state.app.media.stop(()=>{
            this.isReady = false
            this.$store.dispatch('index/setFocusDom', null);
            history.go(-1)
          })
          return
        }
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    playVideo() {
      clearTimeout(this.playTimer)
      this.playTimer = null
      this.playTimer = setTimeout(()=> {
        this.isReady = false
        this.$nextTick(() => {
          var obj = {
            x: 120,
            y: 224,
            w: 440,
            h: 250,
            r: 0,
          }
          let fontSize =
              Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0]) / 100
          let url = process.env.VUE_APP_API + this.list.rela_video_url
          let videoList = [url]

          this.$store.state.app.media = media.DetermineKernel({
            videoList: videoList,
            loop: 1,
            windowPos: obj,
            fontSize: fontSize,
          }, () => {
            this.isReady = true
          })
        })
      },1000)
    },
    popupClose() {
      this.timeNum = new Date().getTime()
      // this.playVideo()
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        this.popupBtnNums = 0
        this.popupModule = 1
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        if (this.$store.state.app.media) {
          this.$store.state.app.media.stop()
        }

        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },

    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      if (this.nextNums == -1) {
        this.$refs.active = []
        this.$refs.active.push(this.$refs.refLease)
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
          this.playVideo()
        })
      }
    },
    submitInfo() {
      this.$store.dispatch('app/setLoadingState', true)
      submitLeaseList({
        home_id: this.$store.getters.getUserInfo.home_id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        supplier_id: JSON.parse(sessionStorage.getItem('supplierInfo')).fk_supplier_id,
        supplier_type: 13,
        id: this.list.id,
      }).then(
        (res) => {
          if (res.code == 200) {
            this.popupModule = 2
            this.$refs.active = ''
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  history.go(-1)
                  // this.playVideo()
                },
              },
            ]
            this.popupMessage = '租借成功！'
            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 2
          this.$refs.active = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
                this.playVideo()
              },
            },
          ]
          this.popupMessage = '<br>租借失败!</br>'
          if (error.response && error.response.data && error.response.data.msg) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },
  },
  destroyed() {
    clearTimeout(this.playTimer)
    this.playTimer = null
  },
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.leaseDetail {
  display: flex;
  position: relative;
  .container {
    position: relative;
    .left {
      width: 4.4rem;
      height: 7.3rem;
      position: absolute;
      .left-up {
        width: 4.4rem;
        height: 2.5rem;
        position: absolute;
        left: 0rem;
        overflow: hidden;
        .videoList {
          width: 4.4rem;
          height: 2.5rem;
          position: absolute;
          left: 0rem;
          width: 100%;
          // height: 2.7rem;
          video {
            width: 100%;
            height: auto;
          }
        }
      }
      .left-down {
        width: 4.4rem;
        height: 3.6rem;
        position: absolute;
        // top: 0.2rem;
        bottom: 0.2rem;
        overflow: hidden;
        border-radius: 0.24rem;
        .lease {
          width: 100%;
          height: 1.1rem;
          position: absolute;
          bottom: 0rem;
          font-size: 0.48rem;
          font-weight: bold;
          letter-spacing: 0.1rem;
          text-align: center;
          line-height: 1.1rem;
          background-image: linear-gradient(to right, #5f7eb7, #4763a2);
          border-radius: 0.3rem;
        }
      }
    }

    .right {
      width: 10.5rem;
      height: 6.65rem;
      position: absolute;
      padding: 0.1rem;
      top: 0.3rem;
      left: 5.5rem;
      border-radius: 0.24rem;
      overflow: hidden;
      .list {
        // overflow-y: auto;
        position: relative;
        transition: all 0.3s;
        .price {
          font-size: 0.3rem;
          font-weight: bold;
          span {
            color: #f64e23;
            font-size: 0.4rem;
          }
        }
        .info,
        .subsidy {
          font-size: 0.3rem;
          font-weight: bold;
          letter-spacing: 0.03rem;
          margin-top: 0.06rem;
          span {
            color: #f64e23;
          }
        }

        .pic {
          width: 100%;
          // height: 5rem;
          margin-top: 0.1rem;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.leaseDetail {
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    // background: repeating-linear-gradient(to right, #c98693, #a95361);
    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;

      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        letter-spacing: 0.03rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        justify-content: center !important;
        align-items: center !important;
        .title {
          font-size: 0.48rem;
          font-weight: bold;
          // padding-bottom: 0.12rem !important;
          p {
            margin-bottom: 0.22rem;
          }
        }
      }
      .result {
        font-size: 0.48rem !important;
        font-weight: bold !important;
        letter-spacing: 0.03rem !important;
      }
      .cancel {
        flex-direction: column;
        div {
          text-align: center;
        }
        div:nth-child(2) {
          margin-top: 0.1rem;
          color: red;
          line-height: 0.48rem;
        }
        //align-items: normal !important;
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

          // background: repeating-linear-gradient(to right, #c98693, #a95361);
        }
      }
    }
  }
  .container {
    .right {
      .scroll {
        top: 2.52rem !important;
        left: 17.8rem !important;
      }
    }
  }
}
</style>
      