<template>
<div class='media_call'>
    <div class="point_text">
        <div class="text_cont">

            <div class="point_message" v-for="(text,index) in pointList" :key="index" >
                <p v-if='(index!=0)'>
                    <span >{{index}}</span>
                </p>
              <img v-else src="../assets/customer.png" alt="">
              <p v-html="text"></p>
            </div>
        </div>
        <div class="audio_state">
            <img class='music_state' :src="audioStateSrc" alt="">
            <div>{{playing}}</div>
            <div>{{recording}}</div>
            <p class='audio_time'>{{this.fuc.formatSeconds(this.time)}}</p>
        </div>
        <audio ref='playAudio' :src="(audioSrc !='' ? audioSrc : defaultAudioSrc)"></audio>
        <div class="button">
            <div :ref='item.ref' v-for='(item,index) in playBtnList' :key='index' :class="(index == 0 ? btnClass : '')">
                {{item.text}}
            </div>
        </div>
    </div>
    <div class="point_pic"></div>

    <el-dialog
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        custom-class="operatePopup"
        title="提示"
    >


      <!--消息弹窗-->
      <div class="popupMessageBox">
        <div class="popupMessage" v-html="popupMessage"></div>
        <div class="messageBtn" >
          <div class="btnItem" :ref="item.ref"  v-for="(item, index) in popupBtnList" :key="index" >
            {{ item.text }}
          </div>
        </div>
      </div>

    </el-dialog>

</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import AlertDialog from "@/components/AlertDialog.vue";
export default {
//import引入的组件需要注入到对象中才能使用
    name:'media_call_message',
    components: {
        AlertDialog
    },
    data() {
        return {
            pageType: 0,
            btnClass:'', //btnClass
            pointList:['欢迎您使用留言咨询服务。','请按住遥控器上的&nbsp;<img style="display:inline-block;width:0.6rem;height:0.6rem" src="' +require(`../assets/voice_icon.png`)+ '"/>&nbsp;按键。','请说出需求，最后请您留下您的联系方式，有助于我们能及时联系上您。'],
            borderIndex:0,
            playBtnList:[
              {
                  text:'播放',
                  ref:'',
              },{
                  text:'发送',
                  ref:'',
              }
            ],
            timerOut:null,
            defaultAudioSrc: require(`../assets/voice/other.mp3`),
            audioStateSrc:require('../assets/null_play_audio.png'),
            audioSrc:'',
            audioState: '-1',   // 0 开始录音  1 停止录音  2 录音文件转换完成 3正在播放
            playing:'无文件',
            recording: '未录音',
            time:0,
            timeEvery:0,
            timerIn:null,
            backHome_time:10,
            dialogVisible: false,
            popupType: true,
            popupBtnList:[{text: '好的',ref: ''}],
            popupMessage: '服务请求已发送成功！'

        };
    },
    created() {
      if (window.location.href.split('type=').length > 1) {
        this.pageType = Number(window.location.href.split('type=')[1].split('&')[0])
      }
      let title = this.pageType == 33 ? '留言挂号' : this.pageType == 32 ? '留言叫车' : this.pageType == 3 ? '政策查询' : '留言咨询'
      this.$store.dispatch('index/setMainTitle', title)
      this.pointList[0] = "欢迎您使用留言服务"

      this.pageType == 33 ? (this.pointList[2] = '请说出您的挂号需求，包括医院、科室、日期等信息。',this.defaultAudioSrc = require('@/assets/voice/hospital.mp3')) :
      this.pageType == 32 ? (this.pointList[2] ='请说出您的出行需求，包括目的地、日期时间等信息。',this.defaultAudioSrc = require('@/assets/voice/onlineCar.mp3')) :
      this.pageType == 3 ? (this.pointList[2] = '请说出您要查询的政策需求。',this.defaultAudioSrc = require('@/assets/voice/policy.mp3'))
      : (this.pointList[2] = '请说出需求，最后请您留下您的联系方式，有助于我们能及时联系上您。',this.defaultAudioSrc = require('@/assets/voice/other.mp3'))




      this.playBtnList[this.borderIndex].ref = 'active'
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active);
        })
    },
    computed: {},
    watch: {
        'dialogVisible'() {
            if (this.dialogVisible) {
              if (!this.popupType) {
                  return
              }
              setTimeout(()=>{
                this.timerIn = setInterval(()=>{
                  if (this.backHome_time > 0) {
                    this.backHome_time --
                    this.popupMessage = '信息已发送成功!<p><span>'+ this.backHome_time + '</span> 秒后将退出留言</p>'
                  } else {
                    clearInterval(this.timerIn)
                    this.timerIn = null
                    history.go(-1)
                  }
                },1000)
              },0)
            } else {
                clearInterval(this.timerIn)
                this.timerIn = null
            }

        },
        'audioSrc'() {
            if (this.audioSrc == '' ||  this.audioSrc == 'null') {
                this.audioState = -1
                this.btnClass = ''
            } else {
                this.btnClass = 'btnClass'
            }
        },
        'audioState'() {
             switch (this.audioState) {
                    case "0": // 开始录音
                        this.time = 0
                        this.timeEvery = 0
                        clearInterval(this.timerIn)
                        this.timerIn = null

                        this.setTime(true)  //累加
                        this.playing = '无文件'
                        this.recording = '录音中'
                        this.audioStateSrc = require('../assets/playing_audio.gif')
                        this.audioSrc = 'null'
                        this.btnClass = ''
                        this.$bridge.callhandler('playRecord', 1, function(res) {
                            console.log('停止播放录音:' + res)
                        })
                        break;
                    case "1": // 停止录音
                        clearInterval(this.timerIn)
                        this.timerIn = null
                        this.playing = '转换中'
                        this.recording = '已录音'
                        this.audioStateSrc = require('../assets/null_play_audio.png')
                        this.audioSrc = 'null'
                        this.btnClass = ''
                        this.$store.dispatch('app/setLoadingState',true)
                        break;
                    case "2": // 转换完成
                        this.$store.dispatch('app/setLoadingState',false)
                        this.playing = '未播放'
                        this.recording = '已录音'
                        this.audioStateSrc = require('../assets/null_play_audio.png')
                        this.audioSrc = 'null'
                        this.btnClass = 'btnClass'
                        break;
                    case "3": // 播放录音
                        clearInterval(this.timerIn)
                        this.timerIn = null
                        this.setTime(false)  //累减
                        this.$store.dispatch('app/setLoadingState',false)
                        this.playing = '正在播放'
                        this.recording = '已录音'
                        this.audioStateSrc = require('../assets/playing_audio.gif')
                        this.audioSrc = 'null'
                        this.btnClass = 'btnClass'
                        break;
                    case "4": // 播放录音完成
                        clearInterval(this.timerIn)
                        this.timerIn = null
                        this.$store.dispatch('app/setLoadingState',false)
                        this.playing = '已播放'
                        this.recording = '已录音'
                        this.audioStateSrc = require('../assets/null_play_audio.png')
                        this.audioSrc = 'null'
                        this.btnClass = 'btnClass'
                        this.time = this.timeEvery
                        break;
             }
        },
        'time'() {
            if (this.time == 0) {
                if (this.time != this.timeEvery &&  this.timeEvery != 0) {
                    this.audioState = "4"
                    clearInterval(this.timerIn)
                    this.timerIn = null
                }
            } else if (this.time >= 117) {  // 超时
                clearInterval(this.timerIn)
                this.timerIn = null

                this.popupType = false
                this.popupMessage = '您的录音已超时，请合理使用录音时间。'
                this.popupBtnList[0].ref = 'active'
                this.dialogVisible = true
            }
        }
    },
    destroyed() {
        this.$store.dispatch('app/setLoadingState',false)
        this.$bridge.callhandler('playRecord', 1, function(res) {
            console.log('停止播放录音:' + res)
        })
        this.$bridge.callhandler('setRecordFlag',1, (data, responseCallback) => {
            responseCallback('停止监听录音')
        })
    },
    mounted() {

        setTimeout(() => {
            this.$refs.playAudio.play()
        }, 600);
        this.$nextTick(()=>{
            this.$bridge.callhandler('setRecordFlag',0, (data, responseCallback) => {
                responseCallback('开始监听录音')
            })
        })

        this.fuc.KeyboardEvents({
            esc:()=>{
              if (!this.dialogVisible) {
                this.$store.dispatch('index/setFocusDom', null)
                history.go(-1)
              }
            },
            down:()=>{
              if (!this.dialogVisible) {
                if (this.playBtnList[this.borderIndex].ref == '' && !this.$store.getters.loadingState && !this.dialogVisible) {
                  this.playBtnList[this.borderIndex].ref = 'active'
                }
              }
            },
            up:()=>{
              if (!this.dialogVisible) {
              }
            },
            left:()=>{
              if (!this.dialogVisible) {
                if (this.borderIndex > 0 && this.playBtnList[this.borderIndex].ref != '' && !this.$store.getters.loadingState && !this.dialogVisible) {
                  this.playBtnList[this.borderIndex].ref = ''
                  this.borderIndex--
                  this.playBtnList[this.borderIndex].ref = 'active'
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active);
                  })
                }
              }
            },
            right:()=>{
              if (!this.dialogVisible) {
                if (this.borderIndex < this.playBtnList.length - 1 && this.playBtnList[this.borderIndex].ref != '' && !this.$store.getters.loadingState && !this.dialogVisible) {
                  this.playBtnList[this.borderIndex].ref = ''
                  this.borderIndex++
                  this.playBtnList[this.borderIndex].ref = 'active'
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active);
                  })
                }
              }
            },
            enter:()=>{
                if (this.playBtnList[this.borderIndex].ref == '' && !this.$store.getters.loadingState &&  this.dialogVisible) {
                  if (this.popupType) {
                    history.go(-1)
                  } else {
                    this.popupBtnList[0].ref = ""
                    this.playBtnList[this.borderIndex].ref = "active"
                    this.dialogVisible = false
                  }
                } else if (this.borderIndex == 0 &&  !this.dialogVisible) {
                    if (this.audioState == '2' || this.audioState == '4') {  // 播放录音
                        this.$bridge.callhandler('playRecord', 0, (res) => { // 播放录音
                            let that = this
                            that.audioState = '3'
                        })
                    }
                } else
                if (this.borderIndex == 1 &&  !this.dialogVisible) { // 发送
                    clearInterval(this.timerIn)
                    this.timerIn = null
                    this.time = this.timeEvery
                    this.audioSrc = 'null'
                    this.$bridge.callhandler('playRecord', 1, function(res) {
                      console.log('停止播放录音:' + res)
                    })
                    let supplierInfo = JSON.parse(sessionStorage.getItem('supplierInfo'));

                    let obj = {
                      userId: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
                      type: this.pageType,
                      vendorId: supplierInfo.fk_supplier_id
                    }
                    this.$store.dispatch('app/setLoadingState',true)
                    this.$bridge.callhandler('createOrder', obj , (res) => {
                        let that = this
                          that.$store.dispatch('app/setLoadingState',false)
                          if (res == "1") { // 成功
                            that.playing = '无文件'
                            that.recording = '未录音'
                            that.time = 0
                            that.popupType = true
                            that.popupMessage = '信息已发送成功!<p><span>'+ this.backHome_time + '</span> 秒后将退出留言</p>'
                            that.playBtnList[this.borderIndex].ref = ''

                            that.popupBtnList[0].ref = 'active'
                            that.dialogVisible = true
                          } else if (res == "0") { // 失败
                              // that.playing = '无文件'
                              // that.recording = '未录音'
                              // that.time = 0
                              that.popupType = false
                              that.popupMessage = '信息发送失败！'
                              that.playBtnList[this.borderIndex].ref = ''
                              that.popupBtnList[0].ref = 'active'
                              that.dialogVisible = true
                          }
                      })

                } else {
                    this.dialogVisible = false
                    this.backHome_time = 10
                }
            }
        })
        let _that=this;
        window.onresize = function () {
            clearTimeout(this.timerOut)
            this.timerOut = null
            this.timerOut = setTimeout(() => {
                if (_that.playBtnList[_that.borderIndex].ref != '') {
                } else {
                }
            },500)
        };
        this.$nextTick(()=>{
            this.$bridge.registerhandler('startRecord',(data, responseCallback) => {
                let that = this
                that.audioState = data
            })
        })

    },
    methods: {
        popupOpend() {
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active);
              document.getElementById('focus_border').style.borderColor = '#5472B0'
            })

        },
        popupClose() {
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active);
            document.getElementById('focus_border').style.borderColor = '#fff'
          })
        },
        getDialogSon(data) {
        },
        setTime(up) {
            this.timerIn = setInterval(() => {
                if (up) {
                    this.time ++
                    this.timeEvery = this.time
                } else {
                    if (this.time > 0) {
                        this.time --
                    }
                }
            }, 1000);
        },
    },
}
</script>
<style lang="less" scoped>
    .media_call {
        width: 100%;
        height: 7.2rem;
        .point_text {
            width: 11.7rem;
            height: 7.2rem;
            float: left;
            .text_cont {
                width: 100%;
                height: 3.78rem;
                background: url('../assets/text_bg.png') no-repeat;
                background-size: 100% 100%;
                font-size:0.36rem;
                color:#b4c0ff;
                font-weight: bold;
                letter-spacing: 0.06rem;
                position:relative;

                display: flex;
                flex-direction: column;
                justify-content: center;
                .point_message {
                    margin-left:1.5rem;
                    margin-bottom:0.15rem;
                    display: flex;
                    align-items: center;
                    img {
                      display: block;
                      width:0.8rem;
                      height:0.8rem;
                      margin-left: -0.1rem;
                    }
                    p {
                        display: flex;
                        align-items: center;
                    }
                    p:nth-child(1) {
                        width:0.4rem;
                        height:0.4rem;
                        line-height: 0.4rem;
                        text-align: center;
                        border-radius: 50%;
                        background: #6699FF;
                        color:#272A59;
                        justify-content: center;
                        font-size: 0.3rem;
                        text-align: center;
                        span {
                            display:block;
                            width:100%;
                            padding-left:0.05rem
                        }
                    }
                    p:nth-child(2) {
                        margin-left: 0.5rem;
                        width:90%;
                    }
                }
                .point_message:nth-child(1) {
                  p {
                    margin-left: 0.2rem;
                  }

                }
                .point_message:nth-child(2) {
                    margin-bottom:0.2rem;
                    p {
                        font-size: 0.4rem;
                    }
                }
            }
            .audio_state {
                width: 100%;
                height: 1.56rem;
                background: url('../assets/audio_bg.png') no-repeat;
                background-size: 100% 100%;
                margin-top: 0.35rem;
                position: relative;
                .music_state {
                    display:block;
                    width:6.8rem;
                    height:1.28rem;
                    position: absolute;
                    top:0.3rem;
                    left:50%;
                    transform: translateX(-50%);
                }
                div {
                    width: 1.9rem;
                    height: 1.56rem;
                    line-height: 1.56rem;
                    text-align: center;
                    font-size:0.28rem;
                    font-weight: bold;
                    color:#fff;
                    float: left;
                }
                div:nth-child(3) {
                    float:right
                }
                .audio_time {
                    color:#fff;
                    width:2rem;
                    font-size: 0.25rem;
                    text-align: center;
                    position: absolute;
                    bottom: 0.22rem;
                    left: 50%;
                    transform: translateX(-50%);
                }
            }
            .button {
                width: 100%;
                height: 1.12rem;
                line-height: 1.22rem;
                margin-top: 0.35rem;
                position: relative;
                div {
                    width: 5.7rem;
                    height: 100%;
                    float: left;
                    border-radius: 0.28rem;
                    font-size: 0.45rem;
                    font-weight: bold;
                    letter-spacing: 0.2rem;
                    text-align: center;
                    background: url('../assets/null_btn_bg.png') no-repeat;
                    background-size: 100% 100% !important;
                    color: #636285;
                    transition: all 0.35s;
                }
                .btnClass {
                    color: #fff;
                    background: url('../assets/1_btn_bg.png') no-repeat;
                }
                div:nth-child(2) {
                    color: #fff;
                    margin-left: 0.3rem;
                    background: url('../assets/1_btn_bg.png') no-repeat;
                    position: absolute;
                    right: 0;
                    top:0
                }
            }
        }
        .point_pic {
            width: 4.8rem;
            height: 7.2rem;
            background: url('../assets/remote.png') no-repeat;
            background-size: 100% 100%;
            float: left;
            margin-left: 0.3rem;
        }
    }

</style>
<style lang="less">
    .media_call {
      .el-dialog {
        .el-dialog__header {
          padding: 0 !important;
          height: 0.8rem !important;
          span {
            display: flex !important;
            align-items: center;
            justify-content: center;
          }
        }
        .el-dialog__body {
          padding: 0 !important;
          margin: 0 !important;
          border: none !important;
          padding-bottom: 0 !important;
          overflow: hidden;
          .popupMessage {
            width: 5.2rem;
            height: 1.8rem;
            padding: 0 0.2rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 0.28rem;
            font-weight: bold;
            letter-spacing: 0.03rem;
            text-indent: 0.03rem;
            line-height: 0.4rem;
          }
          .messageBtn {
            width: 100%;
            height: 0.8rem;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            .btnItem {
              width: 100%;
              height: 100%;
              background: #5472B0;
              color: #fff;
              text-align: center;
              line-height: 0.8rem;
              font-size: 0.38rem;
              font-weight: bold;
              letter-spacing: 0.04rem;
              text-indent: 0.04rem;
              border-radius: 0.16rem;
            }
          }

        }

      }

      .success_item {
          p {
              position:absolute;
              bottom:1.2rem;
              span {
                  display: inline;
                  color:red
              }
          }
      }
      .error_item {
          width: 7.6rem !important;
      }
    }
</style>