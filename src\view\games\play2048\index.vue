<template>
  <div class="wrapper">
    <div class="header">
      <!-- <h1 class="title">2048</h1> -->
      <div class="score">
        <div class="score-container">
          <span>当前得分</span>
          <span>{{ score }}</span>
        </div>
        <div class="best-container">
          <span>最高分数</span>
          <span>{{ bestScore }}</span>
        </div>
      </div>
    </div>
    <!-- <div class="btn btn-mg" @click="newGame">新游戏</div> -->
    <div>
      <div class="over" v-if="over">
        <p>游戏结束!</p>
        <!-- <div class="btn" @click="newGame">Try again</div> -->
      </div>
      <div class="over" v-if="win">
        <p>游戏胜利!</p>
        <!-- <div class="btn" @click="newGame">Try again</div> -->
      </div>
      <div class="box">
        <div class="row" v-for="(row, index) in list" :key="index">
          <div
            class="col"
            :class="'n-' + col"
            v-for="(col, index) in row"
            :key="index"
          >
            {{ col }}
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :show-close="false"
      :close-on-click-modal="false"
      @opened="popupOpend"
      @close="popupClose"
      custom-class="operatePopup"
      :title="popupModule == 1 ? '操作' : '提示'"
    >
      <!--操作弹窗-->
      <div class="btnList" v-if="popupModule == 1">
        <div
          class="btnItem"
          :ref="item.ref"
          v-for="(item, index) in popupBtnList"
          :key="index"
        >
          <div class="box-btnItem" v-html="item.text"></div>
        </div>
      </div>

      <!--消息弹窗-->
      <div class="popupMessage" v-if="popupModule == 2">
        <div class="popupMessage" v-html="popupMessage"></div>
        <div class="popupBtnList">
          <div
            class="popupMessageBtn"
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
          >
            {{ item.text }}
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      flag: false,
      shouldGenerateNewNumber: true,
      popupModule: 1, // 1、按钮弹窗  2、消息提示
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      size: 4,
      score: 0,
      list: [],
      intiNum: [2, 4],
      pr: 0.9,
      //   score: 0,
      bestScore: localStorage.getItem('bestScore'),
      over: false,
      win: false,
      direction: [
        {
          x: 0,
          y: -1,
        },
        {
          x: 0,
          y: 1,
        },
        {
          x: -1,
          y: 0,
        },
        {
          x: 1,
          y: 0,
        },
      ],
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '2048')
    this.$store.dispatch('index/setFocusDom', null)
  },
  mounted() {
    //初始化数组
    this.init()

    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          if (this.popupModule == 1 || this.popupModule == 3) {
            if (this.popupBtnNums < this.popupBtnList.length - 1) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums++
              this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
        }
      },
      up: () => {
        if (this.dialogVisible) {
          if (this.popupModule == 1 || this.popupModule == 3) {
            if (this.popupBtnNums > 0) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums--
              this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
        }
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupModule == 2) {
            if (this.popupBtnNums > 0) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums--
              this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
        }
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupModule == 2) {
            if (this.popupBtnNums < this.popupBtnList.length - 1) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums++
              this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
        }
      },
      enter: () => {
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].fuc(
            this.popupBtnList[this.popupBtnNums]
          )
          return
        }
      },
      esc: () => {
        if (this.dialogVisible) {
          return
        }
        this.dialogVisible = true
        this.popupModule = 1
        this.popupBtnList = [
          {
            text: '继续游戏',
            ref: '',
            fuc: () => {
              this.dialogVisible = false
            },
          },
          {
            text: '重新开始',
            ref: '',
            fuc: () => {
              this.newGame()
              this.dialogVisible = false
            },
          },
          // {
          //   text: '玩法说明',
          //   ref: '',
          //   fuc: () => {
          //     this.$nextTick(() => {
          //       this.$router.push({
          //         path: '/introduce',
          //       })
          //     })
          //   },
          // },
          {
            text: '退出游戏',
            ref: '',
            fuc: () => {
              this.$store.dispatch('index/setFocusDom', null)
              this.$router.go(-1)
            },
          },
        ]
        this.popupBtnList[this.popupBtnNums].ref = 'active'
      },
    })
  },
  methods: {
    popupClose() {
      this.flag = false
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', null)
        setTimeout(() => {
          this.popupBtnList = []
          this.popupBtnNums = 0
          this.popupModule = 1
        }, 200)
      })
    },
    popupOpend() {
      this.flag = true
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        setTimeout(() => {
          document.getElementById('focus_border').style.borderColor = '#5472B0'
        }, 0)
        return
      })
    },
    init() {
      this.popupModule = 1
      this.dialogVisible = true
      this.popupBtnList = [
        {
          text: '开始游戏',
          ref: '',
          fuc: () => {
            // this.newGame()
            this.dialogVisible = false
          },
        },
        {
          text: '玩法说明',
          ref: '',
          fuc: () => {
            this.$nextTick(() => {
              this.$router.push({
                path: '/introduce',
              })
            })
          },
        },
        {
          text: '退出游戏',
          ref: '',
          fuc: () => {
            this.$store.dispatch('index/setFocusDom', null)
            this.$router.go(-1)
          },
        },
      ]
      this.popupBtnNums = 0
      this.popupBtnList[this.popupBtnNums].ref = 'active'
      this.newGame()
      if (!this.flag) {
        document.addEventListener('keyup', this.keyDown)
      }
    },
    newGame() {
      if (this.win) {
        this.score = Number(localStorage.getItem('bestScore'))
      } else {
        this.score = 0
      }
      this.over = false
      this.initNumber()
    },
    continueGame() {
      this.win = false
      this.initNumber()
    },
    initNumber() {
      this.list = Array.from(Array(this.size)).map(() =>
        Array(this.size).fill(undefined)
      )
      if (this.shouldGenerateNewNumber) {
        this.setNewRandom()
        this.setRandom()
      } else {
        this.setNewRandom()
        this.setNewRandom()
      }
    },
    // exit() {
    //   this.popupModule = 2
    //   this.dialogVisible = true
    //   this.popupBtnList = [
    //     {
    //       text: '取消',
    //       ref: '',
    //       fuc: () => {
    //         this.dialogVisible = false
    //       },
    //     },
    //     {
    //       text: '确认',
    //       ref: '',
    //       fuc: () => {
    //         this.$store.dispatch('index/setFocusDom', null)
    //         this.$router.go(-1)
    //       },
    //     },
    //   ]
    //   this.popupMessage = '是否退出游戏？'
    //   this.popupBtnNums = 0
    //   this.popupBtnList[this.popupBtnNums].ref = 'active'
    //   this.$nextTick(() => {
    //     this.$store.dispatch('index/setFocusDom', this.$refs.active)
    //   })
    // },
    //插入新格子
    setRandom() {
      if (this.hasAvailableCells() && this.shouldGenerateNewNumber) {
        let [x, y] = this.randomAvailableCells()
        this.list[x][y] = this.randomValue()
        this.shouldGenerateNewNumber = false
      }
    },
    setNewRandom() {
      if (this.hasAvailableCells()) {
        let [x, y] = this.randomAvailableCells()
        this.list[x][y] = this.randomValue()
      }
    },
    //获取数值
    randomValue() {
      return Math.random() < this.pr ? this.intiNum[0] : this.intiNum[1]
    },
    //获取随机一个空格子坐标
    randomAvailableCells() {
      let cells = this.availableCells()
      if (cells.length) {
        return cells[Math.floor(Math.random() * cells.length)]
      }
    },
    //所有空格子的坐标
    availableCells() {
      let cells = []
      for (let i = 0; i < this.size; i++) {
        for (let j = 0; j < this.size; j++) {
          if (!this.list[i][j]) {
            cells.push([i, j])
          }
        }
      }
      return cells
    },
    //是否存在空格子
    hasAvailableCells() {
      return !!this.availableCells().length
    },
    hasMergedCells() {
      for (let i = 0; i < this.size; i++) {
        for (let j = 0; j < this.size; j++) {
          let cell = this.list[i][j]
          if (cell) {
            for (let dir = 0; dir < 4; dir++) {
              let vector = this.direction[dir]
              if (this.withinBounds(i + vector.x, j + vector.y)) {
                let other = this.list[i + vector.x][j + vector.y]
                if (other && other === cell) {
                  return true
                }
              }
            }
          }
        }
      }
      return false
    },
    has2048() {
      for (let i = 0; i < this.size; i++) {
        for (let j = 0; j < this.size; j++) {
          if (this.list[i][j] === 2048) {
            return true
          }
        }
      }
      return false
    },
    withinBounds(x, y) {
      return x > 0 && y > 0 && x < this.size && y < this.size
    },
    isAvailable() {
      if (this.has2048()) {
        this.win = true
        this.flag = true
        this.popupModule = 1
        this.dialogVisible = true
        this.popupBtnList = [
          {
            text: '继续游戏',
            ref: '',
            fuc: () => {
              this.newGame()
              this.dialogVisible = false
              this.win = false
            },
          },
          // {
          //   text: '继续游戏',
          //   ref: '',
          //   fuc: () => {
          //     this.continueGame()
          //     this.dialogVisible = false
          //   },
          // },
          {
            text: '退出游戏',
            ref: '',
            fuc: () => {
              this.$store.dispatch('index/setFocusDom', null)
              this.$router.go(-1)
            },
          },
        ]
        this.popupBtnNums = 0
        this.popupBtnList[this.popupBtnNums].ref = 'active'
      }
      this.shouldGenerateNewNumber = true

      return this.hasAvailableCells() || this.hasMergedCells()
    },
    //获取0-n的随机数
    randomNum(index) {
      return Math.floor(Math.random() * index)
    },
    //键盘监听事件
    keyDown(e) {
      if (!this.flag) {
        let arr = null
        switch (e.keyCode) {
          case 38: //上
            this.move(1)
            break
          case 40: //下
            this.move(3)
            break
          case 37: //左
            this.move(0)
            break
          case 39: //右
            this.move(2)
            break
        }

        this.setRandom()
        
      }
    },
    //移动算法，i表示旋转次数
    move(i) {
      let arr = this.rotate(Array.from(this.list), i).map((item, index) => {
        return this.moveLeft(item)
      })
      this.list = this.rotate(arr, this.size - i)
      this.setLocalstorage()

      if (!this.isAvailable()) {
        // const audio = new Audio(require('../../../../static/music.mp3')) // 创建音频对象
        //   audio.currentTime = 8 // 设置音频起始时间
        //   audio.play() // 播放音频

        //   // 如果需要在特定时间后停止播放，可以使用 setTimeout
        //   setTimeout(() => {
        //     audio.pause() // 停止播放
        //   }, 1000) // 停止时间
        this.over = true
        this.flag = true
        this.popupModule = 1
        this.dialogVisible = true
        this.popupBtnList = [
          {
            text: '再来一局',
            ref: '',
            fuc: () => {
              this.newGame()
              this.dialogVisible = false
            },
          },
          // {
          //   text: '玩法说明',
          //   ref: '',
          //   fuc: () => {
          //     this.$nextTick(() => {
          //       this.$router.push({
          //         path: '/introduce',
          //       })
          //     })
          //   },
          // },
          {
            text: '退出游戏',
            ref: '',
            fuc: () => {
              this.$store.dispatch('index/setFocusDom', null)
              this.$router.go(-1)
            },
          },
        ]
        this.popupBtnNums = 0
        this.popupBtnList[this.popupBtnNums].ref = 'active'
      }
      this.shouldGenerateNewNumber = true
    },
    //单行左移
    moveLeft(list) {
      let _list = [] //当前行非空格子
      let flg = false
      for (let i = 0; i < this.size; i++) {
        if (list[i]) {
          _list.push({
            x: i,
            merged: false,
            value: list[i],
          })
        }
      }
      _list.forEach((item) => {
        let farthest = this.farthestPosition(list, item)
        let next = list[farthest - 1]
        if (next && next === item.value && !_list[farthest - 1].merged) {
          //合并
          list[farthest - 1] = next * 2
          list[item.x] = undefined
          item = {
            x: farthest - 1,
            merged: true,
            value: next * 2,
          }
          this.score += next * 2
          // const audio = new Audio(require('../../../../static/music.mp3')) // 创建音频对象
          // audio.currentTime = 0.9 // 设置音频起始时间
          // audio.play() // 播放音频

          // // 如果需要在特定时间后停止播放，可以使用 setTimeout
          // setTimeout(() => {
          //   audio.pause() // 停止播放
          // }, 800) // 停止时间
        } else {
          if (farthest != item.x) {
            list[farthest] = item.value
            list[item.x] = undefined
            item.x = farthest
          }
        }
      })
      return list
    },
    //逆时针旋转
    rotate(arr, n) {
      n = n % 4
      if (n === 0) return arr
      let tmp = Array.from(Array(this.size)).map(() =>
        Array(this.size).fill(undefined)
      )
      for (let i = 0; i < this.size; i++) {
        for (let j = 0; j < this.size; j++) {
          tmp[this.size - 1 - i][j] = arr[j][i]
        }
      }
      if (n > 1) tmp = this.rotate(tmp, n - 1)
      return tmp
    },
    //左边最远空格的x位置
    farthestPosition(list, cell) {
      let farthest = cell.x
      while (farthest > 0 && !list[farthest - 1]) {
        farthest = farthest - 1
      }
      return farthest
    },
    setLocalstorage() {
      let score = localStorage.getItem('bestScore')
      if (score) {
        if (this.score > score) {
          localStorage.setItem('bestScore', this.score)
          this.bestScore = this.score
        }
      } else {
        localStorage.setItem('bestScore', this.score)
        this.bestScore = this.score
      }
    },
  },
}
</script>

<style scoped lang="less">
.wrapper {
  display: flex;
  position: relative;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  .header {
    position: absolute;
    top: 1rem;
    left: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #776e65;

    // .title {
    //   font-size: 60px;
    // }
    .score {
      position: absolute;
      // top: 0rem;
      left: -0.8rem;
      display: flex;
      //   justify-content: space-between;
      flex-direction: column;
      height: 4rem; // line-height: 60px;

      div {
        // width: 100px;
        width: 3rem;
        height: 100%;
        display: flex;
        flex-direction: column;
        margin-top: 0.5rem;
        align-items: center;
        justify-content: center;
        padding-left: 0.05rem;
        padding-right: 0.05rem;
        border-radius: 0.05rem;
        // background: #bbada0;
        background: url('../../../assets/matrix_state.png') no-repeat;
        background-size: 100% 100%;

        span {
          display: block;
          width: 80%;
          margin: 0 auto;
        }
        span:nth-child(1) {
          font-size: 0.26rem;
          height: 0.6rem;
          line-height: 0.6rem;
          text-align: center;
          font-weight: 600;
          letter-spacing: 0.05rem;
          color: #1de9f4;
        }
        span:nth-child(2) {
          font-size: 0.5rem;
          height: 0.6rem;
          line-height: 0.6rem;
          text-align: center;
          font-weight: 600;
          white-space: nowrap; /* 防止换行 */
          overflow: hidden; /* 溢出隐藏 */
          text-overflow: ellipsis; /* 使用省略号表示溢出 */
        }
      }

      span {
        font-size: 0.4rem;
        color: #fff;
        font-weight: bold;
      }
    }
  }

  .over {
    position: absolute;
    width: 7.5rem;
    height: 7.5rem;
    margin-top: -0.5rem;
    background: rgba(238, 228, 218, 0.73);
    z-index: 1000;
    border-radius: 0.16rem;
    text-align: center;
    color: #8f7a66;

    p {
      font-size: 0.6rem;
      font-weight: bold;
      height: 0.6rem;
      line-height: 7rem;
    }
  }

  .btn {
    display: inline-block;
    padding: 0 0.2rem;
    height: 0.4rem;
    line-height: 0.4rem;
    border-radius: 0.05rem;
    cursor: pointer;
    text-align: center;
    color: #f9f6f2;
    background: #8f7a66;

    &.btn-mg {
      margin-bottom: 0.1rem;
    }
  }

  .box {
    width: 7.5rem;
    height: 7.5rem;
    padding: 0.15rem;
    margin-top: -0.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-sizing: border-box;
    border-radius: 0.16rem;
    // background: #bbada0;
    background: url('../../../assets/matrix_bg.png') no-repeat;
    background-size: 100% 100%;

    .row {
      width: 100%;
      height: 23%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .col {
        width: 23%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.6rem;
        font-weight: bold;
        color: #fff;
        border-radius: 0.03rem;
        // background: #cec1b3;
        background: #344466;

        &.n-2 {
          background: #a6a096;
          border-radius: 0.05rem;
          box-shadow: inset -0.01rem -0.04rem 0.5rem 0px rgba(0, 0, 0, 0.3);
        }

        &.n-4 {
          background: #647ad9;
          border-radius: 0.05rem;
          box-shadow: inset -0.01rem -0.04rem 0.5rem 0px rgba(0, 0, 0, 0.3);
        }

        &.n-8 {
          background: #f26179;
          border-radius: 0.05rem;
          box-shadow: inset -0.01rem -0.04rem 0.5rem 0px rgba(0, 0, 0, 0.3);
        }

        &.n-16 {
          background: #dfa274;
          border-radius: 0.05rem;
          box-shadow: inset -0.01rem -0.04rem 0.5rem 0px rgba(0, 0, 0, 0.3);
        }

        &.n-32 {
          background: #f67c5f;
          border-radius: 0.05rem;
          box-shadow: inset -0.01rem -0.04rem 0.5rem 0px rgba(0, 0, 0, 0.3);
        }

        &.n-64 {
          background: #f65e36;
          border-radius: 0.05rem;
          box-shadow: inset -0.01rem -0.04rem 0.5rem 0px rgba(0, 0, 0, 0.3);
        }

        &.n-128 {
          background: #edcf72;
          border-radius: 0.05rem;
          box-shadow: inset -0.01rem -0.04rem 0.5rem 0px rgba(0, 0, 0, 0.3);
        }

        &.n-256 {
          background: #cfa725;
          border-radius: 0.05rem;
          box-shadow: inset -0.01rem -0.04rem 0.5rem 0px rgba(0, 0, 0, 0.3);
        }

        &.n-512 {
          background: #9c0;
          border-radius: 0.05rem;
          box-shadow: inset -0.01rem -0.04rem 0.5rem 0px rgba(0, 0, 0, 0.3);
        }

        &.n-1024 {
          background: #3365a5;
          border-radius: 0.05rem;
          box-shadow: inset -0.01rem -0.04rem 0.5rem 0px rgba(0, 0, 0, 0.3);
        }

        &.n-2048 {
          background: #09c;
          border-radius: 0.05rem;
          box-shadow: inset -0.01rem -0.04rem 0.5rem 0px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}
</style>
<style lang="less">
.wrapper {
  .el-dialog {
    //width: fit-content;
    //margin-top: 0 !important;
    //top: 50%;
    //transform: translateY(-50%);
    //border-radius: 0.16rem;
    .el-dialog__header {
      .el-dialog__title {
        font-size: 0.5rem !important;
      }
    }

    .el-dialog__body {
      .btnList {
        padding: 0.2rem 0;

        .btnItem {
          width: 8.75rem;
          height: 1rem;
          line-height: 1.08rem;
          background: #5472b0;
          //color: #fff;
          border-radius: 0.2rem;
          margin-bottom: 0.3rem;
          text-align: center;
          font-weight: bold;
          font-size: 0.5rem;
          position: relative;
          //text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.3);
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;

          img {
            width: 0.48rem;
            height: 0.48rem;
            position: absolute;
            top: 50%;
            left: 2.2rem;
            transform: translateY(-45%);
          }

          .box-btnItem {
            background: linear-gradient(to bottom, #fdfeff 30%, #bed1fb 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            //text-shadow: 0rem 0.02rem 0.05rem rgba(0, 0, 0, 0.5);
          }
        }

        .btnItem:last-child {
          margin-bottom: 0;
        }
      }

      .popupMessage {
        padding: 0.2rem 0;

        .popupMessage {
          //line-height: 0.4rem;
          text-align: center;
          //font-size: 0.24rem;
          font-size: 0.45rem;
          margin-bottom: 0.3rem;
        }

        .popupBtnList {
          display: flex;
          justify-content: space-evenly;

          .popupMessageBtn {
            width: 3.84rem;
            height: 1rem;
            line-height: 1rem;
            background: #5472b0;
            font-weight: bold;
            text-align: center;
            //font-size: 0.24rem;
            font-size: 0.5rem;
            color: #fff;
            border-radius: 0.16rem;
            // margin: 0 auto;
            margin-top: 0.2rem !important;
          }

          .popupMessageBtn:last-child {
            margin-bottom: 0;
            margin-left: 0.4rem;
          }
        }
      }
    }
  }
}
</style>