<template>
  <div class="hextrix">
    <div class="games">
      <canvas id="canvas" class="canvasHextrix"></canvas>
      <div id="overlay" class="faded overlay"></div>
      <div id='startBtn' ></div>
      <div id="helpScreen" class='unselectable'>
        <div id='inst_main_body'></div>
      </div>
      <div class="faded overlay"></div>
<!--      <img id="pauseBtn" src="./images/btn_pause.svg"/>-->
      <img id='restartBtn' src="./images/btn_restart.svg" style="display: none !important"/>
      <div id='highScoreInGameText'>
        <div id='highScoreInGameTextHeader'>最高分</div><div id='currentHighScore'>0</div>
      </div>
      <div id="gameoverscreen">
        <div id='container'>
          <div id='gameOverBox' class='GOTitle'>游戏结束</div>
          <div id='cScore'>1843</div>
          <div id='highScoresTitle' class='GOTitle'>高分排行</div>
          <div class='score'><span class='scoreNum'>1.</span> <div id="1place" style="display:inline;">0</div></div>
          <div class='score'><span class='scoreNum'>2.</span> <div id="2place" style="display:inline;">0</div></div>
          <div class='score'><span class='scoreNum'>3.</span> <div id="3place" style="display:inline;">0</div></div>
          <div class="topis">"确认" 键再来一局，"返回" 键退出</div>
        </div>
        <div id='bottomContainer'>
          <img id='restart' src='./images/btn_restart.svg' height='57px'>
          <div id='socialShare'>

          </div>
          <div id='buttonCont'>
            <ul class="rrssb-buttons">
              <li class="rrssb-facebook">

              </li>
            </ul>

          </div>
        </div>
      </div>
    </div>

    <div class="message">
      <h2>操作说明:</h2>
      <p>
        <span>1.</span>
        <span>左、右健旋转中间六边形边框。</span>
      </p>
      <p>
        <span>2.</span>
        <span>当相邻至少3条同颜色的线条时，这些线条会被清。</span>
      </p>
      <p>
        <span>3.</span>
        <span>下键加速下落。</span>
      </p>
      <p>
        <span>4.</span>
        <span>确认键暂停/继续。</span>
      </p>
      <p>
        <span>5.</span>
        <span>返回键重新开始/退出游戏。</span>
      </p>
    </div>

    <el-dialog
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        custom-class="operatePopup"
        :title="popupModule == 1 ? '操作' : '提示'"
    >
      <!--操作弹窗-->
      <div class="btnList" v-if="popupModule == 1">
        <div
            class="btnItem"
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"

        >
          <div class="box-btnItem"  v-html="item.text"></div>

        </div>
      </div>

      <!--消息弹窗-->
      <div class="popupMessage" v-if="popupModule == 2">
        <div class="popupMessage" v-html="popupMessage"></div>
        <div
            class="popupMessageBtn"
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
        >
          {{ item.text }}
        </div>
      </div>


    </el-dialog>

  </div>
</template>

<script>
import './style/fa/css/font-awesome.min.css';
import './style/style.css';
import './style/rrssb.css';


import './vendor/hammer.min.js';
import './vendor/jsonfn.min.js';
import './vendor/keypress.min.js';
// import './vendor/jquery.js';
import './vendor/sweet-alert.min.js';
import './vendor/rrssb.min.js';



const originalWindow = { ...window };
export default {
  name:'hextrix',
  components: {
  },
  inject: ['reload'],
  data() {
    return {
      dialogVisible: false,
      popupModule: 1, // 1、按钮弹窗  2、消息提示
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [
        {
          text: '重新开始',
          ref: '',
          fuc: this.resumeGame
        },
        {
          text: '退出游戏',
          ref: '',
          fuc: ()=>{
            history.go(-1)
          }
        },
      ],

      random: 0,
    };
  },
  created() {
    this.$store.dispatch('index/setMainTitle',JSON.parse(sessionStorage.getItem('redirectInfo')).title ? JSON.parse(sessionStorage.getItem('redirectInfo')).title : '泡泡龙')
    this.$store.dispatch('index/setFocusDom', null)
  },
  computed: {},
  watch: {},
  async mounted() {
    this.random = Math.random()
    // import  './js/main.js';
    // 每次进入组件时加载第三方脚本
    await this.loadScript('./hextrix_js/main.js?'+ this.random);
    // 执行第三方脚本中的初始化函数（如果有）
    if (window.someThirdPartyInit) {
      window.someThirdPartyInit();

    }
    this.$nextTick(()=>{
      window.initialize();
    })
    this.fuc.KeyboardEvents({
      down:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }

      },
      up:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
      },
      left:()=>{

      },
      right:()=>{

      },
      enter:()=>{
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].fuc(this.popupBtnList[this.popupBtnNums].num)
          return
        }
      },
      esc:()=>{
        this.popupBtnList[this.popupBtnNums].ref = "active"
        if (this.dialogVisible) {
          window.pause();
        }
        this.dialogVisible = !this.dialogVisible

        // history.go(-1)

      }
    },null,true)
  },
  methods:{
    resumeGame() {
      this.dialogVisible = !this.dialogVisible
      init(1);
      canRestart = false;
      $("#gameoverscreen").fadeOut();
    },
    popupClose() {
      window.popupIn = false
      this.popupBtnList[this.popupBtnNums].ref = ""
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', null);
        setTimeout(()=>{
          this.popupBtnNums = 0
          this.popupModule = 1
        },200)
      })

    },
    popupOpend() {
      window.popupIn = true
      if (gameState && gameState != -1) {
        window.pause();
      }
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        setTimeout(()=>{
          document.getElementById('focus_border').style.borderColor = '#5472B0'
        },0)
        return
      })
    },
    loadScript(src) {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => {
          this.scriptLoaded = true;
          resolve();
        };
        script.onerror = reject;
        document.head.appendChild(script);
      });
    },
    unloadScript(src) {
      const scripts = document.querySelectorAll(`script[src="${src}"]`);
      scripts.forEach(script => {
        document.head.removeChild(script);
      });
    },
    // 同步 window 和 originalWindow 的属性
   syncWindows(window, originalWindow) {
      // 获取 originalWindow 的所有键
      const originalKeys = Object.keys(originalWindow);

      // 遍历 window 的所有键
      for (const key in window) {
        if (window.hasOwnProperty(key)) {
          if (originalKeys.includes(key)) {
            // 如果 key 在 originalWindow 中存在，更新 window 的值
            // window[key] = originalWindow[key];
          } else {
            // 如果 key 在 originalWindow 中不存在，删除 window 中的该属性
            const descriptor = Object.getOwnPropertyDescriptor(window, key);
            if (descriptor && descriptor.configurable) {
                delete window[key];
            }
          }
        }
      }

      // 遍历 originalWindow 的所有键，确保 window 中没有遗漏的属性
      for (const key of originalKeys) {
        if (!window.hasOwnProperty(key)) {
          // window[key] = originalWindow[key];
        }
      }
    }


},

  destroyed() {
    window.popupIn = false
    this.unloadScript('./hextrix_js/main.js?'+ this.random);

    this.syncWindows(window, originalWindow);

  },
  beforeDestory() {

  },
}
</script>
<style lang='less' scoped>
  .hextrix {
    height: 7rem;
    border: 1px solid transparent;
    .games {
      margin-top: -0.6rem;
      height: 100%;
      position: relative;
    }
    .message {
      position: absolute;
      height: 4rem;
      right: 1.2rem;
      bottom: 1.2rem;
      width: 2.38rem;
      padding: 0.3rem;
      //border: 0.04rem solid #5FC5DC;
      background: url("../../../assets/connectFour_state.png") no-repeat;
      background-size: 100% 100%;
      border-radius: 0.16rem;
      h2 {
        color: #1EE8F8;
        margin-bottom: 0.2rem;
      }
      p {
        font-size: 0.236rem;
        display: flex;
        margin-bottom: 0.1rem;
        span {
          display: inline-block;
        }
        span:nth-child(2) {
          margin-top: -0.04rem;
          margin-left: 0.06rem;
        }
      }
    }
  }

</style>
<style lang="less">
  .hextrix {
    .el-dialog {
      //width: fit-content;
      //margin-top: 0 !important;
      //top: 50%;
      //transform: translateY(-50%);
      //border-radius: 0.16rem;
      .el-dialog__header {
        .el-dialog__title {
          font-size: 0.5rem !important;
        }
      }
      .el-dialog__body {
        .btnList {
          padding: 0.2rem 0 ;

          .btnItem {
            width: 8.75rem;
            height: 1rem;
            line-height: 1.08rem;
            background: #5472B0;
            //color: #fff;
            border-radius: 0.2rem;
            margin-bottom: 0.3rem;
            text-align: center;
            font-weight: bold;
            font-size: 0.5rem;
            position: relative;
            //text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.3);
            letter-spacing: 0.05rem;
            text-indent: 0.05rem;
            img {
              width: 0.48rem;
              height: 0.48rem;
              position: absolute;
              top: 50%;
              left: 2.2rem;
              transform: translateY(-45%);
            }
            .box-btnItem {
              background: linear-gradient(to bottom, #FDFEFF 30%, #BED1FB 90%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              //text-shadow: 0rem 0.02rem 0.05rem rgba(0, 0, 0, 0.5);
            }
          }
          .btnItem:last-child {
            margin-bottom: 0;
          }
        }
        .popupMessage {
          padding: 0.2rem 0 ;
          .popupMessage {
            //line-height: 0.4rem;
            text-align: center;
            //font-size: 0.24rem;
            font-size: 0.45rem;
            margin-bottom: 0.3rem;
          }
          .popupMessageBtn {

            width: 8.75rem;
            height: 1rem;
            //width: 20vw;
            //height: 0.6rem;
            //line-height: 0.68rem;
            line-height: 1rem;
            background: #5472B0;
            font-weight: bold;
            text-align: center;
            //font-size: 0.24rem;
            font-size: 0.5rem;
            color: #fff;
            border-radius: 0.16rem;
            margin: 0 auto;
            margin-top: 0.2rem !important;
          }
          .popupMessageBtn:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
</style>
