<template>
  <div class='onLineCar'>
    <div class="left_zego">
      <div class="leftList">
        <div class="leftItem" :ref="item.ref" v-for="(item,index) in leftList" :key="index" :style="{opacity: item.show ? 1 : 0.6}">
          <div>
            {{item.title}}
          </div>
        </div>
      </div>
    </div>

    <div class="right_zego">
      <div class="rightInfo">
        <div class="item">
          <div>
            <img :src="require('@/assets/addr.png')" alt="" />
            我的地址:
          </div>
          <div v-if="($store.getters.getUserInfo.fk_province_city_name.length + $store.getters.getUserInfo.addr.length) < 32">
            {{$store.getters.getUserInfo.fk_province_city_name}},{{$store.getters.getUserInfo.addr}}
          </div>
          <marquee v-else>{{$store.getters.getUserInfo.fk_province_city_name}}{{$store.getters.getUserInfo.addr}}</marquee>

          <!--          <div ref="marqueeFather">-->
<!--            <p ref="marquee">-->
<!--              <span>{{$store.getters.getUserInfo.fk_province_city_name}},{{$store.getters.getUserInfo.addr}}</span>-->
<!--            </p>-->
<!--          </div>-->

        </div>
        <div class="item">
          <div>
            <img :src="require('@/assets/phone2.png')" alt="" />
            我的电话:
          </div>
          <div> {{$store.getters.getUserInfo.oldsters[$store.state.app.selectUserIndex].phone}}</div>
        </div>
      </div>

      <div style="position: absolute;top: 1.73rem;left: 6rem;font-size: 0.32rem;font-weight: bold;z-index: 99" v-if="orderInfo" v-show="orderInfo.type != 0">
        <span>（</span>
        <span :style="{color: ![0,1,3].includes(orderInfo.type) ? '#56D0C1' : 'red'}"> {{![0,1,3].includes(orderInfo.type) ? '服务进行中' : '服务已结束'}} </span>
        <span>）</span>
      </div>
      <div class="item_list" :style="{opacity: ![0,1,3].includes(orderInfo.type) ? 1 : 0.4}">
        <div class="item">
          <div>服务信息</div>
          <div></div>
        </div>

        <div class="item">
          <div>出发地址:</div>
          <div v-if="orderInfo.addr.length < 30">{{orderInfo.addr}}</div>
          <marquee v-else>{{orderInfo.addr}}</marquee>

          <!--          <div ref="marqueeFatherOrder">-->
<!--            <p ref="marqueeOrder" style="width: max-content;">-->
<!--              <span>{{orderInfo.addr}}</span>-->
<!--            </p>-->
<!--          </div>-->
        </div>

        <div  class="item">
          <div>叫车时间:</div>
          <div>{{orderInfo.creatTime}}</div>
        </div>

        <div  class="item">
          <div>司机名字:</div>
          <div>{{orderInfo.driverName}}</div>
        </div>

        <div class="item">
          <div>司机电话:</div>
          <div>{{orderInfo.driverPhone}}</div>
        </div>

        <div class="item">
          <div>车牌号码:</div>
          <div>{{orderInfo.driverPlate}}</div>
        </div>



        <div class="item">
          <div>当前状态:</div>
          <div v-if="orderInfo.type == 3" style="color: red !important;">
            {{ orderTypeErrorMessage }}
          </div>
          <div style="color: #56D0C1 !important;font-weight: 600" v-else>
            {{ orderTypeMessage }}
          </div>

        </div>

      </div>

    </div>


    <el-dialog
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        title="在线叫车"
    >
      <!--发起叫车-->
      <div class="message_box sendCall" v-if="popupModule == 1">
        <div>
          您即将发起 <strong style="color: red">立即叫车</strong> 服务。<br/>
          请点击'立即叫车'进行叫车服务。
        </div>
      </div>

      <!--取消叫车-->
      <div class="message_box cancel" v-if="popupModule == 2">
        <div>
          是否要取消当前叫车服务?
        </div>
        <div>
          取消规则说明:<br/>
          当司机接单后，日取消次数为2次。
          当司机未接单，日取消次数为5次。
        </div>
      </div>

      <div class="message_box" style="text-align: center" v-html="popupMessage" v-if="popupModule == 3"></div>

      <div class="popupBtnList">
        <div :ref="item.ref" v-for="(item,index) in popupBtnList" :key="index">
          {{ item.text }}
        </div>

      </div>



    </el-dialog>

  </div>
</template>

<script>

import { GetCarLastOrder, sendCarOrder, cancelCarOrder } from "@/api/index";
import websocketData from "@/utils/websocketData";
import Axios from "axios";
import store from "@/store";
const seamless = require('seamscroll')

export default {
  name:'onLineCar',
  components: {
  },
  data() {
    return {
      card_id:'15efd1277cf464dd40f2ec5c1368eb0b',
      timer: null,
      leftList:[],
      leftNums: 0,
      nextNums: -1,   // -1  说明焦点在左侧   > -1  在右侧

      friendsList:[],
      rightNums: 0,
      firstShow: true,

      dialogVisible: false,
      popupModule: 1,   // 1、叫车二次确认弹窗  2、取消叫车二次确认弹窗  3、接口状态返回
      popupMessage:'',
      popupBtnNums: 0,
      popupBtnList:[
        {
          text: '关闭',
          ref:'',
          fuc: ()=>{
            this.dialogVisible = false
          }
        },
        {
          text: '立即叫车',
          ref:'',
          fuc: this.senOrder
        },
      ],

      orderInfo:{
        type: 0,
        addr:'-',
        creatTime: '-',
        driverName:'-',
        driverPhone: '-',
        driverPlate:'-',
      },
      orderTypeErrorMessage:'叫车取消',
      orderTypeMessage: '',
      timerInter: null
    };
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle','在线叫车')
  },
  computed: {},

  watch: {
    '$store.getters.getOrderInfo':{
      handler(newValue,oldValue) {
        this.setOrderType(this.$store.getters.getOrderInfo)
      },
      deep: true
    }
  },
  destroyed() {
    return
    clearInterval(this.$store.state.app.storeTimer)
    this.$store.state.app.storeTimer = null
  },
  beforeDestory() {
  },
  mounted() {
    // 我的地址滚动条
    // if (this.$refs.marquee.clientWidth > this.$refs.marqueeFather.clientWidth) {
    //   this.$refs.marquee.childNodes[0].style.paddingRight= "1rem"
    //   setTimeout(()=>{
    //     seamless.init({
    //       dom: this.$refs.marquee,
    //       direction: 2,
    //       hoverStop: false
    //     })
    //   },1000)
    // }
    this.getData()
    this.setOrderType(this.$store.getters.getOrderInfo)
    this.fuc.KeyboardEvents({
      down:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length-1) {
            this.popupBtnList[this.popupBtnNums].ref = ""
            this.popupBtnNums ++
            this.popupBtnList[this.popupBtnNums].ref = "active"
            this.$nextTick(()=>{
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
            })
          }
          return
        }

        if (this.leftNums < this.leftList.length -1) {
          this.rightNums = 0
          this.friendsList = []
          this.$store.dispatch('app/setViewAreaOffsetTop', 0)
          this.$refs.active[0].classList.remove('select')
          this.leftList[this.leftNums].ref = ""
          this.leftNums ++
          this.leftList[this.leftNums].ref = "active"
          this.$nextTick(()=>{
            this.$refs.active[0].classList.add('select')
          })
        }

        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      up:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ""
            this.popupBtnNums --
            this.popupBtnList[this.popupBtnNums].ref = "active"
            this.$nextTick(()=>{
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
            })
          }
          return
        }
        if (this.leftNums > 0) {
          this.$store.dispatch('app/setViewAreaOffsetTop', 0)
          this.friendsList = []
          this.$refs.active[0].classList.remove('select')
          this.leftList[this.leftNums].ref = ""
          this.leftNums --
          this.leftList[this.leftNums].ref = "active"
          this.$nextTick(()=>{
            this.$refs.active[0].classList.add('select')
          })

        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      left:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ""
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = "active"
            this.$nextTick(()=>{
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
            })
          }
        }
      },
      right:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ""
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = "active"
            this.$nextTick(()=>{
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
            })
          }
        }
      },
      enter:()=>{
        if (this.dialogVisible) {  // 弹窗
          this.popupBtnList[this.popupBtnNums].ref = ""
          this.leftList[this.leftNums].ref = "active"
          this.dialogVisible = false
          this.popupBtnList[this.popupBtnNums].fuc(this.friendsList[this.rightNums])
          return
        }

        if (this.leftNums == 0 && this.leftList[this.leftNums].show) {
          this.popupBtnList = [
            {
                text: '关闭',
                ref:'',
                fuc: ()=>{
                  this.dialogVisible = false
                }
              },
              {
                text: '立即叫车',
                ref:'',
                fuc: this.senOrder
              }
          ]
          this.dialogVisible = true
          this.popupModule = 1
          this.leftList[this.leftNums].ref = ""
          this.popupBtnList[this.popupBtnNums].ref = "active"
        } else
        if (this.leftNums == 1 && this.leftList[this.leftNums].show) {
          this.popupBtnList = [
            {
              text: '暂不取消',
              ref:'',
              fuc: ()=>{
                this.dialogVisible = false
              }
            },
            {
              text: '确认取消',
              ref:'',
              fuc: this.cancelOrder
            }
          ]
          this.dialogVisible = true
          this.popupModule = 2
          this.leftList[this.leftNums].ref = ""
          this.popupBtnList[this.popupBtnNums].ref = "active"

          return
        }
        return
      },
      esc:()=>{
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ""
          this.friendsList[this.rightNums].ref = "active"
          this.dialogVisible = false
          return
        }
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      }
    })

  },
  methods: {
    popupClose() {
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0]);
          document.getElementById('focus_border').style.borderColor = "#fff"
          this.popupBtnNums = 0
          this.popupModule = 1
        })
    },
    popupOpend() {
      this.$nextTick(()=>{
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0]);
        document.getElementById('focus_border').style.borderColor = "#89A7FF"
      })
    },

    getData(){
      let leftICon = [
        {
          id: 1,
          title: '立即叫车',
          show: false,
          ref:'',
        },{
          id: 2,
          title: '取消叫车',
          show: false,
          ref:'',
        }
      ]
      leftICon[this.leftNums].ref = "active"
      this.leftList = leftICon

      this.$nextTick(()=>{
        this.$store.dispatch('index/setFocusDom',this.$refs.active);
        this.$refs.active[0].classList.add('select')
      })




      this.$store.dispatch('app/setLoadingState', true)
      this.fuc.callTaxi(this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].phone,(res)=>{
        // this.$nextTick(()=>{
        //   if (this.$refs.marqueeOrder.clientWidth > this.$refs.marqueeFatherOrder.clientWidth) {
        //     this.$refs.marqueeOrder.className ="marqueeIng"
        //     // this.$refs.marqueeOrder.childNodes[0].style.paddingRight= this.$refs.marqueeOrder.childNodes[0].offsetWidth +'px'
        //     this.$refs.marqueeOrder.childNodes[0].style.paddingRight= "1rem"
        //     setTimeout(()=>{
        //       seamless.init({
        //         dom: this.$refs.marqueeOrder,
        //         direction: 2,
        //         hoverStop: false
        //       })
        //     },1000)
        //   }
        // })
        this.$store.dispatch('app/setLoadingState', false)
      },(err=>{
        this.$store.dispatch('app/setLoadingState', false)
        this.orderTypeMessage = "暂无历史订单"
      }))
    },
    senOrder() {
      this.$store.dispatch('app/setLoadingState', true);
      this.fuc.sendCallTaxi(res=>{
        if (res.code == 200) {
          // 积分埋点  存在当前页面 且 存在任务  且  未完成任务 且为功能获取
          this.$store.state.app.pointList.map(item=>{
            if (item.redirect && item.redirect.indexOf(this.$route.path) > -1 && !item.harvest && item.redirect.indexOf('pointType=1') > -1) {
              this.fuc.insertPointNum(item.id)
            }
          })
          this.popupModule = 3
          this.leftList[this.leftNums].ref = ""
          this.popupBtnList = [{
            text: '好的',
            ref:'active',
            fuc: ()=>{
              this.dialogVisible = false
            }
            }
          ]
          this.popupMessage = "订单创建成功<br/>请耐心等待司机接单"
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
        this.$store.dispatch('app/setLoadingState', false);
      },(error)=>{
        this.$store.dispatch('app/setLoadingState', false);
        this.popupModule = 3
        this.leftList[this.leftNums].ref = ""
        this.popupBtnList = [{
          text: '好的',
          ref:'active',
          fuc: ()=>{
            this.dialogVisible = false
          }
        }
        ]
        this.popupMessage = "订单创建失败<br/>请稍后再试!<br>"
        if (error.response && error.response.data && error.response.data.data) {
          this.popupMessage += error.response.data.data
        }
        this.popupBtnNums = 0
        this.dialogVisible = true
      })
    },
    setOrderType(data) {
        if (!data) {
          this.leftList[0].show = true
          this.leftList[1].show = false
          return
        }
        let obj = data.data[0]
        if ( [2,4,101].includes(obj.status)) {  // 接单中  已接单  车主到达乘客起点  接到乘客，行程中 送达乘客目的地
          this.leftList[0].show = false
          this.leftList[1].show = true
        } else if([102,103].includes(obj.status)){ // 行程中  不允许取消
          this.leftList[0].show = false
          this.leftList[1].show = false
        }else{  // 1  3   服务完成  服务取消
          this.leftList[0].show = true
          this.leftList[1].show = false
        }


        this.orderInfo.addr = obj.src
        this.orderInfo.creatTime = obj.created_at ? obj.created_at : '-'
        this.orderInfo.driverName = obj.driver_name ? obj.driver_name : '-'
        this.orderInfo.driverPhone = obj.driver_phone ? obj.driver_phone : '-'
        this.orderInfo.driverPlate = obj.plate ? obj.plate : '-'
        this.orderInfo.type = obj.status

        if (obj.status == 2) {  // 接单中  -- 预约中
          this.fuc.dateTime(res =>{
            if (res.time) {
              var now_time  = new Date(res.time).getTime() * 1000
              var creat_time = new Date(obj.created_at).getTime()
              var creat_time_10minutes = creat_time + 1000 * 60 * 10
              // var timeDiff = now_time - creat_time // 时间差毫秒数
              var timeDiff = creat_time_10minutes - now_time  // 时间差毫秒数

              var days = Math.floor(timeDiff / (24 * 3600 * 1000)); // 计算出天数
              var leavel1 = timeDiff % (24 * 3600 * 1000); // 计算天数后剩余的时间
              var hours = Math.floor(leavel1 / (3600 * 1000)); // 计算天数后剩余的小时数
              var leavel2 = timeDiff % (3600 * 1000); // 计算剩余小时后剩余的毫秒数
              var minutes = Math.floor(leavel2 / (60 * 1000)) < 10 ? '0' + Math.floor(leavel2 / (60 * 1000)) : Math.floor(leavel2 / (60 * 1000)); // 计算剩余的分钟数
              var leavel3 = timeDiff % (60  * 1000); // 计算剩余分钟后剩余的毫秒数
              var seconds = Math.floor(leavel3 / 1000) < 10 ? '0' +  Math.floor(leavel3 / 1000)  : Math.floor(leavel3 / 1000); // 计算剩余的秒数
              clearInterval(this.timerInter)
              this.timerInter = null
              this.timerInter = setInterval( ()=> {
                now_time += 1000
                timeDiff = creat_time_10minutes - now_time  // 时间差毫秒数
                days = Math.floor(timeDiff / (24 * 3600 * 1000)); // 计算出天数
                leavel1 = timeDiff % (24 * 3600 * 1000); // 计算天数后剩余的时间
                hours = Math.floor(leavel1 / (3600 * 1000)); // 计算天数后剩余的小时数
                leavel2 = timeDiff % (3600 * 1000); // 计算剩余小时后剩余的毫秒数
                minutes = Math.floor(leavel2 / (60 * 1000)) < 10 ? '0' + Math.floor(leavel2 / (60 * 1000)) : Math.floor(leavel2 / (60 * 1000)); // 计算剩余的分钟数

                leavel3 = timeDiff % (60  * 1000); // 计算剩余分钟后剩余的毫秒数
                seconds = Math.floor(leavel3 / 1000) < 10 ? '0' +  Math.floor(leavel3 / 1000)  : Math.floor(leavel3 / 1000); // 计算剩余的秒数

                if (timeDiff < 0) {
                  clearInterval(this.timerInter)
                  this.timerInter = null
                  this.orderTypeErrorMessage = "叫车取消(接单等待时间小于0)，请联系客服"
                } else {
                  this.orderTypeMessage = "等待接单" + " ( 倒计时" +minutes +":"+seconds + " ) "
                }
              },1000)
              if (timeDiff > 0) {
                this.orderTypeMessage = "等待接单" + " ( 倒计时" +minutes +":"+seconds + " ) "
              } else {   //TODO: 此处可能状态正在接单；但预计接单倒计时时间小于0 --- 禁止下单
                this.orderTypeErrorMessage = "叫车取消(接单等待时间小于0)，请联系客服"
                // str_stus = "<span style='color: red'>叫车取消(接单等待时间小于0)，请联系客服</span>"
              }
            }
          })

        } else
        if (obj.status == 4) {
          clearInterval(this.timerInter)
          this.timerInter = null
          this.orderTypeMessage = "司机已接单,距离你" + obj.driver_distance + '公里，预估时间：' + obj.driver_arrive + '分钟'


        } else
        if (obj.status == 1) {
          this.orderTypeMessage = "服务完成"
        } else
        if (obj.status == 101) {
          this.orderTypeMessage = "司机到达出发地址,请尽快上车"
        } else if (obj.status == 102) {
          this.orderTypeMessage = "行程中"
        } else if (obj.status == 103) {
          this.orderTypeMessage = "已到达目的地"
        } else if (obj.status == 3) {
          this.orderTypeErrorMessage = "叫车取消"
        }

    },
    cancelOrder() {
      this.$store.dispatch('app/setLoadingState', true);
      this.fuc.cancelOrderCar((res)=>{
        if (res.code == 200) {
          this.popupModule = 3
          this.leftList[this.leftNums].ref = ""
          this.popupBtnList = [{
            text: '好的',
            ref:'active',
            fuc: ()=>{
              this.dialogVisible = false
            }
          }
          ]
          this.popupMessage = "取消订单成功"
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
        this.$store.dispatch('app/setLoadingState', false);

      },()=>{
        this.popupModule = 3
        this.leftList[this.leftNums].ref = ""
        this.popupBtnList = [{
          text: '好的',
          ref:'active',
          fuc: ()=>{
            this.dialogVisible = false
          }
        }
        ]
        this.popupMessage = "订单取消失败<br/>请稍后再试!"
        this.popupBtnNums = 0
        this.dialogVisible = true
        this.$store.dispatch('app/setLoadingState', false);
      })

    }
  }
}
</script>
<style lang='less' scoped>
.onLineCar {
  height: 7rem;
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
  gap: 0.16rem;
  font-size: 0.2rem;
  color: #E7E7EF;
  .left_zego {
    width: 3.54rem;
    border-radius: 0.15rem;
    .leftList {
      padding-top: 0.1rem;
      padding: 0.28rem 0.52rem;
      padding-top: 0;
      text-align: center;
      .leftItem {
        height: 0.7rem;
        line-height: 0.78rem;
        margin-bottom: 0.2rem;
        background: #343D74;
        border-radius: 0.2rem;
        position: relative;
        font-weight: bold;
        transition: all 0.3s;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        font-size: 0.34rem;
        color: #E7E7EF;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0,0,0,0.6);
      }
      .select {
        background: #89A7FF;
        transition: all 0.3s;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(102,129,218,0.6);
      }
    }
    .myQR {
      padding: 0rem 0.6rem;
      .qr_img {
        width: 2.05rem;
        height: 2.05rem;
        background: #413F55;
        margin: 0 auto;
        padding: 0.06rem;
        margin-bottom: 0rem;
        border-radius: 0.18rem;
      }
      img {
        border-radius: 0.14rem;
        display: block;
        width: 100%;
        height: 100%;
      }
      div {
        width: 2.2rem;
        font-size: 0.31rem;
        font-weight: bold;
        line-height: 0.45rem;
        text-align: center;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        margin-top: 0.02rem;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0,0,0,0.6);
      }
      div:nth-child(3) {
        margin-top: 0.05rem;
        letter-spacing: 0;
        font-weight: 500;
        font-size: 0.33rem;
      }
    }
  }
  .right_zego {
    width: 12.95rem;
    height: 7rem;
    margin-left: 0;
    border-radius: 0.5rem;
    //padding-left: 0.1rem;
    //margin-left: 0.05rem;
    .rightInfo {
      height: 1.5rem;

      .item {
        //height: 0.7rem;
        //line-height: 0.78rem;
        margin: 0 0.8rem;
        display: flex;
        align-items: center;
        font-size: 0.32rem;
        color: #BEC4DF;
        letter-spacing: 0.02rem;
        text-indent: 0.02rem;
        div {
          display: flex;
          align-items: center;
        }
        img {
          display: block;
          width: 0.48rem;
          height: 0.48rem;
          margin-right: 0.1rem;
          margin-left: -0.1rem;
        }
        div:first-child {
          width: 2.3rem;
        }
        div:last-child {
          //margin-left: 0.26rem;
          display: initial !important;
          max-width: 9.8rem;
          font-weight: bold;
          color: #DAE0FF;
          flex-direction: row;
          overflow: hidden;

          white-space:nowrap;
          p {
            width: max-content;
            display: flex;
            flex-direction: row;
            span {
              float: left;
              white-space:nowrap;
            }

          }
        }
        marquee {
          width: 95%;
          margin-left: 0.36rem;
          color: #DAE0FF;
          font-weight: bold;
        }

      }
      .item:first-child {
        margin-bottom: 0.26rem;
        margin-right: 0 !important;
      }
      .item:last-child {
        height: 0.48rem;
        div {
          height: 100%;
        }
        div:last-child {
          height: 100%;
          line-height: 0.52rem;
          font-size: 0.35rem;
          letter-spacing: 0;
        }
      }
    }
    .item_list {
      //height: calc(100% - 0.76rem);
      height: 5.4rem;
      background: #1E1E49;

      //background: #ccc;
      border-radius: 0.28rem;
      overflow: hidden;
      position: relative;
      letter-spacing: 0.02rem;
      text-indent: 0.02rem;
      .item {
        height: 0.72rem;
        line-height: 0.8rem;
        margin: 0 0.8rem;
        display: flex;
        align-items: center;
        font-size: 0.32rem;
        //margin-top: 0.1rem;
        color: #C3C5D1;
        marquee {
          width: 83%;
          margin-left: 0.16rem;
          color: #DAE0FF;
          font-weight: bold;
        }
        div:nth-child(1) {
          margin-right: 0.2rem;
          display: flex;
          align-items: center;
          color: #BEC4DF;
          img {
            display: block;
            width: 0.3rem;
            height: 0.38rem;
            margin-right: 0.1rem;
          }
        }
        div:nth-child(2) {
          color: #DAE0FF !important;
          font-weight: bold;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-left: 0.1rem;
          max-width: 9.64rem;
        }
      }
      .item:first-child {
        height: 0.8rem;
        line-height: 0.8rem;
        margin-top: 0.05rem;
        border-bottom: 0.03rem solid #5C617F;
        div:first-child {
          color: #DAE0FF !important;
          font-weight: bold;
        }
      }
      .item:nth-child(2) {
        margin-top: 0.1rem;
      }
      .item:last-child {
        width: calc(100% - 1.6rem);
        height: 0.8rem;
        line-height: 0.8rem;
        //border-top: 0.03rem solid #5C617F;
        //position: absolute;
        //bottom: 0rem;
      }



    }
  }
}

</style>
<style lang="less">
.onLineCar {
  position: relative;
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.16rem;
    background-image: linear-gradient(to bottom, #89A7FF, #89A7FF);

    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        //height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(to bottom,rgba(255,255,255,1),rgba(255,255,255,0.65));
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        color: #464646;
        font-size: 0.4rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .cancel {
        flex-direction: column;
        div {
          text-align: center;
        }
        div:nth-child(2) {
          margin-top: 0.1rem;
          color: red;
          line-height: 0.48rem;
        }
        //align-items: normal !important;
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          background: repeating-linear-gradient(to right,#89A7FF,#89A7FF);
        }
      }


    }
  }

}


</style>
