<template>
  <div class="pointRule">
    <div class="container">
      <div class="right scrollParent" ref="refRight">
        <div class="list" ref="refList" :style="{ top: '0rem' }">
          <div class="content" v-html="content"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getExchangeRule } from '@/api/index'
export default {
  data() {
    return {
      content: '',
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '兑换规则')
  },
  mounted() {
    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        const listEl = this.$refs.refList
        const scrollBar =
          this.$refs.refList.parentNode.querySelector('.scrollBar')

        // console.log('可视区域', listEl.parentNode.clientHeight)
        // console.log('元素距离顶部', listEl.offsetTop)
        // console.log('元素高度', listEl.clientHeight)
        // console.log('下拉高度', listEl.parentNode.offsetTop)

        if (listEl && scrollBar) {
          let currentTop = parseInt(listEl.style.top, 10)
          let currentScrollTop = parseInt(scrollBar.style.top, 10)
          let listVisHeight = listEl.parentNode.clientHeight
          let listHeight = listEl.clientHeight

          const radio = listHeight / listVisHeight

          const potentialNewTop = currentTop - 150 // 预计的新top值
          const scrollNewTop = currentScrollTop + 150 / radio
          const maxScrollableHeight =
            listEl.clientHeight - listEl.parentNode.clientHeight // 最大可滚动高度
          const maxSrcollBarHeight =
            scrollBar.parentNode.clientHeight - scrollBar.clientHeight
          if (listVisHeight < listHeight) {
            // 将元素的top值与最大可滚动高度进行比较，以确定是否已经到达或超过了底部
            if (-potentialNewTop < maxScrollableHeight) {
              listEl.style.top = `${potentialNewTop}px`
            } else {
              // 已经到达或超过底部，调整top以确保内容底部和容器底部对齐
              listEl.style.top = `${-maxScrollableHeight - 50}px`
            }
          } else {
            return
          }

          if (scrollNewTop < maxSrcollBarHeight) {
            scrollBar.style.top = `${scrollNewTop}px`
          } else {
            scrollBar.style.top = `${maxSrcollBarHeight}px`
          }
        }
      },
      up: () => {
        const listEl = this.$refs.refList
        const scrollBar =
          this.$refs.refList.parentNode.querySelector('.scrollBar')

        if (listEl && scrollBar) {
          let currentTop = parseInt(listEl.style.top, 10)
          let currentScrollTop = parseInt(scrollBar.style.top, 10)
          let listVisHeight = listEl.parentNode.clientHeight
          let listHeight = listEl.clientHeight
          const radio = listHeight / listVisHeight
          const potentialNewTop = currentTop + 150 // 预计的新top值
          const scrollNewTop = currentScrollTop - 135 / radio

          // 检查是否已经到达最顶部
          if (potentialNewTop >= 0) {
            listEl.style.top = `0px` // 直接设置为0，确保不会超过顶部
            scrollBar.style.top = `0px`
          } else {
            listEl.style.top = `${potentialNewTop}px`
            scrollBar.style.top = `${scrollNewTop}px`
          }
        }
      },
      left: () => {},
      right: () => {},
      enter: () => {},
      esc: () => {
        //   if (this.dialogVisible) {
        //     this.popupBtnList[this.popupBtnNums].ref = ''
        //     this.leftList[this.leftNums].ref = 'active'
        //     this.dialogVisible = false
        //     return
        //   }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      getExchangeRule().then((res) => {
        if (res.code == 200) {
          this.content = res.data.content.replace(
            new RegExp('<p', 'g'),
            '<p style="word-break:break-all;" '
          )
        }
        this.$nextTick(() => {
          this.fuc.setScroll()
        })
      })
    },
  },
}
</script>

<style lang='less' scoped>
.pointRule {
  display: flex;
  position: relative;
  .container {
    position: relative;

    .right {
      width: 16.3rem;
      height: 7rem;
      position: absolute;
      font-size: 0.21rem;
      overflow: hidden;
      border-radius: 0.24rem;

      .list {
        width: 100%;
        overflow-y: auto;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;

        .content {
          font-size: 0.4rem !important;
          padding: 0.15rem;
          line-height: 0.55rem;
          position: relative;
          width: 16rem !important;
          height: initial !important;
          //   padding: 0.15rem;
        }
      }
    }
  }
}
</style>
<style lang="less">
.pointRule {
  .container {
    .right {
      .scroll {
        top: 2.25rem !important;
        left: 17.8rem !important;
      }
    }
  }
}
</style>