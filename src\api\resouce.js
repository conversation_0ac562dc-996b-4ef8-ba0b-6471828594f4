import axios from "axios";
import router from "../router";
import qs from "qs";
import store from "../store/index";
import sha256 from '../utils/sha256.js'
const CryptoJS = require("crypto-js");

let Axiso = axios.create({
    timeout: 60000,
    baseURL: process.env.VUE_APP_API
})

// http request 请求拦截器
Axiso.interceptors.request.use(
    config => {

        config.headers['Content-Type'] ='application/x-www-form-urlencoded'
        if (store.state.app.sha256Button) {
            if (store.state.app.token != "") {
                config.headers["Authorization"] = "Bearer " + CryptoJS.AES.decrypt(store.state.app.token, store.state.app.s).toString(CryptoJS.enc.Utf8);
            }

            let timestamp = parseInt(new Date().getTime() / 1000);  // 秒级
            let paramsStyring = ""
            if (config.params) {
                Object.keys(config.params).forEach((key) => {
                    if (config.params[key]!==null) {
                        paramsStyring += (key + '=' + config.params[key] + '&')
                    }
                });
            }
            paramsStyring += timestamp + "&" + store.state.app.s

            config.headers["Timestamp"] = timestamp;
            config.headers["Sign"] = sha256(paramsStyring)
        }


        if (config.url.indexOf('/api/v1/taxi/') > -1 ) {
            config.baseURL = process.env.VUE_APP_ONLINECAR
        }
        let originalData 
        if(config.data){
            originalData = JSON.parse(JSON.stringify(config.data));
            
        }
        config.data = qs.stringify(config.data) // 转为formdata数据格式
        if( config.url.indexOf('/shopping_aid/create_order') > -1) {
         config.data = originalData // 不转为formdata数据格式
        }

        


        return config;
    },
    error => {
        return error;
    }
);
// 添加响应拦截器
Axiso.interceptors.response.use(
    response => {
        if (store.state.app.sha256Button && response.config.url.indexOf('version.txt') == -1) {  // 排除本地请求
            if (!response.headers['timestamp'] || !response.headers['sign']) {
                // store.state.token = '';
                return
            }
            let promisData = ""
            if (response.data) {
                promisData = JSON.stringify(response.data) + '&'
            }
            promisData += response.headers['timestamp'] + '&' + store.state.app.s

            if (sha256(promisData) != response.headers['sign']) {
                let obj = {
                    code: -333333333,
                    data:{
                        msg: '数据异常'
                    }
                }
                return Promise.resolve(obj);
                return
            }
        }

        if (response.status === 200) {
            return Promise.resolve(response.data);
        } else {
            return Promise.reject(response);
        }
    },
    error => {
        if (error.response && error.response.status) {
            switch (error.response.status) {
                case 404:
                    break;
                case 401:
                    store.state.app.token = "";
                    sessionStorage.removeItem('token');
                    break;
            }
        }
        return Promise.reject(error);
    }
);

export default Axiso;