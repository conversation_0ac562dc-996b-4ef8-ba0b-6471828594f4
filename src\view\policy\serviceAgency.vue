<template>
  <div class="serviceAgency">
    <div class="container">
      <div class="left">
        <div class="messageCenterList scrollParent">
          <div class="leftList" :style="{ top: '0rem' }">
            <div class="messageItem" :ref="item.ref" v-for="(item, index) in leftList" :key="index">
              <div
                class="item_user"
                :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }"
              >
                <div class="content">
                  <div class="pic" v-lazy-container="{ selector: 'img' }">
                    <img :data-src="apiBaseUrl + item.img" alt="" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
      
<script>
import { getServiceAngency } from '@/api/index'

export default {
  name:'serviceAgency',
  components: {},
  data() {
    return {
      leftList: [],
      rightList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      apiBaseUrl: process.env.VUE_APP_API,
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '服务机构')
  },
  computed: {},
  watch: {},
  mounted() {
    this.getData()
    if (sessionStorage.getItem('indexServiceAgency')) {
      this.leftNums = Number(sessionStorage.getItem('indexServiceAgency'))
      sessionStorage.removeItem('indexServiceAgency')
    }
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.leftNums < this.leftList.length - 1) {
          if (this.leftList[this.leftNums + 4]) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums += 4
            this.leftList[this.leftNums].ref = 'active'
          } else {
            if (
              this.leftNums < this.leftList.length - (this.leftList.length % 4) &&
              this.leftList.length % 4 != 0
            ) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums = this.leftList.length - 1
              this.leftList[this.leftNums].ref = 'active'
            }
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.leftNums > 0 && this.leftNums - 4 >= 0) {
          this.leftList[this.leftNums].ref = ''
          this.leftNums -= 4
          this.leftList[this.leftNums].ref = 'active'
        }

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.leftNums % 4 != 0) {
          this.leftList[this.leftNums].ref = ''
          this.leftNums--
          this.leftList[this.leftNums].ref = 'active'
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (this.leftNums < this.leftList.length - 1) {
          if (this.leftNums % 4 != 3) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            this.leftList[this.leftNums].ref = 'active'
          }
        }

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      enter: () => {
        if (this.leftList.length > 0) {
          sessionStorage.setItem('indexServiceAgency', this.leftNums)
          this.$nextTick(() => {
            this.$router.push({
              path: '/serviceDetail',
              query: {
                id: this.leftList[this.leftNums].id,
                title: this.leftList[this.leftNums].title,
              },
            })
          })
        }
        return
      },
      esc: () => {
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      // 获取可预约时间列表
      getServiceAngency({
        // user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        user_id: this.$store.getters.getUserInfo.home_id,
      }).then((res) => {
        if (res.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data))
          list.map((item) => {
            item.ref = ''
          })
          this.leftList = list

          if (this.leftList.length > 0) {
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      })
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.serviceAgency {
  // height: 7rem;
  // overflow: hidden;
  // display: flex;
  // grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
  // gap: 0.16rem;
  // font-size: 0.2rem;
  // color: #e7e7ef;
  .container {
    height: 7.22rem;
    overflow: hidden;
    display: flex;
    // grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
    // gap: 0.16rem;
    font-size: 0.2rem;
    color: #e7e7ef;
    .left {
      width: 16.8rem;
      height: 7.22rem;
      margin-left: 0;
      //padding-left: 0.1rem;
      //margin-left: 0.05rem;

      .messageCenterList {
        width: 16.8rem;
        height: 100%;
        position: relative;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          // width: 83.6%;
          border-radius: 0.3rem;
          position: absolute;
          // left: 0.8rem;
          transition: all 0.2s;
          .messageItem {
            width: 3.98rem;
            height: 3.4rem;
            // padding: 0.5rem;
            margin-bottom: 0.32rem;
            position: relative;
            margin-right: 0.28rem;
            background-size: 100% 100% !important;
            // background: #ccc !important;
            background: #262954;
            border-radius: 0.35rem;
            overflow: hidden;
            .item_user {
              display: flex;
              align-items: center;
              height: 100%;
              text-align: center;
              .content {
                display: flex;
                height: initial !important;
                .pic {
                  width: 3.98rem;
                  height: 3.4rem;
                  img {
                    border-radius: 0.14rem;
                    // display: block;
                    width: 100%;
                    height: 100%;
                  }
                }
              }
              .introduce {
                height: 0.9rem;
                margin-top: 0.1rem !important;
                margin-bottom: 0 !important;
                font-size: 0.22rem !important;
                font-weight: 500 !important;
                line-height: 0.3rem;
                letter-spacing: 0.03rem;
                -webkit-line-clamp: 3 !important;
                background-image: initial !important;
                -webkit-text-fill-color: #bddb6c !important;
              }
            }
          }
          .messageItem:nth-child(4n + 4) {
            margin-right: 0;
          }
        }
      }
    }
  }
}
</style>