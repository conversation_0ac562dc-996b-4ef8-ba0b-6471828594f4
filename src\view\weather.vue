<template>
  <div class='weather_page'>
    <div :class="'weatherItem' + (index== 0 ? '': ' otherItem')" v-for="(item,index) in weatherInfo.future" :key="index" :style="{background: 'url('+(index==0 ? futurePicList[item.weatherType-1].index : futurePicList[item.weatherType-1].bg)+')'}">
      <div class="itemContent" v-if="index == 0">
        <div class="weather_top">
          <p class="weather_date">{{item.dateStr}} 今天</p>
          <div class="weather_api">
            <span>实时空气质量:</span>
            <span class="aqiNum" :style="{background: aqiInfo(Number(item.aqi)).bg}">{{item.aqi}} {{aqiInfo(Number(item.aqi)).text}}</span>
          </div>

        </div>
        <div class="weather_center">
          <img :src="require('@/assets/weather/bigIcon/icon_'+ item.weatherType + '.png')" alt="">
          <div class="temperature">
            <p>{{parseInt(weatherInfo.temperature)}}</p>
            <p>
              <span>℃</span>
              <span>{{weatherInfo.weather}}</span>
            </p>
          </div>
        </div>
        <div class="weather_bottom">
          <div class="left">
            <p>{{item.temperature}}</p>
            <p>{{item.weather}}</p>
            <p>{{item.wind}}</p>
          </div>

          <div class="right">
            <p>
              <span>湿度:</span>
              <span>{{item.humidity}}</span>
            </p>
            <p>
              <span>紫外线强度:</span>
              <span>{{item.uv_index}}</span>
            </p>
            <p>
              <span>穿衣指数:</span>
              <span>{{item.dressing_index}}</span>
            </p>
            <p>
              <span>晨练指数:</span>
              <span>{{item.exercise_index}}</span>
            </p>

          </div>
        </div>
      </div>
      <div class="itemContent" v-else>
        <div class="weather_top">
          <p class="weather_date">{{item.dateStr}}</p>
          <div class="weather_week">
            {{item.week}}
          </div>
        </div>
        <div class="weather_center">
          <img :src="require('@/assets/weather/bigIcon/icon_'+ item.weatherType + '.png')" alt="">
          <div class="temperature">
            {{item.weather}}
          </div>
        </div>
        <div class="weather_bottom">
          <div class="left">
            <p>{{item.temperature}}</p>
            <p>{{item.wind}}</p>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>


export default {
  name:'weather',
  components: {
  },
  data() {
    return {
      weatherInfo: {
        future:[],
      },
      futurePicList:[
        {
          index: require('@/assets/weather/current/index_bg_1.png'),
          bg: require('@/assets/weather/future/bg_1.png')
        },{
          index: require('@/assets/weather/current/index_bg_2.png'),
          bg: require('@/assets/weather/future/bg_2.png')
        },{
          index: require('@/assets/weather/current/index_bg_3.png'),
          bg: require('@/assets/weather/future/bg_3.png')
        },{
          index: require('@/assets/weather/current/index_bg_4.png'),
          bg: require('@/assets/weather/future/bg_4.png')
        },{
          index: require('@/assets/weather/current/index_bg_5.png'),
          bg: require('@/assets/weather/future/bg_5.png')
        },{
          index: require('@/assets/weather/current/index_bg_6.png'),
          bg: require('@/assets/weather/future/bg_6.png')
        },
      ]
    };
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle','天气预报详情')
  },
  computed: {
    aqiInfo(){
      return (num)=>{
        let obj = {
          text: '',
          bg:''
        }
        if (num > 50 && num < 101) {
          obj.text = "良";
          obj.bg = "#FFFF00";
        } else if (num > 100 && num < 151) {
          obj.text = "三级轻度污染";
          obj.bg = "#FF7E00";
        } else if (num > 150 && num < 201) {
          obj.text = "四级轻度污染";
          obj.bg = "#FF0000";
        } else if (num > 200 && num < 301) {
          obj.text = "五级轻度污染";
          obj.bg = "#99004C";
        } else if (num > 300) {
          obj.text = "六级轻度污染";
          obj.bg = "#7E0023";
        } else {
          obj.text = "优";
          obj.bg = "#00B358";
        }
        return obj
      }
    }
  },
  watch: {
    '$store.state.app.weatherInfo': {
      handler(){
        if (!this.$store.state.app.weatherInfo) {
          this.weatherInfo.future = []
          return
        }
        this.$store.dispatch('app/setLoadingState', true)
        this.weatherInfo = this.$store.state.app.weatherInfo
        this.weatherInfo.future.map(item=>{
          let newDate = item.date.substring(item.date.length-4,item.date.length)
          let strDate = newDate.slice(0,2) + '月' + newDate.slice(2,4) + '日'
          item.dateStr = strDate
        })
        setTimeout(()=>{
          this.$store.dispatch('app/setLoadingState', false)
        },100)
      },
      deep: true,
      immediate: true
    },
  },
  mounted() {

    this.fuc.KeyboardEvents({
      down:()=>{

      },
      up:()=>{

      },
      left:()=>{

      },
      right:()=>{

      },
      enter:()=>{

      },
      esc:()=>{
        // this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)

      }
    })
  },
  methods: {

  },
  destroyed() {

  },
  beforeDestory() {
  },
}
</script>
<style lang='less' scoped>
.weather_page {
  height: 6.4rem;
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(7, 1fr); /* 每行显示两个元素 */
  gap: 0rem;
  font-size: 0.24rem;
  color: #E7E7EF;
  .weatherItem {
    background-repeat: no-repeat !important;
    background-size: 100% 100% !important;
    .itemContent {
      padding: 0.26rem;
      height: calc(100% - 0.78rem);
      position: relative;
      .weather_top {
        font-size: 0.22rem !important;
        //line-height: 0.26rem;
        .weather_api {
          margin-top: 0.06rem;
          span {
            display: inline-block;
          }
          span:nth-child(2) {
            margin-left: 0.2rem;
            border-radius: 0.06rem;
            padding: 0 0.16rem;
            font-size: 0.22rem !important;
            color: #666;
          }
        }
      }
      .weather_center {
        display: flex;
        align-items: flex-end;
        justify-content: center;
        margin-top: 0.36rem;
        img {
          display: block;
          width: 2.8rem;
        }
        .temperature {
          display: flex;
          align-items: center;
          margin-left: 0.2rem;
          p:nth-child(1) {
            font-size: 0.6rem;
            margin-right: 0.15rem;
          }
          p:nth-child(2) {
            span {
              display: block;
            }
            span:nth-child(2) {
              width: 1rem;
            }
          }
        }
      }
      .weather_bottom {
        display: flex;
        align-items: flex-end;
        position: absolute;
        bottom: 0;
        .left,.right {
          width: 2.4rem;
          line-height: 0.36rem;
        }
      }
    }
  }
  .weatherItem {
    width: 2rem;
  }
  .weatherItem:first-child {
    width: 4.8rem;
  }
  .otherItem {
    .weather_top {
      text-align: center;
    }
    .weather_center {
      flex-direction: column !important;
      align-items: center !important;
      margin-top: 1.2rem !important;
      img {
        width: 1rem !important;
      }
      .temperature {
        margin-left: 0 !important;
        margin-top: 0.3rem;
      }
    }
    .weather_bottom {
      bottom: 0.5rem !important;
      left: 0;
      width: 100%;
      p {
        width: 100%;
        text-align: center;
      }
    }
  }
}

</style>
<style lang="less">

</style>
