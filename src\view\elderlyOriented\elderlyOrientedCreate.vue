<template>
  <div class="elderlyOrientedCreate">
    <div class="container">
      <div class="left">
        <div class="leftList">
          <div class="pic" ref="refLeft">
            <img :src="require('@/assets/reform_btn.png')" alt="" />
          </div>
        </div>
      </div>

      <div class="right scrollParent" ref="refRight">
        <div class="list" ref="refList" :style="{ top: '0rem' }">
          <div class="content" v-html="content"></div>
        </div>
      </div>

      <div class="orderList">
        <div class="rightContent scrollParent" ref="rightContent" v-if="this.orderList.length > 0">
          <div class="list" :style="{ top: '0rem' }">
            <div class="rightListItem" :ref="item.ref" v-for="(item, index) in orderList" :key="index" :data="item.ref">
              <div class="rightListItemInfo">
                <div class="status" :style="{color: statusList[item.status].color}">
                  {{ statusList[item.status].text }}
                </div>
                <div class="time">
                  <div class="title">申请时间:</div>
                  <div class="name">{{ item.created_at }}</div>
                </div>

                <div class="time" style="display: flex">
                  <div class="title">服务项目:</div>
                  <div class="name">{{ item.title }}</div>
                </div>
              </div>

            </div>
          </div>
        </div>
        <div class="no_info" v-else style="font-size: 0.36rem;color:#b5c0ff;position: absolute;top: 50%;left: 50%;transform: translate(-50%,-50%)">暂无记录</div>
      </div>
      <div class="done"><div class="item">服务记录</div></div>

      <el-dialog
        class="popup_box"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        :title="title"
      >
        <!--发起预约-->
        <div class="message_box sendCall" v-if="popupModule == 1">
          <div class="title">
            <p>请确认需要申请的服务。</p>
            <span>服务内容：</span><span>{{ title }}</span>
          </div>
        </div>
        <div
          class="message_box result"
          style="text-align: center; font-size: 0.37rem; justify-content: center"
          v-html="popupMessage"
          v-if="popupModule == 2"
        ></div>
        <div class="message_box sendCall" v-if="popupModule == 3">
          <div class="title">
            <p>你确认取消预约?</p>
            <span>服务内容：</span><span>{{ thisOrder.title }}</span>
          </div>
        </div>
        <div class="popupBtnList">
          <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index">
            {{ item.text }}
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
      
<script>
import { getShiLaoHuaList, sendShiLaoHuaOrder, cancelShiLaoHuaOrder } from '@/api/index'

export default {
  name: 'elderlyOrientedCreate',
  components: {},
  data() {
    return {
      thisOrder: null,
      statusList:[],
      leftList: [],
      rightList: [],
      orderList:[],

      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧  -2 在订单

      leftNums: 0,
      rightNums: 0,
      orderNums: 0,

      title: '',
      content: '',
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      messageList: [],
      supplierInfo: null,
      orderChange: false,
    }
  },
  created() {
    //设置页面左上角标题
    this.$nextTick(() => {
      this.$store.dispatch('index/setMainTitle', this.title)
    })
    if (localStorage.getItem('statusList')) {
      this.statusList = JSON.parse(localStorage.getItem('statusList'))
    }
  },
  computed: {},
  watch: {

  },
  mounted() {
    this.title = this.$route.query.title
    this.content = sessionStorage.getItem('elderlyOrientedContent')
    this.supplierInfo = JSON.parse(sessionStorage.getItem('supplierInfo'))
    if (this.content && this.content.indexOf('<img') > -1) {
      let reg = new RegExp('/public/storage/', 'g')
      this.content = this.content.replace(reg, process.env.VUE_APP_API + '/public/storage/')
      this.content = this.content.replace(new RegExp('<img', 'g'), '<img style="width:100%;" ')
    }
    this.content =  this.content.replace(
        new RegExp('<p', 'g'),
        '<p style="word-break:break-all;" '
    )
    setTimeout(() => {
      this.fuc.setScroll()
    }, 100)

    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          return
        }
        if (this.nextNums > -1) {
          const listEl = this.$refs.refList
          // const scrollBar = this.$refs.refScroll
          const scrollBar = this.$refs.refList.parentNode.querySelector('.scrollBar')

          // console.log('可视区域', listEl.parentNode.clientHeight)
          // console.log('元素距离顶部', listEl.offsetTop)
          // console.log('元素高度', listEl.clientHeight)
          // console.log('下拉高度', listEl.parentNode.offsetTop)

          if (listEl && scrollBar) {
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight

            const radio = listHeight / listVisHeight

            const potentialNewTop = currentTop - 150 // 预计的新top值

            const scrollNewTop = currentScrollTop + 150 / radio
            const maxScrollableHeight = listEl.clientHeight - listEl.parentNode.clientHeight // 最大可滚动高度
            const maxSrcollBarHeight = scrollBar.parentNode.clientHeight - scrollBar.clientHeight
            if (listVisHeight < listHeight) {
              // 将元素的top值与最大可滚动高度进行比较，以确定是否已经到达或超过了底部
              if (-potentialNewTop < maxScrollableHeight) {
                listEl.style.top = `${potentialNewTop}px`
              } else {
                // 已经到达或超过底部，调整top以确保内容底部和容器底部对齐
                listEl.style.top = `${-maxScrollableHeight - 50}px`
              }
            } else {
              return
            }

            if (scrollNewTop < maxSrcollBarHeight) {
              scrollBar.style.top = `${scrollNewTop}px`
            } else {
              scrollBar.style.top = `${maxSrcollBarHeight}px`
            }
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else // 在订单
        if (this.nextNums == -2) {
          if (this.orderNums < this.orderList.length -1 ) {
            this.orderList[this.orderNums].ref = ""
            this.orderNums ++
            this.orderList[this.orderNums].ref = "select"
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.select)
            })
          }
        }

      },
      up: () => {
        if (this.dialogVisible) {
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          const listEl = this.$refs.refList
          // const scrollBar = this.$refs.refScroll
          const scrollBar = this.$refs.refList.parentNode.querySelector('.scrollBar')

          if (listEl && scrollBar) {
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight
            const radio = listHeight / listVisHeight
            const potentialNewTop = currentTop + 150 // 预计的新top值
            const scrollNewTop = currentScrollTop - 135 / radio

            // 检查是否已经到达最顶部
            if (potentialNewTop >= 0) {
              listEl.style.top = `0px` // 直接设置为0，确保不会超过顶部
              scrollBar.style.top = `0px`
            } else {
              listEl.style.top = `${potentialNewTop}px`
              scrollBar.style.top = `${scrollNewTop}px`
            }
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else // 在订单
        if (this.nextNums == -2) {
          if (this.orderNums > 0 ) {
            this.orderList[this.orderNums].ref = ""
            this.orderNums --
            this.orderList[this.orderNums].ref = "select"
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.select)
            })
          }
        }
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          this.$refs.active = []
          this.$refs.active.push(this.$refs.refLeft)
          this.nextNums = -1
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else // 在订单
        if (this.nextNums == -2) {
          this.orderList[this.orderNums].ref = ""
          this.$refs.active = []
          this.$refs.active.push(this.$refs.refRight)
          this.nextNums = 0
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左
        if (this.nextNums == -1) {
          this.$refs.active = []
          this.$refs.active.push(this.$refs.refRight)
          this.nextNums = 0
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else
        // 在中
        if (this.nextNums > -1 && this.orderList.length > 0) {
          this.$refs.active = []
          this.orderList[this.orderNums].ref = "select"
          this.$refs.active.push(this.$refs.select)
          this.nextNums = -2

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.select)
          })
        }
      },
      enter: () => {
        if (this.$refs.active && this.nextNums == -1) {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.$refs.active.push(this.$refs.refLeft)
            this.dialogVisible = false
            // console.log(123,this.popupBtnList[this.popupBtnNums].fuc(this.leftList[this.leftNums]));
            this.popupBtnList[this.popupBtnNums].fuc(this.leftList[this.leftNums])
            return
          }
          this.popupBtnList = [
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            },
            {
              text: '确认',
              ref: '',
              fuc: this.submitInfo,
            },
          ]
          this.dialogVisible = true
          this.popupModule = 1
          this.$refs.active = ''
          this.popupBtnList[this.popupBtnNums].ref = 'active'
        } else
        if( this.nextNums == -2) {
          this.thisOrder = this.orderList[this.orderNums]

          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.orderList[this.orderNums].ref = "select"
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].fuc(this.leftList[this.leftNums])
            return
          }
          if (this.thisOrder.status != 2) {
            return
          }

          this.popupBtnList = [
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            },
            {
              text: '确认',
              ref: '',
              fuc: this.cancelOrder,
            },
          ]
          this.dialogVisible = true
          this.popupModule = 3
          this.$refs.active = ''
          this.orderList[this.orderNums].ref = ""
          this.popupBtnList[this.popupBtnNums].ref = 'active'
        }
      },
      esc: () => {
        if (this.dialogVisible) {
          if (this.orderChange) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.$refs.active.push(this.$refs.refLeft)
            this.dialogVisible = false
            sessionStorage.removeItem('elderlyOrientedContent')
            this.$store.dispatch('index/setFocusDom', null);
            history.go(-1)
          }
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.$refs.active.push(this.$refs.refLeft)
          this.dialogVisible = false
          return
        }
        sessionStorage.removeItem('elderlyOrientedContent')
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      this.orderChange = false
      this.$nextTick(() => {
        if (this.nextNums == -2) {
          this.$store.dispatch('index/setFocusDom', this.$refs.select)
        } else {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        }
        document.getElementById('focus_border').style.borderColor = '#fff'
        this.popupBtnNums = 0
        this.popupModule = 1
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },

    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      if (this.nextNums == -1) {
        this.$refs.active = []
        this.$refs.active.push(this.$refs.refLeft)
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      }

      getShiLaoHuaList({
        fk_supplier_id: this.supplierInfo.fk_supplier_id,
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        home_id: this.$store.getters.getUserInfo.home_id,
      })
      .then(res=>{
        if (res.code == 200) {
          let data = JSON.parse(JSON.stringify(res.data.unfinish_list))
          data.map(item=>{
            item.ref = ""
          })
          this.orderList = data

          this.$nextTick(()=>{
            this.fuc.setScroll()
          })
        }
      })
    },
    submitInfo() {
      this.$store.dispatch('app/setLoadingState', true)
      sendShiLaoHuaOrder({
        fk_shilaohua_gaizao_id: this.$route.query.id,
        user_id:this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        home_id: this.$store.getters.getUserInfo.home_id,
        fk_supplier_id: this.supplierInfo.fk_supplier_id,
      }).then(
        (res) => {
          if (res.code == 200) {
            this.orderChange = true
            this.popupModule = 2
            this.$refs.active = ''
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  sessionStorage.removeItem('elderlyOrientedContent')
                  history.go(-1)
                },
              },
            ]
            this.popupMessage = '订单发送成功！'
            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 2
          this.$refs.active = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br>订单发送失败!</br>'
          if (error.response && error.response.data && error.response.data.msg) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },
    cancelOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      cancelShiLaoHuaOrder({
        user_id:this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        home_id: this.$store.getters.getUserInfo.home_id,
        fk_supplier_id: this.supplierInfo.fk_supplier_id,
        order_id: this.thisOrder.id
      })
      .then(res=>{
        this.$store.dispatch('app/setLoadingState', false)
        if (res.code == 200) {
          this.orderChange = true
          this.orderList[this.orderNums].status = 3
          this.popupModule = 2
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.popupBtnList[this.popupBtnNums].ref = ''
                this.orderList[this.orderNums].ref = "select"
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '订单取消成功！'
          this.popupBtnNums = 0
          this.dialogVisible = true


        }
      })
      .catch(error=>{
        this.$store.dispatch('app/setLoadingState', false)
        this.popupModule = 2
        this.$refs.active = ''
        this.popupBtnList = [
          {
            text: '确认',
            ref: 'active',
            fuc: () => {
              this.dialogVisible = false
            },
          },
        ]
        this.popupMessage = '<br>订单取消失败！</br>'
        if (error.response && error.response.data && error.response.data.msg) {
          this.popupMessage += error.response.data.msg
        }
        this.popupBtnNums = 0
        this.dialogVisible = true
      })
    }
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.elderlyOrientedCreate {
  display: flex;
  position: relative;
  width: 100%;
  height: 7rem;
  .container {
    position: relative;
    width: 100%;
    height: 100%;
    .left {
      width: 3.2rem;
      height: 2.1rem;
      position: absolute;
      top: 0 !important;
      .leftList {
        .pic {
          width: 3.2rem;
          height: 2.1rem;
          position: absolute;
          font-size: 0.48rem;
          font-weight: bold;
          letter-spacing: 0.1rem;
          text-align: center;
          line-height: 1.1rem;
          // background-image: linear-gradient(to right, #5f7eb7, #4763a2);
          border-radius: 0.3rem;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .right {
      width: 8rem;
      height: 6.8rem;
      position: absolute;
      background: #25294f;
      font-size: 0.21rem;
      overflow: hidden;
      border-radius: 0.24rem;
      margin-left: 4rem;
      padding: 0.2rem;
      padding-bottom: 0;

      .list {
        overflow-y: auto;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        .content {
          position: relative;
          width: 100% !important;
          height: initial !important;
        }
      }
    }

    .orderList {
      width: 3.9rem;
      height: 7rem;
      position: absolute;
      right: 0;
      .rightContent {
        width: 100%;
        height: 7rem;
        position: relative !important;
        overflow: hidden;
        display: inline-block;
        .list {
          // margin-right: 0.22rem;
          position: relative;
          transition: all 0.3s;
          .rightListItem {
            height: 2.1rem;
            //padding-top: 0.18rem !important;
            //padding-right: 0.1rem !important;
            margin-bottom: 0.35rem;
            background: #262954;
            border-radius: 0.24rem;
            font-size: 0.28rem;
            font-weight: bold;
            .rightListItemInfo {
              padding: 0.15rem 0.35rem;
            }
            .status {
              color: #e77302;
              margin-bottom: 0.1rem;
              letter-spacing: 0.03rem;
            }
            .title {
              width: 1.25rem !important;
              color: #b6c0fd;
            }
            .time {
              margin-bottom: 0.1rem;
            }

          }
        }
      }
    }

    .done {
      width: 3.9rem;
      position: fixed;
      top: 1.6rem;
      right: 0.9rem;
      .item {
        font-size: 0.36rem;
        font-weight: bold;
        letter-spacing: 0.03rem;
        color: #b5c0ff;
      }
      .item::before {
        content: '';
        width: 0.1rem;
        height: 0.1rem;
        position: absolute;
        background: #b5c0ff;
        border-radius: 50%;
        top: 50%;
        left: -0.25rem;
      }
    }
  }
}
</style>
<style lang="less">
.elderlyOrientedCreate {
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    // background: repeating-linear-gradient(to right, #c98693, #a95361);
    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        letter-spacing: 0.03rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        justify-content: center !important;
        align-items: center !important;
        .title {
          font-size: 0.48rem;
          font-weight: bold;
          // padding-bottom: 0.12rem !important;
          p {
            margin-bottom: 0.22rem;
          }
        }
      }
      .result {
        font-size: 0.48rem !important;
        font-weight: bold !important;
        letter-spacing: 0.03rem !important;
      }
      .cancel {
        flex-direction: column;
        div {
          text-align: center;
        }
        div:nth-child(2) {
          margin-top: 0.1rem;
          color: red;
          line-height: 0.48rem;
        }
        //align-items: normal !important;
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

          // background: repeating-linear-gradient(to right, #c98693, #a95361);
        }
      }
    }
  }
  .container {
    .right {
      .scroll {
        top: 2.25rem !important;
        left: 13.8rem !important;
      }
    }
    .orderList {
      .scroll {
        left: initial !important;
        top: 2.25rem !important;
        right: 0.9rem !important;
      }
    }
  }
}
</style>
      