<template>
<div class='header'>
    <div class="left_header" ref="headerF">
<!--      <transition name="fadeLogo">-->
        <div v-if="this.$store.getters.main_title.length != 0">
          <p ref="headerS" v-if="!marqueeShow"> {{this.$store.getters.main_title}} </p>
          <marquee v-else>{{this.$store.getters.main_title}}</marquee>
        </div>
<!--      </transition>-->

<!--      <transition name="fadeLogo">-->
        <div style="position: absolute;top: 50%;transform: translateY(-50%);right:0;" v-if="this.$store.getters.main_title.length == 0">
          <img  style="display: block;width: 2.3rem;height: 0.66rem" :src="require('@/assets/logo.png')" alt="">
        </div>
<!--      </transition>-->


    </div>

<!--    <div class="onlineList">-->
<!--      <div v-for="(item,index) in this.$store.state.app.onLineList" :key="index">-->
<!--        <img :src="item.img" :style="{opacity: item.show ? 1 : 0.3}" alt="">-->
<!--      </div>-->
<!--    </div>-->


    <div class="date" v-if="dateShow" :style="{right: this.$store.getters.main_title.length == 0 ? '2.5rem' : 0}">

        <div class="week">
<!--          <div class="weekInfo">-->
<!--            星期{{timeInfo.date.week}}-->
<!--          </div>-->
          <div class="week_day">
            {{timeInfo.date.month}}
            <span>/</span>
            {{timeInfo.date.day}}
          </div>
        </div>
        <div class="time">
          {{timeInfo.time.hours}}:{{timeInfo.time.minutes}}
        </div>
        <div>

        </div>
    </div>


    <transition name="fade">
      <div v-if='this.$route.meta.index==1' class="oneBtn" ref='oneBtn' ></div>
    </transition>
    <transition name="fade">
      <div v-if='this.$route.meta.index==2' class="btnList" ref='threeBtn'>
        <div :class="item.class" :ref="item.ref" v-for="(item,index) in refItem" :key="index"  :style="{opacity: item.customer ? 0.4 : 1}">
          <div class="content" v-html="item.title">

          </div>
        </div>
      </div>
    </transition>


</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import store from "@/store";
import websocketData from "@/utils/websocketData";
export default {
//import引入的组件需要注入到对象中才能使用
components: {},
data() {
    return {
        dateShow: true,
        marqueeShow:false,
        timeInfo: {
          time: {
            hours: '08',
            minutes: '00'
          },
          date: {
            month: '01',
            day: '01',
            week: '一'
          }
        },
        backArr:[],
        backTvSize:{
            top:'',
            width:'',
            left:'',
            height:''
        },
        refItem:[
          {
            class:'oneBtn user',
            title: '用户',
            ref:'user',
            customer: 0
            // bg:require('../assets/myticket_btn.png')
          },
            {
              class:'oneBtn point',
              title:`积分: <img src=`+ require('@/assets/point_icon.png') +`> ` + this.$store.getters.getPointTotal,
              ref:'point',
              customer: 0
            },
            {
              class:'oneBtn ai_bot',
              title:`<img src=`+require('@/assets/video_call.png')+` alt="">
                      <div class="weather_num" style="width: 0.86rem;text-align: right">
                        视频客服
                      </div>`,
              ref:'ai_bot',
              customer: 0
              // bg:require('../assets/media_btn.png')
            },
            {
                class:'oneBtn weather',
                title:`<img src=`+require('@/assets/weather/bigIcon/icon_1.png')+` alt="">
                        <div class="weather_num" style="width: 0.86rem;text-align: right">
                          --℃
                        </div>`,
                ref:'weather',
              customer: 0
                // bg:require('../assets/media_btn.png')
            },
            {
              class:'oneBtn setting',
              title: '',
              ref:'setting',
              customer: 0
              // bg:require('../assets/myticket_btn.png')
            },
        ],
        userInfo: null,
        timeId:null,
        headerTimer:null
    };
},
created(){
    if (window.location.href.indexOf('online_food_aihu') > -1) {
      this.dateShow = false
    } else {
      this.dateShow = true
    }
},
computed: {},
watch: {
    '$route' (to) {
        this.$nextTick(()=>{
          if (window.location.href.indexOf('online_food_aihu') > -1) {
            this.dateShow = false
          } else {
            this.dateShow = true
          }
          this.backArr = []
          this.$store.dispatch('index/setHeaderArr',this.backArr);
          this.getSize(to.meta.index)
        })
      //
    },
    '$store.getters.getPointTotal'() {
      this.refItem[1].title = `积分: <img src=`+ require('@/assets/point_icon.png') +`> ` + this.$store.getters.getPointTotal
      if (this.$store.getters.getPointTotal > 99999) {
        this.refItem[1].title = `积分: <img src=`+ require('@/assets/point_icon.png') +`> ` + this.$store.getters.getPointTotal.toString().substring(0,5) + '...'
      }
    },
    '$store.state.app.onLineList': {
      handler(){
        if (this.$store.state.app.onLineList[0].show == true) {
          this.$store.state.app.onLineList[0].img = require('@/assets/camera_online.png')
        } else  {
          if (this.$store.state.app.onLineList[0].show == -1) {
            this.$store.state.app.onLineList[0].img = require('@/assets/camera_close.png')
          } else {
            this.$store.state.app.onLineList[0].img = require('@/assets/camera_no.png')
          }

        }
        this.refItem[4].title = `<img  src="`+this.$store.state.app.onLineList[0].img+`" />`

        return
        // 连接websocket
        if (this.$store.state.app.onLineList[0].show) {
          this.$nextTick(()=>{
            websocketData.init()
          })
        }
        // 断开websocket
        else {
          this.$nextTick(()=>{
            websocketData.close()
          })
        }
      },
      deep: true,
      immediate: true
    },
    '$store.state.app.weatherInfo': {
        handler(){
          if (!this.$store.state.app.weatherInfo) {
            this.refItem[3].title = `
                                      <img src=`+require('@/assets/weather/bigIcon/icon_1.png')+` alt="">
                                      <div class="weather_num" style="width: 0.86rem;text-align: right">
                                        --℃
                                      </div>
                                  `
            return
          }
          let data = this.$store.state.app.weatherInfo
          if (data.weatherType < 1 || data.weatherType > 6) {
            return
          }
          this.refItem[3].title = `
                                    <img src=`+require('@/assets/weather/bigIcon/icon_'+data.weatherType+'.png')+` alt="">
                                    <div class="weather_num"  style="width: 0.86rem;text-align: right">`+ data.temperature +`</div>
                                  `

      },
      deep: true,
      immediate: true
    },
    '$store.getters.main_title'() {
        this.$nextTick(()=>{
          var fWidth = this.$refs.headerF.getBoundingClientRect().width
          if (this.$refs.headerS) {
            var cWidth =  this.$refs.headerS.getBoundingClientRect().width
          } else {
            this.marqueeShow = false
            return
          }
          if (cWidth > fWidth) {
            this.marqueeShow = true
          } else {
            this.marqueeShow = false
          }
        })
    },
    '$store.state.app.timeInfo':{
      handler(newValue){
        if (newValue) {
          this.timeInfo = this.$store.state.app.timeInfo
        }
      },
      deep: true,
      immediate: true
    },
    '$store.state.app.selectUserIndex': {
        handler(){
          this.userInfo = this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex]
          // this.refItem[1].title = this.userInfo.name
          let imgICon = process.env.VUE_APP_API + '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png'
          let iconMarginTop = 0.06
          if (this.userInfo.avatar) {
            if (this.userInfo.avatar.indexOf('http') == -1) {
              imgICon = process.env.VUE_APP_API + this.userInfo.avatar
              iconMarginTop = 0
            } else {
              imgICon = this.userInfo.avatar
              iconMarginTop = 0
            }
          }
          this.refItem[0].title = `
                                    <img id="userIcon" style="margin-top: `+iconMarginTop+`rem" src=`+ imgICon +` alt="">
                                    <div class="userName">`+ this.userInfo.name +`</div>
                                  `

          this.$nextTick(()=>{
            if (document.getElementById('userIcon')) {
              document.getElementById('userIcon').onerror = this.defImg
            }
          })
        },
        deep: true,
        immediate: true
    }
    // '$store.getters.header_arr': {
    //   handler(){
    //     console.log(9999)
    //   },
    //   deep: true,
    // }
},
mounted() {
    this.getSize(this.$route.meta.index)
    if (this.$store.getters.getUserInfo.customer) {
      this.refItem[2].customer = this.$store.getters.getUserInfo.customer
    }
    // var _that=this;
    // window.addEventListener('resize',()=>{
    //     clearTimeout(_that.headerTimer)
    //     _that.headerTimer=setTimeout(()=>{
    //         _that.getSize(_that.$route.meta.index)
    //     },500)
    // })
},
methods: {
    defImg(){
      let img = event.srcElement;
      img.src = process.env.VUE_APP_API + '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png';
      img.onerror = null; //防止闪图
    },
    getSize(num){
      this.$nextTick(()=>{
        if (document.getElementById('userIcon')) {
          document.getElementById('userIcon').onerror = this.defImg
        }
      })
      this.backArr=[]
        if (num < 1) {
          return
        }
        if(num==1){
            if(this.$refs.oneBtn){
                this.$nextTick(()=>{
                    this.backTvSize.width=this.$refs.oneBtn.getBoundingClientRect().width
                    this.backTvSize.height=this.$refs.oneBtn.getBoundingClientRect().height
                    this.backTvSize.left=this.$refs.oneBtn.getBoundingClientRect().left
                    this.backTvSize.top=this.$refs.oneBtn.getBoundingClientRect().top
                    this.backTvSize.dom = this.$refs.oneBtn
                  // obj.radius=this.$refs.threeItem2.getBoundingClientRect().top

                    this.backArr.push(this.backTvSize);
                    this.$store.dispatch('index/setHeaderArr',this.backArr);
                    // this.backArr=[]
                    // this.$store.dispatch('index/setFocusDom',this.$refs.oneBtn);
                })
            }
        }
        else{
          this.getItem2()
        }
        // clearTimeout(this.timeId)
        // this.$store.dispatch('index/setHeaderArr',this.backArr);
        // this.backArr=[]
    },
    getItem2(){
        this.$nextTick(()=>{
          if(this.$refs.user && this.$refs.user.length > 0){
            let obj={}
            obj.width=this.$refs.user[0].getBoundingClientRect().width
            obj.height=this.$refs.user[0].getBoundingClientRect().height
            obj.left=this.$refs.user[0].getBoundingClientRect().left
            obj.top=this.$refs.user[0].getBoundingClientRect().top
            obj.dom = this.$refs.user[0]
            this.backArr.push(obj)
          }
          if(this.$refs.point && this.$refs.point.length > 0){
            let obj={}
            obj.width=this.$refs.point[0].getBoundingClientRect().width
            obj.height=this.$refs.point[0].getBoundingClientRect().height
            obj.left=this.$refs.point[0].getBoundingClientRect().left
            obj.top=this.$refs.point[0].getBoundingClientRect().top
            obj.dom = this.$refs.point[0]
            this.backArr.push(obj)
          }
          if(this.$refs.ai_bot && this.$refs.ai_bot.length > 0){
            let obj={}
            obj.width=this.$refs.ai_bot[0].getBoundingClientRect().width
            obj.height=this.$refs.ai_bot[0].getBoundingClientRect().height
            obj.left=this.$refs.ai_bot[0].getBoundingClientRect().left
            obj.top=this.$refs.ai_bot[0].getBoundingClientRect().top
            obj.dom = this.$refs.ai_bot[0]
            this.backArr.push(obj)
          }
          if(this.$refs.weather && this.$refs.weather.length > 0){
            let obj={}
            obj.width=this.$refs.weather[0].getBoundingClientRect().width
            obj.height=this.$refs.weather[0].getBoundingClientRect().height
            obj.left=this.$refs.weather[0].getBoundingClientRect().left
            obj.top=this.$refs.weather[0].getBoundingClientRect().top
            obj.dom = this.$refs.weather[0]
            this.backArr.push(obj)
          }
          if(this.$refs.setting && this.$refs.setting.length > 0){
            let obj={}
            obj.width=this.$refs.setting[0].getBoundingClientRect().width
            obj.height=this.$refs.setting[0].getBoundingClientRect().height
            obj.left=this.$refs.setting[0].getBoundingClientRect().left
            obj.top=this.$refs.setting[0].getBoundingClientRect().top
            obj.dom = this.$refs.setting[0]
            this.backArr.push(obj)
          }




          this.$store.dispatch('index/setHeaderArr',this.backArr);
        })
    },
},
beforeDestory() {}, //生命周期 - 销毁完成
}
</script>
<style lang='less' scoped>
    .fade-enter-active, .fade-leave-active {
      transition: opacity 0.8s;
    }
    .fade-enter, .fade-leave-to {
      opacity: 0;
    }

    .fadeLogo-enter-active, .fadeLogo-leave-active {
      transition: opacity 0.8s;
    }
    .fadeLogo-enter, .fadeLogo-leave-to {
      opacity: 0;
    }

    .header{
        width:16.8rem;
        height:2.24rem;
        //margin-left:1.2rem;
        margin: 0 auto;
        position: relative;
        display: flex;
        align-items: center;
        font-family: 微软雅黑 !important;
        .date {
          height: 2.24rem;
          display: flex;
          flex-direction: row;
          align-items: center;
          position: absolute;
          right: 2.5rem;
          top: 50%;
          transform: translateY(-50%);
          .time {
            margin-right: 0.05rem;
            font-size: 0.38rem;
          }
          .week {
            display: flex;
            flex-direction: column;
            //font-size: 0.16rem;
            font-size: 0.38rem;
            //color: #E1E1EB;
            margin-right: 0.2rem;
            //padding-right: 0.38rem;
            //border-right: 0.02rem solid #918FA4
            .week_day {
              display: flex;
              align-items: center;
              span {
                display: inline-block;
                height: 0.34rem;
                line-height: 0.34rem;
                overflow: hidden;
                border-bottom-left-radius: 0.02rem;
                border-bottom-right-radius: 0.04rem;

              }
            }

          }
        }
        .left_header{
            max-width:10rem;
            height:2.24rem;
            font-size:0.5rem;
            line-height: 2.24rem;
            font-weight: bold;
            letter-spacing: 0.05rem;
            float: left;

            white-space: nowrap;
            text-overflow: ellipsis;
           overflow: hidden;

          p {
              float: left;
              color:#fff;
              background-image: linear-gradient(to bottom,rgba(255,255,255,1),rgba(255,255,255,0.65));
              -webkit-background-clip:text;
              -webkit-text-fill-color:transparent;
            }
          marquee {
            color:#D6D7DC;
            //background-image: linear-gradient(to bottom,rgba(255,255,255,1),rgba(255,255,255,0.65));
            //-webkit-background-clip:text;
            //-webkit-text-fill-color:transparent;
          }
        }
        .right_header{
            width:2.9rem;
            height: 0.8rem;
            font-size: 0.3rem;
            margin-top:0.72rem;
            border-left: 0.03rem solid #ccc;
            padding-left:0.3rem;
            letter-spacing: 0.05rem;
            font-weight: bold;
            float: right;
            p{
                color:#fff;
                background-image: linear-gradient(to bottom,rgba(255,255,255,1),rgba(255,255,255,0.65));
                -webkit-background-clip:text;
                -webkit-text-fill-color:transparent;
            }
        }
        .btnList {
          //height:0.6rem ;
          width: 70.5%;
          height: 100%;
          position: absolute;
          left: 0rem;
          display: flex;
          align-items: center;
          top: 50%;
          transform: translateY(-50%);
          .oneBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: auto;
            margin-left: 0.2rem;
            .content {
              color: #F5F4F9;
              font-weight: bold;
            }
          }
          .setting{
            position: absolute;
            right: 0;
            margin-left: 0 !important;
            .content {
              letter-spacing: 0.12rem;
              text-indent: 0.12rem;
              font-size: 0.28rem;
              //text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0
              text-shadow: 0.03rem 0.03rem 0.02rem rgba(0, 0, 0, 0.4);
            }
          }
          .weather {
            position: absolute;
            right: 0.8rem;
          }
          .ai_bot {
            position: absolute;
            right: 2.6rem;
          }
          .weather,.user, .ai_bot {
            .content {
              display: flex;
              flex-direction: row;
              align-items: center;
            }
          }

        }
        .onlineList {
          display: flex;
          flex-direction: row;
          align-items: center;
          position: absolute;
          right: 1.8rem;
          top: 50%;
          transform: translateY(-50%);
          img {
            transition: all 0.8s;
          }
          img:nth-child(1) {
            margin-right: 0.05rem;
          }
          div:first-child {
            img {
              width: 0.46rem;
            }
          }
          div:last-child {
            img {
              width: 0.86rem;
            }
          }
        }
        .oneBtn{
          width:1.82rem;
          height:0.6rem ;
          //width:1.78rem;
          //height:0.9rem ;
          //background:url('../assets/backtv_btn.png') no-repeat;
          //background: #545D94;
          //box-shadow: 0rem .02rem .02rem rgba(118,130,188,1) inset;


          background-size:100% 100%;

          //top: 0;
          //margin-top:0.67rem;
          border-radius: 0.3rem;
          overflow: hidden;
        }

        .threeBtn{
            width:3.6rem;
            height:0.9rem ;
            float: left;
            position: absolute;
            right: 3.35rem;
            margin-top:0.67rem;
            .threeItem{
                width:1rem;
                height:0.9rem ;
                float: left;
                margin-right: 0.2rem;
                border-radius: 0.3rem;
                background-size:100% 100% !important ;
            }
        }
      //.weather {
      //  display: flex;
      //  flex-direction: row;
      //  align-items: center;
      //  font-size: 0.26rem;
      //  margin-left: 0.4rem;
      //
      //  .weather_pic {
      //    img {
      //      width: 0.85rem;
      //    }
      //  }
      //  .weather_num {
      //    width: 0.55rem;
      //    text-align: right;
      //  }
      //}
    }
</style>
<style lang="less">
  .btnList {
    .weather {
      .content {
        img {
          display: block !important;
          width: 0.7rem !important;
          height: 0.7rem !important;
          //margin-right: 0.1rem;
        }
        div {
          font-size: 0.34rem;
          font-weight: 500;
        }
      }
    }
    .ai_bot {
      width: auto !important;
      .content {
        img {
          display: block !important;
          width: 0.42rem !important;
          height: 0.42rem !important;
          margin-right: 0.15rem;
          margin-left: 0.1rem;
        }
        div {
          width: auto !important;
          font-weight: 600;
          letter-spacing: 0.03rem;
          font-size: 0.28rem;
          color: #fff;
          background-image: linear-gradient(to bottom, #ffffff, rgba(255, 255, 255, 0.65));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .user {
      margin-left: 0 !important;
      .content {
        display: flex !important;
        flex-direction: row;
        align-items: center;
        font-size: 0.3rem;
        img {
          display: block;
          width: 0.6rem;
          height: 0.6rem;
          border-radius: 50%;
          background: #fff;
          margin-top: 0 !important;
          margin-right: 0.05rem;
        }
        div {
          letter-spacing: 0.03rem;
          font-size: 0.26rem;
          color: #fff;
          background-image: linear-gradient(to bottom, #ffffff, rgba(255, 255, 255, 0.65));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          padding: 0 0.1rem;
          max-width: 3rem;
          overflow: hidden;
          /*文本不会换行*/
          white-space: nowrap;
          /*当文本溢出包含元素时，以省略号表示超出的文本*/
          text-overflow: ellipsis;
        }
      }

    }
    .setting {
      //margin-right: 0.26rem;
      border-radius: 0 !important;
      .content {
        display: flex !important;
        flex-direction: row;
        align-items: center;
        padding: 0 0.1rem;
        img {
          display: block;
          width: 0.4rem;
          height: 0.42rem;
        }
      }

    }
    .point {
      margin-right: 0.14rem;
      max-width: 2.5rem !important;
      overflow: hidden;
      white-space: nowrap;
      /*当文本溢出包含元素时，以省略号表示超出的文本*/
      text-overflow: ellipsis;
      justify-content: flex-start !important;
      .content {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        letter-spacing: 0.03rem;
        font-size: 0.26rem;
        color: #fff;
        background-image: linear-gradient(to bottom, #ffffff, rgba(255, 255, 255, 0.65));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        padding: 0 0.1rem;
        img {
          display: block;
          width: 0.4rem;
          height: 0.4rem;
          border-radius: 50%;
          margin-top: 0 !important;
          margin-right: 0.08rem;
          margin-left: 0.08rem;
        }
      }

    }
  }


</style>
