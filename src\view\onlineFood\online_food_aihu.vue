<template>
  <div class="online_food_aihu">
    <div class="balance">我的余额:{{money}}元</div>
    <div class="container">
      <div class="left">
        <div class="leftContent scrollParent">
          <div class="leftList" :style="{ top: '0rem' }">
            <div class="leftItem" :ref="item.ref" :style="{background: leftNums == index ? '#5495D2' : '#262954',color: leftNums == index ? '#fff' : '#7980b3'}" v-for="(item, index) in leftList" :key="index">
              <div>
                {{item.week}}
              </div>
              <div>
                {{item.date}}
              </div>

              <div class="selectNum" v-if="item.selectNum > 0">
                <span>已选</span>
                <span>{{item.selectNum}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="right">
        <div class="periodInfo">
          <transition name="fade">
            <div v-if="periodList[periodListIndex]">
              {{periodList[periodListIndexOld].foodPeriodName}}(用餐时间{{ periodList[periodListIndexOld].startTime }}-{{ periodList[periodListIndexOld].endTime }})
            </div>
          </transition>
        </div>
        <div class="rightContent" :ref="rightList[0].ref" v-if="this.rightList.length > 0">
          <el-carousel ref="swiperDom" :loop="false" trigger="none" height="93%"  :indicator-position="rightList.length > 1 ? '' : 'none'" indicator-position="outside" arrow="never" :interval="0" @change="swiperChange">
            <el-carousel-item v-for="(item,index) in rightList" :key="index">
              <div class="swiperInfo">
                <div class="foodSelectNum"  v-if="item.selectNum > 0">
                  已选{{item.selectNum}}份
                </div>
                <div class="img"  v-lazy-container="{ selector: 'img' }">
                  <img :data-src="item.pic1" :data-error="lazyError" :key="item.pic1" alt="">
                </div>
                <div class="foodDetails">
                  <div class="foodTop">
                    <div class="foodName">
                      {{ item.productName }}
                    </div>
                    <div class="price">
                      ￥{{item.price}}
                    </div>
                  </div>
                  <div class="foodCenter">
                    <div class="title">菜品描述</div>
                    <div class="foodValue centerInfo">
                      <div v-if="item.productCombineds.length > 0 && index < 4" v-for="(items,index) in item.productCombineds">
                        {{items.productName}}  {{items.num ? '× ' + items.num : ''}}
                      </div>

                    </div>
                  </div>

                  <div class="foodBottom">
                    <div class="title">菜品库存</div>
                    <div class="foodValue centerInfo">
                      {{item.totalStockNum}}
                    </div>
                  </div>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
        <div class="no_info" v-if="noInfo">暂无数据</div>
        <div class="rightBtn">
          <div class="btnItem" :ref="item.ref" v-for="(item,index) in rightBtn" :key="index" :style="{opacity: item.show ? 1 : 0.4}">
            {{item.text}}
          </div>
        </div>



      </div>

<!--  二次确认弹窗-->
      <el-dialog
        :class="popupModule == 1 ? 'selectNum_info_box' : popupModule == 2 ? 'selectNum_info_box message_box_info' : popupModule == 3 ? 'selectNum_info_box selectPeriod_info_box' : popupModule == 4 ? 'popup_info_box' : 'popup_box'"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        :title="popupModule == 1 ? '请选择份数' : popupModule == 3 ? '选择用餐时间' : '提示'"
        ref="popupBox"
      >
        <!--选择份数1-->
        <div class="selectNum_box" v-if="popupModule == 1">
          <div class="tips" v-if="rightList[rightNums]">
            注: 选择0为取消菜品;最大可选分数为{{Number(this.rightList[this.rightNums].totalStockNum) <= 10 ? Number(this.rightList[this.rightNums].totalStockNum) : 10}}
          </div>

          <div class="popupCenter">
            <div class="delectNum" ref="delectNum">
              <svg t='1678087632198' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='5027' xmlns:xlink='http://www.w3.org/1999/xlink' width='70' height='70'><path d='M256 480m32 0l448 0q32 0 32 32l0 0q0 32-32 32l-448 0q-32 0-32-32l0 0q0-32 32-32Z' fill='#EFF0F5' p-id='5028'></path><path d='M512 64A448 448 0 1 0 960 512 448.5 448.5 0 0 0 512 64z m0 832a384 384 0 1 1 384-384 384.5 384.5 0 0 1-384 384z' fill='#EFF0F5' p-id='5029'></path></svg>
            </div>

            <div class="nums">
              {{selectNums}}
            </div>

            <div class="addNum"  ref="addNum">
              <svg t='1678087565924' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='3770' xmlns:xlink='http://www.w3.org/1999/xlink' width='70' height='70'><path d='M512 958.8C265.6 958.8 65.2 758.4 65.2 512S265.6 65.2 512 65.2 958.8 265.6 958.8 512 758.4 958.8 512 958.8z m0-832c-212.4 0-385.2 172.8-385.2 385.2S299.6 897.2 512 897.2 897.2 724.4 897.2 512 724.4 126.8 512 126.8z' fill='#EFF0F5' p-id='3771'></path><path d='M727.7 485H542.8V300.2h-61.6V485H296.3v61.7h184.9v184.8h61.6V546.7h184.9z' fill='#EFF0F5' p-id='3772'></path></svg>
            </div>
          </div>

          <div class="savePopupNum" ref="savePopupNum">
            确认
          </div>
        </div>

        <!--询问弹窗-->
        <div class="message_box selectNum_box" v-if="popupModule == 2">
          <div class="message" v-html="popupMessage"></div>

          <div class="popupBtnList">
            <div :ref="item.ref" v-for="(item,index) in popupBtnList" :key="index">
              {{item.text}}
            </div>
          </div>

        </div>

        <!--选择用餐时间-->
        <div class="selectPeriod" v-if="popupModule == 3" :style="{height: periodList.length > 2 ? '2.6rem' : '2.3rem'}">
          <div class="periodList" style="left: 0rem" v-if="periodList.length > 0">
            <div class="periodItem" :ref="item.ref" v-for="(item,index) in periodList" :key="index" :data="periodListIndex">
              <p>{{item.foodPeriodName}}</p>
              <p>{{item.startTime}}-{{item.endTime}}</p>
            </div>
          </div>

          <div class="pointer" v-if="periodList.length > 2">
            <div v-for="(item,index) in periodList.length - 1"  :key="index" :style="{width: pointerIndex == index ? '0.2rem': '0.1rem' }"></div>
          </div>


        </div>


        <!--短消息发送成功4-->
        <div class="popupBtnList"  v-if="popupModule == 4">
          <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index"> {{ item.text }}</div>
        </div>

        <div class="message_box result" style="text-align: center; font-size: 0.37rem; justify-content: center" v-html="popupMessage" v-if="popupModule == 5"></div>
        <div class="popupBtnList" v-if="popupModule == 5">
          <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index"> {{ item.text }}</div>
        </div>
      </el-dialog>

    </div>
  </div>
</template>
      
<script>
import { GetUserBalance, GetFoodPeriodList, GetDiningFoodList, ClearShopCart, CreateShopCart } from '@/api/index'

export default {
  name:'online_food_aihu',
  components: {},
  data() {
    return {
      //产品ID  规格ID  餐别ID
      filtrateList:['productId','productSpecId','periodId'],  // 相同餐品筛选条件，非餐品接口直接返回参数，需要在rightList自行赋值，赋值key需要与数组中相同
      noInfo: false,
      money:0,
      lazyError: require('@/assets/default_pic_meal.png'),
      firstShow: true,
      selectDiningRoom: null,

      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      // 左侧列表
      leftList: [],
      leftNums: 0,
      // 右侧餐品
      rightList: [],
      rightNums: 0,
      // 右侧下方按钮
      rightBtn:[
          {
            text: '重新选择',
            ref:'',
            show: false
          },{
            text: '选择用餐时间',
            ref:'',
            show: false
          },{
            text: '我要预约',
            ref:'',
            show: false
          }
      ],
      rightBtnIndex: 0,
      // 餐别列表
      periodList:[],
      periodListIndex: 0,
      periodListIndexOld: 0,
      pointerIndex: 0,

      dialogVisible: false,
      popupModule: 0,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],

      selectNums: 0,
      selectList:{}
    }
  },
  created() {
    this.selectDiningRoom =  JSON.parse(sessionStorage.getItem('selectDiningRoom'))
    //设置页面左上角标题
    this.$nextTick(() => {
        this.$store.dispatch('index/setMainTitle', this.selectDiningRoom.name)
    })

  },
  computed: {},
  watch: {
    '$store.state.app.timeInfo':{
      handler(newValue,oldValue) {
        if (!newValue) {
          return
        }
        // 非刷新的初次加载  每分钟执行
        if (oldValue) {
          // 判断为不同天执行
          if (newValue.date.day !== oldValue.date.day) {
            this.$nextTick(()=>{
              this.getData()
            })
          }
        } else
        // 初次加载且有时间数据
        if (newValue && !oldValue) {
          this.$nextTick(()=>{
            this.getData()
          })
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.$store.getters.loadingState) {
          return
        }
        if (this.dialogVisible) {
          if (this.popupModule == 1) {
            // 增加数值
            if (this.$store.getters.focus_dom == this.$refs.addNum || this.$store.getters.focus_dom == this.$refs.delectNum) {
              this.$store.dispatch('index/setFocusDom', this.$refs.savePopupNum)
            }
            return
          }
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if ((Math.abs(styleTop) +scrollPageTop)  * fontSize > this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight) {
                this.$refs.popupRef.style.top = styleTop - (scrollTop/fontSize - Math.abs(styleTop)) + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * ((scrollTop/fontSize - Math.abs(styleTop)) * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight)) + 'px'
                return
              }
              if (scrollTop > 0) {
                  this.$refs.popupRef.style.top = styleTop - scrollPageTop + 'rem'
                  scrollBar.style.top = barTop + (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))   + 'px'
              }
            }
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          // 餐品
          if (this.rightList[0].ref) {
            this.rightList[0].ref = ''
            this.rightBtn[this.rightBtnIndex].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
        //左侧
        else {
            if (this.leftNums < this.leftList.length -1) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums ++
              this.leftList[this.leftNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
              this.periodListIndex = 0
              this.periodListIndexOld = 0
              this.pointerIndex = 0
              this.getFoodPeriod()
            }
        }
      },
      up: () => {
        if (this.$store.getters.loadingState) {
          return
        }
        if (this.dialogVisible) {
          if (this.popupModule == 1) {
            // 若在保存数值上
            if (this.$store.getters.focus_dom == this.$refs.savePopupNum) {
              this.$store.dispatch('index/setFocusDom', this.$refs.addNum)
            }
            return
          }

          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if (scrollTop > 0) {
                if ((styleTop + scrollPageTop) > 0) {
                  this.$refs.popupRef.style.top = '0rem'
                  scrollBar.style.top = '0px'
                  return
                } else {
                  this.$refs.popupRef.style.top = styleTop + scrollPageTop + 'rem'
                  scrollBar.style.top = barTop - (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))  + 'px'
                }
              }
            }
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightList[this.rightNums] && !this.rightList[this.rightNums].ref) {
            this.rightBtn[this.rightBtnIndex].ref = ""
            this.rightList[0].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }

        }
        // 左侧
        else {
          if (this.leftNums > 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums --
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
            this.periodListIndex = 0
            this.periodListIndexOld = 0
            this.pointerIndex = 0
            this.getFoodPeriod()

          }
        }
      },
      left: () => {
        if (this.$store.getters.loadingState) {
          return
        }
        if (this.dialogVisible) {
          if (this.popupModule == 1) {
            // 若在减少数值上
            if (this.$store.getters.focus_dom == this.$refs.addNum) {
              this.$store.dispatch('index/setFocusDom', this.$refs.delectNum)
            }
            return
          } else
          // 选择餐别
          if (this.popupModule == 3) {
            if (this.periodListIndex > 0) {
              this.periodList[this.periodListIndex].ref = ""
              this.periodListIndex --
              this.periodList[this.periodListIndex].ref = "active"
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active);
                // 指示器
                const element = this.$refs.active[0]
                if (element) {
                  const windowWidth = element.parentNode.parentNode.clientWidth  // 可视区域高度
                  const elementWidth = Number(element.clientWidth)   // 当前元素高度
                  const elementOffsetLeft = element.offsetLeft  // 当前元素距离顶端的高度
                  const windowScrollLeft = element.parentNode.offsetLeft  // 页面下拉高度

                  if(((elementOffsetLeft + elementWidth + windowScrollLeft < 0) || elementOffsetLeft + elementWidth + windowScrollLeft > windowWidth) && element.parentNode.clientWidth > windowWidth) {
                    this.pointerIndex--
                  }
                }
              })
            }
            return
          }
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          // 选餐
          if (this.rightList.length > 0 && this.rightList[0].ref) {
            // 在第一张图
            if(this.rightNums == 0) {
              if (this.leftList.length > 0) {
                this.rightList[0].ref = ''
                this.leftList[this.leftNums].ref = 'active'
                this.nextNums = -1
              }
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            } else {
              this.$refs.swiperDom.prev()
            }
          }
          // 餐品下方按钮
          else {
            if (this.rightBtnIndex == 0) {
              if (this.leftList.length > 0) {
                this.rightBtn[this.rightBtnIndex].ref = ""
                this.leftList[this.leftNums].ref = 'active'
                this.nextNums = -1
              }

            } else {
                this.rightBtn[this.rightBtnIndex].ref = ""
                this.rightBtnIndex --
                this.rightBtn[this.rightBtnIndex].ref = "active"
            }
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })

          }
        }

      },
      right: () => {
        if (this.$store.getters.loadingState) {
          return
        }
        if (this.dialogVisible) {
          // 选择份数
          if (this.popupModule == 1) {
            // 若在减少数值上
            if (this.$store.getters.focus_dom == this.$refs.delectNum) {
              this.$store.dispatch('index/setFocusDom', this.$refs.addNum)
            }
            return
          } else
          // 选择餐别
          if (this.popupModule == 3) {

            if (this.periodList.length > 1) {
              if (this.periodListIndex < this.periodList.length - 1) {
                this.periodList[this.periodListIndex].ref = ""
                this.periodListIndex ++
                this.periodList[this.periodListIndex].ref = "active"
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active);
                  // 指示器
                  const element = this.$refs.active[0]
                  if (element) {
                    const windowWidth = element.parentNode.parentNode.clientWidth  // 可视区域高度
                    const elementWidth = Number(element.clientWidth)   // 当前元素高度
                    const elementOffsetLeft = element.offsetLeft  // 当前元素距离顶端的高度
                    const windowScrollLeft = element.parentNode.offsetLeft  // 页面下拉高度

                    if(((elementOffsetLeft + elementWidth + windowScrollLeft < 0) || elementOffsetLeft + elementWidth + windowScrollLeft > windowWidth) && element.parentNode.clientWidth > windowWidth) {
                      this.pointerIndex++
                    }
                  }
                })
              }
            }

            return
          }
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左侧
        if (this.nextNums == -1) {
            if (this.rightList.length > 0) {
              this.leftList[this.leftNums].ref = ""
              this.nextNums = this.leftNums
              this.rightList[0].ref = "active"
            } else {
              // 去下方按钮
              this.leftList[this.leftNums].ref = ""
              this.rightBtn[this.rightBtnIndex].ref = "active"
              this.nextNums = this.leftNums
            }
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
        }
        // 右侧
        else {
          // 选餐
          if (this.rightList.length > 0 && this.rightList[0].ref) {
            this.$refs.swiperDom.next()
          }
          // 餐品下方按钮
          else {
            if (this.rightBtnIndex < this.rightBtn.length - 1) {
              this.rightBtn[this.rightBtnIndex].ref = ""
              this.rightBtnIndex ++
              this.rightBtn[this.rightBtnIndex].ref = "active"
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          }

        }
      },
      enter: () => {
          if (this.$store.getters.loadingState) {
            return
          }
          if (this.dialogVisible) {
            // 餐品数量选择
            if (this.popupModule == 1) {
              // 增加数值
              if (this.$store.getters.focus_dom == this.$refs.addNum) {
                if (this.selectNums < Number(this.rightList[this.rightNums].totalStockNum)) {
                  this.selectNums ++
                }
              } else
              // 减少数值
              if (this.$store.getters.focus_dom == this.$refs.delectNum) {
                if (this.selectNums > 0) {
                  this.selectNums --
                }
              } else
              // 保存数值
              if (this.$store.getters.focus_dom == this.$refs.savePopupNum) {
                if (this.selectNums >= Number(this.rightList[this.rightNums].totalStockNum)) {
                  this.rightList[0].ref = 'active'
                  this.dialogVisible = false
                  return
                }
                // 选择份数
                this.rightList[this.rightNums].selectNum = this.selectNums
                // 当前选择餐别
                this.rightList[this.rightNums].periodId = this.periodList[this.periodListIndex].foodPeriodId

                let thisObj = this.rightList[this.rightNums]
                // 存在当前日期的点餐
                if (this.selectList[this.leftList[this.leftNums].date]) {

                  let yesObj = -1
                  for (let index = 0; index < this.selectList[this.leftList[this.leftNums].date].length; index++) {
                    let item = this.selectList[this.leftList[this.leftNums].date][index]
                    // 存在filtrateList中属性相同的对象 则替换修改当前数据
                    let same = true
                    this.filtrateList.map(filtra=>{
                      if (item[filtra] != thisObj[filtra]) {
                        same = false
                      }
                    })
                    if (same) {
                      yesObj = index
                    }
                  }

                  if (yesObj == -1) {
                    this.selectList[this.leftList[this.leftNums].date].push(thisObj)
                  } else {
                    if (this.selectNums > 0) {
                      this.selectList[this.leftList[this.leftNums].date][yesObj] = thisObj
                    } else {
                      if (this.selectList[this.leftList[this.leftNums].date].length > 1) {
                        this.selectList[this.leftList[this.leftNums].date].splice(yesObj, 1);
                      } else {
                        delete this.selectList[this.leftList[this.leftNums].date];
                      }
                    }
                  }
                }
                else {
                  this.selectList[this.leftList[this.leftNums].date] = []
                  this.selectList[this.leftList[this.leftNums].date].push(this.rightList[this.rightNums])
                }

                this.rightList[0].ref = 'active'
                this.dialogVisible = false


                // 计算选中  左侧总数
                this.leftList[this.leftNums].selectNum = 0
                if (this.selectList[this.leftList[this.leftNums].date]) {
                  this.selectList[this.leftList[this.leftNums].date].map(item=>{
                    this.leftList[this.leftNums].selectNum += item.selectNum
                  })
                }
                sessionStorage.setItem('foodSelectList',JSON.stringify(this.selectList))

                // 设置重新选择、我要预约是否高亮
                let objKey = Object.keys(this.selectList)
                if (objKey.length > 0) {
                  this.rightBtn[0].show = true
                  this.rightBtn[2].show = true
                } else {
                  this.rightBtn[0].show = false
                  this.rightBtn[2].show = false
                  sessionStorage.removeItem('foodSelectList')
                }
              }

              return
            } else
            // 餐别选择
            if (this.popupModule == 3) {
              this.periodList[this.periodListIndex].fuc(this.rightBtn[this.rightBtnIndex])
              return
            }
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnList[this.popupBtnNums].fuc(this.rightList[this.rightNums])
            this.dialogVisible = false
            return
          }
          // 右侧
          if (this.nextNums > -1) {
            // 菜品
            if (this.rightList.length > 0 && this.rightList[0].ref) {
              // 打开选择份数弹窗
              this.popupModule = 1
              this.selectNums = this.rightList[this.rightNums].selectNum
              this.rightList[0].ref = ""
              this.dialogVisible = true
            }
            else {
              if (this.rightBtn[this.rightBtnIndex].show) {
                // 重新选择
                if (this.rightBtnIndex == 0) {
                  this.rightBtn[this.rightBtnIndex].ref = ""
                  this.popupModule = 2
                  this.popupMessage = "您确认要清除已选择的餐品，重新选择吗?"
                  this.popupBtnList = [{
                    ref:'active',
                    text: '关闭',
                    fuc: ()=>{
                      this.dialogVisible = false
                      this.rightBtn[this.rightBtnIndex].ref = "active"
                    }
                  },{
                    ref:'',
                    text: '确认',
                    fuc:()=>{
                      this.selectList = null
                      this.dialogVisible = false
                      this.rightBtn[this.rightBtnIndex].ref = "active"
                      sessionStorage.removeItem('foodSelectList')
                      this.leftList.map(item=>{
                        item.selectNum = 0
                      })
                      this.rightBtn[0].show = false
                      this.rightBtn[2].show = false
                      this.getFoodList()
                    }
                  }]
                  this.dialogVisible = true
                } else
                // 选择用餐时间
                if (this.rightBtnIndex == 1) {
                  this.selectFoodPeriod()
                } else
                // 我要预约
                if (this.rightBtnIndex == 2) {
                  this.createCart()
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', document.body);
                  })
                }
              }
            }
            return
            if (this.$refs.popupBox) {
              let scrollDom = this.$refs.popupBox.$el.querySelector('.scroll');
              if (scrollDom) {
                scrollDom.remove()
              }
            }
            // 取消订单二次确认弹窗
            if (this.nextNums > -1 && this.rightList[this.rightNums] && this.rightList[this.rightNums].ref) {

              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                },
                {
                  text: '取消订单',
                  ref: '',
                  fuc: null,
                },
              ]
              this.dialogVisible = true
              this.popupModule = 2
              this.rightList[this.rightNums].ref = ''
              this.popupBtnList[this.popupBtnNums].ref = 'active'
            }
            // 发送订单二次确认弹窗
            else {


              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                },
                {
                  text: '确认',
                  ref: '',
                  fuc: this.sendOrder,
                },
              ]
              this.dialogVisible = true
              this.popupModule = 1
              this.leftList[this.leftNums].ref = ''
              this.popupBtnList[this.popupBtnNums].ref = 'active'
            }
          }
      },
      esc: () => {
        if (this.dialogVisible) {
          if (this.popupModule == 1) {
            this.rightList[0].ref = "active"
            this.dialogVisible = false
          } else
          if (this.popupModule == 3) {

            this.periodList[this.periodListIndex].ref = ""
            this.rightBtn[this.rightBtnIndex].ref = "active"
            this.dialogVisible = false


            this.periodListIndex = this.periodListIndexOld
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active[0]);
            })
          }
          return
        }
        if (this.nextNums == -1) {
          if (this.dialogVisible) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.dialogVisible = false
            return
          }
        } else
        if (this.nextNums > -1) {
          if (this.dialogVisible) {
            if (this.popupModule == 2) {
              this.rightList[this.rightNums].ref = 'active'
            }
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.dialogVisible = false
            return
          }
        }
        this.$store.dispatch('index/setFocusDom', document.body);
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      if (this.popupModule == 1  || this.popupModule == 2 || this.popupModule == 3) {
        this.$nextTick(() => {
          if (this.$refs.active[0]) {
            this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
          } else {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          }
          document.getElementById('focus_border').style.borderColor = '#fff'

          setTimeout(()=>{
            this.popupBtnNums = 0
            this.popupModule = 1
          },300)
        })
        return
      }
      if (this.nextNums == -1) {
        this.popupBtnList[this.popupBtnNums].ref = ''
        this.leftList[this.leftNums].ref = 'active'
      } else if (this.nextNums > -1) {
        this.popupBtnList[this.popupBtnNums].ref = ''
        if (this.rightList[this.rightNums] && this.popupModule == 2) {
          this.rightList[this.rightNums].ref = 'active'
        }
      }
      this.$nextTick(() => {
        if (this.$refs.active[0]) {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        }

        document.getElementById('focus_border').style.borderColor = '#fff'
        setTimeout(()=>{
          this.popupBtnNums = 0
          this.popupModule = 1
        },300)
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        if (this.popupModule == 1) {
          this.$store.dispatch('index/setFocusDom', this.$refs.addNum)
        } else {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        }
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },
    getData() {
      this.leftList = []
      for (let i = 1; i< 8; i++) {
        let obj = {
          ref:'',
          date: new Date(this.$store.state.app.timeInfo.timeDate + i*24*60*60*1000).format('yyyy-MM-dd'),
          week: '星期' + new Date(this.$store.state.app.timeInfo.timeDate + i*24*60*60*1000).format('W'),
          selectNum: 0
        }
        this.leftList.push(obj)
      }

      // 删除选中中过期的时间
      if (sessionStorage.getItem('foodSelectList')) {

      }

      if (sessionStorage.getItem('foodSelectList')) {
        // 删除选中中的过期时间
        this.selectList = JSON.parse(sessionStorage.getItem('foodSelectList'))
        let needTure = new Date((this.$store.state.app.timeInfo.timeDate+1*24*60*60*1000)).format('yyyy-MM-dd')
        Object.keys(this.selectList).forEach(key=>{
          if (new Date(key).getTime() < new Date(needTure).getTime()) {
            delete this.selectList[key];
            sessionStorage.setItem('foodSelectList',JSON.stringify(this.selectList))
          }
        })
        // 将选中数据 push到数组中
        this.leftList.map((item,index)=>{
          let data = this.selectList[item.date]
          if (data) {
            data.map(items=>{
              this.leftList[index].selectNum += items.selectNum
            })
          }
        })

        // 设置重新选择、我要预约是否高亮
        let objKey = Object.keys(this.selectList)
        if (objKey.length > 0) {
          this.rightBtn[0].show = true
          this.rightBtn[2].show = true
        } else {
          this.rightBtn[0].show = false
          this.rightBtn[2].show = false
          sessionStorage.removeItem('foodSelectList')
        }

      }

      if (sessionStorage.getItem('foodSendOrder')) {
        this.nextNums = 0
        this.rightBtnIndex = 2
        this.rightBtn[this.rightBtnIndex].ref = "active"
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      } else {
        this.leftList[this.leftNums].ref = "active"
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      }
      this.getUserBalance()
      this.getFoodPeriod()

    },
    // 获取余额
    getUserBalance() {
      GetUserBalance({
        cardId: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].card_id_md5,
        shopId: this.selectDiningRoom.shopId
      })
      .then(res=>{
        if (res.code == 200) {
          this.money = res.data.accountMoney
          sessionStorage.setItem('accountMoney',this.money)
        }
      })
    },
    // 获取餐别
    getFoodPeriod() {
      this.noInfo = false
      this.periodList = []
      this.periodListIndex = 0
      this.rightBtn[1].show = false

      this.rightList = []
      this.rightNums = 0
      this.$store.dispatch('app/setLoadingState',true)
      GetFoodPeriodList({
        cardId: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].card_id_md5,
        shopId: this.selectDiningRoom.shopId,
        mealDate: this.leftList[this.leftNums].date
      })
      .then(res=>{
        if (res.code == 200) {
          this.periodList = res.data

          if (res.data.length > 0) {
            this.rightBtn[1].show = true
            this.getFoodList()
          } else {
            this.rightBtn[1].show = false
          }
        } else {
          this.$store.dispatch('app/setLoadingState',false)
        }
      })
      .catch(err=>{
        this.noInfo = true
        this.$store.dispatch('app/setLoadingState',false)
      })
    },
    // 获取餐品
    getFoodList() {
      if (!this.leftList[this.leftNums]) {
        return
      }
      this.$store.dispatch('app/setLoadingState',true)
      this.rightList= []
      GetDiningFoodList({
        cardId: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].card_id_md5,
        shopId: this.selectDiningRoom.shopId,
        foodPeriodId: this.periodList[this.periodListIndex].foodPeriodId,
        mealDate: this.leftList[this.leftNums].date
      })
      .then(res=>{
        this.$store.dispatch('app/setLoadingState',false)
        if (res.code == 200) {
          this.noInfo = false

          this.rightList = JSON.parse(JSON.stringify(res.data))

          this.rightList.map((item,index)=>{
            item.ref = ''
            item.selectNum = 0
            item.periodId = this.periodList[this.periodListIndex].foodPeriodId
          })


          if (sessionStorage.getItem('foodSelectList')) {
            let data = this.selectList[this.leftList[this.leftNums].date]
            data.map(items=>{
              this.rightList.map((item,index)=>{
                // 存在filtrateList中属性相同的对象 给当前这个对象selectNum 赋值，打上已选标记
                let same = true
                this.filtrateList.map(filtra=>{
                  if (item[filtra] != items[filtra]) {
                    same = false
                  }
                })
                if (same) {
                  this.rightList[index].selectNum = items.selectNum
                }
              })
            })
          }


          if (this.firstShow) {
            if (!sessionStorage.getItem('foodSendOrder')) {
              this.leftList[this.leftNums].ref = ""
              this.rightList[0].ref = 'active'
              this.nextNums = this.leftNums
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            } else {
              sessionStorage.removeItem('foodSendOrder')
            }
            this.firstShow = false
          } else {
            // this.rightBtnIndex = 0
          }


        } else {
          this.noInfo = true
        }
      })
      .catch(()=>{
        this.firstShow = false
        this.noInfo = true
        this.$store.dispatch('app/setLoadingState',false)



      })
    },
    // 选择餐别
    selectFoodPeriod() {
      this.periodListIndexOld = this.periodListIndex
      this.pointerIndex = this.periodListIndex - 1 > 0 ? this.periodListIndex - 1 : 0
      this.popupModule = 3
      this.periodList.map((item,index)=>{
        item.ref = ""
        item.fuc =  () => {
          this.periodListIndexOld = this.periodListIndex
          this.dialogVisible = false
          this.periodList[this.periodListIndex].ref = ""
          this.rightBtn[this.rightBtnIndex].ref = "active"
          this.getFoodList()
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active[0]);
          })
        }
      })

      this.rightBtn[this.rightBtnIndex].ref = ""
      this.periodList[this.periodListIndex].ref = "active"

      setTimeout(() => {
        this.dialogVisible = true
      }, 0)
    },
    // 我要预约
    createCart() {
      // 先清空购物车
      this.$store.dispatch('app/setLoadingState',true)
      ClearShopCart({
        cardId: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].card_id_md5,
        shopId: this.selectDiningRoom.shopId,
      }).then(res=>{
        if (res.code == 200) {
          let sendCart = []
          Object.keys(this.selectList).forEach((key)=>{
            this.selectList[key].map(item=>{
              item.dinnnerDate = key
              item.shopId = this.selectDiningRoom.shopId
              item.itemNum = item.selectNum.toString()
              item.productSpedId = item.productSpecId
              item.operationType = 1
              sendCart.push(item)
            })
          })
          if (sendCart.length < 1) {
            return
          }
          CreateShopCart({
            cardId: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].card_id_md5,
            cart: JSON.stringify(sendCart),
          }).then(res=>{
            this.$store.dispatch('app/setLoadingState',false)
            if (res.code == 200) {
              sessionStorage.setItem('foodSendOrder',true)
              this.$router.push({
                path:'/online_food_aihu_confirm'
              })
            }
          }).catch(error=>{
              this.$store.dispatch('app/setLoadingState', false)
              this.popupModule = 5
              this.rightBtn[this.rightBtnIndex].ref = ""
              this.popupBtnList = [
                {
                  text: '确认',
                  ref: 'active',
                  fuc: () => {
                    this.dialogVisible = false
                    this.rightBtn[this.rightBtnIndex].ref = "active"
                    this.$nextTick(() => {
                      this.$store.dispatch('index/setFocusDom', this.$refs.active)
                    })
                  },
                },
              ]
              this.popupMessage = '<br/>取消失败!<br>'
              if (error.response && error.response.data && error.response.data.msg) {
                this.popupMessage += error.response.data.msg
              }
              this.popupBtnNums = 0
              this.dialogVisible = true
          })

        }
      }).catch(err=>{
        this.$store.dispatch('app/setLoadingState',false)
      })
    },
    swiperChange(index) {
      this.rightNums = index
    }

  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = null
  },
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
  .online_food_aihu {
    //width: 16.73rem;
    width: 18rem;
    .balance {
      height: 2.24rem;
      line-height: 2.24rem;
      position: absolute;
      right: 1.2rem;
      top: 0;
      display: flex;
      text-align: right;
      font-size: 0.46rem;
      font-weight: bold;
      color: #f1f1f1;
      background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.65));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .container {
      height: 7rem;
      //overflow: hidden;
      display: flex;
      flex-direction: row;
      .noData {
        div {
          font-size: 0.5rem;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -200%);
          letter-spacing: 0.05rem;
          text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
        }
      }
      .left {
        width: 3.46rem;
        height: 7rem;
        margin-left: 0;
        .leftContent {
          width: 100%;
          height: 100%;
          position: relative;
          display: inline-block;
          .leftList {
            width: 100%;
            position: absolute;
            transition: all 0.2s;
            .leftItem {
              width: 3.2rem;
              height: 0.8rem;
              position: relative;
              margin-bottom: 0.23rem;
              background-size: 100% 100% !important;
              background: #262954;
              border-radius: 0.24rem;
              //overflow: hidden;
              font-weight: bold;
              text-align: center;
              color: #7980b3;
              background-size: 100% 100%;
              transition: all 0.35s;
              font-size: 0.3rem;
              display: flex;
              align-items: center;
              justify-content: center;
              div:nth-child(2) {
                font-size: 0.24rem;
                margin-left: 0.1rem;
              }
              .selectNum {
                position: absolute;
                top: 0;
                right: -1rem;
                width: 0.85rem;
                height: 0.8rem;
                line-height: 0.8rem;
                background: rgb(38, 41, 84);
                border-radius: 0.24rem;
                font-size: 0.26rem;
                span:nth-child(2) {
                  position: absolute;
                  width: 0.35rem;
                  height: 0.35rem;
                  border-radius: 50%;
                  background: #F54D22;
                  top: -0.06rem;
                  right: -0.06rem;
                  color: #FDF0EC;
                  font-size: 0.22rem;
                  text-align: center;
                  line-height: 0.35rem;
                  letter-spacing: 0;
                }
              }

            }
          }
        }
        .no_content {
          position: absolute;
          line-height: 7rem;
          left: 6rem;
          // width: 4rem;
          // height: 7rem;
          text-align: center;
          font-size: 0.4rem;
          letter-spacing: 0.05rem;
          line-height: 6rem;
          color: #b5c0ff;
        }
      }
      .right {
        overflow: hidden;
        width: 13.5rem;
        height: 7rem;
        margin-left: 1rem;
        position: relative;
        .periodInfo {
          width: 11.6rem;
          height: 0.45rem;
          line-height: 0.45rem;
          font-size: 0.34rem;
          font-weight: bold;
        }
        .rightContent {
          width: 11.2rem;
          height: 5.4rem;
          margin-top: 0.2rem;
          position: relative !important;
          //overflow: hidden;
          display: inline-block;
          border-radius: 0.4rem;
          .list {
            // margin-right: 0.22rem;
            position: relative;
            transition: all 0.3s;
            .rightList {
              height: 3.4rem;
              padding: 0.25rem 0.35rem;
              margin-bottom: 0.28rem;
              margin-right: 0.25rem;
              background: #262954;
              border-radius: 0.24rem;
              font-size: 0.28rem;
              font-weight: bold;
            }
            .rightListItem {
              height: 2.85rem;
              padding: 0.25rem 0.35rem;
              padding-right: 0.1rem !important;
              margin-bottom: 0.28rem;
              margin-right: 0.25rem;
              background: #262954;
              border-radius: 0.24rem;
              font-size: 0.28rem;
              font-weight: bold;
              .status {
                color: #e77302;
                margin-bottom: 0.1rem;
                letter-spacing: 0.03rem;
              }
              .title {
                width: 1.25rem !important;
                color: #b6c0fd;
              }
            }
          }
        }
        .rightBtn {
          display: flex;
          justify-content: center;
          position: absolute;
          bottom: 0;
          .btnItem {
            width:3.6rem;
            height:0.75rem;
            border-radius:0.25rem;
            background: rgb(84, 149, 210);
            font-size:0.38rem;
            letter-spacing: 0.1rem;
            text-align: center;
            color:#f1f1f1;
            line-height: 0.75rem;
            font-weight:bold;
            margin-right: 0.2rem;
            transition: all 0.3s;
          }
          .btnItem:last-child {
            margin-right: 0;
          }

        }
      }
      .no_info {
        width: 10.8rem;
        text-align: center;
        font-size: 0.4rem;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        line-height: 5.4rem;
        color: #b5c0ff;
      }
    }
  }
</style>
<style lang="less">
  .online_food_aihu {
    .right {
      .el-carousel {
        height: 100%;
        overflow: initial !important;


        .el-carousel__item {
          width: 10.8rem;

          //display: flex;
          //align-items: center;
          margin-left: 0.24rem;
          .swiperInfo {
            display: flex;
            align-items: center;
            background-image: linear-gradient(to bottom, #ECF1F8, #CED4E0);
            border-radius: 0.4rem;
            padding: 0.3rem 0;
            //margin:0 0.3rem;
            //padding: 0.3rem 0;
            .foodSelectNum {
              position: absolute;
              left: 0;
              top: 0;
              width: 1.8rem;
              height: 0.7rem;
              color: #fff;
              text-align: center;
              line-height: 0.7rem;
              font-size: 0.3rem;
              font-weight: bold;
              background-color: #F54D22;
              border-top-left-radius: 0.4rem;

            }
            .img {
              width: 5.4rem;
              height: 4.4rem;
              border-radius: 0.3rem;
              overflow: hidden;
              margin-left: 0.36rem;
              img {
                display: block;
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            .foodDetails {
              width: 4.4rem;
              height: 4.4rem;
              margin-left: 0.3rem;
              .title {
                width: 1.8rem;
                text-align: center;
                border-radius: 0.1rem;
                color: #F4FFFE;
                background: #5493DA;
                font-size: 0.3rem;
                letter-spacing: 0.03rem;
                padding: 0.05rem 0;
                margin-bottom: 0.05rem;
              }
              .foodValue {
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                color: #5A5B5D;
                font-size: 0.28rem;
                font-weight: 600;
              }
              .foodValue:nth-child(2) {
                white-space: normal !important;
                height: 1.5rem;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box !important;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 4;
                word-break: break-all;
                margin-bottom: 0.16rem;
              }
              .foodTop {
                display: flex;
                align-items: center;
                justify-content: space-between;
                .foodName {
                  max-width: 3.8rem;
                  font-weight: bold;
                  font-size: 0.46rem;
                  margin-bottom: 0.1rem;
                  max-height: 1.3rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  color: #555658
                }
                .price {
                  color: #F64D22;
                  font-weight: bold;
                  font-size: 0.46rem;
                }
              }
            }
          }
        }
      }
    }
    .el-dialog {
      width: 9.4rem;
      height: 8rem;
      margin-top: 0 !important;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 0.26rem;
      // background: repeating-linear-gradient(to right, #c98693, #a95361);
      background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

      .el-dialog__header {
        height: 10%;
        display: flex;
        align-items: center;
        padding-top: 35px;
        .el-dialog__title {
          display: block;
          line-height: normal !important;
          height: 100%;
          font-size: 0.4rem;
          color: #fff;
          background-image: linear-gradient(
            to bottom,
            rgba(255, 255, 255, 1),
            rgba(255, 255, 255, 0.65)
          );
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          font-weight: bold;
          width: 100%;
          text-align: center;
        }
      }
      .el-dialog__body {
        width: 92%;
        height: 80%;
        position: absolute;
        bottom: 4%;
        left: 4%;
        border-radius: 0.2rem;
        background: #fff;
        padding: 0 !important;
        .message_box {
          padding: 0.4rem;
          color: #464646;
          font-size: 0.32rem;
          font-weight: bold;
          height: 60%;
          display: flex;
          align-items: flex-start;
          flex-direction: column;
          .title,
          .detail,
          .price {
            display: flex;
            font-size: 0.35rem;
            font-weight: bold;
            padding-bottom: 0.22rem !important;
            letter-spacing: 0.03rem;
          }
          .title {
            span {
              color: #f64e23;
              margin-left: 0.2rem;
            }
          }
          .price {
            span {
              color: #f64e23;
            }
          }
        }
        .cancel,.sendCall {
          width: calc(100% - 0.8rem);
          height: 4.26rem;
          //top: 0.4rem;
          //left: 0.4rem;
          padding: 0;
          margin-top: 0.4rem;
          margin-left: 0.4rem;
          //position: relative;
          overflow: hidden;
          position: relative;
          .message_content {
            width: 100%;
            position: absolute;
            transition: all 0.3s;
            .message_item {
              display: flex;
              flex-direction: row;
              font-size: 0.4rem !important;
              margin-bottom: 0.2rem;
              letter-spacing: 0.03rem;
              .type {
                width: 2.2rem;
                font-size: 0.4rem !important;
              }
            }
            .orderItem {
              letter-spacing: 0.03rem;
              font-size: 0.4rem !important;
              div {
                margin-bottom: 0.2rem;
              }
            }
          }
          .scroll {
            top: 1.8rem !important;
            left: auto !important;
            right: 0.8rem !important;
          }
        }
        .sendCall {
          .message_item {
            flex-direction: column !important;
          }
        }
        .result {
          display: flex;
          justify-content: center !important;
          align-items: center !important;
        }
        .info {
          display: flex;
          justify-content: center !important;
          align-items: center !important;
          margin-top: -0.4rem;
          // background: url(http://192.168.6.212/ptyanglaotv/assets/zc_tan_new.png) no-repeat !important;
          .title {
            display: flex;
            flex-direction: column;
            align-items: center !important;
            text-align: center !important;

            // margin-top: 0.9rem;
            span {
              font-size: 0.5rem;
              color: #464646 !important;
              letter-spacing: 0.14rem;
            }
          }
          .phone {
            width: 7rem;
            text-align: center;
            position: absolute;
            top: 2.3rem;
            font-size: 0.3rem;
            color: #464646;
          }
          .tip {
            text-align: center;
            color: #3288dc;
            font-size: 0.4rem;
            margin-top: 1.2rem;
          }
        }
        .popupBtnList {
          display: flex;
          flex-direction: row;
          justify-content: space-evenly;
          margin-top: 0.25rem;
          div {
            width: 3.84rem;
            height: 1rem;
            line-height: 1rem;
            text-align: center;
            border-radius: 0.3rem;
            color: #fff;
            font-size: 0.45rem;
            font-weight: bold;
            letter-spacing: 0.05rem;
            text-indent: 0.05rem;
            // background: repeating-linear-gradient(to right, #c98693, #a95361);
            background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
          }
        }
      }
    }
    .popup_info_box {
      .el-dialog {
        width: 8.6rem;
        height: 5.74rem;
        background: url("../../assets/zc_tan_new.png") no-repeat;
        background-size: 100% 100%;
        .el-dialog__header {
          display: none;
        }
        .el-dialog__body {
          background: transparent;
          .info {
            .title {
              span {
                color: #E7F1FA !important;
              }
            }
            .phone {
              top: 1.2rem;
              color: yellow;
            }
            .tip {
              margin-top: 1.1rem;
            }
          }
        }
      }
    }
    .selectNum_info_box {
      .el-dialog {
        height: 5.5rem;
        .el-dialog__header {
          height: 1.1rem;
          padding: 0 !important;
          .el-dialog__title {
            font-size: 0.48rem;
            line-height: 1.1rem !important;
          }
        }

        .el-dialog__body {
          width: 100%;
          left: 0;
          bottom: 0;
          border-top-left-radius: 0;
          border-top-right-radius: 0;
          .selectNum_box {
            padding: 0.3rem;
            .tips {
              color: #F54D22;
              font-size: 0.34rem;
              margin: 0rem auto;
              width: 7.5rem;
              font-weight: bold;
            }
            .popupCenter {
              display: flex;
              justify-content: space-between;
              font-size: 0.45rem;
              width: 7.5rem;
              align-items: center;
              margin: 0 auto;
              margin-top: 0.6rem;
              .delectNum {
                width: 1.8rem;
                background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
                font-size: 0.3rem;
                height: 1rem;
                line-height: 1.5rem;
                float: left;
                text-align: center;
                position: relative;
                border-radius: 0.3rem;
              }
              .nums {
                width: 2.7rem;
                border: 0.01rem dashed #333;
                line-height: 1rem !important;
                color: #333;
                text-align: center;

              }
              .addNum {
                width: 1.8rem;
                background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
                font-size: 0.3rem;
                height: 1rem;
                float: left;
                text-align: center;
                line-height: 1.5rem;
                position: relative;
                border-radius: 0.3rem;

              }
            }
            .savePopupNum {
              width: 2.8rem;
              background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
              height: 1rem;
              margin: 0.5rem auto;
              color: #fff;
              text-align: center;
              line-height: 1rem;
              border-radius: 0.3rem;
              font-size: 0.38rem;
            }
          }
        }
      }
    }
    .message_box_info {
      .message_box {
        align-items: center !important;
        .message {
          width: 7.3rem;
          min-height: 2.26rem !important;
          text-align: center;
          font-size: 0.44rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          color: #5472B0;

        }
        .popupBtnList {
          width: 100%;
        }
      }

    }
    .selectPeriod_info_box {
      .el-dialog {
        height: 4.2rem;
        .selectPeriod {
          width: 8.1rem;
          height: 2rem;
          overflow: hidden;
          position: relative;
          margin: 0.35rem auto;
          border-radius: 0.3rem;
          .popupTitle {
            height: 0.4rem;
            line-height: 0.4rem;
            font-size: 0.18rem;
            font-weight: bold;
            margin-bottom: 0.06rem;
          }
          .periodList {
            width: max-content;
            position: absolute;
            left: 0;
            transition: all 0.3s;
            .periodItem {
              float: left;
              display: flex;
              flex-direction: column;
              align-items: center;

              width: 3.9rem;
              height: 2.3rem;
              background: #D3D4D9;
              border-radius: 0.3rem;
              position: relative;
              margin-right: 0.3rem;
              display: flex;
              align-items: center;
              justify-content: center;

              p {
                font-size: 0.35rem;
                width: 100%;
                color: #464646;
                text-align: center;
                font-weight: bold;
              }


            }
          }
          .pointer {
            width: 8.1rem;
            position: absolute;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            div {
              width: 0.1rem;
              height: 0.1rem;
              transition: all 0.3s;
              border-radius: 0.05rem;
              background: #888;
              display: inline-block;
              margin-right: 0.08rem;
            }
          }
        }
      }
    }

  }
</style>
      