<template>
  <div class="online_food_index">
    <div class="container">
      <div class="left">
        <div class="leftContent scrollParent"  v-if="this.leftList.length > 0">
          <div class="leftList" :style="{ top: '0rem' }">
            <div class="leftItem" :ref="item.canteen.ref" v-for="(item, index) in leftList" :key="index">
              <div class="item_user">
                <div class="pic" v-lazy-container="{ selector: 'img' }">
                  <img :data-src="item.canteen.cover_img" :data-error="lazyError" :key="index"  alt="" />
                </div>
                <div class="contentInfo">
                  <div class="title">
                    {{ item.canteen.name}}
                  </div>
                  <div class="itemContent">
                    <div>
                      <span>地址: </span>
                      <span>{{item.canteen.short_addr}}</span>
                    </div>
                    <div>
                      <span>电话: </span>
                      <span>{{item.canteen.telephone}}</span>
                    </div>
                    <div>{{item.canteen.remark ? item.canteen.remark : '提供早餐、午餐、晚餐'}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_content" v-else>暂无可下单餐厅</div>
      </div>

      <div class="right">
        <div class="rightContent scrollParent" ref="rightContent" v-if="this.rightList.length > 0">
          <div class="list" :style="{ top: '0rem' }">
            <div class="rightListItem" :ref="item.ref" v-for="(item, index) in rightList" :key="index">
              <div class="status" :style="{color: statusList[item.status].color}">
                {{statusList[item.status].text}}
              </div>
              <div class="itemContent">
                  <div class="title">订单金额:</div>
                  <div class="value">{{ (Number(item.paid_price) / 100).toFixed(2) }}元</div>
              </div>
              <div class="itemContent foodName">
                <div class="title">下单餐厅:</div>
                <div class="value">{{ item.canteen_name }}
                </div>
              </div>
              <div class="itemContent orderTime">
                  <div class="title">申请时间:</div>
                  <div class="value">{{ new Date(item.created_at * 1000).format('yyyy/MM/dd hh:mm:ss') }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_info" v-else>暂无近期订单</div>



      </div>

<!--  二次确认弹窗-->
      <el-dialog
        :class="popupModule == 4 ? 'popup_info_box' : 'popup_box'"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        title="订单详情"
        ref="popupBox"
      >
        <!--取消预约-->
        <div class="message_box cancel scrollParent" v-if="popupModule == 2">
          <div class="message_content" ref="popupRef" :style="{top: '0rem'}">
            <div class="message_item">
              <div class="type">申请时间:</div>
              <div class="value">{{ new Date(this.rightList[this.rightNums].created_at * 1000).format('yyyy/MM/dd hh:mm:ss') }}</div>
            </div>
            <div class="message_item">
              <div class="type">下单餐厅:</div>
              <div class="value">{{ this.rightList[this.rightNums].canteen_name }}</div>
            </div>
            <div class="message_item">
              <div class="type">订单金额:</div>
              <div class="value">{{ (Number(this.rightList[this.rightNums].paid_price) / 100).toFixed(2) }}元</div>
            </div>

            <div class="message_item">
              <div class="type">订单详情:</div>
              <div class="value"></div>
            </div>
            <div class="orderInfo">
              <div class="orderItem" v-for="(item,index) in this.rightList[this.rightNums].order_detail_aihuwang" :key="index">
                <div>
                  <span>{{item.date}} (星期{{new Date(item.date).format('W')}})</span>
                </div>
                <div style="padding-left: 0.2rem">
                  <span>{{item.plan_kind_name}}:{{item.describes}} </span>
                  <span> ￥{{(Number(item.price) / 100).toFixed(2)}} </span>
                  <span> ×{{item.num}}份</span>
                </div>
              </div>

            </div>

          </div>
        </div>

        <div class="message_box result" style="text-align: center; font-size: 0.37rem; justify-content: center" v-html="popupMessage" v-if="popupModule == 3"></div>

        <div class="popupBtnList">
          <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index"> {{ item.text }}</div>
        </div>
      </el-dialog>

    </div>
    <div class="address">
      <template>
        <div class="name">餐厅列表</div>
      </template>
    </div>

    <div class="done"><div class="item">服务记录</div></div>
  </div>
</template>
      
<script>
import { GetDiningRoomList, cancelPayOrder, GetStatus } from '@/api/index'

export default {
  name:'online_food_index',
  components: {},
  data() {
    return {
      statusList: null,
      lazyError: require('@/assets/font_default.jpg'),
      leftList: [],
      rightList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      order_detail: [],

      popupTimer: null,
      firstShow: true,
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
    }
  },
  created() {
    //设置页面左上角标题
    this.$nextTick(() => {
      this.$store.dispatch('index/setMainTitle', "助餐")
    })
  },
  computed: {
    getOrderStatus() {
      return ()=>{

      }
    }
  },
  watch: {},
  mounted() {
    if (sessionStorage.getItem('onlineFoodIndexFocus')) {
      this.leftNums = Number(sessionStorage.getItem('onlineFoodIndexFocus'))
      sessionStorage.removeItem('onlineFoodIndexFocus')
    }
    sessionStorage.removeItem('selectDiningRoom')
    if (localStorage.getItem('statusList')) {
      this.statusList = JSON.parse(localStorage.getItem('statusList'))
      this.getData()
    } else {
      this.getStatus()
    }
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if ((Math.abs(styleTop) +scrollPageTop)  * fontSize > this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight) {
                this.$refs.popupRef.style.top = styleTop - (scrollTop/fontSize - Math.abs(styleTop)) + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * ((scrollTop/fontSize - Math.abs(styleTop)) * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight)) + 'px'
                return
              }
              if (scrollTop > 0) {
                  this.$refs.popupRef.style.top = styleTop - scrollPageTop + 'rem'
                  scrollBar.style.top = barTop + (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))   + 'px'
              }
            }
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1 && this.rightList[this.rightNums] && this.rightList[this.rightNums].ref) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums++
            this.rightList[this.rightNums].ref = 'active'

            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
        //左侧
        else {
            if (this.leftNums < this.leftList.length -1) {
              this.leftList[this.leftNums].canteen.ref = ''
              this.leftNums ++
              this.leftList[this.leftNums].canteen.ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
        }
      },
      up: () => {
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if (scrollTop > 0) {
                if ((styleTop + scrollPageTop) > 0) {
                  this.$refs.popupRef.style.top = '0rem'
                  scrollBar.style.top = '0px'
                  return
                } else {
                  this.$refs.popupRef.style.top = styleTop + scrollPageTop + 'rem'
                  scrollBar.style.top = barTop - (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))  + 'px'
                }
              }
            }
          }
          return
        }

        // 右侧
        if (this.nextNums > -1) {
          if (this.rightList.length < 1 || !this.rightList[this.rightNums].ref) {
            if (this.rightList.length > 0) {
              this.rightList[this.rightNums].ref = "active"
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (this.rightNums > 0 && this.rightList.length - 1 >= 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums--
            this.rightList[this.rightNums].ref = 'active'
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 左侧
        else {
          if (this.leftNums > 0) {
            this.leftList[this.leftNums].canteen.ref = ''
            this.leftNums --
            this.leftList[this.leftNums].canteen.ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          if (this.leftList.length > 0) {
            this.rightList[this.rightNums].ref = ''
            this.leftList[this.leftNums].canteen.ref = 'active'
            this.nextNums = -1
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左侧
        if (this.nextNums == -1) {
            if (this.rightList.length > 0) {
              this.leftList[this.leftNums].canteen.ref = ""
              this.nextNums = this.leftNums
              this.rightList[this.rightNums].ref = "active"
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
        }
      },
      enter: () => {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            // 取消订单关闭时
            if (this.popupModule == 2) {
              this.rightList[this.rightNums].ref = 'active'
            }
            this.popupBtnList[this.popupBtnNums].fuc(this.rightList[this.rightNums])
            this.dialogVisible = false
            return
          }
          // 左侧
          if (this.nextNums == -1) {
            sessionStorage.setItem('selectDiningRoom',JSON.stringify(this.leftList[this.leftNums].canteen))
            sessionStorage.setItem('onlineFoodIndexFocus',this.leftNums)
            this.$router.push({
              path: './online_food_aihu'
            })
          }
          else {
            clearTimeout(this.popupTimer)
            this.popupTimer = null
            if (this.$refs.popupBox) {
              let scrollDom = this.$refs.popupBox.$el.querySelector('.scroll');
              if (scrollDom) {
                scrollDom.remove()
              }
            }
            // 取消订单二次确认弹窗
            if (this.nextNums > -1 && this.rightList[this.rightNums] && this.rightList[this.rightNums].ref) {
              this.popupModule = 2
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                }
              ]

              // 正在进行中、待支付
              if ([2,5].includes(this.rightList[this.rightNums].status)) {
                this.popupBtnList.push({
                  text: '取消订单',
                  ref: '',
                  fuc: this.cancelOrder,
                })
              }

              this.dialogVisible = true
              this.rightList[this.rightNums].ref = ''
              this.popupBtnList[this.popupBtnNums].ref = 'active'
            }
          }
      },
      esc: () => {
        if (this.nextNums == -1) {
          if (this.dialogVisible) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.leftList[this.leftNums].canteen.ref = 'active'
            this.dialogVisible = false
            return
          }
        } else
        if (this.nextNums > -1) {
          if (this.dialogVisible) {
            if (this.popupModule == 2) {
              this.rightList[this.rightNums].ref = 'active'
            }
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.dialogVisible = false
            return
          }
        }
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    getStatus() {
      GetStatus().then(res=>{
        if (res.code == 200) {
          this.statusList = JSON.parse(res.data)
          this.getData()
        }
      })
    },
    popupClose() {
      clearTimeout(this.popupTimer)
      this.popupTimer = null
      if (this.nextNums == -1) {
        this.popupBtnList[this.popupBtnNums].ref = ''
        this.leftList[this.leftNums].canteen.ref = 'active'
      } else if (this.nextNums > -1) {
        this.popupBtnList[this.popupBtnNums].ref = ''
        if (this.rightList[this.rightNums] && this.popupModule == 2) {
          this.rightList[this.rightNums].ref = 'active'
        }
      }

      this.popupTimer = setTimeout(()=>{
        this.popupBtnNums = 0
        this.popupModule = 1
      },1000)
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0] ? this.$refs.active[0] : this.$refs.active)
        document.getElementById('focus_border').style.borderColor = '#fff'
      })
    },
    popupOpend() {
      clearTimeout(this.popupTimer)
      this.popupTimer = null
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },
    getData() {
      //获取餐厅列表
      this.$store.dispatch('app/setLoadingState',true)
      GetDiningRoomList({
        cardId: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].card_id_md5,
      }).then((res) => {
        this.$store.dispatch('app/setLoadingState',false)
        if (res.code == 200) {
          if (res.data) {
            let leftListClone = JSON.parse(JSON.stringify(res.data.service_list))
            leftListClone.map((item) => {
              item.canteen.ref = ''
              if (item.canteen.cover_img) {
                item.canteen.cover_img = item.canteen.cover_img.indexOf('http') > -1 ? item.canteen.cover_img : process.env.VUE_APP_API + item.canteen.cover_img
              }
            })
            this.leftList = leftListClone
          }


          if (res.data.unfinish_list) {
            let rightListClone = JSON.parse(JSON.stringify(res.data.unfinish_list))
            rightListClone.map((item) => {
              item.ref = ''
            })
            this.rightList = rightListClone
          }

          if (this.leftList.length > 0) {
            this.nextNums = -1
            this.leftList[this.leftNums].canteen.ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          } else if(this.rightList.length > 0) {
            this.nextNums = this.rightNums
            this.rightList[this.rightNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      }).catch(()=>{
        this.$store.dispatch('app/setLoadingState',false)
        this.$store.dispatch('index/setFocusDom', document.body)
      })

    },
    cancelOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      cancelPayOrder({
        cardId: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].card_id_md5,
        orderId: this.rightList[this.rightNums].order_no,
      }).then(
        (res) => {
          if (res.code == 200) {
            let rightClone = JSON.parse(JSON.stringify(res.data))
            if (rightClone) {
              rightClone.map((item) => {
                item.ref = ''
              })
            } else {
              rightClone = []
            }

            this.rightList = rightClone
            if (this.$refs.rightContent) {
              let scrollDom = this.$refs.rightContent.querySelector('.scroll');
              this.$refs.rightContent.childNodes[0].style.top = "0rem"
              if (scrollDom) {
                scrollDom.remove()
              }
            }
            this.popupModule = 3
            this.popupMessage = '<br/>退订成功!<br>'
            this.popupBtnNums = 0
            if (this.rightList[this.rightNums]) {
              this.rightList[this.rightNums].ref = ''
            }
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  // if (this.rightList.length > 0) {
                    // if (this.rightList[this.rightNums-1]) {
                    //   this.rightNums --
                    // } else {
                    //   this.rightNums
                    // }
                    this.rightList[this.rightNums].ref = 'active'
                  // } else {
                  //   this.nextNums = -1
                  //   this.rightNums = 0
                  //   this.leftList[this.leftNums].ref = 'active'
                  // }
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active)
                  })
                },
              },
            ]
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.rightList[this.rightNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
                if (this.rightList.length > 0) {
                  this.rightList[this.rightNums].ref = 'active'
                  this.rightNums = 0
                } else {
                  this.nextNums = -1
                  this.rightNums = 0
                  this.leftList[this.leftNums].ref = 'active'
                }
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active)
                })
              },
            },
          ]
          this.popupMessage = '<br/>取消失败!<br>'
          if (error.response && error.response.data && error.response.data.msg) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )

    },
  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = null
  },
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
  .online_food_index {
    width: 16.73rem;
    .address {
      display: flex;
      position: absolute;
      top: 1.6rem;
      left: 1.45rem;
      .name {
        font-size: 0.36rem;
        font-weight: bold;
        letter-spacing: 0.03rem;
        color: #b5c0ff;
      }
      .name::before {
        content: '';
        width: 0.1rem;
        height: 0.1rem;
        position: absolute;
        background: #b5c0ff;
        border-radius: 50%;
        top: 50%;
        left: -0.25rem;
      }
    }
    .done {
      position: absolute;
      top: 1.6rem;
      right: 4.2rem;
      .item {
        font-size: 0.36rem;
        font-weight: bold;
        letter-spacing: 0.03rem;
        color: #b5c0ff;
      }
      .item::before {
        content: '';
        width: 0.1rem;
        height: 0.1rem;
        position: absolute;
        background: #b5c0ff;
        border-radius: 50%;
        top: 50%;
        left: -0.25rem;
      }
    }
    .container {
      height: 7rem;
      overflow: hidden;
      display: flex;
      flex-direction: row;
      .noData {
        div {
          font-size: 0.5rem;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -200%);
          letter-spacing: 0.05rem;
          text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
        }
      }
      .left {
        width: 12.75rem;
        height: 7rem;
        margin-left: 0;
        .leftContent {
          width: 11.6rem;
          height: 100%;
          position: relative;
          display: inline-block;
          .leftList {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
            border-radius: 0.3rem;
            position: absolute;
            // left: 0.8rem;
            transition: all 0.2s;
            .leftItem {
              width: 100%;
              height: 3.34rem;
              position: relative;
              margin-right: 0.28rem;
              margin-bottom: 0.32rem;
              background-size: 100% 100% !important;
              background: #262954;
              border-radius: 0.3rem;
              overflow: hidden;
              .item_user {
                display: flex;
                align-items: center;
                height: 100%;
                padding: 0 0.35rem;
                .pic {
                  width: 2.5rem !important;
                  height: 2.8rem !important;
                  border-radius: 0.15rem;
                  position: relative;
                  overflow: hidden;
                  img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    //background: #7B87B6
                  }
                }
                .contentInfo {
                  margin-left: 0.5rem !important;
                  height: calc(100% - 0.8rem);
                  width: calc(100% - 3.3rem);
                  position: relative;
                  .title {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-size: 0.42rem;
                    color: rgb(181, 193, 252);
                    font-weight: bold;
                    height: 0.8rem;
                    line-height: 0.8rem;
                    margin-bottom: 0.2rem;
                    border-bottom: 0.03rem solid rgb(93, 99, 129);
                  }
                  .itemContent {
                    font-size: 0.36rem;
                    letter-spacing: 0.03rem;
                    color: rgb(181, 191, 245);
                    margin: 0.1rem 0;
                    overflow: hidden;
                    div:last-child {
                      font-weight: bold;
                      color: rgb(240, 165, 49);
                      margin-top: 0.1rem;
                    }
                  }
                }
              }
            }
          }
        }
        .no_content {
          position: absolute;
          line-height: 7rem;
          left: 6rem;
          // width: 4rem;
          // height: 7rem;
          text-align: center;
          font-size: 0.4rem;
          letter-spacing: 0.05rem;
          line-height: 6rem;
          color: #b5c0ff;
        }
      }
      .right {
        width: 4.75rem;
        height: 7rem;
        //position: relative;
        .rightContent {
          width: 4.75rem;
          height: 100%;
          position: relative !important;
          overflow: hidden;
          display: inline-block;
          .list {
            // margin-right: 0.22rem;
            position: relative;
            transition: all 0.3s;
            .rightList {
              height: 3.4rem;
              padding: 0.25rem 0.35rem;
              margin-bottom: 0.28rem;
              margin-right: 0.25rem;
              background: #262954;
              border-radius: 0.24rem;
              font-size: 0.28rem;
              font-weight: bold;
            }
            .rightListItem {
              height: 2.86rem;
              padding: 0.25rem 0.35rem;
              padding-right: 0.1rem !important;
              margin-bottom: 0.28rem;
              margin-right: 0.25rem;
              background: #262954;
              border-radius: 0.24rem;
              font-size: 0.28rem;
              font-weight: bold;
              .status {
                color: #e77302;
                margin-bottom: 0.1rem;
                letter-spacing: 0.03rem;
              }
              .itemContent {
                display: flex;
                letter-spacing: 0.02rem;
                margin-bottom: 0.1rem;
                .title {
                  width: 1.3rem !important;
                  color: #b6c0fd;
                  margin-right: 0.1rem;
                }
                .value {
                  width: 2.4rem;
                }
              }
              .orderTime {
                flex-direction: column;
                .value {
                  width: 100%;
                }
              }
              .foodName {
                .value {
                  overflow: hidden;
                  display: -webkit-box;
                  text-overflow:ellipsis;
                  -webkit-line-clamp:2;
                  -webkit-box-orient: vertical
                }
              }
            }
          }
        }
  }
      .no_info {
        line-height: 7rem;
        text-align: center;
        font-size: 0.4rem;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        line-height: 6rem;
        color: #b5c0ff;
      }
    }
  }
</style>
<style lang="less">
  .online_food_index {
    .el-dialog {
      width: 9.4rem;
      height: 8rem;
      margin-top: 0 !important;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 0.26rem;
      // background: repeating-linear-gradient(to right, #c98693, #a95361);
      background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

      .el-dialog__header {
        height: 10%;
        display: flex;
        align-items: center;
        padding-top: 35px;
        .el-dialog__title {
          display: block;
          line-height: normal !important;
          height: 100%;
          font-size: 0.4rem;
          color: #fff;
          background-image: linear-gradient(
            to bottom,
            rgba(255, 255, 255, 1),
            rgba(255, 255, 255, 0.65)
          );
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          font-weight: bold;
          width: 100%;
          text-align: center;
        }
      }
      .el-dialog__body {
        width: 92%;
        height: 80%;
        position: absolute;
        bottom: 4%;
        left: 4%;
        border-radius: 0.2rem;
        background: #fff;
        padding: 0 !important;
        .message_box {
          padding: 0.4rem;
          color: #464646;
          font-size: 0.32rem;
          font-weight: bold;
          height: 60%;
          display: flex;
          align-items: flex-start;
          flex-direction: column;
          .title,
          .detail,
          .price {
            display: flex;
            font-size: 0.35rem;
            font-weight: bold;
            padding-bottom: 0.22rem !important;
            letter-spacing: 0.03rem;
          }
          .title {
            span {
              color: #f64e23;
              margin-left: 0.2rem;
            }
          }
          .price {
            span {
              color: #f64e23;
            }
          }
        }
        .sendCall {
        }
        .cancel,.sendCall {
          width: calc(100% - 0.8rem);
          height: 4.26rem;
          //top: 0.4rem;
          //left: 0.4rem;
          padding: 0;
          margin-top: 0.4rem;
          margin-left: 0.4rem;
          //position: relative;
          overflow: hidden;
          position: relative;
          .message_content {
            width: 100%;
            position: absolute;
            transition: all 0.3s;
            .message_item {
              display: flex;
              flex-direction: row;
              font-size: 0.4rem !important;
              margin-bottom: 0.2rem;
              letter-spacing: 0.03rem;
              .type {
                width: 2.2rem;
                font-size: 0.4rem !important;
              }
            }
            .orderInfo {
              padding-left: 0.4rem;
              .orderItem {
                letter-spacing: 0.03rem;
                font-size: 0.34rem !important;
                div {
                  margin-bottom: 0.2rem;
                }
              }
            }

          }
          .scroll {
            top: 1.8rem !important;
            left: auto !important;
            right: 0.8rem !important;
          }
        }
        .sendCall {
          .message_item {
            flex-direction: column !important;
          }
        }
        .result {
          display: flex;
          justify-content: center !important;
          align-items: center !important;
        }
        .info {
          display: flex;
          justify-content: center !important;
          align-items: center !important;
          margin-top: -0.4rem;
          // background: url(http://192.168.6.212/ptyanglaotv/assets/zc_tan_new.png) no-repeat !important;
          .title {
            display: flex;
            flex-direction: column;
            align-items: center !important;
            text-align: center !important;

            // margin-top: 0.9rem;
            span {
              font-size: 0.5rem;
              color: #464646 !important;
              letter-spacing: 0.14rem;
            }
          }
          .phone {
            width: 7rem;
            text-align: center;
            position: absolute;
            top: 2.3rem;
            font-size: 0.3rem;
            color: #464646;
          }
          .tip {
            text-align: center;
            color: #3288dc;
            font-size: 0.4rem;
            margin-top: 1.2rem;
          }
        }
        .popupBtnList {
          display: flex;
          flex-direction: row;
          justify-content: space-evenly;
          margin-top: 0.25rem;
          div {
            width: 3.84rem;
            height: 1rem;
            line-height: 1rem;
            text-align: center;
            border-radius: 0.3rem;
            color: #fff;
            font-size: 0.45rem;
            font-weight: bold;
            letter-spacing: 0.05rem;
            text-indent: 0.05rem;
            // background: repeating-linear-gradient(to right, #c98693, #a95361);
            background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
          }
        }
      }
    }
    .popup_info_box {
      .el-dialog {
        width: 8.6rem;
        height: 5.74rem;
        background: url("../../assets/zc_tan_new.png") no-repeat;
        background-size: 100% 100%;
        .el-dialog__header {
          display: none;
        }
        .el-dialog__body {
          background: transparent;
          .info {
            .title {
              span {
                color: #E7F1FA !important;
              }
            }
            .phone {
              top: 1.2rem;
              color: yellow;
            }
            .tip {
              margin-top: 1.1rem;
            }
          }
        }
      }
    }
  }
</style>
      