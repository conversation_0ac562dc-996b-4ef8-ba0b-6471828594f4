<template>
<div class='botton_btn' :style='{width:ButtonList.length>2?"11.4rem":"7.5rem"}'>
    <div v-for='(value,index) in ButtonList' :key='index' :ref='value.ref' :style='value.style'>{{value.name}}</div>
</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
//import引入的组件需要注入到对象中才能使用
components: {},
props:{
    ButtonList:{   //[{name:xxxx,color:'rgb(0,0,0)'}]
      type:Array
    },
},
data() {
    return {
        obj:{
            width:"",
            top:'',
            height:"",
            left:"",
        },
    };
},
created() {

},
computed: {},
watch: {
    
},
mounted() {

},
methods: {
    getFocus(){
        this.$nextTick(()=>{
            //设置外边框的宽高及位置信息
            if(this.$refs.btnItem){
                this.obj.width=this.$refs.btnItem[0].getBoundingClientRect().width+7.6+'px';
                this.obj.height=this.$refs.btnItem[0].getBoundingClientRect().height+7.6+'px';
                this.obj.left=this.$refs.btnItem[0].getBoundingClientRect().left-5.5+'px';
                this.obj.top=this.$refs.btnItem[0].getBoundingClientRect().top-5.9+'px';
                this.$emit('getSon',this.obj)
            }
           
        })
    },
},
beforeDestory() {

},
}
</script>
<style lang='less'>
    .botton_btn{
        height: 1rem;
        display: flex;
        margin:0 auto;
        justify-content: space-between;
        div{
            width:3.6rem;
            height:1rem;
            border-radius: 0.22rem;
            font-weight: bold;
            font-size:0.38rem;
            text-align: center;
            line-height: 1.1rem;
            letter-spacing: 0.05rem;
        }
    }
</style>