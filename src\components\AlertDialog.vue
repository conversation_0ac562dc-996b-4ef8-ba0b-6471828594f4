<template>
<div class='dialog' v-if="dialogshow">
    <div class="dialog_main">
        <div class="dialog_title">{{dialogTitle}}</div>
        <div class="dialog_content">
          <!-- 编辑联系方式 -->
          <div v-if='dialogType=="phone"' class='phone'>
              <p :ref='inputRef'><span ref='inputdata' class="spanTel">{{tel}}</span></p>
              <p class="tip">•按遥控器上的"#"号键,可进行删除操作</p>
          </div>
          <!-- 成功失败提示框 -->
          <div v-else-if='(dialogType=="success")||(dialogType=="error")' class="success">
            <div class="dialogTip">
              <div class="dialog_img">
                <img src="../assets/alert_service.png" alt="">
              </div>
              <p v-if="dialogType=='success'" class="success_item" v-html='dialogMessage'></p>
              <p v-else class="success_item" v-html='dialogMessage'></p>
            </div>
            <p v-if="dialogType=='success'&&!dialogMark" class="error_item">请保持通话畅通，服务人员会尽快与您联系！</p>
            <p v-else-if="dialogType=='error'&&!dialogMark"  class="error_item" >对此造成的不便，我们深表歉意。</p>
          </div>
          <!-- 取消预约 -->
          <div v-else-if='dialogType=="sure"' class="sure">
              <div class="sure_img">
                <img src="../assets/alert_service.png" alt="">
              </div>
              <p v-html='dialogMessage' class="sureText"></p>
          </div>
          <!-- 服务评价 -->
          <div v-else-if='dialogType=="satisfy"' class="satisfy">
              <div v-for='value in 5'  :key='value' :class="satisfaction>=value?'getcolor':'nocolor'"></div>
          </div>
          <!-- 支付方式 -->
          <div v-else class="card">
            <div  v-for='(value,index) in cards' :key='index' :ref='value.ref' :class="value.class"></div>
          </div>
        </div>
        <div :class="dialogButtonList.length>1?'dialog_btn':'dialogBtn'" >
          <div v-for='(value,index) in  dialogButtonList' :key='index' :ref='value.ref'>{{value.name}}</div>
        </div>
    </div>
</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
//import引入的组件需要注入到对象中才能使用
components: {},
props:{
    dialogshow: {  // 控制弹窗显示与否
      type:Boolean,
      required: false
    },
    dialogType:{  // 弹窗类型   pay(支付方式)、phone（编辑电话号码） 、success（成功,） error(失败显示的)
      type:String,
      required: false
    },
    dialogButtonList:{  // 按钮循环  [{index:1,name:"关闭",ref:''}]
      type:Array,
      required: false
    },
    dialogTitle:{//弹窗标题
      type:String,
      required: false
    },
    dialogMessage:{//第一行的内容
      type:String,
      required: false
    },
    satisfaction:{
      type:Number,
      required: false
    },
    dialogMark:{//控制提示框的第二行文本是否显示 0:不显示  1:显示
      type:Number,
      required:false
    }
},
data() {
    return {
      obj:{
        width:'',
        height:'',
        top:'',
        left:'',
      },
      inputRef:'',
      tel:this.$store.getters.getUserInfo?this.$store.getters.getUserInfo.phone:'------',
      cards:[{
        class:'card_left',
        ref:'',
      },{
        class:'card_right',
        ref:'',
      }]
    };
},
created() {

},
computed: {},
watch: {
  tel(newVlaue){
     this.tel=newVlaue;
  }
},
mounted() {

},
methods: {
   getFocus(){
        this.$nextTick(()=>{
            //设置外边框的宽高及位置信息
            if(this.inputRef=='btnItem'){
              //输入框
              this.obj.width=this.$refs.btnItem.getBoundingClientRect().width****+'px';
              this.obj.height=this.$refs.btnItem.getBoundingClientRect().height****+'px';
              this.obj.left=this.$refs.btnItem.getBoundingClientRect().left-5.5+'px';
              this.obj.top=this.$refs.btnItem.getBoundingClientRect().top-5.9+'px';
              this.$emit('getDialogSon',this.obj)
            }else if(this.inputRef==''){
              //确认框
              if(this.$refs.btnItem){
                this.obj.width=this.$refs.btnItem[0].getBoundingClientRect().width****+'px';
                this.obj.height=this.$refs.btnItem[0].getBoundingClientRect().height****+'px';
                this.obj.left=this.$refs.btnItem[0].getBoundingClientRect().left-5.5+'px';
                this.obj.top=this.$refs.btnItem[0].getBoundingClientRect().top-5.9+'px';
                this.$emit('getDialogSon',this.obj)
              }
            }else{
              //支付方式调用
              if(this.$refs.btnItem){
                this.obj.width=this.$refs.btnItem[0].getBoundingClientRect().width****+'px';
                this.obj.height=this.$refs.btnItem[0].getBoundingClientRect().height****+'px';
                this.obj.left=this.$refs.btnItem[0].getBoundingClientRect().left-5.5+'px';
                this.obj.top=this.$refs.btnItem[0].getBoundingClientRect().top-5.9+'px';
                this.$emit('getDialogSon',this.obj)
              }
            }
        })
    },
},
beforeDestory() {

},
}
</script>
<style lang='less'>
    .dialog{
        width:19.2rem;
        height: 10.8rem;
        position: absolute;
        top:0;
        left: 0;
        background:rgba(0,0,0,.5);
        z-index:99;
        .dialog_main{
          width:9.25rem;
          // height: 5.7rem;
          position: absolute;
          top:2.5rem;
          left: 4.8rem;;
          border-radius: 0.22rem;
          overflow: hidden;
          .dialog_title,.dialog_btn,.dialogBtn{
            width:9.25rem;
            height: 1.1rem;
            background:#545D94;
            color:#fff;
            line-height: 1.1rem;
            font-size:0.4rem;
            font-weight:bold;
            letter-spacing: 0.05rem;
          }
          .dialog_title{
            text-align: center;
          }
          .dialog_content{
            width:9.25rem;
            padding:0.3rem 0;
            background:#EFF0F5;
            .phone{
              p{
                width: 8.45rem;
                height: 0.8rem;
                margin:0 0.4rem 0.1rem 0.4rem;
                .spanTel{
                  display: inline-block;
                  width: 8.05rem;
                  height: 0.8rem;
                  background:#CBD1DD;
                  border-radius: 0.22rem;
                  padding: 0 0.2rem;
                  font-size:0.36rem;
                  font-weight: bold;
                  letter-spacing: 0.03rem;
                  line-height: 0.85rem;
                }
              }
              .tip{
                font-size:0.3rem;
                height: 0.5rem;
                font-weight: bold;
                color:#7D7D7E;
                letter-spacing: 0.03rem;
                margin-bottom: 0;
                line-height: 0.5rem;
              }
              
            }
            .success{
              width:7.5rem;
              margin:0 auto;
              padding:0.3rem 0;
              div{
                width:5.2rem;
                height:1.1rem;
                margin:0 auto;
                display: table;
                .dialog_img{
                  width: 1.1rem;
                  height: 1.1rem;
                  img{
                    width: 100%;
                    height: 100%;
                    background-size:100% 100%;
                  }
                }
                .success_item{
                  display: table-cell;
                  width:4rem;
                  height:1.1rem;
                  color:#3E3E3E;
                  font-size:0.34rem;
                  font-weight: bold;
                  margin-left: 0.1rem;
                  letter-spacing: 0.02rem;
                  text-align: left;
                  vertical-align: middle;
                }
              }
              .error_item{
                width:7.2rem;
                height:0.5rem;
                color:#3E3E3E;
                font-size:0.34rem;
                font-weight: bold;
                letter-spacing: 0.02rem;
                margin:0 auto;
                text-align: center;
              }
            }
            .sure{
              width:7rem;
              height:1.1rem;
              padding: 0.3rem 0;
              margin:0 auto;
              display: table;
              .sure_img{
                  width: 1.1rem;
                  height: 1.1rem;
                  img{
                    width: 100%;
                    height: 100%;
                    background-size:100% 100%;
                  }
              }
              p{
                display: table-cell;
                  width:5.8rem;
                  height:1.1rem;
                  color:#3E3E3E;
                  font-size:0.34rem;
                  font-weight: bold;
                  margin-left: 0.1rem;
                  letter-spacing: 0.02rem;
                  text-align: left;
                  vertical-align: middle;
              }
            }
            .satisfy{
              width:5.9rem;
              height: 1.3rem;
              display: flex;
              justify-content: space-between;
              margin:0 auto;
              div{
                width:0.9rem;
                height: 0.85rem;
                margin-top:0.225rem ;
              } 
              .nocolor{
                background:url('../assets/wu_nocolor.png') no-repeat;
                background-size:100% 100%;
              }
              .getcolor{
                background:url('../assets/wu_yellow.png') no-repeat;
                background-size:100% 100%;
              }
            }
            .card{
              width:9.25rem;
              height:2.4rem;
              padding: 0.3rem 0;
              div{
                height:2.4rem;
                width:4.16rem;
                float: left;
                margin-left:0.34rem;
              }
              .card_left{
                width:4.16rem;
                height: 2.4rem;
                background:url('../assets/pay_ticket.png') no-repeat;
                background-size:100% 100%;
              }
              .card_right{
                width:4.16rem;
                height: 2.4rem;
                background:url('../assets/pay.png') no-repeat;
                background-size:100% 100%;
              }
            }
          }
          .dialog_btn{
            display: flex;
            div{
              width:4.6rem;
              height: 1.1rem;
              text-align: center;
              border-right:0.05rem solid #B6BBD2;
              letter-spacing: 0.1rem;
              &:last-child{
                border:none;
              }
            }            
          }
          .dialogBtn{
            div{
              width:9.25rem;
              height: 1.1rem;
              text-align: center;
              letter-spacing: 0.1rem;
            }   
          }
        }
    }
</style>