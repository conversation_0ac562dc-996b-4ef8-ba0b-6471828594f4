<!--活动预约&&咨询服务-->
<template>
  <div class='consultServiceList'>
    <div class="serve_title" v-if="supplierInfo">· {{supplierInfo.title}} ({{supplierInfo.address}})</div>

    <div class="left_more scrollParent">
      <div class="leftList" :style="{top: '0rem'}">
        <div class="leftItem" :ref="item.ref" v-for="(item,index) in leftList" :key="index">
          <div>
            {{item.title}}
          </div>
          <div class="badge" v-if="item.badge"></div>
        </div>
      </div>
    </div>

    <div class="right_more">
      <div class="item_list scrollParent" :key="leftNums" v-if="rightList.length > 0">
        <div class="rightList" :key="leftNums" :style="{top: '0rem'}">
          <div class="friendsItem" :ref="item.ref" v-for="(item,index) in rightList" :key="index">
              <div class="item_user" :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }">
                <div class="pic" v-lazy-container="{ selector: 'img' }">
                  <img :data-src="item.img" :data-error="lazyError" :key="index" alt=""  />
                  <div class="status" v-if="item.is_appointment">
                    已预约
                  </div>
                </div>
                <div class="itemContent">
                  <div class="title">
                    {{item.title}}
                  </div>
                  <div class="subtitle" v-if="item.subtitle">
                    {{item.subtitle}}
                  </div>
                  <div class="describe" v-if="item.describe">
                    {{item.describe}}
                  </div>
                </div>
              </div>
          </div>
        </div>
      </div>
      <div v-else class="no_distribution">
        暂无数据
      </div>



    </div>

    <div class="serve_title orderListTitle" v-if="supplierInfo">· 服务记录</div>

    <div class="order_more">
      <div class="orderList scrollParent" ref="orderContent"  v-if="orderList.length > 0">
        <div class="list"  :style="{ top: '0rem' }">
          <div class="listInfo" :ref="item.ref" v-for="(item, index) in orderList" :key="index">
            <div class="listContent">
              <div class="status">
                <template>
                  <div :style="{  color: statusList[item.status].color}" >
                    {{ statusList[item.status].text }}
                  </div>
                </template>
              </div>


              <div class="good" v-for="[key, value] in Object.entries(sendPostInfo[$route.query.type].orderObj)" :key="key">
<!--                {{ key }}: {{ value }}-->
                <div class="goodInfo" v-if="key.indexOf('time') > -1">
                  <div class="title">{{value}}：</div>
                  <div class="value">{{ new Date(item[key]*1000).format('yyyy/MM/dd hh:mm') }}</div>
                </div>
                <div class="goodInfo" v-else-if="key.indexOf('dataTimeValue') > -1">
                  <div class="title">{{value}}：</div>
                  <div class="value" :style="{fontSize: item.titleBig ? '0.28rem' : '0.268rem',marginTop: item.titleBig ? '0' : '0.02rem'}">
                    <span>{{ item.dataTimeValue.topTime }}</span>
                    <span>{{ item.dataTimeValue.bottomTime }}</span>
                  </div>
                </div>
                <div class="goodInfo" style="display: flex" v-else>
                  <div class="title">{{value}}：</div>
                  <div class="value">{{item[key]}}</div>
                </div>

              </div>


            </div>
          </div>
        </div>
      </div>
      <div class="no_distribution" v-else>
        暂无近期记录
      </div>
    </div>

    <div class="notice" v-if="supplierInfo && supplierInfo.telephone">
      <img :src="require('../../assets/phone.png')" alt="">
      客服热线: {{supplierInfo.telephone}}
    </div>

    <!--  二次确认弹窗-->
    <el-dialog
        :class="popupModule == 4 ? 'popup_info_box' : 'popup_box'"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        :title="this.supplierInfo.title"
        ref="popupBox"
    >
      <!--发起预约-->
      <div class="message_box sendCall scrollParent" v-if="popupModule == 1 && sendData">
        <div class="message_content" ref="popupRef" :style="{top: '0rem'}">
          <div class="message_item" v-for="(item,index) in sendData" :key="index" >
            <div class="itemType">
              <span class="type">商品{{index + 1}}:</span>
              <span class="value">{{item.name}}</span>
            </div>
            <div class="itemValue">
              <span>数量:{{item.payNums}}{{item.fk_vegetables_unit_name}} </span>
              <span>单价:{{item.price}}元</span>
            </div>
          </div>
        </div>

      </div>

      <!--取消预约-->
      <div class="message_box cancel scrollParent" v-if="popupModule == 2">
        <div class="message_content" ref="popupRef" :style="{top: '0rem'}">
          <div class="message_item">
            <div class="type">
              当前状态:
            </div>
            <div class="value">
              {{ statusList[this.orderList[this.orderNums].status].text }}
            </div>
          </div>

          <div class="message_item" v-for="[key, value] in Object.entries(sendPostInfo[$route.query.type].orderObj)" :key="key">
            <template  v-if="key.indexOf('time') > -1">
              <div class="type">{{value}}：</div>
              <div class="value">{{ new Date(orderList[orderNums][key]*1000).format('yyyy/MM/dd hh:mm') }}</div>
            </template>
            <template  v-else-if="key.indexOf('dataTimeValue') > -1">
              <div class="type">{{value}}：</div>
              <div class="value" style="max-width: 74%">
                <template v-if="!isSameDay(orderList[orderNums].start_time*1000,orderList[orderNums].end_time*1000)">
                  <span>{{ new Date(orderList[orderNums].start_time * 1000).format('yyyy/MM/dd hh:mm') }} 至 <br/></span>
                  <span>{{ new Date(orderList[orderNums].end_time * 1000).format('yyyy/MM/dd hh:mm') }}</span>
                </template>
                <template v-else>
                  <span>{{ orderList[orderNums].dataTimeValue.topTime }}</span>
                  <span>{{ orderList[orderNums].dataTimeValue.bottomTime }}</span>
                </template>

              </div>
            </template>
            <template  style="display: flex" v-else>
              <div class="type">{{value}}：</div>
              <div class="value">{{orderList[orderNums][key]}}</div>
            </template>

          </div>

          <div class="message_item">
            <div class="type">商家:</div>
            <div class="value">{{ this.supplierInfo.title }}</div>
          </div>

          <div class="message_item">
            <div class="type">地址:</div>
            <div class="value">{{ this.supplierInfo.address }}</div>
          </div>

        </div>
      </div>

      <div class="message_box result" style="text-align: center; font-size: 0.37rem; justify-content: center" v-html="popupMessage" v-if="popupModule == 3"></div>

      <div class="message_box info" v-if="popupModule == 4">
        <div class="title"><span>你的预约</span><span>已经发送成功！</span></div>
        <div class="tip">您可以在“服务记录”中查看预约信息。</div>
      </div>

      <div class="popupBtnList">
        <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index"> {{ item.text }}</div>
      </div>
    </el-dialog>


  </div>
</template>

<script>

import {
  getActiveOrgList,
  getActiveOrgInfo,
  getActiveOrderList,
  cancelActiveOrder,

  getZiXunOrgList,
  getZiXunOrgInfo,
  getZiXunOrderList,
  cancelZiXunOrder
} from "@/api/index";

export default {
  name:'consultServiceList',
  components: {
  },
  data() {
    return {
      lazyError: require("@/assets/yaowu.png"),
      statusList: null,
      supplierInfo: null,
      sendPostInfo:{
        51: {
          leftListFuc: getZiXunOrgList,
          leftSendPromise:{
            fk_zixun_service_org_id: 0
          },
          rightListFuc: getZiXunOrgInfo,
          rightSendPromise:{
            user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
            fk_zixun_service_org_class_id: 0,
            fk_zixun_service_org_id: 0
          },
          orderListFuc: getZiXunOrderList,
          orderListPromise: {
            user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
            fk_supplier_id: 0
          },
          orderObj: {
            apply_time: '申请时间',
            dataTimeValue: '预约时间',
            fk_zixun_service_org_class_title: '咨询类别',
            zixunshi_name: '受理老师'
          },
          cancelOrderFuc: cancelZiXunOrder
        },
        54: {
          leftListFuc: getActiveOrgList,
          leftSendPromise:{
            fk_huodongyuyue_service_org_id: 0
          },
          rightListFuc: getActiveOrgInfo,
          rightSendPromise:{
            user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
            fk_huodongyuyue_service_org_class_id: 0,
            fk_huodongyuyue_service_org_id: 0
          },
          orderListFuc: getActiveOrderList,
          orderListPromise: {
            user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
            fk_supplier_id: 0
          },
          orderObj: {
            apply_time: '申请时间',
            dataTimeValue: '预约时间',
            huodong_name: '活动项目',
          },
          cancelOrderFuc: cancelActiveOrder


        }
      },
      timer: null,
      leftList:[],
      leftNums: 0,
      nextNums: -1,   // -1  说明焦点在左侧   > -1  在右侧

      rightList:[],
      rightNums: 0,

      orderList:[],
      orderNums: 0,

      firstShow: true,

      dialogVisible: false,
      popupModule: 1,   // 1、好友弹窗  2、消息提示
      popupMessage:'',
      popupBtnNums: 0,
      popupBtnList:[
        {
          text: '呼叫好友',
          ref:'',
          fuc: this.callMyfriend
        },
        {
          text: '删除好友',
          ref:'',
          fuc: this.delectMyfriend
        },
      ],
      bgList:[
        {
          color:'rgba(89,167,216,1)',
          bg: require('@/assets/1_bg_pic.png')
        },{
          color:'rgba(210,126,126,1)',
          bg: require('@/assets/2_bg_pic.png')
        },{
          color:'rgba(90,184,148,1)',
          bg: require('@/assets/3_bg_pic.png')
        },{
          color:'rgba(141,133,218,1)',
          bg: require('@/assets/4_bg_pic.png')
        },{
          color:'rgba(108,151,206,1)',
          bg: require('@/assets/5_bg_pic.png')
        },{
          color:'rgba(198,164,100,1)',
          bg: require('@/assets/6_bg_pic.png')
        },
      ],
      sendData: null



    };
  },
  created() {
    if (localStorage.getItem('statusList')) {
      this.statusList = JSON.parse(localStorage.getItem('statusList'))
    }
    this.supplierInfo = JSON.parse(sessionStorage.getItem('supplierInfo'))
    sessionStorage.removeItem('consultData')
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', JSON.parse(sessionStorage.getItem('redirectInfo')).title)
    if (sessionStorage.getItem('consultServiceListFocus')) {
      this.leftNums=Number(sessionStorage.getItem('consultServiceListFocus').split(',')[0])
      this.rightNums=Number(sessionStorage.getItem('consultServiceListFocus').split(',')[1])
      this.nextNums = this.leftNums
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.getData()

    this.fuc.KeyboardEvents({
      down:()=>{
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if ((Math.abs(styleTop) +scrollPageTop)  * fontSize > this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight) {
                this.$refs.popupRef.style.top = styleTop - (scrollTop/fontSize - Math.abs(styleTop)) + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * ((scrollTop/fontSize - Math.abs(styleTop)) * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight)) + 'px'
                return
              }
              if (scrollTop > 0) {
                this.$refs.popupRef.style.top = styleTop - scrollPageTop + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))   + 'px'
              }
            }
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
            if (this.rightNums < this.rightList.length - 1) {
              if (this.rightList[this.rightNums + 2]) {
                this.rightList[this.rightNums].ref = ""
                this.rightNums += 2
                this.rightList[this.rightNums].ref = "active"
              } else {
                if (this.rightNums  < (this.rightList.length - this.rightList.length % 2) && this.rightList.length%2 != 0) {
                  this.rightList[this.rightNums].ref = ""
                  this.rightNums = this.rightList.length - 1
                  this.rightList[this.rightNums].ref = "active"
                }
              }
          }
        } else
        // 左侧
        if (this.nextNums == -1){
          if (this.leftNums < this.leftList.length -1) {
            this.rightNums = 0
            this.rightList = []
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ""
            this.leftNums ++
            this.leftList[this.leftNums].ref = "active"
            this.$nextTick(()=>{
              this.$refs.active[0].classList.add('select')
            })
            if (this.leftList[this.leftNums].fuc) {
              this.leftList[this.leftNums].fuc(this.leftList[this.leftNums])
            }
          }
        } else
        // 订单
        if (this.nextNums == -2) {
          if (this.orderNums < this.orderList.length -1) {
            this.orderList[this.orderNums].ref = ""
            this.orderNums ++
            this.orderList[this.orderNums].ref = "active"

          }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      up:()=>{
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if (scrollTop > 0) {
                if ((styleTop + scrollPageTop) > 0) {
                  this.$refs.popupRef.style.top = '0rem'
                  scrollBar.style.top = '0px'
                  return
                } else {
                  this.$refs.popupRef.style.top = styleTop + scrollPageTop + 'rem'
                  scrollBar.style.top = barTop - (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))  + 'px'
                }
              }
            }
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
            if (this.rightList[this.rightNums - 2]) {
              this.rightList[this.rightNums].ref = ""
              this.rightNums -= 2
              this.rightList[this.rightNums].ref = "active"
            }
        } else
        // 左侧
        if (this.nextNums == -1) {
            if (this.leftNums > 0) {
              this.rightNums = 0
              this.rightList = []
              this.$refs.active[0].classList.remove('select')
              this.leftList[this.leftNums].ref = ""
              this.leftNums --
              this.leftList[this.leftNums].ref = "active"
              this.$nextTick(()=>{
                this.$refs.active[0].classList.add('select')
              })
              if (this.leftList[this.leftNums].fuc) {
                this.leftList[this.leftNums].fuc(this.leftList[this.leftNums])
              }
            }

          } else
        // 订单
        if (this.nextNums == -2) {
          if (this.orderNums > 0) {
            this.orderList[this.orderNums].ref = ""
            this.orderNums --
            this.orderList[this.orderNums].ref = "active"
          }
        }

        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      left:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在订单
        if (this.nextNums == -2) {
          if (this.rightList.length > 0) {
            this.orderList[this.orderNums].ref = ""
            this.rightList[this.rightNums].ref = "active"
            this.nextNums = this.rightNums
          } else {
            this.orderList[this.orderNums].ref = ""
            this.leftList[this.leftNums].ref = "active"
            this.nextNums = -1
          }
        } else
        // 在右侧
        if (this.nextNums > -1) {
          if(this.rightNums % 2 == 0){
            this.rightList[this.rightNums].ref = ""
            this.leftList[this.leftNums].ref = "active"
            this.nextNums = -1
          } else {
            this.rightList[this.rightNums].ref = ""
            this.rightNums --
            this.rightList[this.rightNums].ref = "active"
          }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      right:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1) {
            if (this.rightNums % 2 != 1) {
              this.rightList[this.rightNums].ref = ""
              this.rightNums ++
              this.rightList[this.rightNums].ref = "active"
            } else
            if (this.orderList.length > 0) {
              this.rightList[this.rightNums].ref = ""
              this.orderList[this.orderNums].ref = "active"
              this.nextNums = -2
            }
          } else if (this.orderList.length > 0) {
            this.rightList[this.rightNums].ref = ""
            this.orderList[this.orderNums].ref = "active"
            this.nextNums = -2
          }
        } else
        // 在左侧
        if (this.nextNums == -1) {
          if ( this.rightList.length > 0) {
            this.leftList[this.leftNums].ref = ""
            this.nextNums = this.leftNums
            this.rightList[this.rightNums].ref = "active"
          } else
          if (this.orderList.length > 0){
            this.leftList[this.leftNums].ref = ""
            this.orderList[this.orderNums].ref = "active"
            this.nextNums = -2
          }
        }
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      },
      enter:()=>{
        if (this.dialogVisible) {
          // 弹窗
          this.popupBtnList[this.popupBtnNums].ref = ''
          // 取消订单关闭时
          if (this.nextNums == -1) {
            this.leftList[this.leftNums].ref = 'active'
          } else if(this.nextNums == -2 && this.orderList[this.orderNums]) {
            this.orderList[this.orderNums].ref = 'active'
          } else {
            this.rightList[this.rightNums].ref = 'active'
          }


          this.popupBtnList[this.popupBtnNums].fuc(this.rightList[this.rightNums])
          this.dialogVisible = false
          return
        }
        if (this.nextNums > -1 && this.rightList.length > 0) {
            if (!this.$route.query.type) {
              return
            }
            sessionStorage.setItem('consultData', JSON.stringify(this.rightList[this.rightNums]))
            sessionStorage.setItem('consultServiceListFocus',this.leftNums + ',' + this.rightNums)
            this.$nextTick(()=>{
                this.$router.push({
                  path: './consultServiceInfo',
                  query: {
                    type: this.$route.query.type,
                    class_id: this.leftList[this.leftNums].id
                  }
                })
            })
        } else
        if (this.nextNums == -2) {
          this.orderList[this.orderNums].ref = ''
          this.popupModule = 2
          this.popupBtnNums =  0
          this.popupBtnList = [
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            }
          ]
          if (this.orderList[this.orderNums].status == 2) {
            this.popupBtnList.push({
              text: '取消预约',
              ref: '',
              fuc: this.cancelOrder,
            })
          }


          this.popupBtnList[this.popupBtnNums].ref="active"
          this.dialogVisible = true
        }
      },
      esc:()=>{
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ""
          if (this.nextNums == -1) {
            this.leftList[this.leftNums].ref = 'active'
          } else if(this.nextNums == -2 && this.orderList[this.orderNums]) {
            this.orderList[this.orderNums].ref = 'active'
          } else {
            this.rightList[this.rightNums].ref = 'active'
          }
          this.dialogVisible = false
          return
        }
        sessionStorage.removeItem('consultServiceListFocus')
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      }
    })
  },
  methods: {
    defImg(index){
      let img = event.srcElement;
      img.src = require("../../assets/yaowu.png");
      img.onerror = null; //防止闪图
    },
    popupClose() {
      this.popupBtnList[this.popupBtnNums].ref = ''
      if (this.nextNums == -1) {
        this.leftList[this.leftNums].ref = 'active'
      } else if (this.nextNums == -2) {
        this.orderList[this.orderNums].ref = 'active'
      } else {
        if (this.rightList[this.rightNums]) {
          this.rightList[this.rightNums].ref = 'active'
        }
      }

      this.$nextTick(() => {
        if (this.$refs.active[0]) {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        }

        document.getElementById('focus_border').style.borderColor = '#fff'
        // document.getElementById('focus_border').style.borderColor = 'transparent'

        // setTimeout(()=>{
        this.popupBtnNums = 0
        this.popupModule = 1
        // },100)

      })
    },
    popupOpend() {
      this.$nextTick(()=>{
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0]);
        document.getElementById('focus_border').style.borderColor = "#4b68a7"
      })
    },

    getData(){
      if (!this.$route.query.type) {
        return
      }
      this.$store.dispatch('index/setFocusDom',this.$refs.active);

      Object.keys(this.sendPostInfo[this.$route.query.type].leftSendPromise).forEach(key => {
        if (key.indexOf('service_org_id') > -1) {
          this.sendPostInfo[this.$route.query.type].leftSendPromise[key] = this.supplierInfo.fk_supplier_id
        }
      });

      this.sendPostInfo[this.$route.query.type].leftListFuc(this.sendPostInfo[this.$route.query.type].leftSendPromise)
      .then(res=>{
        if (res.code == 200) {
          let leftICon = res.data
          // leftICon = leftICon.concat(JSON.parse(JSON.stringify(res.data)))
          leftICon.map((item,index)=>{
            item.ref = ""
            item.fuc = this.getRightList
          })
          leftICon[this.leftNums].ref = "active"
          this.leftList = leftICon


          this.$nextTick(()=>{
            if (!sessionStorage.getItem('consultServiceListFocus')) {
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
            }
            this.$refs.active[0].classList.add('select')
            this.leftList[this.leftNums].fuc(this.leftList[this.leftNums])
          })

        }
      })
      .finally(() => {
        this.getOrderList()
      });
    },
    // 右侧数据
    getRightList(item) {
      this.$store.dispatch('app/setLoadingState', true)
      this.rightList = []
      Object.keys(this.sendPostInfo[this.$route.query.type].rightSendPromise).forEach(key => {
        if (key.indexOf('service_org_id') > -1) {
          this.sendPostInfo[this.$route.query.type].rightSendPromise[key] = this.supplierInfo.fk_supplier_id
        }
        if (key.indexOf('service_org_class_id') > -1) {
          this.sendPostInfo[this.$route.query.type].rightSendPromise[key] = this.leftList[this.leftNums].id
        }
      });

      this.sendPostInfo[this.$route.query.type].rightListFuc(this.sendPostInfo[this.$route.query.type].rightSendPromise)
      .then(res=>{
        this.$store.dispatch('app/setLoadingState', false)
        if (res.code == 200) {
          let rightList =  JSON.parse(JSON.stringify(res.data))
          // rightList = rightList.concat(JSON.parse(JSON.stringify(res.data)))
          // rightList = rightList.concat(JSON.parse(JSON.stringify(res.data)))
          // rightList = rightList.concat(JSON.parse(JSON.stringify(res.data)))
          // rightList = rightList.concat(JSON.parse(JSON.stringify(res.data)))
          // rightList = rightList.concat(JSON.parse(JSON.stringify(res.data)))
          // rightList = rightList.concat(JSON.parse(JSON.stringify(res.data)))
          // rightList = rightList.concat(JSON.parse(JSON.stringify(res.data)))

          rightList.map(item=>{
            item.title = item.name
            item.subtitle = item.fk_zixun_service_org_class_title || null
            item.ref = ""
            if (item.img) {
              item.img = item.img.indexOf('http') > -1 ? item.img : process.env.VUE_APP_API + item.img
            }
          })
          this.rightList = rightList

          if (this.firstShow && this.rightList.length > 0) {
            if (sessionStorage.getItem('consultServiceListFocus')) {
              this.nextNums = this.leftNums
              this.leftList[this.leftNums].ref = ""
              this.rightList[this.rightNums].ref = "active"
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active);
                this.fuc.setScroll()
              })
              sessionStorage.removeItem('consultServiceListFocus')
            } else {
              // this.rightNums = 0
              // this.nextNums = -1
              this.nextNums = this.leftNums
              this.leftList[this.leftNums].ref = ""
              this.rightList[this.rightNums].ref = "active"
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active);
                this.fuc.setScroll()
              })
            }
            this.firstShow = false
          }

          this.$nextTick(()=>{
            this.fuc.setScroll()
          })
        }
      })
      .catch(err=>{
        this.$store.dispatch('app/setLoadingState', false)
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom',this.$refs.active);
        })
      })


    },

    getOrderList() {
      Object.keys(this.sendPostInfo[this.$route.query.type].orderListPromise).forEach(key => {
        if (key.indexOf('supplier_id') > -1) {
          this.sendPostInfo[this.$route.query.type].orderListPromise[key] = this.supplierInfo.fk_supplier_id
        }
      });
      this.sendPostInfo[this.$route.query.type].orderListFuc(this.sendPostInfo[this.$route.query.type].orderListPromise)
      .then(res=>{
        if (res.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data))

          list.map(item=>{
            item.ref = ""
            item.titleBig = true
            if (!this.isSameDay(item.start_time*1000,item.end_time*1000)) {
              item.titleBig = false
              item.dataTimeValue = {
                topTime: new Date(item.start_time * 1000).format('MM/dd hh:mm') + ' - ',
                bottomTime: new Date(item.end_time * 1000).format('MM/dd hh:mm')
              }
            } else {
              item.dataTimeValue = {
                topTime: new Date(item.start_time * 1000).format('yyyy/MM/dd'),
                bottomTime: new Date(item.start_time * 1000).format('hh:mm') + '-' + new Date(item.end_time * 1000).format('hh:mm')
              }
            }
          })
          this.orderList = list
          this.$nextTick(() => {
            this.fuc.setScroll()
          })
        }
      })
    },
    isSameDay(date1, date2) {
      return new Date(date1).toDateString() === new Date(date2).toDateString();
    },
    cancelOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      let orderID = this.orderList[this.orderNums].id
      this.sendPostInfo[this.$route.query.type].orderListPromise.order_id = orderID
      this.sendPostInfo[this.$route.query.type].cancelOrderFuc(this.sendPostInfo[this.$route.query.type].orderListPromise)
      .then(res => {
          if (res.code == 200) {

            let rightClone = JSON.parse(JSON.stringify(res.data))
            if (rightClone) {
              rightClone.map((item) => {
                item.ref = ''
                item.titleBig = true
                if (!this.isSameDay(item.start_time*1000,item.end_time*1000)) {
                  item.titleBig = false
                  item.dataTimeValue = {
                    topTime: new Date(item.start_time * 1000).format('MM/dd hh:mm') + ' - ',
                    bottomTime: new Date(item.end_time * 1000).format('MM/dd hh:mm')
                  }
                } else {
                  item.dataTimeValue = {
                    topTime: new Date(item.start_time * 1000).format('yyyy/MM/dd'),
                    bottomTime: new Date(item.start_time * 1000).format('hh:mm') + '-' + new Date(item.end_time * 1000).format('hh:mm')
                  }
                }
              })
            } else {
              rightClone = []
            }

            this.orderList = rightClone
            if (this.$refs.orderContent) {
              let scrollDom = this.$refs.orderContent.querySelector('.scroll');
              this.$refs.orderContent.childNodes[0].style.top = "0rem"
              if (scrollDom) {
                scrollDom.remove()
              }
            }
            this.popupModule = 3
            this.leftList[this.leftNums].ref = ''
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                },
              },
            ]
            this.popupMessage = '<br/>取消预约成功!<br>'
            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
          this.getRightList()
        })
      .catch(error => {
          this.orderList[this.orderNums].ref = ""
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br/>取消失败!<br>'
          if (error.response && error.response.data && error.response.data.msg) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
      })
    },
  },
  destroyed() {
  },
  beforeDestory() {
  },
}
</script>
<style lang='less' scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
.consultServiceList {
  height: 7rem;
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 每行显示两个元素 */
  //gap: 0.16rem;
  font-size: 0.2rem;
  color: #E7E7EF;
  .serve_title {
    font-size: 0.36rem;
    width: 68%;
    font-weight: bold;
    letter-spacing: 0.03rem;
    color: #B5C0FF;
    position: absolute;
    top: 1.6rem;
  }
  .orderListTitle {
    left: 73%;
  }
  .notice {
    position: absolute;
    font-size: 0.28rem;
    right: 1.2rem;
    bottom: 0.6rem;
    color: #B2BAEF;
    display: flex;
    flex-direction: row;
    align-items: center;
    img {
      position: relative;
      top: 0.02rem;
      left: -0.05rem;
      width: 0.32rem;
      height: 0.32rem;
    }
  }
  .left_more {
    width: 2rem;
    border-radius: 0.2rem;
    height: 7rem;
    position: relative;
    .leftList {
      width: calc(100% - 0.2rem);
      text-align: center;
      position: absolute;
      transition: all 0.3s;
      .leftItem {
        height: 0.82rem;
        //height: 2.195rem;
        line-height: 0.92rem;
        margin-bottom: 0.2rem;
        background: #343D74;
        border-radius: 0.2rem;
        position: relative;
        font-weight: bold;
        transition: all 0.3s;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        //font-size: 0.34rem;
        font-size: 0.33rem;
        color: #E7E7EF;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0,0,0,0.6);
        .badge {
          width: 0.15rem;
          height: 0.15rem;
          border-radius: 50%;
          background: #FF5F5F;
          position: absolute;
          top: 50%;
          right: 0.2rem;
          transform: translateY(-50%);
        }
      }
      .select {
        background: #89A7FF;
        transition: all 0.3s;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(102,129,218,0.6);
      }
    }
  }
  .right_more {
    width: 10.2rem;
    height: 7rem;
    margin-left: 0;
    //padding-left: 0.1rem;
    //margin-left: 0.05rem;
    .no_distribution {
      width: 100%;
      height: 100%;
      font-size: 0.4rem;
      text-align: center;
      line-height: 6rem;
      color: #B5C0FF;
    }
    //.item_title {
    //  height: 0.6rem;
    //  line-height: 0.6rem;
    //  background: #ccc;
    //  font-size: 0.28rem;
    //  font-weight: bold;
    //  color: #000;
    //  padding-left: 0.2rem;
    //}
    .item_list {
      height: 100%;
      border-radius: 0.24rem;
      overflow: hidden;
      position: relative;
      .rightList {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
        gap: 0;
        position: absolute;
        top: 0;
        transition: all 0.2s;
        .friendsItem {
          border-radius: 0.24rem;
          height: 3.3603rem;
          overflow: hidden;
          color: #000;
          font-weight: bold;
          position: relative;
          margin-bottom: 0.28rem;
          margin-right: 0.32rem;
          background-size: 100% 100% !important;

          .typeIcon {
            display: block;
            width: 0.75rem;
            height: 0.6rem;
            position: absolute;
            left: 0.5rem;
            top: 0.46rem;
          }
          .typeIcon_phone {
            top: 0.35rem;
            width: 0.42rem;
            height: 0.7rem;
          }
          .item_user {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            background: #262954;
            .pic {
              width: 1.9rem;
              height: 2.4rem;
              margin-left: 0.3rem;
              border: 0.04rem solid #B7B9C8;
              box-sizing: border-box;
              border-radius: 0.12rem;
              position: relative;
              .status {
                width: 0.8rem;
                height: 0.35rem;
                position: absolute;
                background: #FD8B19;
                border-radius: 0.05rem;
                position: absolute;
                left: -0.04rem;
                top: -0.04rem;
                font-size: 0.2rem;
                text-align: center;
                line-height: 0.45rem;
                font-weight: bold;
                color: #fff;
                letter-spacing: 0.03rem;
              }
            }
            img {
              display: block;
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
            .itemContent {
              width: 2.2rem;
              height: 2.7rem;
              margin-left: 0.25rem;
              display: flex;
              flex-direction: column;
              //justify-content: space-between;
              .title {
                //height: 1.62rem !important;
                font-size: 0.36rem;
                padding-top: 0.2rem;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                text-indent: 0.02rem ;
                margin-bottom: 0.1rem;
              }
              .subtitle {
                font-weight: 500;
                font-size: 0.26rem;
              }
              .describe {
                font-weight: 500;
                color: #B7C0F9;
                font-size: 0.24rem;
                margin-top: 0.2rem;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 4;
                -webkit-box-orient: vertical;
                letter-spacing: 0.01rem;
              }
            }
            div {
              line-height: 0.4rem;
              color: #E7E7EF;
              //color: #888;
            }
            div:nth-child(1) {
              font-size: 0.45rem;
              letter-spacing: 0.05rem;
              text-indent: -0.05rem;
            }
            div:nth-child(2) {
              font-size: 0.3rem;
              font-weight: 600;
            }
          }
        }
        .friendsItem:nth-child(2n+2) {
          margin-right: 0.2rem;
        }
        .friendsItem:nth-child(2n+1) {
          margin-left: 0.03rem;
        }
      }
    }
  }

  .order_more {
    width: 4.08rem;
    height: 7rem;
    margin-left: 0.26rem;
    border-radius: 0.24rem;
    .no_distribution {
      width: 100%;
      height: 100%;
      font-size: 0.32rem;
      text-align: center;
      line-height: 6rem;
      color: #B5C0FF;
    }
    .orderList {
      height: 7rem;
      overflow: hidden;
      position: relative;
      .list {
        width: 95%;
        margin-right: 0.2rem;
        position: absolute;
      }
      .listInfo {
        height: 3.3603rem;
        margin-bottom: 0.28rem;
        background: #262954;
        border-radius: 0.24rem;
        font-size: 0.28rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        .listContent {
          padding: 0.05rem 0.19rem;
          .status {
            color: #e77302;
            margin-bottom: 0.1rem;
            margin-top: 0.1rem;
            letter-spacing: 0.03rem;
          }
          .good,
          .price,
          .pay,
          .time {
            margin-bottom: 0.1rem;
            .title {
              color: #b6c0fd;
              margin-right: 0.06rem;
            }
          }
          .good,
          .price {
            display: flex;
          }
        }
      }
    }

  }
}

</style>
<style lang="less">
.consultServiceList {
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    // background: repeating-linear-gradient(to right, #c98693, #a95361);
    background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 35px;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
            to bottom,
            rgba(255, 255, 255, 1),
            rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        .title,
        .detail,
        .price {
          display: flex;
          font-size: 0.35rem;
          font-weight: bold;
          padding-bottom: 0.22rem !important;
          letter-spacing: 0.03rem;
        }
        .title {
          span {
            color: #f64e23;
            margin-left: 0.2rem;
          }
        }
        .price {
          span {
            color: #f64e23;
          }
        }
      }
      .sendCall {
      }
      .cancel,.sendCall {
        width: calc(100% - 0.8rem);
        height: 4.26rem;
        //top: 0.4rem;
        //left: 0.4rem;
        padding: 0;
        margin-top: 0.4rem;
        margin-left: 0.4rem;
        //position: relative;
        overflow: hidden;
        position: relative;
        .message_content {
          width: 100%;
          position: absolute;
          transition: all 0.3s;
          .message_item {
            display: flex;
            flex-direction: row;
            font-size: 0.4rem;
            margin-bottom: 0.14rem;
            letter-spacing: 0.03rem;
            .type {
              width: 1.9rem;
              font-size: 0.4rem;
            }
          }
          .orderItem {
            letter-spacing: 0.03rem;
            font-size: 0.4rem !important;
            div {
              margin-bottom: 0.2rem;
            }
          }
        }
        .scroll {
          top: 1.8rem !important;
          left: auto !important;
          right: 0.8rem !important;
        }
      }
      .cancel {
        .message_item {
          font-size: 0.34rem !important;
          .type {
            font-size: 0.34rem !important;
          }
        }
      }
      .sendCall {
        .message_item {
          flex-direction: column !important;
        }
      }
      .result {
        display: flex;
        justify-content: center !important;
        align-items: center !important;
      }
      .info {
        display: flex;
        justify-content: center !important;
        align-items: center !important;
        margin-top: -0.4rem;
        // background: url(http://192.168.6.212/ptyanglaotv/assets/zc_tan_new.png) no-repeat !important;
        .title {
          display: flex;
          flex-direction: column;
          align-items: center !important;
          text-align: center !important;

          // margin-top: 0.9rem;
          span {
            font-size: 0.5rem;
            color: #464646 !important;
            letter-spacing: 0.14rem;
          }
        }
        .phone {
          width: 7rem;
          text-align: center;
          position: absolute;
          top: 2.3rem;
          // left: 50%;
          // transform: translateX(-50%);
          font-size: 0.3rem;
          // color: yellow;
          color: #464646;
        }
        .tip {
          text-align: center;
          color: #3288dc;
          font-size: 0.4rem;
          margin-top: 1.2rem;
        }
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.1rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: -0.05rem;
          // background: repeating-linear-gradient(to right, #c98693, #a95361);
          background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
        }
      }
    }
  }

}


</style>
