<template>
  <div class="maze_container">
    <div class="news-list">
      <div class="news-list-left">
        <canvas id="mycanvas" class="canvas" width="510" height="510"></canvas>
      </div>
      <div class="rule">
        <h2>操作说明</h2>
        <h3>游戏目标：</h3>
        <p>使用你的上、下、左、右方向键来移动蓝色方块，红色方块是终点。</p>
        <h3>操作方式：</h3>
        <p>• 遥控器左按键：方块向左移动。</p>
        <p>• 遥控器右按键：方块向右移动。</p>
        <p>• 遥控器上按键：方块向上移动。</p>
        <p>• 遥控器下按键：方块向下移动。</p>
        <p>• 遥控器“返回”按键实现游戏暂停。</p>
        <p>• 遥控器“中心键”为确认按键。</p>
      </div>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :show-close="false"
      :close-on-click-modal="false"
      @opened="popupOpend"
      @close="popupClose"
      custom-class="operatePopup"
      :title="popupModule == 1 ? '操作' : '提示'"
    >
      <!--操作弹窗-->
      <div class="btnList" v-if="popupModule == 1">
        <div
          class="btnItem"
          :ref="item.ref"
          v-for="(item, index) in popupBtnList"
          :key="index"
        >
          <div class="box-btnItem" v-html="item.text"></div>
        </div>
      </div>

      <!--消息弹窗-->
      <div class="popupMessage" v-if="popupModule == 2">
        <div class="popupMessage" v-html="popupMessage"></div>
        <div class="popupBtnList">
          <div
            class="popupMessageBtn"
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
          >
            {{ item.text }}
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      flag: false,
      popupModule: 1, // 1、按钮弹窗  2、消息提示
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      aa: 16,
      chess: null,
      context: null,
      tree: [],
      isling: [],
      a: 0,
      b: 0,
      x: 20,
      y: 20,
      end: false,
      time: 0,
      i: 0,
      h: 0,
      m: 0,
      s: 0,
      ms: 0,
      req: null,
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '迷宫')
    this.$store.dispatch('index/setFocusDom', null)
  },
  mounted() {
    this.init()
    this.start()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
      },
      up: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
      },
      left: () => {
        if (this.dialogVisible) {
          return
        }
      },
      right: () => {
        if (this.dialogVisible) {
          return
        }
      },
      enter: () => {
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].fuc(
            this.popupBtnList[this.popupBtnNums]
          )
          return
        }
      },
      esc: () => {
        if (this.dialogVisible) {
          return
        }
        this.dialogVisible = true
        this.popupModule = 1
        this.popupBtnList = [
          {
            text: '继续游戏',
            ref: '',
            fuc: () => {
              this.dialogVisible = false
              this.flag = false
            },
          },
          {
            text: '重新开始',
            ref: '',
            fuc: () => {
              // this.init()
              this.renovates()
              this.dialogVisible = false
            },
          },
          {
            text: '退出游戏',
            ref: '',
            fuc: () => {
              this.$store.dispatch('index/setFocusDom', null)
              history.go(-1)
            },
          },
        ]
        this.popupBtnList[this.popupBtnNums].ref = 'active'
      },
    })
  },
  methods: {
    popupClose() {
      this.flag = false
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', null)
        setTimeout(() => {
          this.popupBtnList = []
          this.popupBtnNums = 0
          this.popupModule = 1
        }, 200)
      })
    },
    popupOpend() {
      this.flag = true
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        setTimeout(() => {
          document.getElementById('focus_border').style.borderColor = '#5472B0'
        }, 0)
        return
      })
    },
    init() {
      this.chess = document.getElementById('mycanvas')
      this.context = this.chess.getContext('2d')

      this.setupArrays()
      this.drawChessBoard()
      this.load()
      this.startGameLoop()
    },
    setupArrays() {
      for (let i = 0; i < this.aa; i++) {
        this.tree[i] = Array(this.aa).fill(-1)
      }
      for (let i = 0; i < this.aa * this.aa; i++) {
        this.isling[i] = Array(this.aa * this.aa).fill(-1)
      }
    },
    drawChessBoard() {
      for (let i = 0; i < this.aa + 1; i++) {
        this.context.strokeStyle = '#fff'
        this.context.moveTo(15 + i * 30, 15)
        this.context.lineTo(15 + i * 30, 15 + 30 * this.aa)
        this.context.stroke()
        this.context.moveTo(15, 15 + i * 30)
        this.context.lineTo(15 + 30 * this.aa, 15 + i * 30)
        this.context.stroke()
      }
    },
    getnei(a) {
      const x = parseInt(a / this.aa)
      const y = a % this.aa
      const mynei = []
      if (x - 1 >= 0) mynei.push((x - 1) * this.aa + y) // 上节点
      if (x + 1 < this.aa) mynei.push((x + 1) * this.aa + y) // 下节点
      if (y + 1 < this.aa) mynei.push(x * this.aa + y + 1) // 右节点
      if (y - 1 >= 0) mynei.push(x * this.aa + y - 1) // 左节点
      const ran = parseInt(Math.random() * mynei.length)
      return mynei[ran]
    },
    search(a) {
      if (this.tree[parseInt(a / this.aa)][a % this.aa] > 0) {
        return this.search(this.tree[parseInt(a / this.aa)][a % this.aa])
      } else {
        return a
      }
    },
    value(a) {
      if (this.tree[parseInt(a / this.aa)][a % this.aa] > 0) {
        return (this.tree[parseInt(a / this.aa)][a % this.aa] = this.value(
          this.tree[parseInt(a / this.aa)][a % this.aa]
        ))
      } else {
        return -this.tree[parseInt(a / this.aa)][a % this.aa]
      }
    },
    union(a, b) {
      const a1 = this.search(a)
      const b1 = this.search(b)
      if (a1 !== b1) {
        if (
          this.tree[parseInt(a1 / this.aa)][a1 % this.aa] <
          this.tree[parseInt(b1 / this.aa)][b1 % this.aa]
        ) {
          this.tree[parseInt(a1 / this.aa)][a1 % this.aa] +=
            this.tree[parseInt(b1 / this.aa)][b1 % this.aa]
          this.tree[parseInt(b1 / this.aa)][b1 % this.aa] = a1
        } else {
          this.tree[parseInt(b1 / this.aa)][b1 % this.aa] +=
            this.tree[parseInt(a1 / this.aa)][a1 % this.aa]
          this.tree[parseInt(a1 / this.aa)][a1 % this.aa] = b1
        }
      }
    },
    drawline(a, b) {
      const x1 = parseInt(a / this.aa)
      const y1 = a % this.aa
      const x2 = parseInt(b / this.aa)
      const y2 = b % this.aa
      const x3 = (x1 + x2) / 2
      const y3 = (y1 + y2) / 2
      this.context.strokeStyle = 'white'
      if (x1 - x2 === 1 || x1 - x2 === -1) {
        this.context.clearRect(29 + x3 * 30, y3 * 30 + 16, 2, 28)
      } else {
        this.context.clearRect(x3 * 30 + 16, 29 + y3 * 30, 28, 2)
      }
    },
    startGameLoop() {
      while (this.search(0) !== this.search(this.aa * this.aa - 1)) {
        const num = parseInt(Math.random() * this.aa * this.aa)
        const neighbour = this.getnei(num)
        if (this.search(num) === this.search(neighbour)) {
          continue
        } else {
          this.isling[num][neighbour] = 1
          this.isling[neighbour][num] = 1
          this.drawline(num, neighbour)
          this.union(num, neighbour)
        }
      }
    },
    load() {
      this.a = this.aa * 30 - 10
      this.b = this.aa * 30 - 10
      this.context.fillStyle = '#2bb4f9'
      this.context.fillRect(this.x, this.y, 20, 20)
      this.context.fillStyle = 'red'
      this.context.fillRect(this.a, this.b, 20, 20)
      // this.chess.addEventListener('keydown', this.doKeyDown, true)
      this.chess.focus()
      // window.addEventListener('keydown', this.doKeyDown, true)
      document.addEventListener('keydown', this.doKeyDown)
    },
    doKeyDown(e) {
      if (!this.flag) {
        const keyID = e.keyCode ? e.keyCode : e.which
        if (this.i === 1) {
          if (keyID === 38) {
            // 上键的移动方向
            if (
              this.y - 30 >= 0 &&
              this.isling[((this.x - 20) / 30) * this.aa + (this.y - 20) / 30][
                ((this.x - 20) / 30) * this.aa + (this.y - 20) / 30 - 1
              ] === 1
            ) {
              this.clearCanvas()
              this.y -= 30
              this.context.fillRect(this.x, this.y, 20, 20)
              e.preventDefault()
              this.gameover()
              this.show()
            }
          }
          if (keyID === 39) {
            // 右键的移动方向
            if (
              this.x + 30 <= 15 + 30 * this.aa &&
              this.isling[((this.x - 20) / 30) * this.aa + (this.y - 20) / 30][
                ((this.x - 20) / 30) * this.aa + (this.y - 20) / 30 + this.aa
              ] === 1
            ) {
              this.clearCanvas()
              this.x += 30
              this.context.fillRect(this.x, this.y, 20, 20)
              e.preventDefault()
              this.gameover()
              this.show()
            }
          }
          if (keyID === 40) {
            // 下键的移动方向
            if (
              this.y + 30 <= 15 + 30 * this.aa &&
              this.isling[((this.x - 20) / 30) * this.aa + (this.y - 20) / 30][
                ((this.x - 20) / 30) * this.aa + (this.y - 20) / 30 + 1
              ] === 1
            ) {
              this.clearCanvas()
              this.y += 30
              this.context.fillRect(this.x, this.y, 20, 20)
              e.preventDefault()
              this.gameover()
              this.show()
            }
          }
          if (keyID === 37) {
            // 左键的移动方向
            if (
              this.x - 30 >= 0 &&
              this.isling[((this.x - 20) / 30) * this.aa + (this.y - 20) / 30][
                ((this.x - 20) / 30) * this.aa + (this.y - 20) / 30 - this.aa
              ] === 1
            ) {
              this.clearCanvas()
              this.x -= 30
              this.context.fillRect(this.x, this.y, 20, 20)
              e.preventDefault()
              this.gameover()
              this.show()
            }
          }
        }
      }
    },
    clearCanvas() {
      this.context.clearRect(this.x - 2, this.y - 2, 25, 25)
      this.context.fillStyle = '#2bb4f9'
    },
    gameover() {
      if (this.x >= this.a && this.y >= this.b) {
        this.end = true
        this.flag = true
        setTimeout(() => {
          this.popupModule = 1
          this.dialogVisible = true
          this.popupBtnList = [
            {
              text: '再来一局',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            },
            {
              text: '退出游戏',
              ref: '',
              fuc: () => {
                this.$store.dispatch('index/setFocusDom', null)
                history.go(-1)
              },
            },
          ]
          this.popupBtnNums = 0
          this.popupBtnList[this.popupBtnNums].ref = 'active'
        }, 200)
      }
    },
    show() {
      if (this.end) {
        // this.aa += 2
        if (this.aa === 20) {
          this.stop()
        } else {
          this.end = false
          this.context.clearRect(0, 0, 510, 510)
          this.setupArrays()
          this.drawChessBoard()
          this.startGameLoop()
          this.a = this.aa * 30 - 10
          this.b = this.aa * 30 - 10
          this.x = 20
          this.y = 20
          this.load()
        }
      }
    },

    reset() {
      this.i = 1
    },
    start() {
      this.i = 1
    },
    stop() {
      this.i = 0
      clearInterval(this.time)
    },
    toDub(n) {
      return n < 10 ? '0' + n : '' + n
    },
    toDubms(n) {
      return n < 10 ? '00' + n : n < 100 ? '0' + n : '' + n
    },
    renovates() {
      // 重置游戏状态
      this.end = false
      this.flag = false
      this.i = 1

      // 清空画布
      this.context.clearRect(0, 0, 510, 510)

      // 重置位置
      this.x = 20
      this.y = 20

      // 重新初始化游戏
      this.setupArrays()
      this.drawChessBoard()
      this.startGameLoop()

      // 重新设置起点和终点
      this.a = this.aa * 30 - 10
      this.b = this.aa * 30 - 10

      // 绘制起点和终点
      this.context.fillStyle = '#2bb4f9'
      this.context.fillRect(this.x, this.y, 20, 20)
      this.context.fillStyle = 'red'
      this.context.fillRect(this.a, this.b, 20, 20)
    },
  },
}
</script>

<style lang="less" scoped>
.maze_container {
  position: relative;
  body {
    margin: 0;
    padding: 0;
    font-family: 'Microsoft YaHei', '微软雅黑', 'consolas';
    background-attachment: fixed;
  }
  a {
    text-decoration: none;
    color: #000;
  }
  .container {
    width: 10rem;
    margin: 0 auto;
  }
  .navbar .navbar-content-1 a {
    float: left;
    color: #fff;
    line-height: 0.5rem;
    display: inline-block;
    width: 0.9rem;
    white-space: nowrap;
    text-align: left;
  }
  .navbar .navbar-content-2 a {
    float: right;
    color: #fff;
    line-height: 0.5rem;
    display: inline-block;
    width: auto;
    white-space: nowrap;
    text-align: right;
  }
  .navbar .navbar-content a {
    float: left;
    color: #fff;
    line-height: 0.5rem;
    display: inline-block;
    width: 0.9rem;
    white-space: nowrap;
    text-align: center;
  }
  .navbar .navbar-content a:hover {
    color: #ccc;
  }
  .news-list {
    position: absolute;
    top: -0.7rem;
    left: 5rem;
    border-radius: 0.15rem;
    padding: 0.3rem 0.2rem;
    min-height: 3rem;
  }
  .about .about-des {
    border-left: 0.05rem solid #abc;
    margin-top: 0.15rem;
  }
  .about .about-des p {
    padding-left: 0.1rem;
    line-height: 0.28rem;
    text-indent: 0.02rem;
  }
  .news-list-left {
    width: 7.5rem;
    height: 7.5rem;
    background: url('../../../assets/connectFour_state.png') no-repeat;
    background-size: 100% 100%;

    .canvas {
      display: block;
      // margin: 0.5rem auto;
      margin: 0 !important;
      box-shadow: none !important;
      width: 100% !important;
      height: 100% !important;
    }
  }
  .rule {
    position: absolute;
    width: 3.6rem;
    // height: 100%;
    right: -4rem;
    top: 0.35rem;
    padding: 0.2rem;
    // border-radius: 0.05rem;
    background: url('../../../assets/connectFour_state.png') no-repeat;
    background-size: 100% 100%;
    h3 {
      margin-top: 0.15rem;
      margin-bottom: 0.08rem;
    }
    p {
      font-size: 0.24rem;
    }
  }
}
</style>
<style lang="less">
.maze_container {
  .el-dialog {
    //width: fit-content;
    //margin-top: 0 !important;
    //top: 50%;
    //transform: translateY(-50%);
    //border-radius: 0.16rem;
    .el-dialog__header {
      .el-dialog__title {
        font-size: 0.5rem !important;
      }
    }
    .el-dialog__body {
      .btnList {
        padding: 0.2rem 0;

        .btnItem {
          width: 8.75rem;
          height: 1rem;
          line-height: 1.08rem;
          background: #5472b0;
          //color: #fff;
          border-radius: 0.2rem;
          margin-bottom: 0.3rem;
          text-align: center;
          font-weight: bold;
          font-size: 0.5rem;
          position: relative;
          //text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.3);
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          img {
            width: 0.48rem;
            height: 0.48rem;
            position: absolute;
            top: 50%;
            left: 2.2rem;
            transform: translateY(-45%);
          }
          .box-btnItem {
            background: linear-gradient(to bottom, #fdfeff 30%, #bed1fb 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            //text-shadow: 0rem 0.02rem 0.05rem rgba(0, 0, 0, 0.5);
          }
        }
        .btnItem:last-child {
          margin-bottom: 0;
        }
      }
      .popupMessage {
        padding: 0.2rem 0;
        .popupMessage {
          //line-height: 0.4rem;
          text-align: center;
          //font-size: 0.24rem;
          font-size: 0.45rem;
          margin-bottom: 0.3rem;
        }
        .popupMessageBtn {
          width: 8.75rem;
          height: 1rem;
          //width: 20vw;
          //height: 0.6rem;
          //line-height: 0.68rem;
          line-height: 1rem;
          background: #5472b0;
          font-weight: bold;
          text-align: center;
          //font-size: 0.24rem;
          font-size: 0.5rem;
          color: #fff;
          border-radius: 0.16rem;
          margin: 0 auto;
          margin-top: 0.2rem !important;
        }
        .popupMessageBtn:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>