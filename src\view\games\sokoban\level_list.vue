<template>
  <div class="sokoban_level">
      <div class="level_list" :key="leftNums">
        <div class="level_item" v-for="(item,index) in levelsNums" :key="index" :ref="item.ref" :style="{background: pass_level_list.includes(index) ? '#5FC5DC' : ''}">
          <div>
            {{index + 1}}
          </div>
        </div>
      </div>
  </div>
</template>

<script>

import levels from '@/view/games/sokoban/js/mapdata100'

export default {
  name:'sokoban',
  components: {
  },
  data() {
    return {
      levelsNums: 0,
      leftNums: 0,
      pass_level_list: []
    };
  },
  created() {
    this.$store.dispatch('index/setMainTitle', '关卡选择')

    if (localStorage.getItem('sokoban_pass_level_list')) {
      this.pass_level_list = JSON.parse(localStorage.getItem('sokoban_pass_level_list'))
    }
  },
  computed: {},
  watch: {},
  mounted() {


    this.getData()
    this.fuc.KeyboardEvents({
      down:()=>{
        if (this.levelsNums.length > 0) {
          if (this.levelsNums[this.leftNums + 15]) {
            this.levelsNums[this.leftNums].ref = ""
            this.leftNums += 15
            this.levelsNums[this.leftNums].ref = "active"
          } else if(this.leftNums + (this.levelsNums.length % 15) < this.levelsNums.length) {
            this.levelsNums[this.leftNums].ref = ""
            this.leftNums = this.levelsNums.length - 1
            this.levelsNums[this.leftNums].ref = "active"
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      up:()=>{
        if (this.levelsNums.length > 0) {
          if (this.levelsNums[this.leftNums - 15]) {
            this.levelsNums[this.leftNums].ref = ""
            this.leftNums -= 15
            this.levelsNums[this.leftNums].ref = "active"
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      left:()=>{
        if (this.levelsNums.length > 0) {
          if (this.leftNums % 15 != 0) {
            this.levelsNums[this.leftNums].ref = ""
            this.leftNums --
            this.levelsNums[this.leftNums].ref = "active"
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      right:()=>{
        if (this.levelsNums.length > 0) {
          if (this.leftNums % 15 != 14) {
            this.levelsNums[this.leftNums].ref = ""
            this.leftNums ++
            this.levelsNums[this.leftNums].ref = "active"
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter:()=>{
        if (this.levelsNums.length > 0) {
          localStorage.setItem('sokoban_level_now', this.leftNums)
          this.$store.dispatch('index/setFocusDom', null);
          history.go(-1)
        }
      },
      esc:()=>{
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      }
    })


  },
  methods: {
    getData() {

      this.levelsNums = JSON.parse(JSON.stringify(levels))
      this.levelsNums.map((item,index)=>{
        item.level = index;
        item.ref = ''
      })

      if (localStorage.getItem('sokoban_level_now')) {
        this.leftNums = Number(localStorage.getItem('sokoban_level_now'))
      }

      this.levelsNums[this.leftNums].ref = "active"

      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active)
      })
    }

  },
  destroyed() {

  },
  beforeDestory() {
  },
}
</script>
<style lang='less' scoped>
  .sokoban_level {
    height: 7rem;
    border: 1px solid transparent;
    .level_list {
      width: 94%;
      height: 100%;
      margin: auto;
      margin-top: -0.2rem;

      .level_item {
        float: left;
        width: 0.784rem;
        height: 0.8rem;
        line-height: 0.8rem;
        border: 0.04rem solid #5FC5DC;
        border-radius: 0.14rem;
        margin-bottom: 0.2rem;
        margin-right: 0.2rem;
        font-weight: bold;
        font-size: 0.28rem;
        text-align: center;
      }
      .level_item:nth-child(15n) {
        margin-right: 0;
      }
    }
  }
</style>
<style lang="less">
  .sokoban_level {

  }

</style>
