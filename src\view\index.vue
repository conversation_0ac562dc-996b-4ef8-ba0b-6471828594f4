<template>
  <div class='index' id="index">
    <div class="index_left">
      <div class="indexItem"  :ref="item.ref"  :data="item.ref" :style="{ width: column == 2 ? '3.53rem': '2.262rem' }" v-for="(item,index) in iconList" :key="index">

        <div :class="['background', { active: item.ref }]" :style="{ background: 'url('+item.css+')' }">
          <img :src="item.icon" alt="">
        </div>
        <div :class="['background', { active: !item.ref }]" :style="{ background: 'url('+item.css_free+')' }">
          <img :src="item.icon_free" alt="">
        </div>

        <div class="remark"  v-if="item.remark">
          {{item.remark}}
        </div>
        

        <div class="iconTitle">{{item.title}}</div>
        <div class="iconPinyin">{{ item.pinyin }}</div>

        <transition name="fade">
          <div class="readRed" v-if="!item.is_read"></div>
        </transition>

      </div>
    </div>

    <div class="index_right">
      <div class="right_item" :ref="rightIcon.length > 0 ? rightIcon[0].ref : '' " v-for="(item,index) in 1" :key="index" :data="rightIcon[0] ? rightIcon[0].ref : ''">
<!--        默认-->
        <transition name="fade" >
          <div class="defaultBg" style="width: 100%;height: 100%" v-if="adHomeType == 0">
            <img :src="require('@/assets/loading.gif')" alt="">
          </div>
        </transition>
<!--        在线叫车1-->
        <transition name="fade" >
          <div class="map_onlineCar" style="width: 100%;height: 100%" v-if="adHomeType == 1">
            <GaoDeMap  mapKey="6b068e4709952491c7604b70eb153e01" style="transform:translate3d(0,0,0)" :mapLoad="mapLoad" :center="markers[0].position" :markers="markers" :polyline="polyline"></GaoDeMap>
            <transition name="fade" >
              <div v-if="driverInfoShow">
                <div class="driver_line">
                  <div>{{driver_info}}</div>
                </div>
                <div class="driver_user_info">
                  <div class="sign">
                    {{$store.getters.getOrderInfo && $store.getters.getOrderInfo.data[0].driver_name}}
                    {{$store.getters.getOrderInfo && $store.getters.getOrderInfo.data[0].driver_phone}}
                    {{$store.getters.getOrderInfo && $store.getters.getOrderInfo.data[0].plate}}
                  </div>
                </div>
              </div>
            </transition>
          </div>
        </transition>

<!--        未接来电2-->
        <transition name="fade">
          <div class="callOut_remind" v-if="adHomeType == 2">
            <div class="remind_content">
              <div class="title">
                未接来电提醒
              </div>
              <div class="callOut_info">
                <div class="avatar">
                  <div class="avatar_pic">
                    <img ref="avatarPic" @error="avatarError" :src="rightIcon[0].friend_avatar" alt="">
                  </div>
                  <span>{{rightIcon[0].friend_title}}</span>
                </div>

                <div class="created_time">
                  {{rightIcon[0].created_at}}
                </div>


              </div>
            </div>
          </div>
        </transition>

<!--        预约挂号3-->
        <transition name="fade">
          <div class="hospital_remind" v-if="adHomeType == 3">
            <div class="remind_content">
              <el-carousel trigger="none" :indicator-position="rightIcon.length > 1 ? '' : 'none'" height="100%" arrow="never" :interval="adSwiperInter" @change="swiperChange">
                <el-carousel-item v-for="(item,index) in rightIcon" :key="index">
                  <div class="title">
                    预约挂号提醒
                  </div>
                  <div class="hospital_info">
                    <div>
                      <span>预约时间</span><strong>:</strong>
                      <span>{{ item.yy_date }} {{ item.week_name }} {{ item.morning_afternoon_name }}</span>
                    </div>
                    <div>
                      <span>医院</span><strong>:</strong>
                      <span>普陀中心医院</span>
                    </div>
                    <div>
                      <span>科室</span><strong>:</strong>
                      <span>{{ item.ks_name }}</span>
                    </div>
                    <div>
                      <span>类型</span><strong>:</strong>
                      <span>专家门诊</span>
                    </div>
                    <div>
                      <span>医生</span><strong>:</strong>
                      <span>{{ item.doctor_name }}</span>
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>
        </transition>

<!--        天气信息4-->
        <transition name="fade">
          <div class="weather_remind" :style="{background: 'url('+futurePicList[rightIcon[0].weatherType-1].index +')'}"  v-if="adHomeType == 4">
            <div class="remind_content">
              <div class="title">
<!--                .split('区')[0]-->
                <p>{{$store.getters.getUserInfo.fk_province_city_name ? '上海 ' + $store.getters.getUserInfo.fk_province_city_name.split(',')[0] : '中国'}}</p>
                <img :src="require('@/assets/position.png')" style="object-fit: contain;margin-top: 0.02rem;" alt="">
              </div>
              <div class="weather_info">
                <div class="weather_top">
                  <div class="weather_left">
                    <img :src="require('@/assets/weather/bigIcon/icon_'+weatherInfo.weatherType +'.png')" alt="">
                    <span>{{ rightIcon[0].temperature.replace('~','-').replace('℃','') }}</span>
                  </div>
                  <div class="weather_right">
                    <span>{{weatherInfo.temperature.split('℃')[0]}}</span>
                    <span>℃</span>
                  </div>

                </div>

                <div class="weather_bottom">
                  <div>
                    {{rightIcon[0].weather}}
                  </div>|
                  <div>
                    空气<span :style="{background: aqiInfo(Number(rightIcon[0].aqi)).bg,borderRadius: '0.18rem',padding:'0.05rem 0.15rem',fontSize: '0.32rem',marginLeft: '0.1rem'}"
                  >{{rightIcon[0].aqi}}{{aqiInfo(Number(rightIcon[0].aqi)).text}} </span>
                  </div>|
                  <div>
                    {{rightIcon[0].wind}}
                  </div>
                </div>

              </div>
            </div>




          </div>
        </transition>

<!--        图文5-->
        <transition name="fade">
          <div class="ad" v-if="adHomeType == 5">
            <div class="remind_content">
              <el-carousel  trigger="none" height="100%"  :indicator-position="rightIcon.length > 1 ? '' : 'none'" arrow="never" :interval="adSwiperInter" @change="swiperChange">
                  <el-carousel-item v-for="(item,index) in rightIcon" :key="index" :style="{background: 'url('+item.url +')'}">
                    <div class="ad_content" v-html="item.content"></div>
                  </el-carousel-item>
              </el-carousel>
            </div>
          </div>
        </transition>
      </div>
    </div>

    <el-dialog
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        custom-class="operatePopup"
        :title="popupModule == 1 ? '切换用户' : popupModule == 3 ? '用户协议及隐私政策' : '提示'"
    >
      <!--呼叫弹窗-->
      <div class="callMyFriend" v-if="popupModule == 1" :style="{height: popupBtnList.length > 2 ? '3rem' : '2.8rem'}">
<!--        <div class="popupTitle">切换用户</div>-->
        <div class="userList" style="left: 0rem" v-if="popupBtnList.length > 0">
          <div class="userItem" :ref="item.ref" v-for="(item,index) in popupBtnList" :key="index" :data="swiperIndex">
            <img @error="defImg" :src="item.avatar" alt="">
            <span>{{ item.text }}</span>
          </div>
        </div>

        <div class="pointer" v-if="popupBtnList.length > 2">
          <div v-for="(item,index) in popupBtnList.length - 1"  :key="index" :style="{width: pointerIndex == index ? '0.2rem': '0.08rem' }"></div>
        </div>


      </div>

      <!--消息弹窗-->
      <div class="popupMessage" v-if="popupModule == 2">
        <div class="popupMessage" v-html="popupMessage"></div>
        <div
            class="popupMessageBtn"
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
        >
          {{ item.text }}
        </div>
      </div>

      <!--隐私政策-->
      <div class="popupMessage" v-if="popupModule == 3">
        <div class="popupMessage" v-html="popupMessage"></div>
        <div class="messageBtnList">
          <div
              class="popupMessageBtn"
              :ref="item.ref"
              v-for="(item, index) in popupBtnList"
              :key="index"
          >
            {{ item.text }}
          </div>
        </div>

      </div>
    </el-dialog>


    <div :style="{display: 'flex',flexDirection: 'row',alignItems: 'center',position: 'absolute',right: '1.2rem',bottom: '0.3rem'}">
      <div class="service_call" v-if="$store.getters.getUserInfo.hotline">
        <span>客服热线:</span>
        <span>{{$store.getters.getUserInfo.hotline}}</span>
      </div>

      <div class="xiaochengxu_logo" v-if="wxLogoShow"  :style="{marginLeft: '0.4rem'}">
        <img @error="logoError" :src="xiaochengxuLogo" alt="">
      </div>


    </div>


  </div>
</template>

<script>

import {
  GetIndexMessage,
  GetIndexPageICon,
  GetIndexICon,
  getDepartmentList,
  SetLogRead,
  GetFriendIsCall,
  GetZegoToken,
  GetPrivacyPolicy,
  SetPrivacyPolicy,
  GetUserInfo,
  youAreMyFriend,
  GetCallTime,
  GetCustomerService
} from "@/api/index";
import Axios from 'axios'
import GaoDeMap from "@/components/MapContainer"
import media from "@/utils/mediaPlayer.js";
import websocketData from "@/utils/websocketData";
import pinyin from '@/utils/pinYin'
import store from "@/store";

const swiperInter = 10000
export default {
name:'index',
components: {
  GaoDeMap
},
data() {
    return {
      wxLogoShow: true,
      timeNum: new Date().getTime(),
      isReady: false,
      column: 3,
      xiaochengxuLogo: null,
      zegoToken: null,
      firstSend: true,
      nowTime: null,
      adHomeType: 0,  // 1 在线叫车  2 未接来电  3 预约挂号  4 天气信息  5 图文 6 视频轮播
      adHomeData: null,
      adSwiperInter: 0,
      MP:null,
      pointerIndex: 0,
      swiperIndex: 0,
      timer: null,
      driver_info: '',
      driverInfoShow: false,
      markers:[
        {  // 司机位置&&中心点
          id: 1,
          icon: require('@/assets/99_taxi_car.png'),
          width: 30,
          height: 50,
          position: [121.473667, 31.230525],
          autoRotation: true,
          angle:0,
          // offset:[0.5,0.5],
          // autoRotation: true,
        },
        {  // 上车位置
          id: 2,
          width: 30,
          height: 50,
          position: [121.473667, 31.28],
          icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
          autoRotation: true,
          angle:0,

          // offset:[0.5,0.5],
          label: {
            width: 50,
            height: 30,
            borderWidth: 1,
            borderRadius: 10,
            bgColor: '#ffffff',
            content: "上车点"
          }
        },
      ],
      polyline: {
        path: [],
        showDir: true, //配置项：带箭头的线
        strokeColor:'#6ACCBF',
        strokeWeight: 10, //配置项：线的宽度
      },
      iconList:[],
      rightIcon:[],
      nums: 0,
      nextNums: -2,
      dialogVisible: false,
      popupModule: 1, // 1、好友弹窗  2、消息提示
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [
        {
          text: '呼叫好友',
          ref: '',
          fuc: this.callMyfriend,
        },
        {
          text: '删除好友',
          ref: '',
          fuc: this.delectMyfriendPopup,
        },
      ],
      futurePicList:[
        {
          index: require('@/assets/weather/current/index_bg_1.png'),
          bg: require('@/assets/weather/future/bg_1.png')
        },{
          index: require('@/assets/weather/current/index_bg_2.png'),
          bg: require('@/assets/weather/future/bg_2.png')
        },{
          index: require('@/assets/weather/current/index_bg_3.png'),
          bg: require('@/assets/weather/future/bg_3.png')
        },{
          index: require('@/assets/weather/current/index_bg_4.png'),
          bg: require('@/assets/weather/future/bg_4.png')
        },{
          index: require('@/assets/weather/current/index_bg_5.png'),
          bg: require('@/assets/weather/future/bg_5.png')
        },{
          index: require('@/assets/weather/current/index_bg_6.png'),
          bg: require('@/assets/weather/future/bg_6.png')
        },
      ],
      weatherInfo: null,
      playTimer: null,
      customerInfo: null
    };
},
created() {
    this.$store.state.app.media = null
    sessionStorage.removeItem('privacy_policy')
    sessionStorage.removeItem('supplierFromIndex')
    // !tag 民政版（3列）  否则 大众版（2列）
    this.column = this.$store.getters.getUserInfo.box_type == 1 ? 2 : 3

    this.xiaochengxuLogo = process.env.VUE_APP_API + '/public/storage/uploaded/2024_04/sdlkfjkljfjdslkfjldskjfl.png'
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle','')
    sessionStorage.removeItem('redirectInfo')
},
computed: {
    aqiInfo(){
      return (num)=>{
        let obj = {
          text: '',
          bg:''
        }
        if (num > 50 && num < 101) {
          obj.text = "良";
          obj.bg = "#FFFF00";
        } else if (num > 100 && num < 151) {
          obj.text = "三级轻度污染";
          obj.bg = "#FF7E00";
        } else if (num > 150 && num < 201) {
          obj.text = "四级轻度污染";
          obj.bg = "#FF0000";
        } else if (num > 200 && num < 301) {
          obj.text = "五级轻度污染";
          obj.bg = "#99004C";
        } else if (num > 300) {
          obj.text = "六级轻度污染";
          obj.bg = "#7E0023";
        } else {
          obj.text = "优";
          obj.bg = "#00B358";
        }
        return obj
      }
    }
  },
watch: {
  "$store.state.app.settingShow": {
    handler() {
      // if (this.$store.state.app.settingShow) {
      //   this.$store.state.app.media.stop()
      // } else {
      //   this.playVideo()
      // }
    },
    deep: true
  },
  "$store.getters.getOrderInfo": {
    handler(){
      if (this.$store.getters.getOrderInfo) {
          let data  = this.$store.getters.getOrderInfo.data[0]
          this.markers[0].position = [Number(data.driver_lon),Number(data.driver_lat)]
          this.markers[1].position = [Number(data.custom_lon),Number(data.custom_lat)]

          this.markers[0].icon = require('@/assets/'+ data.taxi_company_type +'_taxi_car.png')


          if (data.status == 4) { // 司机已接单
            this.driver_info = data.driver_name + "车辆正在赶来,距离你" + data.driver_distance + '公里，预计' + data.driver_arrive + '分钟后到达'
          } else if (data.status == 101) {  // 司机到达起点位置
            this.driver_info = data.driver_name + "到达出发地址,请尽快上车"
          }

          if ([1,2,3,102,103].includes(this.$store.getters.getOrderInfo.data[0].status)) {
            this.$store.state.app.adHomeType = this.adHomeType  = this.adHomeType
            this.getAdHomeTime(this.nowTime)
          } else {
            this.polyline.path = this.$store.getters.getOrderInfo.route.paths[0].steps
            this.$store.state.app.adHomeType = this.adHomeType = 1
            this.getAdHomeTime(this.nowTime)
          }
      } else {
        this.$store.state.app.adHomeType = this.adHomeType = this.adHomeType
        this.getAdHomeTime(this.nowTime)
      }
    },
    deep: true,
    immediate: true
  },
  "$store.state.app.selectUserIndex":{
    handler(){
      this.fuc.callTaxi(this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].phone,(res)=>{
      },(err=>{

      }))
      this.getData()

    },
    deep: true,
  },
  "$store.state.app.timeInfo" :{
    handler(date, oldVal) {
      if (date) {
        this.nowTime = date.timeDate
        this.getAdHomeTime(this.nowTime)
      }
    },
    deep: true, // 开启深度监听
    immediate: true
  },
  "$store.state.app.webscoketMessage":{
    handler(res, oldVal) {
      if (res.code >= 200 && res.code < 300) {
        switch (res.code) {
            // 好友不在线
          case 201:
            // 小程序一次性订阅通知失败
          case 204:
            this.popupMessage = res.msg + '<br/>' + '请稍后再拨'
            if (res.code == 204) {
              this.popupMessage = `该用户未授权来电通知,<br/>暂时无法通知您的来电信息!`
            }
            this.popupModule = 2
            // this.$store.dispatch('index/setFocusDom', null);
            this.popupBtnList = [
              {
                text: '关闭',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  this.popupBtnList[this.popupBtnNums].ref = ''
                  this.rightIcon[0].ref = "active"
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                  })
                },
              },
            ]
            this.rightIcon[0].ref = ""
            setTimeout(() => {
              this.dialogVisible = true
            }, 300)

            break
            // 好友在线
          case 202:
            this.sendZegoCall(res.data.room_id,res.data.user_id,this.zegoToken)
            break;
            // 右侧接口需要刷新 （刷新右屏数据）
          case 260:
            this.getIndexMessagePage()
            break;
            // 有新的未接来电（刷新小红点）
          case 261:
            this.getData()
            this.getIndexMessagePage()
            break;
        }
      }
    },
    deep: true, // 开启深度监听
  },
  "dialogVisible"() {
    if (this.dialogVisible) {
      if (this.$store.state.app.media) {
        this.$store.state.app.media.stop()
      }
    } else {
      this.getIndexMessagePage()
    }
  }
},
mounted() {
  sessionStorage.removeItem('serviceFocus')
  this.$watch(
      () => this.$store.state.app.onLineList[1].show,
      (newVal, oldVal) => {
        if (newVal) {
          this.getData()
        }
      }
  );
    this.getData()

    this.fuc.getPointTotals()

    this.adSwiperInter = swiperInter
    this.fuc.KeyboardEvents({
        down:()=>{
          if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
            return
          }
          if (this.dialogVisible) {
            if (this.popupBtnNums > 0) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums--
              this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (this.nums > -1) {
            if (this.iconList[this.nums + this.column]) {
              this.iconList[this.nums].ref = ""
              this.nums += this.column
              this.iconList[this.nums].ref = "active"
              /**
               * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
               */
              // sessionStorage.setItem('indexFocus',this.nums)
              sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
            } else {
              if (this.nums  < (this.iconList.length - this.iconList.length % this.column) && this.iconList.length % this.column != 0) {
                this.iconList[this.nums].ref = ""
                this.nums = this.iconList.length - 1
                this.iconList[this.nums].ref = "active"
                /**
                 * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
                 */
                // sessionStorage.setItem('indexFocus',this.nums)
                sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
              }
            }
          } else
          if(this.nums == -1){
            if (this.$store.getters.focus_dom.className.indexOf('weather') > -1 || this.$store.getters.focus_dom.className.indexOf('ai_bot') > -1) {
              if (this.rightIcon.length > 0) {
                sessionStorage.removeItem('settingBtn')
                sessionStorage.removeItem('selectUser')
                sessionStorage.removeItem('weatherBtn')
                sessionStorage.removeItem('pointBtn')
                sessionStorage.removeItem('aiBotBtn')

                if (this.rightIcon.length > 0) {
                  this.rightIcon[0].ref = "active"
                  this.$forceUpdate()
                  if (!sessionStorage.getItem('indexFocus')) {
                    /**
                     * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
                     */
                    // sessionStorage.setItem('indexFocus',this.nums)
                    sessionStorage.setItem('indexFocus', -2)
                  }
                } else {
                  this.iconList[this.nums].ref = "active"
                  this.$forceUpdate()
                  sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
                }
                this.nums = this.nextNums = -2
                this.$nextTick(()=>{
                  this.$store.dispatch('index/setFocusDom',this.$refs.active[0] ? this.$refs.active[0] : this.$refs.active);
                })


              }
              return
            }

            this.iconList[this.nextNums].ref='active';
            this.nums = this.nextNums
            /**
             * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
             */
            // sessionStorage.setItem('indexFocus',this.nums)
            sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
          }
          else{
            if (this.rightIcon.length > 0) {
              this.rightIcon[0].ref = "active"
              this.$forceUpdate()
              if (!sessionStorage.getItem('indexFocus')) {
                /**
                 * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
                 */
                // sessionStorage.setItem('indexFocus',this.nums)
                sessionStorage.setItem('indexFocus', -2)
              }
            } else {
              this.iconList[this.nums].ref = "active"
              this.$forceUpdate()
              sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
            }
          }
          this.$nextTick(()=>{
            this.$store.dispatch('index/setFocusDom',this.$refs.active[0] ? this.$refs.active[0] : this.$refs.active);
          })
          sessionStorage.removeItem('settingBtn')
          sessionStorage.removeItem('selectUser')
          sessionStorage.removeItem('weatherBtn')
          sessionStorage.removeItem('pointBtn')
          sessionStorage.removeItem('aiBotBtn')
        },
        up:()=>{
          if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
            return
          }
          if (this.dialogVisible) {
            if (this.popupBtnNums > 0) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.popupBtnNums--
              this.popupBtnList[this.popupBtnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
            return
          }
          if (this.nums > -1 && this.iconList[this.nums]) {
            if (this.nums < this.column) {
              sessionStorage.removeItem('settingBtn')
              sessionStorage.removeItem('selectUser')
              sessionStorage.removeItem('weatherBtn')
              sessionStorage.removeItem('indexFocus')
              sessionStorage.removeItem('pointFocus')
              sessionStorage.removeItem('aiBotFocus')
              this.iconList[this.nums].ref='';
              this.nextNums = this.nums
              this.nums = -1
              // this.$nextTick(()=>{
              //   this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[0].dom);
              //   sessionStorage.setItem('pointBtn',true)
              // })
              // return
              // if (this.$store.state.app.onLineList[0].show != false) {
              //   this.$nextTick(()=>{
              //     this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[0].dom);
              //     sessionStorage.setItem('settingBtn',true)
              //   })
              // } else
              if (this.$store.getters.getUserInfo.oldsters.length > 1) {
                this.$nextTick(()=>{
                  this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[0].dom);
                  sessionStorage.setItem('selectUser',true)
                })
              } else {
                this.$nextTick(()=>{
                  this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[1].dom);
                  sessionStorage.setItem('pointBtn',true)
                })
              }
            }
            else
            if (this.iconList[this.nums - this.column]) {
              this.iconList[this.nums].ref = ""
              this.nums -= this.column
              this.iconList[this.nums].ref = "active"
              /**
               * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
               */
              // sessionStorage.setItem('indexFocus',this.nums)
              sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
              this.$nextTick(()=>{
                this.$store.dispatch('index/setFocusDom',this.$refs.active);
              })
            }
          }  else
          if (this.nums == -2) {
            this.nums = -1
            this.rightIcon[0].ref = ""
            this.$forceUpdate()
            sessionStorage.removeItem('settingBtn')
            sessionStorage.removeItem('selectUser')
            sessionStorage.removeItem('weatherBtn')
            sessionStorage.removeItem('indexFocus')
            sessionStorage.removeItem('pointFocus')
            sessionStorage.removeItem('aiBotFocus')
            this.$nextTick(()=>{
              this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[3].dom);
              sessionStorage.setItem('weatherBtn',true)
            })
            return;
            // if (this.$store.state.app.onLineList[0].show != false) {
            //   this.$nextTick(()=>{
            //     this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[0].dom);
            //     sessionStorage.setItem('settingBtn',true)
            //   })
            // } else
            if (this.$store.getters.getUserInfo.oldsters.length > 1) {
              this.$nextTick(()=>{
                this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[1].dom);
                sessionStorage.setItem('selectUser',true)
              })
            }
            else {
              this.$nextTick(()=>{
                this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[3].dom);
                sessionStorage.setItem('weatherBtn',true)
              })
            }

          }
        },
        left:()=>{
          if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
            return
          }
          if (this.dialogVisible) {
            if (this.popupModule == 3) {
              if (this.popupBtnNums > 0) {
                this.popupBtnList[this.popupBtnNums].ref = ""
                this.popupBtnNums --
                this.popupBtnList[this.popupBtnNums].ref = "active"
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active);
                })
              }

              return
            }
            if (this.popupBtnList.length > 1) {
              if (this.swiperIndex > 0) {
                this.popupBtnList[this.swiperIndex].ref = ""
                this.swiperIndex --
                this.popupBtnList[this.swiperIndex].ref = "active"
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active);
                  // 指示器
                  const element = this.$refs.active[0]
                  if (element) {
                    const windowWidth = element.parentNode.parentNode.clientWidth  // 可视区域高度
                    const elementWidth = Number(element.clientWidth)   // 当前元素高度
                    const elementOffsetLeft = element.offsetLeft  // 当前元素距离顶端的高度
                    const windowScrollLeft = element.parentNode.offsetLeft  // 页面下拉高度

                    if(((elementOffsetLeft + elementWidth + windowScrollLeft < 0) || elementOffsetLeft + elementWidth + windowScrollLeft > windowWidth + 1) && element.parentNode.clientWidth > windowWidth) {
                      this.pointerIndex--
                    }
                  }
                })
              }
            }
            return
          }
          if (this.nums > -1) {
            if(this.nums % this.column  != 0){
              this.iconList[this.nums].ref='';
              this.nums -= 1
              sessionStorage.removeItem('indexFocus')
              this.iconList[this.nums].ref='active';
              /**
               * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
               */
              // sessionStorage.setItem('indexFocus',this.nums)
              sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
              this.$nextTick(()=>{
                this.$store.dispatch('index/setFocusDom',this.$refs.active);
              })
            }
          } else
          if (this.rightIcon[0] && this.rightIcon[0].ref == "active" && this.iconList.length > 0) {
            this.rightIcon[0].ref = ""
            if (this.nextNums == -2 && this.iconList[2]) {
              this.nums = 2
            } else {
              this.nums = this.nextNums
            }
            this.iconList[this.nums].ref = 'active';

            /**
             * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
             */
            // sessionStorage.setItem('indexFocus',this.nums)
            sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
            this.adSwiperInter = swiperInter
            this.$nextTick(()=>{
              this.$store.dispatch('index/setFocusDom',this.$refs.active);
            })
          } else
          if ((this.nums == -1 || this.nums == -2) && this.iconList.length > 0) {
            // if (this.$store.getters.focus_dom.className.indexOf('user') > -1) {
            //   return
            //   sessionStorage.removeItem('settingBtn')
            //   sessionStorage.removeItem('selectUser')
            //   sessionStorage.removeItem('weatherBtn')
            //   sessionStorage.removeItem('pointBtn')
            //   this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[0].dom);
            //   sessionStorage.setItem('pointBtn',true)
            // } else
            if (this.$store.getters.focus_dom.className.indexOf('weather') > -1) {
              sessionStorage.removeItem('settingBtn')
              sessionStorage.removeItem('selectUser')
              sessionStorage.removeItem('weatherBtn')
              sessionStorage.removeItem('pointBtn')
              // if (this.$store.state.app.onLineList[0].show == false) {
              this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[2].dom);
              sessionStorage.setItem('aiBotBtn',true)
            } else
            if (this.$store.getters.focus_dom.className.indexOf('ai_bot') > -1) {
              sessionStorage.removeItem('settingBtn')
              sessionStorage.removeItem('selectUser')
              sessionStorage.removeItem('weatherBtn')
              sessionStorage.removeItem('pointBtn')
              sessionStorage.removeItem('aiBotBtn')
              // if (this.$store.state.app.onLineList[0].show == false) {
              this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[1].dom);
              sessionStorage.setItem('pointBtn',true)
            } else
            if(this.$store.getters.focus_dom.className.indexOf('point') > -1) {
              sessionStorage.removeItem('settingBtn')
              sessionStorage.removeItem('selectUser')
              sessionStorage.removeItem('weatherBtn')
              sessionStorage.removeItem('pointBtn')
              sessionStorage.removeItem('aiBotBtn')
                // if (this.iconList[this.nextNums]) {
                //   this.iconList[this.nextNums].ref = 'active';
                //   this.nums = this.nextNums
                //   sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
                //   this.$nextTick(()=>{
                //     this.$store.dispatch('index/setFocusDom',this.$refs.active);
                //   })
                // } else {
                  if (this.$store.getters.getUserInfo.oldsters.length > 1) {
                    this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[0].dom);
                    sessionStorage.setItem('selectUser',true)
                  } else
                  if (this.iconList.length > 0) {
                    sessionStorage.removeItem('settingBtn')
                    sessionStorage.removeItem('selectUser')
                    sessionStorage.removeItem('weatherBtn')
                    sessionStorage.removeItem('pointBtn')
                    sessionStorage.removeItem('aiBotBtn')
                    this.nums = this.nextNums = 0
                    this.iconList[this.nums].ref = 'active';
                    sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
                    this.$nextTick(()=>{
                      this.$store.dispatch('index/setFocusDom',this.$refs.active);
                    })
                  }
                // }
            } else
            if (this.$store.getters.focus_dom.className.indexOf('user') > -1) {
              sessionStorage.removeItem('settingBtn')
              sessionStorage.removeItem('selectUser')
              sessionStorage.removeItem('weatherBtn')
              sessionStorage.removeItem('pointBtn')
              sessionStorage.removeItem('aiBotBtn')

              this.iconList[this.nextNums].ref='active';
              this.nums = this.nextNums
              /**
               * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
               */
              // sessionStorage.setItem('indexFocus',this.nums)
              sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
              this.$nextTick(()=>{
                this.$store.dispatch('index/setFocusDom',this.$refs.active);
              })
            }
          }

        },
        right:()=>{
          if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
            return
          }
          if (this.dialogVisible) {
            if (this.popupModule == 3) {
              if (this.popupBtnNums < this.popupBtnList.length-1) {
                this.popupBtnList[this.popupBtnNums].ref = ""
                this.popupBtnNums ++
                this.popupBtnList[this.popupBtnNums].ref = "active"
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active);
                })
              }
              return
            }
            if (this.popupBtnList.length > 1) {
              if (this.swiperIndex < this.popupBtnList.length - 1) {
                this.popupBtnList[this.swiperIndex].ref = ""
                this.swiperIndex ++
                this.popupBtnList[this.swiperIndex].ref = "active"
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active);
                  // 指示器
                  const element = this.$refs.active[0]
                  if (element) {
                    const windowWidth = element.parentNode.parentNode.clientWidth  // 可视区域高度
                    const elementWidth = Number(element.clientWidth)   // 当前元素高度
                    const elementOffsetLeft = element.offsetLeft  // 当前元素距离顶端的高度
                    const windowScrollLeft = element.parentNode.offsetLeft  // 页面下拉高度

                    if(((elementOffsetLeft + elementWidth + windowScrollLeft < 0) || elementOffsetLeft + elementWidth + windowScrollLeft > windowWidth + 1) && element.parentNode.clientWidth > windowWidth) {
                      this.pointerIndex++
                    }
                  }
                })
              }

            }
            return
          }
          if (this.nums > -1 && this.iconList[this.nums]) {
            if (this.nums % this.column != this.column - 1) {
              this.iconList[this.nums].ref = '';
              this.nums += 1
              this.iconList[this.nums].ref = 'active';
              /**
               * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
               */
              // sessionStorage.setItem('indexFocus',this.nums)
              sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
            } else
            if ((this.nums % this.column == this.column - 1 || this.nums == this.iconList.length - 1) && this.rightIcon.length > 0) {
              this.iconList[this.nums].ref = '';
              this.rightIcon[0].ref = "active"
              this.nextNums = this.nums
              this.nums = -2
              /**
               * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
               */
              // sessionStorage.setItem('indexFocus',this.nums)
              sessionStorage.setItem('indexFocus', -2)
              // this.adSwiperInter = 0
            }
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active);
            })
          } else
          if (this.nums < 0) {
            this.$nextTick(()=>{
              if (this.$store.getters.focus_dom.className.indexOf('user') > -1) {
                sessionStorage.removeItem('settingBtn')
                sessionStorage.removeItem('selectUser')
                sessionStorage.removeItem('weatherBtn')
                sessionStorage.removeItem('pointBtn')
                sessionStorage.removeItem('aiBotBtn')

                this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[1].dom);
                sessionStorage.setItem('pointBtn',true)
              } else
              if (this.$store.getters.focus_dom.className.indexOf('point') > -1) {
                sessionStorage.removeItem('settingBtn')
                sessionStorage.removeItem('selectUser')
                sessionStorage.removeItem('weatherBtn')
                sessionStorage.removeItem('pointBtn')
                sessionStorage.removeItem('aiBotBtn')
                // if (this.$store.getters.getUserInfo.oldsters.length > 1) {
                  this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[2].dom);
                  sessionStorage.setItem('aiBotBtn',true)
                // } else {
                //   this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[3].dom);
                // }
              } else
              if (this.$store.getters.focus_dom.className.indexOf('ai_bot') > -1) {

                sessionStorage.removeItem('settingBtn')
                sessionStorage.removeItem('selectUser')
                sessionStorage.removeItem('weatherBtn')
                sessionStorage.removeItem('pointBtn')
                sessionStorage.removeItem('aiBotBtn')
                // if (this.$store.getters.getUserInfo.oldsters.length > 1) {
                this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[3].dom);
                sessionStorage.setItem('weatherBtn',true)
                // } else {
                //   this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[3].dom);
                // }
              } else
              if (this.$store.getters.focus_dom.className.indexOf('weather') > -1) {
                if (this.rightIcon.length > 0) {
                  sessionStorage.removeItem('settingBtn')
                  sessionStorage.removeItem('selectUser')
                  sessionStorage.removeItem('weatherBtn')
                  sessionStorage.removeItem('pointBtn')
                  sessionStorage.removeItem('aiBotBtn')

                  if (this.rightIcon.length > 0) {
                    this.rightIcon[0].ref = "active"
                    this.$forceUpdate()
                    if (!sessionStorage.getItem('indexFocus')) {
                      /**
                       * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
                       */
                      // sessionStorage.setItem('indexFocus',this.nums)
                      sessionStorage.setItem('indexFocus', -2)
                    }
                  } else {
                    this.iconList[this.nums].ref = "active"
                    this.$forceUpdate()
                    sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
                  }
                  this.nums  = -2
                  this.nextNums = 2
                  this.$nextTick(()=>{
                    this.$store.dispatch('index/setFocusDom',this.$refs.active[0] ? this.$refs.active[0] : this.$refs.active);
                  })


                }

              }
              // else
              // if (this.$store.getters.focus_dom.className.indexOf('setting') > -1) {
              //   sessionStorage.removeItem('settingBtn')
              //   sessionStorage.removeItem('selectUser')
              //   sessionStorage.removeItem('weatherBtn')
              //   sessionStorage.removeItem('pointBtn')
              //   if (this.$store.getters.getUserInfo.oldsters.length > 1) {
              //     this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[2].dom);
              //     sessionStorage.setItem('selectUser',true)
              //   } else {
              //     this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[3].dom);
              //   }
              // }
            })
          }
        },
        enter:()=>{
            if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
              return
            }
            if (this.dialogVisible) {
              this.popupBtnList[this.popupBtnNums].fuc()
              return
            }
            // 有视频轮播，则必须页面显示1s后才能进入下一级，避免快进快出导致安卓视频播放异常
            let time = new Date().getTime()
            if (time - this.timeNum < 0) {
              return
            }
          if (!this.$store.getters.getUserInfo.is_agree) {
              this.openPrivacyPolicy(()=>{
                if (this.iconList[this.nums] && this.iconList[this.nums].ref == "active") {
                  if (this.$store.state.app.media) {
                    this.$store.state.app.media.stop()
                  }
                  this.goNextPage()
                } else
                if (this.rightIcon[0] && this.rightIcon[0].ref && this.nums == -2 && sessionStorage.getItem('indexFocus')) {
                  // 未接来电
                  if (this.adHomeType == 2) {
                    this.callUser({data: this.rightIcon[0]})
                  } else
                      // 视频轮播
                  if (this.adHomeType == 6 && this.$store.state.app.media && !this.$store.state.app.media.isFullscreen) {
                    this.$store.state.app.media.fullscreen('t')
                    this.$store.state.app.media.MP.style.borderRadius = 0

                    this.$nextTick(() => {
                      this.$store.dispatch('index/setFocusDom', document.body);
                    })
                  } else
                      // 天气
                  if (this.adHomeType == 4) {
                    /**
                     * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
                     */
                    // sessionStorage.setItem('indexFocus',this.nums)
                    sessionStorage.setItem('indexFocus', -2)
                    this.$store.dispatch('index/setFocusDom', null);
                    this.$router.push({path:'/weather'})
                  }
                }
                else {
                  if (this.$store.getters.focus_dom.className.indexOf('point') > -1) {
                    if (this.$store.state.app.media) {
                      this.$store.state.app.media.stop(()=>{
                        this.$store.dispatch('index/setFocusDom', null);
                        this.$router.push({path:'/pointTask'})
                      })
                      return
                    }
                    this.$store.dispatch('index/setFocusDom', null);
                    this.$router.push({path:'/pointTask'})
                  } else
                  if (this.$store.getters.focus_dom.className.indexOf('setting') > -1) {
                    if (this.$store.state.app.onLineList[1].show != false) {
                      // 关闭摄像头
                      // alert(999)
                    }
                    // sessionStorage.setItem('settingBtn',true)
                    // this.fuc.backTv()
                    // this.$store.state.app.settingShow = true
                  } else
                  if (this.$store.getters.focus_dom.className.indexOf('weather') > -1) {
                    if (this.$store.state.app.media) {
                      this.$store.state.app.media.stop()
                    }
                    // sessionStorage.setItem('weatherBtn',true)
                    this.$store.dispatch('index/setFocusDom', null);
                    this.$router.push({path:'/weather'})
                  } else
                      // 切换用户
                  if (this.$store.getters.focus_dom.className.indexOf('user') > -1) {
                    this.selectUser()
                  }
                }
              })
              return
          }
          if (this.iconList[this.nums] && this.iconList[this.nums].ref == "active") {
            if (this.$store.state.app.media) {
              // 有视频轮播，则必须页面显示1s后才能进入下一级，避免快进快出导致安卓视频播放异常
              let time = new Date().getTime()
              if (time - this.timeNum < 0) {
                return
              }
              if (!this.isReady) {
                return
              }
              this.$store.state.app.media.stop(()=>{
                this.$store.state.app.media = null
                this.goNextPage()
              })
              return
            }
            this.goNextPage()
          } else
          // 动态屏确认
          if (this.rightIcon[0] && this.rightIcon[0].ref && this.nums == -2 && sessionStorage.getItem('indexFocus')) {
            // 未接来电
            if (this.adHomeType == 2) {
              this.callUser({data: this.rightIcon[0]})
            } else
                // 视频轮播
            if (this.adHomeType == 6 && this.$store.state.app.media && !this.$store.state.app.media.isFullscreen) {
              this.$store.state.app.media.fullscreen('t')
              this.$store.state.app.media.MP.style.borderRadius = 0

              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', document.body);
              })
            } else
                // 天气
            if (this.adHomeType == 4) {
              /**
               * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
               */
              // sessionStorage.setItem('indexFocus',this.nums)
              sessionStorage.setItem('indexFocus', -2)
              this.$store.dispatch('index/setFocusDom', null);
              this.$router.push({path:'/weather'})
            }
          }
          else {
            if (this.$store.getters.focus_dom.className.indexOf('point') > -1) {
              if (this.$store.state.app.media) {
                this.$store.state.app.media.stop(()=>{
                  this.$store.dispatch('index/setFocusDom', null);
                  this.$router.push({path:'/pointTask'})
                })
                return
              }
              this.$store.dispatch('index/setFocusDom', null);
              this.$router.push({path:'/pointTask'})
            } else
            if (this.$store.getters.focus_dom.className.indexOf('setting') > -1) {
              if (this.$store.state.app.onLineList[1].show != false) {
                // 关闭摄像头
                // alert(999)
              }
              // sessionStorage.setItem('settingBtn',true)
              // this.fuc.backTv()
              // this.$store.state.app.settingShow = true
            } else
            if (this.$store.getters.focus_dom.className.indexOf('weather') > -1) {
              if (this.$store.state.app.media) {
                this.$store.state.app.media.stop(()=>{
                  this.$store.dispatch('index/setFocusDom', null);
                  this.$router.push({path:'/weather'})
                })
                return
              }
              // sessionStorage.setItem('weatherBtn',true)
              this.$store.dispatch('index/setFocusDom', null);
              this.$router.push({path:'/weather'})
            } else
            if (this.$store.getters.focus_dom.className.indexOf('ai_bot') > -1) {
              // 呼叫视频客服
              if (this.$store.getters.getUserInfo.customer) {
                return
              }
              if (!this.$store.state.app.onLineList[0].show) {
                this.no_camerOnline()
                return
              }
              GetCustomerService()
              .then(server_data=>{
                if (server_data.code == 200) {
                  let obj = {
                    data: {
                      fk_friend_id: server_data.data.customer_id
                    }
                  }
                  this.customerInfo = obj
                  this.callUser(obj)
                }
              })
              .catch(err=>{
                if (err.response && err.response.data.msg) {
                  this.popupMessage = err.response.data.msg
                  this.popupModule = 2
                  this.popupBtnList = [
                    {
                      text: '关闭',
                      ref: 'active',
                      fuc: () => {
                        this.dialogVisible = false
                        this.popupBtnList[this.popupBtnNums].ref = ''
                        // this.rightIcon[0].ref = 'active'


                        this.$nextTick(() => {
                          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                        })
                      },
                    },
                  ]
                  // this.rightIcon[0].ref = ''
                  this.dialogVisible = true
                }
              })

            } else
                // 切换用户
            if (this.$store.getters.focus_dom.className.indexOf('user') > -1) {
              this.selectUser()
            }
          }

        },
        esc:()=>{
          if (this.$store.state.app.media && this.$store.state.app.media.isFullscreen) {
            this.$store.state.app.media.fullscreen('t')

            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active);
            })
            return
          }
          if (this.dialogVisible) {
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[1].dom);
            })
          }

          // if (this.$route.path == '/tetris') {
          //   history.go(-1)
          // } else {
          //   // this.fuc.backTv()
          // }
        }

    })
    sessionStorage.removeItem('zegoListIndex')

},
methods: {
    goNextPage() {
        // if (this.iconList[this.nums] && this.iconList[this.nums].redirect) {
        // 非更多服务,所有服务必须存在供应商
        if (this.iconList[this.nums].id != 7) {
            // 存储当前页面焦点位置
            sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
            sessionStorage.setItem('indexIconList', JSON.stringify(this.iconList))
            // if (this.iconList[this.nums].has_supplier) {
            sessionStorage.setItem('redirectInfo', JSON.stringify(this.iconList[this.nums]))
            // 获取供应商 根据供应商配置跳转页面进行跳转
            GetIndexICon({
              fid: this.iconList[this.nums].id,
              user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
              home_id: this.$store.getters.getUserInfo.home_id
            }).then(res=>{
              if (res.code == 200 && res.data.length > 0) {
                if (res.data.length > 1) {
                  sessionStorage.setItem('supplierList', JSON.stringify(res.data))
                  this.$nextTick(()=>{
                    this.$router.push({
                      path: './supplier'
                    })
                  })
                } else {
                  sessionStorage.setItem('supplierInfo', JSON.stringify(res.data[0]))
                  this.$nextTick(()=>{
                    if (res.data[0].redirect) {
                      this.$router.push({
                        path: res.data[0].redirect
                      })
                    }
                  })
                }
              }
            })
          // } else {
          //   this.$nextTick(()=>{
          //     this.$router.push({
          //       path: this.iconList[this.nums].redirect
          //     })
          //   })
          // }
        } else {
          sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
          sessionStorage.setItem('indexIconList', JSON.stringify(this.iconList))
          // 更多服务跳转固定页面
          this.$nextTick(()=>{
            this.$router.push({
              path: '/moreServices'
            })
          })
        }

      // }
    },
    openPrivacyPolicy(callback) {
      GetPrivacyPolicy().then(res=>{
        let policeData = res.data
        this.popupMessage = `<div>在您使用【银发宝】服务之前，请点击“查看协议”按键,<br />仔细阅读<span>《`+policeData.title+` `+policeData.version+`》</span><br />如您同意上诉用户条款、隐私政策或其中任何约定，请点击底部“同意”按键</div>`
        this.popupModule = 3
        this.popupBtnNums = 1
        // this.$store.dispatch('index/setFocusDom', null);
        this.popupBtnList = [
          {
            text: '取消',
            ref: '',
            fuc: () => {
              this.dialogVisible = false
              this.popupBtnList[this.popupBtnNums].ref = ''
              if (sessionStorage.getItem('indexFocus')) {
                if (Number(sessionStorage.getItem('indexFocus')) > -1) {
                  this.iconList[this.nums].ref = "active"
                } else {
                  this.rightIcon[0].ref = "active"
                }
              }

              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active[0] ? this.$refs.active[0] : this.$refs.active)
              })
            },
          },
          {
            text: '同意',
            ref: 'active',
            fuc: () => {
              let obj = {
                user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
                home_id: this.$store.getters.getUserInfo.home_id,
                fk_privacy_policy_id: policeData.id
              }

              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active[0] ? this.$refs.active[0] : this.$refs.active)
                SetPrivacyPolicy(obj)
                    .then(res=>{
                      if (res.code == 200) {
                        this.dialogVisible = false
                        this.popupBtnList[this.popupBtnNums].ref = ''
                        if (sessionStorage.getItem('indexFocus')) {
                          if (Number(sessionStorage.getItem('indexFocus')) > -1) {
                            this.iconList[this.nums].ref = "active"
                          } else {
                            this.rightIcon[0].ref = "active"
                          }
                        }
                        this.fuc.getUserInfo((res)=>{
                          console.log(res)
                          this.dialogVisible = false
                          this.popupBtnList[this.popupBtnNums].ref = ''
                          if (sessionStorage.getItem('indexFocus')) {
                            if (Number(sessionStorage.getItem('indexFocus')) > -1) {
                              this.iconList[this.nums].ref = "active"
                            } else {
                              this.rightIcon[0].ref = "active"
                            }
                          }
                          this.$nextTick(() => {
                            this.$store.dispatch('index/setFocusDom', this.$refs.active[0] ? this.$refs.active[0] : this.$refs.active)
                          })
                        })
                      }
                    })
              })
            },
          },
          {
            text: '查看协议',
            ref: '',
            fuc: () => {
              this.$nextTick(()=>{
                sessionStorage.setItem('privacy_policy',encodeURI(JSON.stringify(policeData)))
                this.$router.push({
                  path: './detailsPage?title=' + policeData.title+` `+policeData.version
                })
              })
            },
          },
        ]

        let arrList = this.iconList[this.nums] ? this.iconList[this.nums] : this.rightIcon[0]
        if (arrList) {
          arrList.ref = ""
        }

        setTimeout(() => {
          this.dialogVisible = true
        }, 300)
      })
    },
    // 呼叫用户
    callUser(res) {
      GetCallTime({
        fk_home_id: this.$store.getters.getUserInfo.home_id
      })
      .then(data=> {
        if (data.code == 200) {
          // true:超时  false:未超时
          if (data.data.timed_out == false) {
            GetFriendIsCall({
              fk_friend_id: res.data.fk_friend_id,
            })
            .then((isCallData) => {
                  if (isCallData.code == 200) {
                    // 对方闲置状态
                    if (!isCallData.data.is_call) {
                      Axios.all([
                        GetZegoToken({ user_id: this.$store.getters.getUserInfo.home_id }),
                      ]).then(
                          Axios.spread((tokenData) => {
                            if (tokenData.code == 200) {
                              this.zegoToken = tokenData.data.token
                              this.callMyfriend(res)
                            }
                          })
                      )
                    }
                    // 对方正在通话中
                    else {
                      this.popupMessage = '对方正在通话中,请稍后再拨!'
                      this.popupModule = 2
                      this.popupBtnList = [
                        {
                          text: '关闭',
                          ref: 'active',
                          fuc: () => {
                            this.dialogVisible = false
                            this.popupBtnList[this.popupBtnNums].ref = ''
                            this.rightIcon[0].ref = 'active'
                            this.$nextTick(() => {
                              this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                            })
                          },
                        },
                      ]
                      this.rightIcon[0].ref = ''
                      this.dialogVisible = true
                    }
                  }
                })
          }
        } else {
          this.no_CallTime()
        }
      })
      .catch(err=>{
        this.no_CallTime()
      })

    },
    no_CallTime() {
      this.popupMessage = '剩余通话时长不足'
      this.popupModule = 2
      // this.$store.dispatch('index/setFocusDom', null);
      this.popupBtnList = [
        {
          text: '关闭',
          ref: 'active',
          fuc: () => {
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].ref = ''
            if (this.leftNums == 0) {
              this.applyList[this.rightNums].ref = 'active'
            } else if (this.leftNums == 1) {
              this.friendsList[this.rightNums].ref = 'active'
            } else if (this.leftNums == 2) {
              this.addFriendBtn[this.rightNums].ref = 'active'
            }
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
            })
          },
        },
      ]

      if (this.leftNums == 0) {
        this.applyList[this.rightNums].ref = ''
      } else if (this.leftNums == 1) {
        this.friendsList[this.rightNums].ref = ''
      } else if (this.leftNums == 2) {
        this.addFriendBtn[this.rightNums].ref = ''
      }

      setTimeout(() => {
        this.dialogVisible = true
      }, 300)
    },
    callMyfriend(item) {
      this.$store.dispatch('app/setLoadingState', true)
      this.dialogVisible = false
      if (this.popupBtnList[this.popupBtnNums]) {
        this.popupBtnList[this.popupBtnNums].ref = ''
      }
      if (sessionStorage.getItem('aiBotBtn')) {
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[2].dom);
        })
      } else {
        this.rightIcon[0].ref = 'active'
      }

      setTimeout(() => {
        this.$store.dispatch('app/setLoadingState', false)
        if (!this.$store.state.app.onLineList[0].show) {
          this.no_camerOnline()
          return
        }

        websocketData.value().socket.send({
          type: 1, // 视频通话
          user_id: item.data.fk_friend_id,

          user_id_caller_avatar: process.env.VUE_APP_API + '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png',
          user_id_caller_title: this.$store.getters.getUserInfo.oldsters[0].name,
        })
      }, 300)
    },
    sendZegoCall(roomID, userReceiver, token) {
      let receiverObj = {
        title: this.rightIcon[0].friend_title,
        avatar: '',
      }

      if (this.rightIcon[0].friend_avatar) {
        receiverObj.avatar = this.rightIcon[0].friend_avatar.indexOf('http') > -1 ? this.rightIcon[0].friend_avatar : process.env.VUE_APP_API + this.rightIcon[0].friend_avatar
      } else {
        receiverObj.avatar = process.env.VUE_APP_API +  '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png'
      }


      let be_friend = 0
      let friendid = this.rightIcon[0].fk_friend_id
      if (this.customerInfo) {
        friendid = this.customerInfo.data.fk_friend_id
      }
      youAreMyFriend({
        fk_user_id: this.$store.getters.getUserInfo.home_id,
        fk_friend_id: friendid
      })
      .then(res=>{
        if (res.code == 200) {
          be_friend = res.data.be_friend
        }
      })
      .finally(() => {
        let obj = {
          user_id_caller: this.$store.getters.getUserInfo.home_id,
          // user_id_caller_avatar: '',
          // user_id_caller_title: '',
          user_id_token: token,
          user_id_receiver: userReceiver,
          user_id_receiver_avatar: receiverObj.avatar,
          user_id_receiver_title: receiverObj.title,
          img_rul_receiver: process.env.VUE_APP_API +  '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png',
          room_id: roomID,
          be_friend: be_friend
        }

        this.$bridge.callhandler('enterVideoChat', obj, () => {})

        this.dialogVisible = false
        this.popupBtnList[this.popupBtnNums].ref = ''

        if (sessionStorage.getItem('aiBotBtn')) {
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[2].dom);
          })
        } else {
          this.setIsRead(this.rightIcon[0])
          this.rightIcon[0].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
          })
        }

      });
    },
    // 未连接摄像头
    no_camerOnline() {
      this.popupMessage = '请先连接摄像头'
      this.popupModule = 2
      // this.$store.dispatch('index/setFocusDom', null);
      this.popupBtnList = [
        {
          text: '关闭',
          ref: 'active',
          fuc: () => {
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].ref = ''
            if (sessionStorage.getItem('indexFocus')) {
              if (Number(sessionStorage.getItem('indexFocus')) > -1) {
                this.iconList[this.nums].ref = "active"
              } else {
                this.rightIcon[0].ref = "active"
              }
            }

            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active[0] ? this.$refs.active[0] : this.$refs.active)
            })
          },
        },
      ]

      let arrList = this.iconList[this.nums] ? this.iconList[this.nums] : this.rightIcon[0]
      arrList.ref = ""

      setTimeout(() => {
        this.dialogVisible = true
      }, 300)
    },
    // 设置未读来电为已读
    setIsRead(item) {
      SetLogRead({
        id: item.id,
      }).then((res) => {})
    },
    // 获取医院订单
    getHospitalLog(callbackTrue,callbackFalse) {
      getDepartmentList({
        mac: sessionStorage.getItem('MAC'),
        card_id:this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].card_id_md5,
        hospital_id: 1
      }).then(res=>{
        if (res.code == 200) {
          if (callbackTrue) {
            callbackTrue(res.data.unfinish_orders)
          }
        }
      }).catch(err=>{
        if (callbackFalse) {
          callbackFalse()
        }
      })

    },
    // 获取动态屏数据
    getIndexMessagePage() {
      GetIndexMessage({
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        home_id: this.$store.getters.getUserInfo.home_id
      }).then(res=>{
        if (res.code == 200) {
          this.getHospitalLog((data)=>{
            let arrData = res.data
            let hospitalData = []
            data.map(item=>{
              if (item.status == 2) {
                hospitalData.push(item)
              }
            })
            arrData.hospital_data.data = hospitalData
            this.adHomeData = arrData
            // this.adHomeData.weather_data.data.push({
            //   start_time: '17:57:00',
            //   end_time:'17:57:59'
            // })
            this.getAdHomeTime(this.nowTime)
          },()=>{

            let arrData = res.data
            this.adHomeData = arrData
            // this.adHomeData.hospital_data.data = []
            this.getAdHomeTime(this.nowTime)
          })
        }
      })
    },
    // 获取动态屏各显示时间段/数据
    getAdHomeTime(time) {
      if (this.adHomeData) {
        // 叫车出行时 停止所有操作
        if (this.adHomeType == 1) {
          this.rightIcon = [{
            ref: ''
          }]
          if (this.$store.state.app.media) {
            this.$store.state.app.media.stop()
          }
          return
        }

        // 视频通话未接来电
        if (this.adHomeData.call_data && this.adHomeData.call_data.data.length > 0) {
          this.adHomeShowContent(2)
          return
        }

        // 预约挂号订单信息
        if (this.adHomeData.hospital_data && this.adHomeData.hospital_data.data.length > 0) {
          let type = true
          this.rightIcon = []
          this.adHomeData.hospital_data.data.map(item=>{
            let targetDate = new Date(item.yy_date + ' 00:00:00').getTime()
            let nowDate =  new Date(time).getTime()
            if (targetDate - nowDate <= 1 * 24 * 60 * 60  * 1000) {
              this.rightIcon.push(item)
              type = false
              this.adHomeShowContent(3)
            }
          })
          if (!type) {
            return
          }
        }

        // 天气信息
        if (this.adHomeData.weather_data && this.adHomeData.weather_data.data.length > 0) {
          let type = true
          this.adHomeData.weather_data.data.map(item=>{
            let nowDateStart_time = new Date(time).format('yyyy-MM-dd ')  + item.start_time
            let nowDateEnd_time = new Date(time).format('yyyy-MM-dd ')  + item.end_time
            if (new Date().isDuringDate(nowDateStart_time, nowDateEnd_time, time)) {
              this.adHomeShowContent(4)
              type = false
            }
          })
          if (!type) {
            return
          }
        }

        // 图文推送
        if (this.adHomeData.img_data && this.adHomeData.img_data.data.length > 0) {
          let type = true
          if (new Date().isDuringDate(this.adHomeData.img_data.start_time,this.adHomeData.img_data.end_time,time)) {
            this.adHomeShowContent(5)
            type = false
          }
          if (!type) {
            return
          }
        }

        // 默认视频播放
        this.adHomeShowContent(6)
      }
    },
    // 设置显示内容
    adHomeShowContent(type) {
      if (!this.adHomeData) {
        return
      }
      type ? this.adHomeType = type : this.adHomeType = 0

      if (this.adHomeType != 6) {
        if (this.$store.state.app.media) {
          this.$store.state.app.media.stop()
        }
      }

      if (this.adHomeType == 2) {
        this.rightIcon = []
        let arr =  JSON.parse(JSON.stringify(this.adHomeData.call_data.data[0]))
        arr.ref = this.rightIcon[0] && this.rightIcon[0].ref != "" ? this.rightIcon[0].ref : ""
        if (this.nums == -2 ) {
          arr.ref = "active"
        }
        if (arr.friend_avatar) {
          arr.friend_avatar = arr.friend_avatar.indexOf('http') == -1 ? process.env.VUE_APP_API +arr.friend_avatar : arr.friend_avatar
        } else {
          arr.friend_avatar = process.env.VUE_APP_API +  '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png'
        }
        this.rightIcon[0] = arr
        if (this.nums == -2 ) {
          this.rightIcon[0].ref = "active"
        }
      } else
      if (this.adHomeType == 3) {
          // this.rightIcon = []
        this.rightIcon[0].ref = ""
        if (this.nums == -2 ) {
          this.rightIcon[0].ref = "active"
        }
      } else
      if (this.adHomeType == 4) {
        this.rightIcon = []
        this.weatherInfo = JSON.parse(decodeURI(localStorage.getItem('weatherList')))
        this.rightIcon[0] = JSON.parse(decodeURI(localStorage.getItem('weatherList'))).future[0]
        this.rightIcon[0].ref = ""
        if (this.nums == -2 ) {
          this.rightIcon[0].ref = "active"
        }
      }  else
      if (this.adHomeType == 5) {
        this.rightIcon = []
        let arr = JSON.parse(JSON.stringify(this.adHomeData.img_data.data))
        arr.map((item,index)=>{
          if (item.url) {
            item.url = item.url.indexOf('http') == -1 ? process.env.VUE_APP_API + item.url : item.url
          }
        })
        arr[0].ref = ""
        if (this.nums == -2 ) {
          arr[0].ref = "active"
        }
        this.rightIcon = arr
      }else
      if (this.adHomeType == 6) {
        if (this.dialogVisible) {
          if (this.$store.state.app.media) {
            this.$store.state.app.media.stop()
          }
          return
        }
        this.rightIcon = []
        this.rightIcon = JSON.parse(JSON.stringify(this.adHomeData.video_data.data))
        if (this.nums == -2 ) {
          this.rightIcon[0].ref = "active"
        }
        this.playVideo()
      }
    },
    // 动态屏 未接来电 默认头像图片
    avatarError() {
      this.$nextTick(()=>{
        if (this.$refs.avatarPic) {
          this.$refs.avatarPic.src = process.env.VUE_APP_API + '/public/storage/uploaded/2024_04/jfsdlkfjieowurioewjflsdj.png'
        }
      })
    },
    // 切换用户 头像默认图
    defImg(){
      let img = event.srcElement;
      img.src = process.env.VUE_APP_API + '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png';
      img.onerror = null; //防止闪图
    },
    logoError() {
      this.wxLogoShow = !this.wxLogoShow
      return
      let img = event.srcElement;
      img.src = require('@/assets/xiaochengxu_logo_bak.png');
      img.onerror = null; //防止闪图
    },
    // 地图加载完成事件
    mapLoad(res) {
      if (res) {
        this.driverInfoShow = true
      }
    },
    swiperChange(index) {
    },
    popupClose() {
      this.$nextTick(() => {
        if (!sessionStorage.getItem('indexFocus')) {
          this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[0].dom);
        }
        document.getElementById('focus_border').style.borderColor = '#fff'
        // this.popupBtnNums = 0
        // this.popupModule = 1
        if (sessionStorage.getItem('aiBotBtn')) {
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[2].dom);
          })
          return
        }
        setTimeout(()=>{
          this.popupBtnList = []
          this.popupBtnNums = 0
          this.popupModule = 1
        },200)
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        // this.$store.dispatch('index/setFocusDom', null);
        document.getElementById('focus_border').style.borderColor = '#5472B0'
      })
    },
    selectUser() {
      // sessionStorage.setItem('selectUser',true)
      this.swiperIndex = this.$store.state.app.selectUserIndex
      this.pointerIndex = this.swiperIndex - 1 > 0 ? this.swiperIndex - 1 : 0
      this.popupModule = 1
      // this.$store.dispatch('index/setFocusDom', null);
      this.$store.getters.getUserInfo.oldsters.map((item,index)=>{
        item.text = item.name
        item.ref = ""
        item.fuc =  () => {
          this.dialogVisible = false
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.$store.state.app.selectUserIndex = this.swiperIndex
          sessionStorage.setItem('selectUserIndex', this.$store.state.app.selectUserIndex)
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[2].dom);
          })
        }

        if (item.avatar) {
          item.avatar = item.avatar.indexOf('http') > -1 ? item.avatar : process.env.VUE_APP_API + item.avatar
        } else {
          item.avatar = process.env.VUE_APP_API + '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png'
        }
      })

      this.popupBtnList = this.$store.getters.getUserInfo.oldsters

      this.popupBtnList[this.$store.state.app.selectUserIndex].ref = "active"

      setTimeout(() => {
        this.dialogVisible = true
      }, 0)
    },
    checkMediaType(url) {
      //  return 1  图片   return 2  视频   return 0  未知
        // 创建URL对象
        var link = new URL(url);

        // 获取路径部分（去除参数）
        var path = link.pathname;

        // 获取路径的最后一个点之后的内容作为文件扩展名
        var extension = path.split('.').pop().toLowerCase();

        // 声明支持的图片和视频文件扩展名
        var imageExtensions = ['jpg', 'jpeg', 'gif', 'png'];
        var videoExtensions = ['mp4', 'wmv', 'avi', 'mov'];

        // 判断文件扩展名是否在图片扩展名数组中
        if (imageExtensions.includes(extension)) {
          return 1;
        }

        // 判断文件扩展名是否在视频扩展名数组中
        if (videoExtensions.includes(extension)) {
          return 2;
        }

        // 扩展名不在图片或视频数组中，返回null表示无法确定媒体类型
        return 0;
    },
    getData(){
      if (this.$store.state.app.media) {
        this.$store.state.app.media.stop()
      }
      if(sessionStorage.getItem('indexFocus')){
        this.nums = Number(sessionStorage.getItem('indexFocus'))
      }
      this.$store.dispatch('app/setLoadingState', true)
      GetIndexPageICon({
        user_id: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].id,
        home_id: this.$store.getters.getUserInfo.home_id
      })
      .then(res=>{
        this.$store.dispatch('app/setLoadingState', false)
        if (res.code == 200) {
          let iconList = res.data
          let iconLeft = []
          iconList.map((item,index)=>{
            if (index < this.column * 2) {   //advertising_position == 0 为按钮  否则为广告位  左侧最多拿到6个焦点位
              item.pinyin = pinyin.getPinYin(item.title,' ', true)
              item.ref = ""
              item.icon =  process.env.VUE_APP_API + item.icon
              item.icon_free =  process.env.VUE_APP_API + item.icon_free
              item.css =  process.env.VUE_APP_API + item.css
              item.css_free =  process.env.VUE_APP_API + item.css_free
              iconLeft.push(item)
            }
            if(sessionStorage.getItem('indexFocus') && Number(sessionStorage.getItem('indexFocus')) > 0){
              if (item.id == Number(sessionStorage.getItem('indexFocus'))) {
                this.nums = index
              }
            }
          })
          this.iconList = iconLeft
          if (!sessionStorage.getItem('weatherBtn') && !sessionStorage.getItem('settingBtn') && !sessionStorage.getItem('selectUser') && !sessionStorage.getItem('pointBtn') && !sessionStorage.getItem('aiBotBtn')) {
            if (this.iconList.length > 0 && this.iconList[this.nums]) {
              this.iconList[this.nums].ref = "active"
              /**
               * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
               */
              // sessionStorage.setItem('indexFocus',this.nums)
              sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
            } else
            if (this.nums == -2 ) { //&& this.rightIcon.length > 0
              if (this.rightIcon.length > 0) {
                this.rightIcon[0].ref= 'active'
              } else {
                this.rightIcon[0]= {
                  ref: 'active'
                }
              }

              // this.adSwiperInter = 0
              if (this.iconList[2]) {
                this.nextNums = 2
              } else {
                this.nextNums = this.iconList.length - 1
              }
            } else {
              this.nums = 0
              this.nextNums = -1
              this.iconList[this.nums].ref = "active"
              /**
               * TODO: 修改焦点位置存储为icon ID,否则 为-2  在右侧
               */
              // sessionStorage.setItem('indexFocus',this.nums)
              sessionStorage.setItem('indexFocus', this.iconList[this.nums].id)
            }
            if (this.iconList.length < 1) {
              if (this.firstSend) {
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[0].dom);
                })
              }
            } else {

              if (this.firstSend) {
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active);
                })
              }
            }
          } else {
            if (sessionStorage.getItem('weatherBtn')) {
              this.$nextTick(() => {
                // this.firstSend ? this.nums = -1 : this.nums = -2
                this.nums = -1
                this.nextNums = 0
                this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[3].dom);
                // sessionStorage.removeItem('weatherBtn')
              })
            } else
            if (sessionStorage.getItem('aiBotBtn')) {
              this.$nextTick(() => {
                // this.firstSend ? this.nums = -1 : this.nums = -2
                this.nums = -1
                this.nextNums = 0
                this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[2].dom);
                // sessionStorage.removeItem('weatherBtn')
              })
            } else
            if (sessionStorage.getItem('selectUser')) {
              // this.firstSend ? this.nums = -1 : this.nums = -2
              this.nums = -1
              this.nextNums = 0
              this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[0].dom);
              // sessionStorage.removeItem('selectUser')
            } else if (sessionStorage.getItem('pointBtn')) {
              this.nums = -1
              this.nextNums = 0
              this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[1].dom);
            } else {
              // this.firstSend ? this.nums = -1 : this.nums = -2
              this.nums = -1
              this.nextNums = 0
              this.$store.dispatch('index/setFocusDom', this.$store.getters.header_arr[0].dom);
              // sessionStorage.removeItem('settingBtn')
            }
          }

          this.firstSend = false

          this.$nextTick(()=>{
            if (document.getElementById('focus_border')) {
              document.getElementById('focus_border').style.borderColor = '#fff'
            }
            if (!this.$store.getters.getUserInfo.is_agree) {
              this.openPrivacyPolicy()
            } else {
              this.getIndexMessagePage()
            }
          })
        }
      })
      .catch(err=>{
        this.$store.dispatch('app/setLoadingState', false)
      })
      // GetUserInfo({ 'mac': sessionStorage.getItem('MAC') }).then((data) => {
      //   if (data.code == 200) {
      //
      //   } else {
      //     document.body.innerHTML = '<div style="width:19.2rem;height:10.8rem;background:url(' + require('@/assets/illegal.jpg') + ') no-repeat;background-size:100% 100%;position:absolute;top:0;left:0" >'
      //   }
      // })
      // .catch((err) => {
      //   document.body.innerHTML = '<div style="width:19.2rem;height:10.8rem;background:url(' + require('@/assets/illegal.jpg') + ') no-repeat;background-size:100% 100%;position:absolute;top:0;left:0" >'
      // })
    },
    playVideo() {
      clearTimeout(this.playTimer)
      this.playTimer = null
      this.playTimer = setTimeout(()=>{
        this.$nextTick(()=>{
          // || this.$store.state.app.media
          if (this.rightIcon.length < 1 ) {
            return
          }

          var obj={
            x:887,
            y:255,
            w:907.5,
            h:636,
            r: 30
          }
          let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0]) / 100

          let videoList = []
          this.rightIcon.map(item=>{
            if (item.url) {
              item.url = item.url.indexOf('http') == -1 ? process.env.VUE_APP_API + item.url : item.url
              // item.url = 'http://192.168.10.68:24002/public/storage/uploaded/2024_03/b89717afc46b8ef7c5b9737543cde9b8.mp4'
              videoList.push(item.url)
            }
          })
          if (!this.$store.state.app.media) {
            this.$store.state.app.media = media.DetermineKernel({
              videoList: videoList,
              loop:1,
              windowPos: obj,
              fontSize: fontSize
            },()=>{
              this.isReady = true
            });
          }
        })
      },1000)
    }
},
beforeDestroy() {
  clearTimeout(this.playTimer)
  this.playTimer = null
  if (this.$store.state.app.media) {
    this.$store.state.app.media.stop()
  }
},
beforeDestory() {
},
}
</script>
<style lang='less' scoped>
  .fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s;
  }
  .fade-enter, .fade-leave-to {
    opacity: 0;
  }

  .index {
    height: 6.6rem;
    padding-top: 0.25rem;
    //overflow: hidden;
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
    gap: 0rem;
    font-size: 0.2rem;
    // 倒影
    //-webkit-box-reflect: below -0.1rem linear-gradient(transparent 86%, rgba(0, 0, 0, 1) 160%);


    .index_left {
      //width: 7.45rem;
      //width: 7.56rem;
      width: 7.6rem;
      //display: grid;
      //grid-template-columns: repeat(3, 1fr); /* 每行显示两个元素 */
      //gap: 0.16rem;
      transition: all 0.3s ease;
      .indexItem {
        height: 3.1rem;
        line-height: 2.2rem;
        border-radius: 0.3rem;
        -webkit-border-radius: 0.3rem; /* Safari 和 Chrome */
        -moz-border-radius: 0.3rem; /* Firefox */
        overflow: hidden;
        color: #000;
        position: relative;
        //background: #ccc;
        transition: all 0.3s ease;
        float: left;
        margin-right: 0.27rem;
        margin-bottom: 0.27rem;
        .remark {
          position: absolute;
          top: 0;
          right: 0.2rem;
          z-index: 99;
          color: #fff;
          height: 0.6rem !important;
          line-height: 0.6rem !important;
          font-size: 0.22rem;
          letter-spacing: 0.03rem;
          text-indent: -0.03rem;
        }
        img {
          display: block;
          width: 1.66rem;
          height: 1.34rem;
          position: absolute;
          top: 18%;
          left: 50%;
          transform: translateX(-50%);
        }
        .iconTitle, .iconPinyin {
          color: #ECE5FA;
          font-weight: bold;
          position: absolute;
          bottom: 0.5rem;

          width: 100%;
          height: 0.5rem;
          line-height: 0.5rem;
          font-size: 0.34rem;
          text-align: center;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
        }
        .iconTitle {
          text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.4);
        }
        .iconPinyin {
          font-size: 0.14rem;
          letter-spacing: 0 !important;
          text-indent: 0 !important;
          bottom: 0.14rem;
          font-weight: 500;
          color: rgba(255,255,255,0.5);
          //font-weight: bold;
        }
        .readRed {
          width: 0.2rem;
          height: 0.2rem;
          background: red;
          border-radius: 50%;
          position: absolute;
          top: 0.22rem;
          right: 0.22rem;
        }
      }


      .background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        //box-shadow: inset -0.08rem 0.1rem 0.1rem -0.12rem rgba(255, 255, 255, 0.5);
        //transition: all 0.3s;
        background-repeat: no-repeat !important;
        background-size: cover !important;
      }

      .background.active {
        opacity: 1;
        //box-shadow: inset -0.08rem 0.1rem 0.1rem -0.12rem rgba(0, 0, 0, 0.8);
        //transition: all 0.3s;
      }

    }
    .index_right {
      width: 9.2rem;
      height: 6.48rem;
      border-radius: 0.3rem;
      background: url("../assets/kuang_index.png") no-repeat;
      background-size: 100% 100%;
      .right_item {
        width: 9.075rem;
        height: 6.36rem;
        padding: 0.06rem;
        border-radius: 0.28rem;
        overflow: hidden;
        position: relative;
        .amap-container {
          border-radius: 0.28rem;
        }

        .driver_line {
          width: calc(100% - 0.12rem);
          min-height: 0.5rem;
          background: rgba(0,0,0,0.6);
          position: absolute;
          top: 0.06rem;
          z-index: 999;
          //border-top-left-radius: 0.28rem;
          //border-top-right-radius: 0.28rem;
          border-radius: 0.28rem !important;
          display: flex;
          align-items: center;
          justify-content: center;
          div {
            width: 100%;
            font-size: 0.3rem;
            font-weight: bold;
            color: rgb(86, 208, 193) !important;
            overflow:hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            display: -webkit-box;
            -webkit-box-orient: vertical;

            line-height: 0.5rem;
            padding: 0 0.5rem;
            font-family: 黑体;
            letter-spacing: 0.02rem;
          }
        }
        .driver_user_info {
          position: absolute;
          left: 0.06rem;
          bottom: 0.06rem;
          z-index: 999;
          font-size: 0.24rem;

          width: calc(100% - 0.12rem);
          height: 0.5rem;

          border-radius: 0.28rem;
          //border-bottom-left-radius: 0.28rem;
          //border-bottom-right-radius: 0.28rem;
          //position: relative;
          animation: swinging 1.5s ease-in-out infinite alternate;
          transform-origin: 1.5rem 0.13rem;
          margin-top: 0.05rem;
          right: 0.05rem;
          overflow: hidden;


          .sign {
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.6);
            //border-radius: 0.28rem;
            position: absolute;
            bottom: 0;
            font-size: 0.3rem;
            //color: saddlebrown;
            color: rgb(86, 208, 193) !important;
            //font-family: serif;
            font-weight: bold;
            text-align: left;
            line-height: 0.5rem;
            padding: 0 0.5rem;
            font-family: 黑体;
            letter-spacing: 0.02rem;

            //text-shadow: 0 2px 0 rgba(255, 255, 255, 0.3),
            //  0 -2px 0 rgba(0, 0, 0, 0.7);
          }

          .strings {
            width: 1rem;
            height: 1rem;
            border: 0.05rem solid brown;
            position: absolute;
            border-right: none;
            border-bottom: none;
            transform: rotate(45deg);
            top: 0.3rem;
            left: 1rem;
          }

          .pin {
            width: 0.25rem;
            height: 0.25rem;
            border-radius: 50%;
            position: absolute;
          }

          .pin.top {
            background: gray;
            left: 1.4rem;
          }

          .pin.left,
          .pin.right {
            background: brown;
            top: 0.76rem;
            box-shadow: 0 0.02rem 0 rgba(255, 255, 255, 0.3);
          }

          .pin.left {
            left: 0.65rem;
          }

          .pin.right {
            right: 0.65rem;
          }

          //@keyframes swinging {
          //  from {
          //    transform: rotate(10deg);
          //  }
          //
          //  to {
          //    transform: rotate(-10deg);
          //  }
          //}

        }
        .map_onlineCar {
          background: url("../assets/onlieCar_right_bg.jpg") no-repeat;
          background-size: 100% 100%;
        }
        .ad,.hospital_remind,.weather_remind,.callOut_remind {
          width: 100%;
          height: 100%;
          border-radius: 0.28rem;
          overflow: hidden;
          font-family: 黑体;
          letter-spacing: 0.01rem;
          background-repeat: no-repeat !important;
          .remind_content {
            padding: 0.6rem;
            height: -webkit-fill-available;
            .title {
              font-size: 0.38rem;
              text-shadow: 0.03rem 0.03rem 0.05rem rgba(0, 0, 0, 0.5);
              font-weight: bold;
            }
          }

        }
        .ad {
          .remind_content {
            padding: 0 !important;
            height: 100%;
            width: 100%;
            .el-carousel {
              height: 100%;
            }
          }
        }

        .hospital_remind {
          background: url("../assets/hospital_right_bg.jpg") no-repeat;
          background-size: 100% 100%;
          .remind_content {
            padding: 1.2rem;
            padding-bottom: 0.4rem;
            font-weight: bold;
            .el-carousel {
              height: 100%;
            }

            .hospital_info {
              div {
                font-size: 0.36rem;
                display: flex;
                span {
                  display: inline-block;
                  min-height: 0.6rem;
                  line-height: 0.6rem;
                }
                span:nth-child(1) {
                  width: 1.6rem;
                  text-align: justify;
                  text-align-last: justify;
                  color: #C0D0FF
                }
                span:nth-child(3) {
                  max-width: 4.6rem;
                  display: -webkit-box;
                  overflow: hidden;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  text-overflow: ellipsis;
                }
                strong {
                  color: #C0D0FF;
                  line-height: 0.6rem;
                  margin-right: 0.2rem;
                }
              }
              div:nth-child(1) {
                margin-top: 0.4rem;
                span:nth-child(3) {
                  color: #FFB700 !important;
                }
              }
            }

          }

        }
        .weather_remind {
          background-size: cover !important;
          .remind_content {
            padding: 0.85rem;
            font-weight: bold;
            .title {
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              img {
                display: block;
                width: 0.32rem;
                height: 0.38rem;
                margin-left: 0.1rem;
              }
            }
            .weather_top {
              height: 2.1rem;
              overflow: hidden;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-top: 0.8rem;
              .weather_left {
                display: flex;
                flex-direction: column;
                width: 1.58rem;
                height: 100%;
                align-items: center;
                position: relative;
                margin-right: 0.1rem;
                img {
                  width: 100%;
                  height: 70%;
                  display: block;
                }
                span {
                  display: inline-block;
                  font-size: 0.32rem;
                  position: absolute;
                  bottom: 0.25rem;
                  text-shadow: 0.03rem 0.03rem 0.05rem rgba(0, 0, 0, 0.5);
                }
              }
              .weather_right {
                //width: 3.8rem;
                font-size: 2rem;
                text-shadow: 0.03rem 0.03rem 0.05rem rgba(0, 0, 0, 0.5);
                display: flex;
                span:nth-child(2) {
                  font-size: 0.8rem;
                  margin-left: 0.1rem;
                  margin-top: 0.3rem;
                  font-weight: 500;
                }
              }
            }
            .weather_bottom {
              //display: flex;
              //flex-direction: row;
              //justify-content: center;
              text-align: center;
              font-size: 0.34rem;
              margin-top: 0.3rem;
              letter-spacing: 0.02rem;
              text-shadow: 0.03rem 0.03rem 0.05rem rgba(0, 0, 0, 0.5);
              div {
                padding: 0 0.1rem;
                display: inline-block;
                margin-bottom: 0.2rem;
                span {
                  //display: inline-block;
                  //max-width: 1rem;
                  //overflow: hidden;
                  //text-overflow: ellipsis;
                  //white-space: nowrap;
                }
              }
            }
          }

        }
        .callOut_remind {
          background: url("../assets/callOut_right_bg.jpg") no-repeat;
          background-size: 100% 100%;
          .remind_content {
            padding: 1rem;
            font-weight: bold;
            .title {
              text-align: center;
            }
            .callOut_info {
              display: flex;
              flex-direction: column;
              align-items: center;
              .avatar {
                width: 2.4rem;
                height: 3.2rem;
                overflow: hidden;
                margin-top: 0.4rem;
                display: flex;
                flex-direction: column;
                align-items: center;
                .avatar_pic {
                  width: 2.2rem;
                  height: 2.2rem;
                  border-radius: 50%;
                  border: 0.06rem solid transparent;
                  overflow: hidden;
                  box-sizing: border-box;
                  background-clip: padding-box, border-box;
                  background-origin: padding-box, border-box;
                  background-image: linear-gradient(to right, transparent, transparent), linear-gradient(180deg, #C9CBC6, #AC9C8C);
                  img {
                    display: block;
                    width: 100%;
                    height: 100%;
                  }
                }
                span {
                  display: inline-block;
                  font-size: 0.34rem;
                  text-shadow: 0.03rem 0.03rem 0.05rem rgba(0, 0, 0, 0.5);
                  margin-top: 0.5rem;
                }
              }
              .created_time {
                font-size: 0.4rem;
                font-weight: 500;
                color: #DBD4CC

              }
            }

          }

        }
        .defaultBg {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          img {
            width: 4rem;
            height: 4rem;
          }
        }
      }
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    .xiaochengxu_logo {
      //position: absolute;
      //bottom: 0.3rem;
      //right: 1.2rem;
      width: 1.92rem;
      height: 0.9rem;
      img {
        display: block;
        width: 1.92rem;
        height: auto;
      }
      img[src=""],img:not([src]){
        opacity:0;
      }
    }
    .service_call {
      width: max-content;
      height: 0.8rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      //position: absolute;
      //bottom: 0.3rem;
      //right: 3.6rem;
      font-size: 0.28rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #fff;
      background-image: linear-gradient(to bottom, #ffffff, rgba(255, 255, 255, 0.65));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      span {
        display: block;
      }
    }
  }

</style>

<style lang="less">
  .index {
    .map_onlineCar {
      border-radius: 0.28rem;
    }
    .operatePopup {
      .el-dialog__header {
        .el-dialog__title {
          font-size: 0.5rem !important;
        }
      }
      .el-dialog__body {
        .callMyFriend {
          width: 8.76rem;

          //width: 4rem;
          //height: 2rem;
          overflow: hidden;
          position: relative;
          .popupTitle {
            height: 0.4rem;
            line-height: 0.4rem;
            font-size: 0.18rem;
            font-weight: bold;
            margin-bottom: 0.06rem;
          }
          .userList {
            width: max-content;
            position: absolute;
            left: 0;
            transition: all 0.3s;
            margin-top: 0.16rem;
            .userItem {
              width: 4.28rem;
              margin-right: 0.2rem;
              float: left;
              display: flex;
              flex-direction: column;
              align-items: center;
              border-radius: 0.18rem;
              margin-top: 0.1rem;
              span {
                display: block;
                margin-top: 0.1rem;
                font-size: 0.38rem;
                font-weight: bold;
                letter-spacing: 0.02rem;
              }
              img {
                display: block;
                width: 1.6rem;
                height: 1.6rem;
                border-radius: 50%;
              }

            }
          }
          .pointer {
            width: 4rem;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            justify-content: center;
            align-items: center;
            div {
              width: 0.08rem;
              height: 0.08rem;
              transition: all 0.3s;
              border-radius: 0.04rem;
              background: #888;
              display: inline-block;
              margin-right: 0.08rem;
            }
          }
        }
        //.popupMessage {
        //  padding: 0.2rem 0 ;
        //  .popupMessage {
        //    line-height: 0.4rem;
        //    text-align: center;
        //    font-size: 0.24rem;
        //    margin-bottom: 0.3rem;
        //    span {
        //      color: #406BCC;
        //      text-decoration: underline;
        //    }
        //  }
        //  .popupMessageBtn {
        //    width: 20vw;
        //    height: 0.6rem;
        //    line-height: 0.6rem;
        //    background: #5472B0;
        //    font-weight: bold;
        //    text-align: center;
        //    font-size: 0.24rem;
        //    color: #fff;
        //    border-radius: 0.16rem;
        //    margin: 0 auto;
        //    margin-top: 0.2rem !important;
        //  }
        //  .popupMessageBtn:last-child {
        //    margin-bottom: 0;
        //  }
        //  .messageBtnList {
        //    display: flex;
        //    .popupMessageBtn {
        //      width: 12vw;
        //    }
        //  }
        //}
        .popupMessage {
          padding: 0.2rem 0 ;
          .popupMessage {
            //line-height: 0.4rem;
            text-align: center;
            //font-size: 0.24rem;
            font-size: 0.45rem;
            margin-bottom: 0.3rem;
          }
          .popupMessageBtn {

            width: 8.75rem;
            height: 1rem;
            //width: 20vw;
            //height: 0.6rem;
            //line-height: 0.68rem;
            line-height: 1.18rem;
            background: #5472B0;
            font-weight: bold;
            text-align: center;
            //font-size: 0.24rem;
            font-size: 0.5rem;
            color: #fff;
            border-radius: 0.16rem;
            margin: 0 auto;
            margin-top: 0.2rem !important;
          }
          .popupMessageBtn:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    .ad {
      .el-carousel {
        height: 100%;
        .el-carousel__item {
          background-size: 100% 100% !important;
          background-repeat: no-repeat !important;
        }
        .ad_content {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%,-50%);
        }
      }

    }
  }

</style>
