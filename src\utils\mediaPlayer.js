import Bridge from '@/utils/bridge'
import store from '@/store/index'

var windowP = null
var playIndex = 0

var media = null

function getChromeVersion() {
    var arr = navigator.userAgent.split(' ');
    var chromeVersion = '';
    for(var i=0;i < arr.length;i++){
        if(/chrome/i.test(arr[i]))
            chromeVersion = arr[i]
    }
    if(chromeVersion){
        return Number(chromeVersion.split('/')[1].split('.')[0]);
    } else {
        return false;
    }
}

function DetermineKernel(P,callback) {
    if (JSON.stringify(navigator.userAgent).indexOf('Windows') > -1 || getChromeVersion() > 100) { // pc
        return new MediaPlayer_PC(P,callback);
    } else {
        return new MediaPlayer_Android(P,callback)
    }
}

// 通知APK播放
function MediaPlayer_Android(P,callback) {
    windowP = P
    this.MP = this
    this.MP.style = {}
    this.MP.mediaInfo = P
    //视频地址
    this.src = '';

    this.isFullscreen = 0;

    this.winPos = P.windowPos ? P.windowPos : {
        x: 0,
        y: 0,
        w: 1279,
        h: 719
    };
    this.init(P,callback);
    // store.state.app.media = this
}

MediaPlayer_Android.prototype = {
    init: (P,callback_app)=> {
        if (store.state.app.media) {
            this.stop()
        }
        var obj  = {
            cmd: 1,
            data:{
                x: Math.round(P.windowPos.x) ,
                y: Math.round(P.windowPos.y),
                w: Math.round(P.windowPos.w) ,
                h: Math.round(P.windowPos.h),
                r: Math.round(P.windowPos.r),
                full_window: 0,
                loop: P.loop ? 1 : 0,
                url: P.videoList
            }
        }
        obj = JSON.stringify(obj)

        setTimeout(()=>{
            Bridge.callhandler('setPlayer', obj,(res)=>{
                if (callback_app) {
                    callback_app()
                }
            })
        },200)
    },
    open: function (MRL) {

    },
    play: function () {


    },
    pause: function (t) {

    },
    windowPos: function (Pos) {

    },
    fullscreen: function (A) {
        var obj = null
        try {
            switch (A) {
                // 切换
                case 'toggle':
                case 't':
                    this.isFullscreen = 1 - this.isFullscreen;
                    obj  = {
                        cmd: 2,
                        data:{
                            x: Math.round(windowP.windowPos.x) ,
                            y: Math.round(windowP.windowPos.y),
                            w: Math.round(windowP.windowPos.w) ,
                            h: Math.round(windowP.windowPos.h),
                            r: Math.round(windowP.windowPos.r),
                            loop: windowP.loop ? 1 : 0,
                            full_window: this.isFullscreen
                        }
                    }
                    this.fullscreen(this.isFullscreen);
                    break;

                // 全屏
                case 'fs':
                case 1:
                {
                    obj  = {
                        cmd: 2,
                        data:{
                            loop: windowP.loop ? 1 : 0,
                            full_window: 1
                        }
                    }
                    this.isFullscreen = 1;
                }
                break;
                // 返回当前全屏状态
                default:
                    return this.isFullscreen;
            }
        } catch (E) { }

        obj = JSON.stringify(obj)
        Bridge.callhandler('setPlayer', obj,()=>{
        })

        return this;


        // if (A) {  // 1  全屏
        //     obj  = {
        //         cmd: 2,
        //         data:{
        //             loop: windowP.loop ? 1 : 0,
        //             full_window: A
        //         }
        //     }
        //     this.isFullscreen = 1
        // } else {  // 退出全屏
        //     obj  = {
        //         cmd: 2,
        //         data:{
        //             x: Math.round((P.windowPos.x + 446) * P.fontSize) ,
        //             y: Math.round((P.windowPos.y + 128) * P.fontSize),
        //             w: Math.round((P.windowPos.w + 448) * P.fontSize) ,
        //             h: Math.round((P.windowPos.h + 320) * P.fontSize),
        //             r: Math.round((p.windowPos.r) * P.fontSize),
        //             loop: windowP.loop ? 1 : 0,
        //             full_window: A,
        //         }
        //     }
        //     this.isFullscreen = 0
        // }
        // obj = JSON.stringify(obj)
        // Bridge.callhandler('setPlayer', obj,()=>{
        // })
    },
    stop: function (callback) {  // 停止播放  消毁
        store.state.app.media = null
        var obj  = {
            cmd: 0
        }
        obj = JSON.stringify(obj)
        Bridge.callhandler('setPlayer', obj,()=>{
            if (callback) {
                callback()
            }
        })
    },
    setWindow: function (obj) {

    }
}

// PC浏览器
function MediaPlayer_PC(P,callback) {
    //平台名
    this.platform = 'pc';

    P = P || {};

    if (document.getElementById('videoPlayer')) {
        document.getElementById("videoPlayer").remove()
    }
    //播放器
    this.video = document.createElement('video');
    this.audio = document.createElement('audio');

    this.MP = this.video;

    this.MP.controls = 'controls';
    this.MP.id = 'videoPlayer';

    //视频地址
    this.src = '';

    this.isFullscreen = 0;

    this.winPos = P.windowPos ? P.windowPos : {
        x: 0,
        y: 0,
        w: 1279,
        h: 719
    };

    this.init(P,callback);
    this.onStop = function () { };
    this.windowPos(this.winPos);
    // store.state.app.media = this
}

MediaPlayer_PC.prototype = {
    init: function (P,callback) {

        this.stop()
        playIndex = 0
        windowP = P
        this.open(P.videoList)
        if (callback) {
            callback()
        }
        if (P.videoList.length < 2) {
            if (P.loop) {
                this.MP.loop = 'loop';
            }
            return
        }
        this.MP.onended =  ()=> {
            if (P.loop) {
                playIndex++
                if (playIndex > P.videoList.length-1) {
                    playIndex = 0
                }
                this.MP.src = P.videoList[playIndex]
            }

        };

    },
    open: function (urlList) {
        // if (/mp3/.test(MRL)) {
        //     this.MP = this.audio;
        // }
        // document.body.appendChild(this.MP,document.body.firstElementChild);
        // document.body.insertBefore(this.MP,document.getElementById('home').firstElementChild);
        document.getElementById('app').firstElementChild.appendChild(this.MP)

        // this.MP.loop = 'loop';

        this.MP.muted = 'muted '

        this.MP.autoplay = 'autoplay';

        this.MP.src = urlList[playIndex];
    },

    pause: function (t) {
        if (t == 'toggle' || t == 't') {
            this.MP.paused ? this.MP.play() : this.MP.pause();
            return this;
        }
        this.MP.pause();
    },
    windowPos: function (Pos) {
        var styleKey = {
            x: 'left',
            y: 'top',
            w: 'width',
            h: 'height',
            r: 'borderRadius'
        }
        this.MP.style.position = 'absolute';
        this.MP.style.zIndex = '99';
        this.MP.style.background = "#000"
        this.MP.style.transition = "all 0.3s"
        // this.MP.style.objectFit = "cover"
        Object.keys(Pos).forEach((key)=>{
            this.MP.style[styleKey[key]] = (Pos[key] * windowP.fontSize + 'px');
        })
        // with (this.MP.style) {
        //     setTimeout(() => {
        //         position = 'absolute';
        //         width = ((Pos.w) * windowP.fontSize + 'px');
        //         height = ((Pos.h)* windowP.fontSize+ 'px');
        //         left = ((Pos.x) * windowP.fontSize + 'px');
        //         top =  ((Pos.y) * windowP.fontSize + 'px') ;
        //         zIndex = '999';
        //         background = "#000"
        //     }, 0);
        // }
    },
    fullscreen: function (A) {
        try {
            switch (A) {
                // 切换
                case 'toggle':
                case 't':
                    {
                        this.isFullscreen = 1 - this.isFullscreen;
                        if (this.isFullscreen) {
                            this.MP.muted = false
                        } else {
                            this.MP.muted = true
                        }
                        this.fullscreen(this.isFullscreen);
                    }
                    break;

                // 全屏
                case 'fs':
                case 1:
                    {
                        this.windowPos({
                            x: 0,
                            y: 0,
                            w: 1920,
                            h: 1080
                        });
                        this.isFullscreen;
                    }
                    break;

                // 窗口化
                case 'window':
                case 'win':
                case 0:
                    {
                        this.windowPos(this.winPos);
                        this.isFullscreen = 0;
                    }
                    break;

                // 返回当前全屏状态
                default:
                    return this.isFullscreen;
            }
        } catch (E) { }

        return this;
    },
    stop: function (callback) {
        if (document.getElementById("videoPlayer")) {
            this.MP.pause()
            this.MP.removeAttribute('src')
            this.MP.load()
            this.MP = null
            document.getElementById("videoPlayer").remove()
            if (callback) {
                callback()
            }
        }
        store.state.app.media = null
    },
    setWindow: function (obj) {
        this.windowPos(obj)
        this.stop()
    }
}


export default {
    DetermineKernel
};