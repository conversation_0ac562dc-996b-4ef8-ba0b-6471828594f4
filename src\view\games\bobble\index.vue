<template>
  <div class="bobble">

    <div class="game">
      <div class="game-l" ref="game_l">
        <div class="game-t" ref="game_t">
          <div class="arrow" ref="arrow"></div>
          <div class="bulllet" ref="bulllet"></div>
        </div>
        <div class="game-b" ref="game_b">
          <div class="cannon">

          </div>
        </div>
      </div>
    </div>

    <div class="game-r">
      <div class="game-rb">
        <div class="fraction">
          <p class="fraction_title">当前分数</p>
          <span class="scoring" ref="scoring">0</span>

          <div class="message">
            <h2>操作说明:</h2>
            <p>
              <span>1.</span>
              <span>左、右键调节炮台角度。</span>
            </p>
            <p>
              <span>2.</span>
              <span>确认键发射泡泡弹。</span>
            </p>
            <p>
              <span>3.</span>
              <span>三个以上相同颜色泡泡弹相连得分。</span>
            </p>
            <p>
              <span>4.</span>
              <span>返回键重新游戏/退出游戏。</span>
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="pop" ref="pop">
      游戏开始
    </div>
    <div class="over" ref="over">
      <img src="./images/GameOver.png" alt="游戏结束">
    </div>



    <el-dialog
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        custom-class="operatePopup"
        :title="popupModule == 1 ? '操作' : '提示'"
    >
      <!--操作弹窗-->
      <div class="btnList" v-if="popupModule == 1">
        <div
            class="btnItem"
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"

        >
          <div class="box-btnItem"  v-html="item.text"></div>

        </div>
      </div>

      <!--消息弹窗-->
      <div class="popupMessage" v-if="popupModule == 2">
        <div class="popupMessage" v-html="popupMessage"></div>
        <div
            class="popupMessageBtn"
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
        >
          {{ item.text }}
        </div>
      </div>


    </el-dialog>

  </div>
</template>

<script>

export default {
  name:'bobble',
  components: {
  },
  inject: ['reload'],
  data() {
    return {
      dialogVisible: false,
      popupModule: 1, // 1、按钮弹窗  2、消息提示
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [
        {
          text: '重新开始',
          ref: 'active',
          num: 0,
          fuc: this.NextLevel
        },
        {
          text: '退出游戏',
          ref: '',
          num: -1,
          fuc: this.NextLevel
        },
      ],

      gameL:null,    // 整个游戏区
      gameB: null,   //发射区域
      gameT: null,   //小球区域
      arrow: null,   //炮台
      bulllet: null, // 子弹
      // option: null,   // 下一个球区域
      // start: null,   // 开始按钮
      pop: null,     // 游戏开始文字区域
      over: null,    // 游戏结束文字区域
      scoring: null,  // 计分区域

      bullData: [], //存放小球初始数组
      size: 44 * this.$store.getters.screen_multiple,     // 小球尺寸
      maxRows: 0,   //最大行
      maxSort: 0,   // 最大列
      idx: 0,       // 小球编号
      mark: 0,     // 分数
      bullet:{
        color: 'red'
      },
      flag: true,   // 游戏开关
      transform: 0
    };
  },
  created() {
    this.$store.dispatch('index/setMainTitle',JSON.parse(sessionStorage.getItem('redirectInfo')).title ? JSON.parse(sessionStorage.getItem('redirectInfo')).title : '泡泡龙')
    this.$store.dispatch('index/setFocusDom', null)
  },
  computed: {},
  watch: {},
  mounted() {
    this.gameL = this.$refs.game_l
    this.gameB = this.$refs.game_b
    this.gameT = this.$refs.game_t
    this.arrow = this.$refs.arrow
    this.bulllet = this.$refs.bulllet;

    this.pop = this.$refs.pop
    this.over = this.$refs.over
    this.scoring = this.$refs.scoring

    this.maxRows = ~~ (this.gameT.offsetHeight / this.size)
    this.maxSort = ~~ (this.gameT.offsetWidth / this.size)

    this.spread();
    this.init();
    this.initbull();

    this.arrow.style.transform = `rotate(${this.transform}deg)`

    this.fuc.KeyboardEvents({
      down:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }

      },
      up:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
      },
      left:()=>{
        if (this.dialogVisible) {
          return
        }
        this.transform -= 4
        if (this.transform  < -70) {
          this.transform = -70
        }
        this.arrow.style.transform = `rotate(${this.transform}deg)`

        this.bulllet.style.transform = `rotate(${this.transform}deg)`

      },
      right:()=>{
        if (this.dialogVisible) {
          return
        }
        this.transform += 4
        if (this.transform  > 70) {
          this.transform = 70
          return
        }
        this.arrow.style.transform = `rotate(${this.transform}deg)`

        this.bulllet.style.transform = `rotate(${this.transform}deg)`



      },
      enter:()=>{
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].fuc(this.popupBtnList[this.popupBtnNums].num)
          return
        }
        if (this.flag) {
          let timer;
          let speed = 18;
          let _speedX = speed;
          //发射子弹 子弹发射角度
          let iAngle = Number(this.arrow.style.transform.match(/rotate\((.+)deg\)/)?.[1]);
          clearInterval(timer);
          timer = setInterval(() => {
            let x = this.bulllet.offsetLeft, y = this.bulllet.offsetTop;
            if (x < 0 || x > (this.gameT.offsetWidth - this.size)) {
              _speedX *= -1;
            }
            let collisionBalls = this.collisionBall({ x, y });
            if (collisionBalls?.length > 0) {
              let collisionIdx = this.getShortDistance(collisionBalls);
              //寻找碰撞到的球的最近的非连接的兄弟球的下标
              let targetIdx = this.getFreeSpace(collisionIdx, { x, y });
              //命中处理
              this.hitTarget(targetIdx);
              clearInterval(timer);
              return false;
            }

            x += _speedX * Math.cos((iAngle - 90) * Math.PI / 180);
            y += speed * Math.sin((iAngle - 90) * Math.PI / 180);
            this.bulllet.style.left = x + "px";
            this.bulllet.style.top = y + "px";

          }, 1000 / 60);
        }

      },
      esc:()=>{

        this.dialogVisible = !this.dialogVisible
        return

      }
    },null,true)


  },
  methods: {
    popupClose() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', null);
        setTimeout(()=>{
          this.popupBtnNums = 0
          this.popupModule = 1
        },200)
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        setTimeout(()=>{
          document.getElementById('focus_border').style.borderColor = '#5472B0'
        },0)
        return
      })
    },
    NextLevel(type) {
      // 重新开始

      if (type == 0) {
        this.dialogVisible = !this.dialogVisible
        this.reload()

      } else
      // 退出
      if (type == -1) {
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      }
    },
    spread() {
      let fragment = document.createDocumentFragment();
      let vm = this;
      for (let i = 0; i < this.maxRows; i++) {
        let line = Number(!(i % 2 === 0))
        for (let j = 0; j < this.maxSort - line; j++) {
          let ele = document.createElement("div");
          ele.classList.add("bull");

          let color = this.getColor();
          let idx = this.idx
          let left = this.size * (j + (i % 2) / 2);
          let top = i * (this.size - (i && 6));
          let bull = new Proxy({
            left,
            top,
            color,
            ele,
            connect: false,
            idx,
            row: i
          }, {
            set(target, key, val) {
              if (key === "connect" && val === false) {
                vm.dropOff(vm.bullData[target.idx].ele);
              }
              target[key] = val;
              return true;  // 一定要返回 true，表明设置成功
            },

          })
          fragment.appendChild(ele)
          this.bullData.push(bull);
          idx++
          this.idx = idx
        }
      }
      this.gameT.appendChild(fragment);
    },
    // 初始化炮台
    init() {
      this.setStyle(this.arrow, {
        top: this.gameT.offsetHeight - (60 * this.$store.getters.screen_multiple) + ["px"],
        left: (this.gameT.offsetWidth / 2) - (this.arrow.offsetWidth / 2) + "px",
        zIndex: 2,
      })
    },
    // 初始化子弹
    initbull() {
      let color = this.getColor();
      this.bulllet.style.cssText = '';

      let cssObj = {
        display: "block",
        background: 'url('+color+') no-repeat',
        left: (this.gameB.offsetWidth / 2) - (this.size / 2) + "px",
        top: this.gameT.offsetHeight - (20 * this.$store.getters.screen_multiple) + ["px"],
        zIndex: 3,
      }

      if (this.transform != 0) {
        cssObj.transform = `rotate(${this.transform}deg)`
      }

      this.bullet.color = color;
      this.setStyle(this.bulllet, cssObj)
      // this.option.style.backgroundColor = color;
    },

    dropOff(ele) {
      this.animate({
        ele,
        styleJson: {
          top: ele.offsetTop + (40 * this.$store.getters.screen_multiple) + 'px',
          opacity: 0,
          transform: 'scale(.5)'
        },
        callback() {
          ele.style.cssText = '';
          this.setStyle(ele, {
            display: 'none'
          })
        }
      })
    },
    placeBall(ball, color = '') {
      ball.connect = true;
      ball.color = color || this.getColor();
      ball.ele.style.cssText = '';
      this.setStyle(ball.ele, {
        display: "block",
        top: ball.top  + 'px',
        left: ball.left + 'px',
        // backgroundColor: ball.color
        background: 'url('+color+') no-repeat',
      });
    },
    //动画函数
    animate({ ele, styleJson = {}, time = 300, speed = 'linear', callback } = {}) {
      ele.style.transition = `${time}ms ${speed}`;
      const end = () => {
        callback && callback();
        ele.removeEventListener('transitionend', end);
        ele.style.transition = '';
      }
      this.setStyle(ele, styleJson);
      ele.addEventListener('transitionend', end, false);
    },

    //命中目标后处理置换球 三色消除 失根消除 重置子弹
    hitTarget(idx) {
      let target = this.bullData[idx];
      if (target) {
        this.placeBall(target, this.bullet.color);
        let colorBalls = this.findSeriesNode([idx], true);
        if (colorBalls.length >= 3) {
          //有三个以上相同颜色连通的球
          colorBalls.forEach(item => this.bullData[item].connect = false);
          let arr = [];
          this.traceConnect().forEach(item => {
            arr.push(item)
            this.bullData[item].connect = false;
          });
          this.mark += (colorBalls.length + arr.length);
          this.scoring.innerText = this.mark;
        }
        this.initbull();
      } else {
        this.flag = false;
        this.over.style.zIndex = 1;
        this.over.style.opacity = 1;

      }
    },



    //递归追踪与中心ball坐标相连通的所有复合条件的兄弟节点
    findSeriesNode(sameColorSiblings = [], color) {
      let collectArr = sameColorSiblings.slice();
      const recu = (arr) => {  // 用箭头函数代替普通函数
        for (let i = 0; i < arr.length; i++) {
          let siblings = this.getSameTypeSibilings(arr[i], color);
          siblings = siblings.filter(item => (
              collectArr.indexOf(item) === -1
          ));
          collectArr = collectArr.concat(siblings);
          if (siblings.length > 0) {
            recu(siblings);
          }
        }
      };
      recu(collectArr);
      return collectArr;
    },
    //查询所有小球是否链接
    traceConnect() {
      let lose = [];
      for (let i = 0; i < this.bullData.length; i++) {
        if (this.bullData[i].connect) {
          let temp = this.iteration([i]);
          let res = temp.some(item => item < this.maxSort)
          if (!res) {
            lose.push(i)
          }
        }
      }

      return lose;
    },
    //递归追踪与中心ball坐标相连通的所有复合条件的兄弟节点
    iteration(sameColor = [], color) {
      let collectArr = sameColor.slice();
      const recu = (arr) => {
        for (let i = 0; i < arr.length; i++) {
          let siblings = this.getSameType(arr[i], color);

          siblings = siblings.filter(item => {
            return collectArr.indexOf(item) === -1
          });

          collectArr = collectArr.concat(siblings);
          if (siblings.length > 0) {
            recu(siblings);
          }

        }
      }
      recu(collectArr);
      return collectArr;
    },
    //寻找与中心点相连并且颜色相同的兄弟下标数组
    getSameType(idx, color = false) {
      return Object.entries(this.seek(idx)).map(([key, val]) => val).filter(item => {
        if (!this.bullData[item]) { return false }
        let flag = this.bullData[item].connect === true;
        if (color) {
          return flag && this.bullData[item].color === this.bullData[idx].color;
        }
        return flag;


      })
    },
    //寻找与中心点相连并且符合条件的兄弟
    getSameTypeSibilings(idx, color = false) {

      return Object.entries(this.seek(idx)).map(([key, value]) => value).filter(item => {
        //如果有color参数寻找颜色一样 并且 connect为true的兄弟
        //如果没有color参数 寻找connect为true的兄弟
        if (!this.bullData[item]) {
          return false;
        }
        let flag = this.bullData[item].connect === true;
        if (color) {
          return flag && this.bullData[item].color === this.bullData[idx].color
        }
        return flag;
      })
    },
    collisionBall({ x = 0, y = 0 } = {}) {
      let balls = this.bullData.filter(item => item.connect);
      balls = this.getCollisionsDistance(balls, x, y);
      if (balls.length === 0 && y < this.size / 2) {
        //判断子弹是否碰撞到顶边 既没有碰撞到任何球 y的位置也<22
        let topBall = this.bullData.slice(0, this.maxSort).reduce((acc, curr) => {
          if (Math.abs(acc.left - x) >= Math.abs(curr.left - x)) {
            acc = curr;
          }
          return acc;
        });
        balls[0] = {
          idx: topBall.idx,
          distance: 0
        }
      }
      return balls;
    },
    getShortDistance(arr = []) {
      if (arr.length === 0) {
        return arr;
      }
      if (arr.length === 1) {
        return arr[0].idx;
      }
      return arr.reduce((acc, curr) => {
        if (acc.distance >= curr.distance) {
          acc = curr;
        }
        return acc;
      }).idx;
    },
    getCollisionsDistance(balls, x, y) {
      return balls.map(item => {
        let _x = item.left - x;
        let _y = item.top - y;
        let distance = Math.sqrt(_x * _x + _y * _y);
        if (distance < this.size) {
          return {
            idx: item.idx,
            distance
          }
        }
        return null;
      }).filter(item => item !== null);
    },
    //根据下标寻找兄弟中距离目标最近的空位
    getFreeSpace(idx, { x = 0, y = 0 } = {}) {
      if (this.bullData[idx].connect === false) {
        return idx;
      }
      let balls = Object.entries(this.seek(idx)).map(([key, value]) => value).filter(item => {
        return this.bullData[item]?.connect === false;
      }).map(item => this.bullData[item]);

      return this.getShortDistance(this.getCollisionsDistance(balls, x, y))
    },
    //传入下标 返回6角方向的兄弟们
    seek(idx = 0) {
      let { tens, units } = this.getDigit(idx)
      return this.getRigthSolt(idx, {
        tl: (tens - 1) * this.maxSort + (units - 0),
        tr: (tens - 1) * this.maxSort + (units + 1),
        ml: (tens - 0) * this.maxSort + (units - 1),
        mr: (tens - 0) * this.maxSort + (units + 1),
        bl: (tens + 1) * this.maxSort + (units - 1),
        br: (tens + 1) * this.maxSort + (units - 0),
      })
    },
    //拆分一个数的十位与各位
    getDigit(num) {
      return {
        tens: ~~(num / this.maxSort),
        units: ~~(num % this.maxSort)
      }
    },
    //重置六个兄弟ball中错误行或不存在的项下标为null
    getRigthSolt(idx = 0, { tl, tr, ml, mr, bl, br } = {}) {
      const diffRow = {
        t: -1,
        m: 0,
        b: 1
      }
      let row = this.bullData[idx].row;
      return Object.entries({ tl, tr, ml, mr, bl, br }).reduce((acc, [key, val]) => {
        acc[key] = (this.bullData[val] && this.isRightSolt(this.bullData[val].row, row, diffRow[key[0]])) ? val : null;
        return acc
      }, {})
    },
    //判断某个兄弟ball 的行号是否正确 放置边角错行
    isRightSolt(sRow, row, padNum) {
      return (row + padNum) === sRow;
    },
    getColor() {
      // const colors = ['#f25a5a', '#eccf64', '#b68eb1', '#6697ca', '#7dc6d7', '#80c29c'];
      const colors = [require('./images/box1.png'), require('./images/box2.png'), require('./images/box3.png'), require('./images/box4.png'), require('./images/box5.png'), require('./images/box6.png')];

      return colors[~~(Math.random() * colors.length)]
    },
    setStyle(dom, css) {
      for (var key in css) {
        dom['style'][key] = css[key];
      }
    },
    getPosition(element) {
      var pos = {
        left: 0,
        top: 0
      }
      while (element.offsetParent) {
        pos.left += element.offsetLeft;
        pos.top += element.offsetTop;
        element = element.offsetParent;
      }
      return pos;
    }
  },
  destroyed() {

  },
  beforeDestory() {
  },
}
</script>
<style lang='less' scoped>
  .bobble {
    //body,
    //html {
    //  background: url(./images/bg.jpeg) no-repeat;
    //  background-size: 100%;
    //}
    height: 7rem;
    border: 1px solid transparent;
    position: relative;
    .game {
      display: flex;
      justify-content: center;
      align-items: center;
      background: url('./images/bg.jpg') no-repeat;
      background-size: 100% 100% !important;
      border-radius: 0.16rem;
      width: max-content;
      margin: auto;
      margin-top: -0.2rem;
      //background: rgba(0, 0, 0, .5);
      .game-l {
        height: 7.4rem;
        width: 8rem;
        position: relative;
      }

      .game-l .game-t {
        position: relative;
        height: 93%;
        width: 100%;
        //border-bottom: 2px solid #ccc;
      }





      .game-b {
        width: 100%;
        height: 0.25rem;
        position: absolute;
        bottom: 0;
        .cannon {
          width: 0.96rem;
          height: 0.25rem;
          background: url("./images/base.png") no-repeat;
          background-size: 100% 100%;
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-52%);
        }
      }
    }


    .game-r {
      height: 5rem;
      width: 3rem;
      position: absolute;
      bottom: -0.2rem;
      right: 0;
      //border: 0.04rem solid #5FC5DC;
      //border-radius: 0.16rem;

      background: url("../../../assets/connectFour_state.png") no-repeat;
      background-size: 100% 100%;
      .game-rb {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 0.2rem;
        font-weight: 700;
        user-select: none;

        .fraction {

          .fraction_title,.scoring {
            margin-top: 20px;
            color: #f8df54;
            text-align: center;
          }
          .scoring {
            display: block;
            margin: auto;
            margin-top: 10px;
            width: 2.13rem;
            background-color: #f8df54;
            color: #000;
          }
          .message {
            width: 2.38rem;
            height: 3.2rem;
            padding: 0.3rem;
            border-radius: 0.16rem;
            margin-top: 0.1rem;
            h2 {
              color: #1EE8F8;
              //margin-bottom: 0.2rem;
            }
            p {
              font-size: 0.236rem;
              display: flex;
              //margin-bottom: 0.1rem;
              //span {
              //  display: inline-block;
              //}
              //span:nth-child(2) {
              //  margin-top: -0.04rem;
              //  margin-left: 0.06rem;
              //}
            }
          }

        }
      }
    }





    .img img {
      position: absolute;
      top: -0.5rem;
      left: -1rem;
      width: 4rem;
      height: 2rem;
    }



    .arrow {
      position: absolute;
      width: 0.7rem;
      height: 1.04rem;
      background: url(./images/cannon.png) no-repeat;
      background-size: 100% 100%;
      transform-origin: 50% 70%;
    }

    .bulllet {
      position: absolute;
      width: 0.44rem;
      height: 0.44rem;
      border-radius: 50%;
      background-size: 100% 100% !important;
      transform-origin: 48.8% 75.5%;
      //box-shadow: -0.02rem -0.02rem 0.12rem rgb(100 100 100 / 70%) inset;
    }

    .pop {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-2.1rem, -1.5rem);
      font-size: 0.6rem;
      background-image: linear-gradient(to right, #5bbbd5, #63c834);
      -webkit-background-clip: text;
      color: transparent;
      transition: all 0.3s;
      opacity: 0;
      z-index: -1;
    }

    .over {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      opacity: 0;
      transition: all 0.3s;
      z-index: -1;
    }

    .over img {
      width: 4.4rem;

    }

  }
</style>
<style lang="less">
  .bobble {
    .bull, .bulls {
      position: absolute;
      width: 0.44rem;
      height: 0.44rem;
      border-radius: 50%;
      //background-color: #f0d264;
      background-size: 100% 100% !important;
      text-align: center;
      line-height: 0.44rem;
      color: #fff;
      //box-shadow: -0.02rem -0.02rem 0.12rem rgb(100 100 100 / 70%) inset;
      user-select: none;
      display: none;
    }

    .bulls {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-0.22rem, -0.22rem);
      display: block;
    }
    img {
      /* border 0 照顾低版本浏览器 如果图片外面包含了链接会有边框的问题 */
      border: 0;
      /* 取消图片底侧有空白缝隙的问题 */
      vertical-align: middle
    }

    .hide,
    .none {
      display: none
    }

    /* 清除浮动 */
    .clearfix:after {
      visibility: hidden;
      clear: both;
      display: block;
      content: ".";
      height: 0
    }

    .clearfix {
      *zoom: 1
    }

    .el-dialog {
      //width: fit-content;
      //margin-top: 0 !important;
      //top: 50%;
      //transform: translateY(-50%);
      //border-radius: 0.16rem;
      .el-dialog__header {
        .el-dialog__title {
          font-size: 0.5rem !important;
        }
      }
      .el-dialog__body {
        .btnList {
          padding: 0.2rem 0 ;

          .btnItem {
            width: 8.75rem;
            height: 1rem;
            line-height: 1.08rem;
            background: #5472B0;
            //color: #fff;
            border-radius: 0.2rem;
            margin-bottom: 0.3rem;
            text-align: center;
            font-weight: bold;
            font-size: 0.5rem;
            position: relative;
            //text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.3);
            letter-spacing: 0.05rem;
            text-indent: 0.05rem;
            img {
              width: 0.48rem;
              height: 0.48rem;
              position: absolute;
              top: 50%;
              left: 2.2rem;
              transform: translateY(-45%);
            }
            .box-btnItem {
              background: linear-gradient(to bottom, #FDFEFF 30%, #BED1FB 90%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              //text-shadow: 0rem 0.02rem 0.05rem rgba(0, 0, 0, 0.5);
            }
          }
          .btnItem:last-child {
            margin-bottom: 0;
          }
        }
        .popupMessage {
          padding: 0.2rem 0 ;
          .popupMessage {
            //line-height: 0.4rem;
            text-align: center;
            //font-size: 0.24rem;
            font-size: 0.45rem;
            margin-bottom: 0.3rem;
          }
          .popupMessageBtn {

            width: 8.75rem;
            height: 1rem;
            //width: 20vw;
            //height: 0.6rem;
            //line-height: 0.68rem;
            line-height: 1rem;
            background: #5472B0;
            font-weight: bold;
            text-align: center;
            //font-size: 0.24rem;
            font-size: 0.5rem;
            color: #fff;
            border-radius: 0.16rem;
            margin: 0 auto;
            margin-top: 0.2rem !important;
          }
          .popupMessageBtn:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }



</style>
