<template>
  <div class="appointment">
    <div class="container">
      <div class="left">
        <div
          class="messageCenterList scrollParent"
          v-if="this.leftList.length > 0"
        >
          <div class="leftList" :style="{ top: '0rem' }">
            <div
              class="messageItem"
              :ref="item.ref"
              v-for="(item, index) in leftList"
              :key="index"
            >
              <div class="item_user">
                <div class="content">
                  <div class="name">
                    {{ item.start_time }}-{{ item.end_time }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_info" v-else>暂无内容</div>
      </div>
      <el-dialog
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        title="预约挂号"
      >
        <!--发起预约-->
        <div class="message_box sendCall" v-if="popupModule == 1">
          <div class="message_content">
            <div class="hospital">医院名称：{{ $route.query.hos_name }}</div>
            <div class="department">
              科室名称：{{ $route.query.dept_name }}
              {{ $route.query.dept_name }}
            </div>
            <div class="doctor" v-if="$route.query.resource_name">
              选择医生：{{ $route.query.resource_name }}
            </div>
            <div class="doctor" v-else>门诊类型：{{ $route.query.title }}</div>
            <div class="time">
              预约时间：{{ $route.query.date }}
              {{ leftList[leftNums] && leftList[leftNums].start_time }}-{{
                leftList[leftNums] && leftList[leftNums].end_time
              }}
            </div>
          </div>
        </div>

        <!--取消预约-->
        <div class="message_box cancel" v-if="popupModule == 2">
          <div>是否要取消预约?</div>
        </div>

        <div
          class="message_box"
          style="
            text-align: center;
            font-size: 0.37rem;
            justify-content: center;
          "
          v-html="popupMessage"
          v-if="popupModule == 3"
        ></div>

        <div class="popupBtnList">
          <div
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
          >
            {{ item.text }}
          </div>
        </div>
      </el-dialog>
    </div>
    <div class="btnList_bottom">
      <div class="btnList">
        <div
          class="btnItem"
          v-for="(item, index) in btnList"
          :key="index"
          :ref="item.ref"
        >
          <div class="text">{{ item.text }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
      
      <script>
import { getSJTimeList, createSJOrder } from '@/api/index'

export default {
  components: {},
  data() {
    return {
      unfinishList: [],
      leftList: [],
      rightList: [],
      statusList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧  -2在最右边
      rightNums: 0,
      unfinishNums: 0,
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      btnList: [
        {
          text: '重新开始',
          ref: '',
        },
        {
          text: '退出',
          ref: '',
        },
      ],
      btnNums: 0,
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '预约挂号-选择时间')
    this.$store.dispatch('index/setFocusDom', null)
  },
  computed: {},
  watch: {},
  mounted() {
    this.statusList = JSON.parse(localStorage.getItem('statusList'))

    this.hospital_name = this.$route.query.title
    this.hospital_addr = this.$route.query.addr
    this.ks = this.$route.query.ks_name
    this.getData()
    if (sessionStorage.getItem('indexFocusDoc')) {
      this.rightNums = Number(sessionStorage.getItem('indexFocusDoc'))
      sessionStorage.removeItem('indexFocusDoc')
    }
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          return
        }
        if (this.nextNums > -1) {
          if (
            this.leftNums < this.leftList.length - 1 &&
            this.leftList.length > 0
          ) {
            if (this.leftList[this.leftNums + 4]) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums += 4
              this.leftList[this.leftNums].ref = 'active'
            } else {
              if (
                this.leftNums <
                  this.leftList.length - (this.leftList.length % 4) &&
                this.leftList.length % 4 != 0
              ) {
                this.leftList[this.leftNums].ref = ''
                this.leftNums = this.leftList.length - 1
                this.leftList[this.leftNums].ref = 'active'
              } else {
                this.leftList[this.leftNums].ref = ''
                this.nextNums = -1
                this.btnList[this.btnNums].ref = 'active'
              }
            }
          } else {
            this.leftList[this.leftNums].ref = ''
            this.nextNums = -1
            this.btnList[this.btnNums].ref = 'active'
          }
        }

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          return
        }
        if (this.nextNums == -1 && this.leftList.length > 0) {
          this.btnList[this.btnNums].ref = ''
          this.nextNums = this.leftNums
          this.leftList[this.leftNums].ref = 'active'
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        } else if (this.nextNums > -1) {
          if (this.leftNums > 0 && this.leftNums - 4 >= 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums -= 4
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        if (this.nextNums > -1) {
          if (this.leftNums % 4 != 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        } else if (this.nextNums == -1) {
          if (this.btnNums > 0) {
            this.btnList[this.btnNums].ref = ''
            this.btnNums--
            this.btnList[this.btnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        if (this.nextNums > -1) {
          if (this.leftNums < this.leftList.length - 1) {
            if (this.leftNums % 4 != 3) {
              this.leftList[this.leftNums].ref = ''
              this.leftNums++
              this.leftList[this.leftNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          }
        } else if (this.nextNums == -1) {
          if (this.btnNums < this.btnList.length - 1) {
            this.btnList[this.btnNums].ref = ''
            this.btnNums++
            this.btnList[this.btnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      },
      enter: () => {
        if (this.nextNums > -1 && this.leftList.length > 0) {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].fuc(
              this.leftList[this.leftNums]
            )
            return
          }
          if (this.leftList[this.leftNums]) {
            this.popupBtnList = [
              {
                text: '关闭',
                ref: '',
                fuc: () => {
                  this.dialogVisible = false
                },
              },
              {
                text: '发起预约',
                ref: '',
                fuc: this.createSJOrder,
              },
            ]
            this.dialogVisible = true
            this.popupModule = 1
            this.leftList[this.leftNums].ref = ''
            this.popupBtnList[this.popupBtnNums].ref = 'active'
          }
        } else if (this.nextNums == -1) {
          if (this.btnNums === 0 || this.btnNums === 1) {
            const sessionKeys = [
              'indexFocusDOne',
              'indexFocusDTwo',
              'chosenIndex',
              'indexDocOrDept',
              'indexFocusAppment',
              'indexChosenDept',
            ]

            if (this.btnNums === 1) {
              sessionKeys.push('indexFocusHos')
            }

            sessionKeys.forEach((key) => sessionStorage.removeItem(key))

            // this.$router.push({
            //   path: this.btnNums === 0 ? './hospital_sj' : './supplier',
            // })
            if (this.$route.query.order_type == 1) {
              if (this.btnNums === 0) {
                history.go(-5)
              } else {
                history.go(-6)
              }
            } else if (this.$route.query.order_type == 2) {
              if (this.btnNums === 0) {
                history.go(-6)
              } else {
                history.go(-7)
              }
            }
          }
        }
        return
      },
      esc: () => {
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.unfinishList[this.unfinishNums].ref = 'active'
          this.dialogVisible = false
          return
        }

        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        this.popupBtnNums = 0
        this.popupModule = 1
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#b36371'
      })
    },
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState', true)
      // let leftICon = []
      getSJTimeList({
        hos_org_code: this.$route.query.hos_org_code,
        one_dept_code: this.$route.query.one_dept_code,
        dept_code: this.$route.query.dept_code,
        order_type: this.$route.query.order_type,
        page: 1,
        limit: 50,
        resource_code: this.$route.query.resource_code,
        order_num_type: 100,
        start_time: this.$route.query.start_time,
        end_time: this.$route.query.end_time,
      })
        .then(
          (res) => {
            this.$store.dispatch('app/setLoadingState', false)
            if (res.code == 200) {
              let list = JSON.parse(JSON.stringify(res.data.data))

              list.map((item) => {
                item.ref = ''
              })
              this.leftList = list
              if (this.leftList.length > 0) {
                this.nextNums = this.leftNums
                this.leftList[this.leftNums].ref = 'active'
                // this.$nextTick(() => {
                //   this.$store.dispatch('index/setFocusDom', this.$refs.active)
                // })
              } else {
                this.nextNums = -1
                this.btnList[this.btnNums].ref = 'active'
              }
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          },
          (err) => {
            this.$store.dispatch('app/setLoadingState', false)
            if (err.response) {
              this.nextNums = -1
              this.btnList[this.btnNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          }
        )
        .catch(() => {
          this.$store.dispatch('app/setLoadingState', false)
        })
    },
    createSJOrder() {
      this.$store.dispatch('app/setLoadingState', true)

      createSJOrder({
        hos_org_code: this.leftList[this.leftNums].hos_org_code,
        one_dept_code: this.$route.query.one_dept_code,
        dept_code: this.$route.query.dept_code,
        order_type: this.leftList[this.leftNums].order_type,
        page: 1,
        limit: 50,
        resource_code: this.leftList[this.leftNums].resource_code,
        order_num_type: this.leftList[this.leftNums].order_num_type,
        start_time: this.leftList[this.leftNums].start_time,
        end_time: this.leftList[this.leftNums].end_time,
        user_card_type: '1',
        user_name:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].name,
        user_phone:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].phone,
        replace_user_card_type: '9',
        replace_user_card_id: 'inesa',
        replace_user_name: '上海仪电数字技术股份有限公司',
        num_source_date: this.leftList[this.leftNums].schedule_date,
        event_id: 0,
        device_type: 4,
        schedule_id: this.leftList[this.leftNums].schedule_id,
        num_source_id: this.leftList[this.leftNums].num_source_id,
        fk_user_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].id,
        home_id: this.$store.getters.getUserInfo.home_id,
      }).then(
        (res) => {
          if (res.code == 200) {
            this.popupModule = 3
            this.leftList[this.leftNums].ref = ''
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  const sessionKeys = [
                    'indexFocusDOne',
                    'indexFocusDTwo',
                    'chosenIndex',
                    'indexDocOrDept',
                    'indexFocusAppment',
                    'indexChosenDept',
                  ]

                  if (this.btnNums === 1) {
                    sessionKeys.push('indexFocusHos')
                  }

                  sessionKeys.forEach((key) => sessionStorage.removeItem(key))

                  this.$router.push({
                    path: this.btnNums === 0 ? './hospital_sj' : './index',
                  })
                },
              },
            ]
            this.popupMessage = '预约成功！<br/>请及时赴约！'
            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.leftList[this.leftNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br/>预约失败!<br>'
          if (
            error.response &&
            error.response.data &&
            error.response.data.msg
          ) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
  <style lang="less" scoped>
.appointment {
  width: 16.6rem;
  position: relative;
  .container {
    width: 100%;
    height: 6rem;
    overflow: hidden;
    display: flex;
    // grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
    // gap: 0.16rem;
    font-size: 0.2rem;
    color: #e7e7ef;
    .left {
      width: 100%;
      height: 6rem;
      margin-left: 0rem;
      //padding-left: 0.1rem;
      //margin-left: 0.05rem;

      .messageCenterList {
        width: 100%;
        height: 100%;
        position: relative;
        display: inline-block;
        overflow: hidden;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          left: 0.4rem;
          transition: all 0.2s;
          .messageItem {
            width: 3.8rem;
            height: 1.8rem;
            // padding: 0.5rem;
            margin-bottom: 0.3rem;
            background-size: 100% 100% !important;
            // background: #262954;
            background: url('../../assets/hospital_btn_bg.png') no-repeat;

            border-radius: 0.24rem;
            overflow: hidden;
            .item_user {
              display: flex;
              height: 100%;
              // padding: 0.15rem;
              .content {
                display: flex;
                padding: 0 0.1rem;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                height: initial !important;

                .name {
                  // width: 100%;
                  // height: 1.8rem;
                  text-align: center;
                  font-size: 0.4rem;
                  font-weight: bold;
                }
                .info {
                  text-align: center;
                  font-size: 0.22rem;
                  font-weight: bold;
                  overflow: hidden;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  text-overflow: ellipsis;
                  -webkit-line-clamp: 3 !important;
                }
              }
            }
          }
          .messageItem:not(:nth-child(4n + 4)) {
            margin-right: 0.28rem;
          }
        }
      }
      .no_info {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        line-height: 6rem;
        width: 4rem;
        height: 7rem;
        text-align: center;
        font-size: 0.6rem;
      }
    }
  }
  .btnList_bottom {
    position: absolute;
    bottom: -1.36rem;
    left: 50%;
    transform: translateX(-50%);
    height: 1rem;
    .btnList {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 100%;
      .btnItem {
        text-align: center;
        width: 3.5rem;
        height: 1rem;
        margin-left: 0.3rem;
        font-size: 0.4rem;
        font-weight: bold;
        border-radius: 0.24rem;
        background: url('../../assets/btn_gh_click_bg.png') no-repeat;
        background-size: 100% 100% !important;
        .text {
          line-height: 1.08rem;
        }
      }
    }
  }
}
</style>
  <style lang="less">
.appointment {
  .container {
    .left {
      .scroll {
        top: 2.25rem !important;
        left: 18rem !important;
      }
    }
  }
}
</style>
  <style lang="less">
.appointment {
  // position: relative;

  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    background: repeating-linear-gradient(to right, #c98693, #a95361);
    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.2rem 0.4rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        align-items: center;
        .message_content {
          margin-top: 0.3rem;
          .hospital,
          .department,
          .doctor,
          .time {
            font-size: 0.4rem;
            font-weight: bold;
            padding-bottom: 0.22rem !important;
          }
        }
      }
      .cancel {
        flex-direction: column;
        div {
          text-align: center;
        }
        div:nth-child(2) {
          margin-top: 0.1rem;
          color: red;
          line-height: 0.48rem;
        }
        //align-items: normal !important;
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          background: repeating-linear-gradient(to right, #c98693, #a95361);
        }
      }
    }
  }
  .container {
    .right {
      .scroll {
        top: 2.35rem !important;
        left: 18rem !important;
      }
    }
  }
}
</style>