// webSocket类
import store from "@/store/index";
import sha256 from "@/utils/sha256";

class Socket {
  // 接收消息参数
  message = null;
  constructor(options) {
    const { url, heartTime, reconnectTime } = options;
    const classSelf = this;

    this.reconnectTime = reconnectTime || 5000; // 重连时间
    this.lockReconnect = false;//避免重复连接
    this.ws = null; //WebSocket的引用
    this.wsUrl = url; //这个要与后端提供的相同

    //心跳检测
    this.heartCheck = {
      timeout: heartTime || 15000,//毫秒
      timeoutObj: null,
      serverTimeoutObj: null,
      reset: function () {
        clearTimeout(this.timeoutObj);
        clearTimeout(this.serverTimeoutObj);
        return this;
      },
      start: function () {
        const self = this;
        this.timeoutObj = setTimeout(function () {
          //这里发送一个心跳，后端收到后，返回一个心跳消息， 【发送格式与后端沟通】
          //onmessage拿到返回的心跳就说明连接正常
          classSelf.send({ type: 35, data: 'HeartBeat' });
          self.serverTimeoutObj = setTimeout(function () {//如果超过一定时间还没重置，说明后端主动断开了
            classSelf.close();//如果onclose会执行reconnect，我们执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
          }, self.timeout)
        }, this.timeout)
      }
    }
    this.createWebSocket();/**启动连接**/

    // 强制退出
    window.onunload = () => {
      this.closeWs();
    }
  }


  //创建WebSocket连接,如果不确定浏览器是否支持，可以使用socket.js做连接
  createWebSocket() {
    try {
      if ('WebSocket' in window) {
        // let signStr = this.wsUrl.split('?')[1] + "&" + store.state.app.s
        // // 签名
        // let headers = {
        //   // Timestamp: parseInt(new Date().getTime() / 1000),
        //   // Sign: sha256(signStr)
        // };
        // let protocol = Object.keys(headers).map(key => `${key}-${headers[key]}`)
        this.ws = new WebSocket(this.wsUrl);
      }
      this.initEventHandle();
    } catch (e) {
      // console.error(e)
      this.reconnect(this.wsUrl);
    }
  }


  /*********************初始化开始**********************/
  initEventHandle() {
    // 连接成功建立后响应
    this.ws.onopen = () => {
      console.log("成功连接到" + this.wsUrl);
      //心跳检测重置
      this.heartCheck.reset().start();
    }
    // 收到服务器消息后响应
    this.ws.onmessage = (e) => {
      //如果获取到消息，心跳检测重置
      //拿到任何消息都说明当前连接是正常的
      this.heartCheck.reset().start();
      //Json转换成Object
      if (e.data === '连接成功') {
        return
      }
      var msg = eval('(' + e.data + ')');

      // 接收参数签名验证
      let signaturePassed = true
      Object.keys(msg).forEach((key) => {
        if (!msg.hasOwnProperty('timestamp') || !msg.hasOwnProperty('sign')) {
          signaturePassed = false
        }
      });
      if(!signaturePassed) {
        this.closeWs(()=>{
          console.error('数据异常，主动关闭ws')
        })
        return
      }


      // golang 会自动排序，为保证顺序一至  先排序
      const sortedKeys = Object.keys(msg).sort();
      // 创建一个新的对象，按照排序后的属性名插入属性
      const sortedObj = {};
      sortedKeys.forEach(key => {
        sortedObj[key] = msg[key];
      });

      // 整理结构，去除签名
      let timestamp = sortedObj.timestamp
      let sign = sortedObj.sign
      delete sortedObj.timestamp;
      delete sortedObj.sign;

      let msgSignStr = JSON.stringify(sortedObj) + "&" + timestamp + "&" + store.state.app.s
      if (sha256(msgSignStr) != sign) {
        this.closeWs(()=>{
          console.error('客户端数据异常，主动关闭ws')
        })
        return
      }

      // HeartBeat type 35
      if (msg.type && msg.type == 35) {
        //忽略心跳的信息，因为只要有消息进来，断线重连就会重置不会触发
      } else {
        //处理消息的业务逻辑
        store.state.app.webscoketMessage = msg
        if (typeof this.message === 'function') {
          this.message(msg);
        }
      }
    }

    // 连接关闭后响应
    this.ws.onclose = () => {
      console.log("关闭" + this.wsUrl + "连接");
      this.reconnect(this.wsUrl);//重连
    }
    this.ws.onerror = () => {
      this.reconnect(this.wsUrl);//重连
    };
  }
  /***************初始化结束***********************/

  // 重新连接
  reconnect() {
    if (this.lockReconnect) return;
    this.lockReconnect = true;
    //没连接上会一直重连，设置延迟避免请求过多
    setTimeout(() => {
      this.createWebSocket();
      console.log(this.wsUrl + "正在重连，当前时间" + new Date())
      this.lockReconnect = false;
    }, this.reconnectTime); //这里设置重连间隔(ms)
  }

  // 发送消息
  send(data) {
    if (this.ws.readyState == 1) {
      // golang 会自动排序，为保证顺序一至  先排序
      const sortedKeys = Object.keys(data).sort();
      // 创建一个新的对象，按照排序后的属性名插入属性
      const sortedObj = {};
      sortedKeys.forEach(key => {
        sortedObj[key] = data[key];
      });

      // 发送消息增加签名
      let timestamp = parseInt(new Date().getTime() / 1000);  // 秒级
      let paramsStyring = JSON.stringify(sortedObj) + "&" + timestamp + "&" + store.state.app.s

      sortedObj.timestamp = timestamp
      sortedObj.sign = sha256(paramsStyring)

      //自定义消息串，让后端接收
      this.ws.send(JSON.stringify(sortedObj));
      return true
    } else {
      console.log(this.wsUrl + "连接超时，请刷新重试!");
      return false
    }
  }

  // 关闭WebSocket
  closeWs(callback) {
    this.ws.close();
    this.heartCheck.reset();
    this.message = null;
    this.lockReconnect = true;
    this.ws.onclose = () => {
      console.log("关闭" + this.wsUrl + "连接");
      if (callback) {
        callback(true)
      }
    }
  }

}


export default Socket