import Vue from 'vue'
const state = {
    token:'',
    media: null,  // 媒体原型
    settingShow: false,  // 设置是否显示
    settingBtn:[
        {
            ref:'',
            title:'网络设置',
            icon: require('@/assets/setting1.png'),
            status:'未连接'
        },
        {
            ref:'',
            title:'网络检测',
            icon: require('@/assets/setting2.png'),
            status:''
        },
        {
            ref:'',
            title:'分辨率',
            icon: require('@/assets/setting3.png'),
            status:'1080P-50Hz'
        },
        {
            ref:'',
            title:'图像缩放',
            icon: require('@/assets/setting4.png'),
            status:'宽:93 高:93'
        },
        {
            ref:'',
            title:'应用管理',
            icon: require('@/assets/setting5.png'),
            status:''
        },
        {
            ref:'',
            title:'恢复出场',
            icon: require('@/assets/setting6.png'),
            status:'状态:良'
        },
        {
            ref:'',
            title:'关于本机',
            icon: require('@/assets/setting7.png'),
            status:''
        },
        {
            ref:'',
            title:'更多',
            icon: require('@/assets/setting8.png'),
            status:''
        }
    ],    // 设置内按钮
    settingBtnIndex: 0,  // 设置选中按钮下标
    screen_multiple:1,
    userInfo:null,       // 家庭及用户信息
    selectUserIndex: 0,  // 选择的家庭用户下标
    weatherInfo: null,   // 天气信息
    timeInfo: null,
    is_tv:false,
    loadingState:false,
    viewAreaOffsetTop: 0,
    orderInfo: null,    // 在线叫车最近订单信息
    storeTimer: null,
    webscoketMessage: null,
    onLineList:[
        {
            show: false, // true  在线&打开   false 不在线   -1 关闭
            img: require('@/assets/camera_no.png')
        },{
            show: true,
            img: require('@/assets/tvOnLine.png')
        }
    ],  // 摄像头 网络在线情况
    aiShow: false,
    aiSayList:[
        {
            type: 0,   // 0  ai   1  语音识别内容
            message:'有什么可以帮助您'
        }
    ],
    adHomeType: 0,  // 首页动态屏显示类型
    versionShow: false, // 版本信息显示
    pointTotal: 0,
    pointList:[],
    s: '4f2d9187b6d9a3b0a2f8c604a2b7855d',
    sha256Button: 1,
}

const mutations = {
    SET_POINT:(state, number) => {
        Vue.set(state, 'pointTotal', number)
    },
    SET_AI_SAY:(state, arr) => {
        Vue.set(state, 'aiSayList', arr)
    },
    SET_SCREEN_MULTIPLE: (state, num) => {
        Vue.set(state, 'screen_multiple', num)
    },
    SET_USER_INFO: (state, num) => {
        Vue.set(state, 'userInfo',  num)
    },
    SET_ORDER_INFO: (state, obj) => {
        Vue.set(state, "orderInfo", obj)
    },
    SET_LOADING_STATE: (state, ste) => {
        Vue.set(state, 'loadingState', ste)
    },
    SET_VIEW_AREA_OFFSET_TOP:(state, ste)=>{
        Vue.set(state, 'viewAreaOffsetTop', ste)
    },

}

const actions = {
    setAiSay ({ commit, state }, arr) {
        return new Promise((resolve, reject) => {
            commit("SET_AI_SAY", arr)
        })
    },
    setScreenMultiple({ commit, state }, num) {
        return new Promise((resolve, reject) => {
            commit("SET_SCREEN_MULTIPLE", num)
        })
    },
    setUserInfo({ commit, state }, obj) {
        return new Promise((resolve, reject) => {
            commit("SET_USER_INFO", obj)
        })
    },
    setOrderInfo({ commit, state }, obj) {
        return new Promise((resolve, reject) => {
            commit("SET_ORDER_INFO", obj)
        })
    },
    setLoadingState({ commit, state }, blu) {
        return new Promise((resolve, reject) => {
            commit("SET_LOADING_STATE", blu)
        })
    },
    setViewAreaOffsetTop({ commit, state }, num) {
        return new Promise((resolve, reject) => {
            commit("SET_VIEW_AREA_OFFSET_TOP", num)
        })
    },
    setPoint({ commit, state }, num) {
        return new Promise((resolve, reject)=>{
            commit('SET_POINT',num)
        })
    }
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}