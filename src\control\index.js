import store from '@/store/index'
import todo from './todo'
import {music} from "@/unit/music";

const keyboard = {
  37: 'left',
  38: 'rotate',  // 切换角度  上键
  39: 'right',
  40: 'down',
  32: 'space',
  83: 's',      // 静音
  82: 'r',
  13: 'p'       // 确认键 暂停/继续
}

let keydownActive
const boardKeys = Object.keys(keyboard).map(e => parseInt(e, 10))
const keyDown = e => {
  if (e.metaKey === true || boardKeys.indexOf(e.keyCode) === -1) {
    return
  }
  const type = keyboard[e.keyCode]
  if (type === keydownActive) {
    return
  }
  keydownActive = type
  todo[type].down(store)
}

const keyUp = e => {
  if (e.metaKey === true || boardKeys.indexOf(e.keyCode) === -1) {
    return
  }
  const type = keyboard[e.keyCode]
  if (type === keydownActive) {
    keydownActive = ''
  }
  todo[type].up(store)
}

const fuc ={
  start: () =>{
    document.addEventListener('keydown', keyDown, true)
    document.addEventListener('keyup', keyUp, true)
    if (typeof music.reset == "function") {
      music.reset()
    }
  },
  end:() =>{
    document.removeEventListener('keydown', keyDown, true)
    document.removeEventListener('keyup', keyUp, true)
  }
}
export default fuc
