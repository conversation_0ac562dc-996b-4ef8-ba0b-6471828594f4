class Krakout {
  game1() {
    // 倒金字塔
    const BRICK_X = this.height / 12
    const BRICK_Y = BRICK_X
    const BRICK_COLUMN_NUM = 9
    const BRICK_ROW_NUM = 9
    const BRICK_WIDTH = (this.height / 1.2 - BRICK_X * 2) / BRICK_ROW_NUM
    const BRICK_HEIGHT = this.height / 30

    let bricks = []
    let num = 0
    // 倒金字塔
    for (let i = 0; i < BRICK_COLUMN_NUM; i++) {
      let row = []
      // 计算当前行的砖块数量，顶部最多，逐渐递减
      let bricksInRow = BRICK_ROW_NUM - i;
      // 确保砖块数量不为负数
      if (bricksInRow <= 0) break;
      // 计算每行的左侧偏移量，使砖块居中
      let offsetX = (BRICK_WIDTH * (BRICK_ROW_NUM - bricksInRow)) / 2;

      for (let j = 0; j < bricksInRow; j++) {
        // 创建砖块
        let brick = {
          x: BRICK_X + offsetX + BRICK_WIDTH * j,  // 调整x以居中每一行
          y: BRICK_Y + BRICK_HEIGHT * i,
          color: this.colors[Math.floor(Math.random() * this.colors.length)]
        }
        row.push(brick)
        num++
      }

      bricks.push(row)
    }
    return {
      BRICK_X: BRICK_X,
      BRICK_Y: BRICK_Y,
      BRICK_COLUMN_NUM: BRICK_COLUMN_NUM,
      BRICK_ROW_NUM: BRICK_ROW_NUM,
      BRICK_WIDTH: BRICK_WIDTH,
      BRICK_HEIGHT: BRICK_HEIGHT,
      bricks: bricks,
      num: num
    }
  }
  game2() {
    // 隔行隔砖
    const BRICK_X = this.height / 12
    const BRICK_Y = BRICK_X
    const BRICK_COLUMN_NUM = 7
    const BRICK_ROW_NUM = 9
    const BRICK_WIDTH = (this.height / 1.2 - BRICK_X * 2) / BRICK_ROW_NUM
    const BRICK_HEIGHT = this.height / 30

    let bricks = []
    let num = 0

    for (let i = 0; i < BRICK_COLUMN_NUM; i++) {
      let row = []
      for (let j = 0; j < BRICK_ROW_NUM; j++) {
        if ((i % 2 == 0 && j % 2 == 0) || (i % 2 != 0 && j % 2 != 0)) {
          let brick = {
            x: BRICK_X + BRICK_WIDTH * j,
            y: BRICK_Y + BRICK_HEIGHT * i,
            color: this.colors[Math.floor(Math.random() * this.colors.length)]
          }
          row.push(brick)
          num++
        }
      }
      bricks.push(row)
    }
    return {
      BRICK_X: BRICK_X,
      BRICK_Y: BRICK_Y,
      BRICK_COLUMN_NUM: BRICK_COLUMN_NUM,
      BRICK_ROW_NUM: BRICK_ROW_NUM,
      BRICK_WIDTH: BRICK_WIDTH,
      BRICK_HEIGHT: BRICK_HEIGHT,
      bricks: bricks,
      num: num
    }
  }
  constructor(ele, {height, colors, tip, tipNum, failAttrs, successAttrs, playGame} = {height: null, colors: null, tip: null, tipNum: null, failAttrs: null, successAttrs: null, playGame: 0}) {
    this.ele = ele

    this.ele && ele.setAttribute('tabindex', -1)

    this.height = (typeof height == 'number' && height > 0) ? height : document.documentElement.clientHeight
    this.width = this.height / 1.2

    const barHeight = this.height / 50
    const barWidth = this.height / 3.6
    this.barAttrs = {
      x: barWidth,
      y: this.height - 2 * barHeight,
      width: barWidth,
      height: barHeight,
      radius: barHeight / 2,
      step: this.height / 60
    }
    this.bar = {}

    this.ballAttrs = {
      radius: barHeight,
      xAcceleration: this.height / 100,
      yAcceleration: this.height / 100
    }
    this.ball = {}

    this.colors = Array.isArray(colors) ? colors : ['#33b5e5', '#0099cc', '#aa66cc', '#9933cc', '#99cc00', '#669900', '#ffbb33', '#ff8800', '#ff4444', '#cc0000']


    this.gameList = [this.game1(),this.game2()]
    this.brickAttrs = {
      x: this.gameList[playGame].BRICK_X,
      y: this.gameList[playGame].BRICK_Y,
      columnNum: this.gameList[playGame].BRICK_COLUMN_NUM,
      rowNum: this.gameList[playGame].BRICK_ROW_NUM,
      width: this.gameList[playGame].BRICK_WIDTH,
      height: this.gameList[playGame].BRICK_HEIGHT
    }

    let bricks = this.gameList[playGame].bricks



    this.map = bricks
    this.bricksNum = this.gameList[playGame].num
    this.bricks = []

    this.score = 0
    this.time = 0

    this.tip = Array.isArray(tip) ? tip : ['暂无提示信息']
    this.tipNum = (typeof tipNum == 'number' && tipNum > 0) ? tipNum : 20

    this.stage = 0

    this.barRequestId = null
    this.renderRequestId = null



    this.tan = this.game1().BRICK_HEIGHT / (Math.sqrt(2) * this.ballAttrs.radius) + 1

    const failImage = new Image()
    failAttrs = (failAttrs && typeof failAttrs == 'object') ? failAttrs : {}
    this.failAttrs = Object.assign({
      tip: ['游戏失败'],
      imgSrc: require('./image/failed.png')
    }, failAttrs)
    failImage.src = this.failAttrs.imgSrc
    failImage.onload = () => {
      this.failAttrs.image = failImage
    }

    const successImage = new Image()
    successAttrs = (successAttrs && typeof successAttrs == 'object') ? successAttrs : {}
    this.successAttrs = Object.assign({
      tip: ['恭喜通关'],
      imgSrc: require('./image/win.png')
    }, successAttrs)
    successImage.src = this.successAttrs.imgSrc
    successImage.onload = () => {
      this.successAttrs.image = successImage
    }
  }
  init() {
    const { ele, width, height, tip, tipNum, playGames } = this
    if (!ele) {
      return
    }
    ele.width = width
    ele.height = height
    
    const context = ele.getContext('2d')
    this.context = context
    context.clearRect(0, 0, width, height)

    const tipWidth = 0.75 * width
    const tipHeight = tipWidth / 2
    const x = (width - tipWidth) / 2
    const y = (height - tipHeight) / 2

    context.beginPath()
    context.fillStyle = 'transparent'
    context.lineJoin = 'round'
    context.fillRect(x, y, tipWidth, tipHeight)
    context.closePath()

    const tipLength = tip.length
    const fontSize = parseInt(tipWidth / tipNum)
    const tipX = x + tipWidth / 2
    const yInterval = (tipHeight - fontSize * tipLength) / (tipLength + 1)
    const tipY = y + yInterval
    context.beginPath()
    context.font = `${fontSize}px serif`
    context.fillStyle = 'rgb(255, 230, 0)'
    context.textBaseline = 'top'
    context.textAlign = 'center'
    tip.forEach((text, index) => {
      context.fillText(text.slice(0, tipNum), tipX, tipY + (yInterval + fontSize) * index)
    })
    context.closePath()

    this.stage = 0
    this.score = 0
    this.time = 0

    ele.focus()
  }
  initGame() {
    const { bar, barAttrs, context, width, height, map } = this

    context.clearRect(0, 0, width, height)

    bar.x = barAttrs.x
    this.renderBar()

    this.initBall()

    this.bricks = JSON.parse(JSON.stringify(map))
    this.initMap()

    this.stage = 1
  }
  renderBar() {
    const { context, bar: {x}, barAttrs: {y, width, height} } = this
    context.beginPath()
    context.moveTo(x, y)
    context.lineTo(x + width, y)
    context.lineWidth = height
    context.strokeStyle = '#fff'
    context.lineCap="round";
    context.closePath()
    context.stroke()
  }
  initBall() {
    const { context, ballAttrs: {radius, yAcceleration: vy}, barAttrs: {y, width, radius: ballRadius}, bar: {x}} = this
    const ballX = x + width / 2
    const ballY = y - ballRadius - radius
    context.beginPath()
    context.arc(ballX, ballY, radius, 0, 2 * Math.PI)
    context.closePath()
    context.fillStyle = '#fff'
    context.fill()
    this.ball = {
      x: ballX,
      y: ballY,
      vx: 0,
      vy: -vy
    }
  }
  initMap() {
    const { context, brickAttrs: {width, height} } = this
    this.map.forEach(row => {
      row.forEach(brick => {
        const {x, y, color} = brick
        context.beginPath()
        context.fillStyle = color
        context.fillRect(x, y, width, height)
        context.closePath()
      })
    })
  }
  bind(eventType, callback) {
    this.ele.addEventListener(eventType, callback.bind(this))
    return this
  }
  moveBar(direction, num) {
    if (num != null) {
      this.startMoveBar(direction, num)
      return
    }
    if (this.barRequestId != null) {
      return
    }
    this.startMoveBar(direction)
  }
  startMoveBar(direction, num) {
    let { bar, barAttrs: {radius, step, width: barWidth, y: barY}, bar: {x: barX}, width, stage, ball, ballAttrs: {radius: ballRadius, xAcceleration: vxA}, brickAttrs: {width: brickWidth} } = this
    step = num ? num : step
    if (direction == 'left') {
      if (bar.x > radius) {
        if (bar.x - step < radius) {
          bar.x = radius
        } else {
          bar.x -= step
        }
      }
    } else if (direction == 'right') {
      const right = width - barWidth - radius
      if (bar.x < right) {
        if (bar.x + step > right) {
          bar.x = right
        } else {
          bar.x += step
        }
      }
    }
    if (stage == 1) {
      this.render()
    } else if (stage == 2) {
      const {x, vx, y} = ball
      if ((barX > radius) && (parseInt(barX + barWidth) < parseInt(width - radius)) && (x >= barX - radius) && (x <= barX + barWidth + radius)) {
        if (y >= barY - radius - ballRadius) {
          if (direction == 'left') {
            ball.vx = Math.max(vx - vxA, -brickWidth / 2)
          } else if (direction == 'right') {
            ball.vx = Math.min(vx + vxA, brickWidth / 2)
          }
        } else if (barY - radius < y) {
          if (direction == 'left') {
            ball.vx = Math.max(vx - vxA, -brickWidth / 2)
            ball.x = barX - radius - ballRadius
          } else if (direction == 'right') {
            ball.vx = Math.min(vx + vxA, brickWidth / 2)
            ball.x = barX + barWidth + radius + ballRadius
          }
        }
      }
    }
    if (num == null) {
      this.barRequestId = window.requestAnimationFrame(() => {
        this.startMoveBar(direction, num)
      })
    }
  }
  stopMoveBar() {
    if (this.barRequestId != null) {
      window.cancelAnimationFrame(this.barRequestId)
      this.barRequestId = null
    }
  }
  start() {
    this.stage = 2
    this.time = Date.now()
    this.renderRequestId = window.requestAnimationFrame(this.render.bind(this))
  }
  render() {
    const { context, width, height, ballAttrs: {radius}, ball: {y}, score, bricksNum } = this
    if (this.stage == 1) {
      context.clearRect(0, y - radius - 1, width, height - y + radius)
      this.renderBar()
      this.initBall()
      return
    }
    context.clearRect(0, 0, width, height)
    this.renderBar()
    this.renderBall()
    this.renderBrick()
    this.renderInfo()
    if (y - radius >= height) {
      this.fail()
      window.cancelAnimationFrame(this.renderRequestId)
      return
    }
    if (score >= bricksNum * 100) {
      this.success()
      window.cancelAnimationFrame(this.renderRequestId)
      return
    }
    this.renderRequestId = window.requestAnimationFrame(this.render.bind(this))
  }
  renderBall() {
    let { context, ball: {x, y, vx, vy}, ballAttrs: {radius}, barAttrs: { radius: barRadius, y: barY, width: barWidth }, bar: {x: barX}, width } = this
    x += vx
    y += vy
    const barTop = barY - barRadius - radius
    if (x <= radius && vx <= 0) {
      x = radius
      this.ball.vx = -vx
    } else if (x >= width - radius && vx >= 0) {
      x = width - radius
      this.ball.vx = -vx
    }
    if (y <= radius && vy < 0) {
      y = radius
      this.ball.vy = -vy
    } else if (vy > 0) {
      if ((x >= barX) && (x <= barX + barWidth) && (y >= barTop) && y - vy < barTop + barRadius) {
        y = barTop
        this.ball.vy = -vy
      } else if (x > barX + barWidth && x - radius <= barX + barWidth + barRadius && y >= barTop && y < barY) {
        if (vx >= 0) {
          this.ball.vy = -vy
          this.ball.vx += vy * 2 / 3
        } else {
          this.ball.vx = -vx - vy * 2 / 3
        }
      } else if (x < barX && x + radius >= barX - barRadius && y >= barTop && y < barY) {
        if (vx <= 0) {
          this.ball.vy = -vy
          this.ball.vx -= vy * 2 / 3
        } else {
          this.ball.vx = -vx + vy * 2 / 3
        }
      }
    }
    this.ball.x = x
    this.ball.y = y
    context.beginPath()
    context.arc(x, y, radius, 0, 2 * Math.PI)
    context.closePath()
    context.fillStyle = '#fff'
    context.fill()
  }
  renderBrick() {
    const { context, bricks, brickAttrs: {width, height}, ball: {x: ballX, y: ballY, vx, vy}, ballAttrs: {radius}, tan } = this
    for (let i = 0; i < bricks.length; i++) {
      for (let j = 0; j < bricks[i].length; j++) {
        const {x, y, color} = bricks[i][j]
        if (ballX + radius >= x && ballX - radius <= x + width && ballY - radius <= y + height && ballY + radius >= y) {
          this.score += 100
          bricks[i].splice(j--, 1)
          if (vx > 0 && (ballX - vx) < x && Math.abs((y + height / 2 - (ballY - vy)) / (x - (ballX - vx))) < tan) {
            this.ball.x = x - radius
            this.ball.vx = -vx
          } else if (vx < 0 && (ballX - vx) > x + width && Math.abs((y + height / 2 - (ballY - vy)) / (x + width - (ballX - vx))) < tan) {
            this.ball.x = x + width + radius
            this.ball.vx = -vx
          } else {
            if (vy < 0) {
              this.ball.y = y + height + radius
            } else {
              this.ball.y = y - radius
            }
            this.ball.vy = -vy
          }
          continue
        }
        context.beginPath()
        context.fillStyle = color
        context.fillRect(x, y, width, height)
        context.closePath()
      }
    }
  }
  renderInfo() {
    const { context, height, width, brickAttrs: {height: brickHeight}, map, score, time } = this
    const interval = height / 20
    const fontSize = parseInt(width / 20)
    const top = map[map.length - 1][0].y + brickHeight + interval * 2
    context.beginPath()
    context.font = `${fontSize}px serif`
    context.fillStyle = 'rgba(255, 230, 0, 0.7)'
    context.textBaseline = 'top'
    context.textAlign = 'center'
    context.fillText(`得分：${score}`, width / 2, top + interval)
    context.fillText(`用时：${Math.max((Date.now() - time) / 1000, 0).toFixed(2)}秒`, width / 2, top + fontSize + 2 *interval)
    context.closePath()
  }
  fail() {
    this.stage = 3
    this.end()
  }
  success() {
    this.stage = 4
    this.end()
  }
  end() {
    this.stopMoveBar()
    const { context, width, height, score, time } = this
    const imageAttrs = this.stage == 3 ? this.failAttrs : this.successAttrs
    let { image, tip } = imageAttrs
    context.clearRect(0, 0, width, height)
    let initY = height
    const fontSize = parseInt(width / 20)
    const yInterval = fontSize
    if (image) {
      initY *= 3 / 2
      const wScale = image.width / width
      const hScale = image.height / height * 2
      let x = 0, y = 0, imgWidth, imgHeight
      if (wScale < hScale) {
        imgHeight = height / 2
        imgWidth = imgHeight / image.height * image.width
        x = (width - imgWidth) / 2
      } else {
        imgWidth = width
        imgHeight = imgWidth / image.width * image.height
        y = (height / 2 - imgHeight) / 2
      }
      context.drawImage(image, x, y, imgWidth, imgHeight)
    }
    tip = Array.isArray(tip) ? tip : ['游戏结束']
    tip = [...tip, `得分：${score}   用时：${Math.max((Date.now() - time) / 1000, 0).toFixed(2)}秒`]
    const tipY = (initY - tip.length * fontSize - yInterval * (tip.length - 1)) / 2
    context.beginPath()
    context.font = `${fontSize}px serif`
    context.fillStyle = 'rgb(255, 230, 0)'
    context.textBaseline = 'top'
    context.textAlign = 'center'
    tip.forEach((text, index) => {
      context.fillText(text.slice(0, 20), width / 2, tipY + (yInterval + fontSize) * index)
    })
    context.closePath()
  }
}

export default Krakout
