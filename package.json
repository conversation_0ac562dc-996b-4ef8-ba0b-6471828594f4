{"name": "GateWay_TV", "version": "0.1.0", "private": true, "scripts": {"serve": "SET NODE_OPTIONS=--openssl-legacy-provider &&  vue-cli-service serve", "build:dev": "SET NODE_OPTIONS=--openssl-legacy-provider &&  vue-cli-service build --mode development", "build:prod": "SET NODE_OPTIONS=--openssl-legacy-provider &&  vue-cli-service build --mode production", "build:prod_inner": "SET NODE_OPTIONS=--openssl-legacy-provider &&  vue-cli-service build --mode production_inner", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "axios": "^0.24.0", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "element-ui": "2.15.8", "jquery": "^3.7.1", "less": "3.9.0", "less-loader": "4.1.0", "seamscroll": "^0.0.12", "vue": "^2.6.11", "vue-lazyload": "^1.3.5", "vue-router": "^3.5.3", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "javascript-obfuscator": "^2.19.1", "terser-webpack-plugin": "^4.2.3", "vue-template-compiler": "^2.6.11", "webpack-obfuscator": "^2.6.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}