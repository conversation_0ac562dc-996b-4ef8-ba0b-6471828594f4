<template>
  <div class="sokoban">
    <div class="game">
      <div class="toops">
        <div id="msg"></div>
      </div>
      <canvas id="canvas" :width="800*this.$store.getters.screen_multiple" :height="800*this.$store.getters.screen_multiple"></canvas>
      <div class="leval_toops" v-if="showPassLeval">
        过关
      </div>
      <div class="message">
        <h2>操作说明:</h2>
        <p>
          <span>1.</span>
          <span>用遥控器上的上、下、左、右键移动小人。</span>
        </p>
        <p>
          <span>2.</span>
          <span>把箱子全部推到小球的位置即可过关。</span>
        </p>
        <p>
          <span>3.</span>
          <span>箱子只可向前推，不能往后拉，并且小人一次只能推动一个箱子。</span>
        </p>
      </div>

    </div>

    <el-dialog
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        custom-class="operatePopup"
        :title="popupModule == 1 ? '操作' : '提示'"
    >
      <!--操作弹窗-->
      <div class="btnList" v-if="popupModule == 1">
        <div
            class="btnItem"
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"

        >
          <div class="box-btnItem"  v-html="item.text"></div>

        </div>
      </div>

      <!--消息弹窗-->
      <div class="popupMessage" v-if="popupModule == 2">
        <div class="popupMessage" v-html="popupMessage"></div>
        <div
            class="popupMessageBtn"
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
        >
          {{ item.text }}
        </div>
      </div>


    </el-dialog>
  </div>
</template>

<script>

import levels from '@/view/games/sokoban/js/mapdata100'

export default {
  name:'sokoban',
  components: {
  },
  data() {
    return {
      showPassLeval: false,
      dialogVisible: false,
      popupModule: 1, // 1、按钮弹窗  2、消息提示
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [
        {
          text: '重玩本关',
          ref: '',
          num: 0,
          fuc: this.NextLevel
        },
        {
          text: '上一关',
          ref: '',
          num: -1
          ,
          fuc: this.NextLevel
        },
        {
          text: '下一关',
          ref: '',
          num: 1,
          fuc: this.NextLevel
        }
      ],

      oImgs: {
        "block" : require("./images/block.png"),  // 偶数格子
        "block2" : require("./images/block2.png"), // 基数格子
        "wall" : require("./images/wall.png"),
        "box" : require("./images/box.png"),
        "ball" : require("./images/ball.png"),
        "up" : require("./images/up.png"),
        "down" : require("./images/down.png"),
        "left" : require("./images/left.png"),
        "right" : require("./images/right.png"),
        "transparentBlock" :require('./images/transparent.png')
      },
      block: null,
      block2: null,
      transparentBlock: null,
      wall: null,
      box: null,
      ball: null,
      up:null,
      down: null,
      left: null,
      right: null,

      can: null,
      cxt: null,
      msg: null,
      w: 50 * this.$store.getters.screen_multiple,
      h: 50 * this.$store.getters.screen_multiple,
      curMap: null, //当前的地图
      curLevel: null, //当前等级的地图
      curMan: null, //初始化小人
      iCurlevel: 0, //关卡数
      moveTimes: 0, //移动了多少次
      perPosition: new this.Point(5,5) //小人的初始标值

    };
  },
  created() {

  },
  computed: {},
  watch: {},
  mounted() {
    this.$store.dispatch('index/setMainTitle',JSON.parse(sessionStorage.getItem('redirectInfo')).title ? JSON.parse(sessionStorage.getItem('redirectInfo')).title : '推箱子')
    this.$store.dispatch('index/setFocusDom', null)
    this.can = document.getElementById("canvas");
    this.msg = document.getElementById("msg");
    this.cxt = this.can.getContext("2d");

    this.imgPreload(this.oImgs,(images)=>{
      this.block = images.block;
      this.block2 = images.block2;
      this.transparentBlock = images.transparentBlock
      this.wall = images.wall;
      this.box = images.box;
      this.ball = images.ball;
      this.up = images.up;
      this.down = images.down;
      this.left = images.left;
      this.right = images.right;
      this.init();
    });

    this.fuc.KeyboardEvents({
      down:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        this.go("down");
      },
      up:()=>{
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        this.go("up");
      },
      left:()=>{
        if (this.dialogVisible) {
          return
        }
        this.go("left");

      },
      right:()=>{
        if (this.dialogVisible) {
          return
        }
        this.go("right");
      },
      enter:()=>{
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].fuc(this.popupBtnList[this.popupBtnNums].num)
          this.dialogVisible = false
          return
        }

      },
      esc:()=>{
        if (this.dialogVisible) {
          this.dialogVisible = false
          if (this.popupModule == 2) {
            setTimeout(()=>{
              this.openMenu()
            },300)
          }
          return
        }
        this.openMenu()

      }
    })


  },
  methods: {
    openMenu() {
      this.dialogVisible= true
      this.popupModule =  1
      this.popupBtnList = [
        {
          text: '重玩本关',
          ref: 'active',
          num: 0,
          fuc: this.NextLevel
        },
        {
          text: '上一关',
          ref: '',
          num: -1
          ,
          fuc: this.NextLevel
        },
        {
          text: '下一关',
          ref: '',
          num: 1,
          fuc: this.NextLevel
        },
        {
          text: '关卡选择',
          ref: '',
          num: 1,
          fuc: ()=>{
            this.$store.dispatch('index/setFocusDom', null);
            this.$router.push({
              path: './sokobanLevel'
            })
          }
        },
        {
          text: '退出游戏',
          ref: '',
          num: 1,
          fuc: ()=>{
            this.$store.dispatch('index/setFocusDom', null)
            history.go(-1)
          }
        }
      ]
    },
    popupClose() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', null);
        setTimeout(()=>{
          this.popupBtnList = []
          this.popupBtnNums = 0
          this.popupModule = 1
        },200)
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        setTimeout(()=>{
          document.getElementById('focus_border').style.borderColor = '#5472B0'
        },0)
        return
      })
    },
    imgPreload(srcs,callback){
      var count = 0,imgNum = 0,images = {};
      for(var src in srcs ){
        imgNum++;

        images[src] = new Image();
        images[src].onload = function(){
          //判断是否所有的图片都预加载完成
          if (++count >= imgNum)
          {
            callback(images);
          }
        }
        images[src].src = srcs[src];
      }
    },
    //初始化游戏
    init(){
      this.initLevel();//初始化对应等级的游戏
      this.showMoveInfo();//初始化对应等级的游戏数据
    },
    //初始化游戏等级
    initLevel(){
      if (localStorage.getItem('sokoban_level_now')) {
        this.iCurlevel = Number(localStorage.getItem('sokoban_level_now'))
      }
      this.curMap = this.copyArray(levels[this.iCurlevel]);//当前移动过的游戏地图
      this.curLevel = levels[this.iCurlevel];//当前等级的初始地图
      this.curMan = this.down;//初始化小人
      this.InitMap();//初始化地板
      this.DrawMap(this.curMap);//绘制出当前等级的地图
    },
    //完善关卡数据及游戏说明
    showMoveInfo(){
      // this.msg.innerHTML = "第" + (this.iCurlevel+1) +"/100关 移动次数: "+ this.moveTimes;
      this.msg.innerHTML = "第 " + (this.iCurlevel+1) +" / "+levels.length+"关";
    },
    // 克隆二维数组
    copyArray(arr){
      var b=[];//每次移动更新地图数据都先清空再添加新的地图
      for (var i=0;i<arr.length ;i++ )
      {
        b[i] = arr[i].concat();//链接两个数组
      }
      return b;
    },
    //绘制地板
    InitMap(){
      let passTheLevel = []
      if (localStorage.getItem('sokoban_pass_level_list')) {
        passTheLevel = JSON.parse(localStorage.getItem('sokoban_pass_level_list'))
      }
      if (passTheLevel.includes(this.iCurlevel)) {
        this.showPassLeval = true
      } else {
        this.showPassLeval = false
      }

      for (var i=0;i<16 ;i++ )
      {
        for (var j=0;j<16 ;j++ )
        {
          if (j % 2 === 0) { // 偶数格
            this.cxt.drawImage(this.block,this.w*j,this.h*i,this.w,this.h);
            if (i % 2 === 0) {
              this.cxt.drawImage(this.block2,this.w*j,this.h*i,this.w,this.h);
            }
          } else {  //奇数格
            this.cxt.drawImage(this.block2,this.w*j,this.h*i,this.w,this.h);
            if (i % 2 === 0) {
              this.cxt.drawImage(this.block,this.w*j,this.h*i,this.w,this.h);
            }
          }
        }
      }
    },
    //小人位置坐标
    Point(x,y){
      this.x = x;
      this.y = y;
    },
    //绘制每个游戏关卡地图
    DrawMap(level){
      for (var i=0;i<level.length ;i++ )
      {
        for (var j=0;j<level[i].length ;j++ )
        {
          var pic = this.transparentBlock
          switch (level[i][j])
          {
            case 1://绘制墙壁
              pic = this.wall;
              break;
            case 2://绘制陷进
              pic = this.ball;
              break;
            case 3://绘制箱子
              pic =this.box;
              break;
            case 4://绘制小人
              pic = this.curMan;//小人有四个方向 具体显示哪个图片需要和上下左右方位值关联
              //获取小人的坐标位置
              this.perPosition.x = i;
              this.perPosition.y = j;
              break;
            case 5://绘制箱子及陷进位置
              pic = this.box;
              break;
          }
          //每个图片不一样宽 需要在对应地板的中心绘制地图
          // this.cxt.drawImage(pic,this.w*j-(pic.width-this.w)/2,this.h*i-(pic.height-this.h),pic.width,pic.height)
          // if (![1,2,3,4,5].includes(level[i][j])) {
          //   pic.width = this.w/pic.width * pic.width
          //   pic.height = this.w/pic.height * pic.height
          // }

          if (![4].includes(level[i][j])) {
            pic.width = this.w/pic.width * pic.width
            pic.height = this.w/pic.height * pic.height
          } else {
            pic.width = this.w/pic.width * (pic.width/0.8)
            pic.height = this.w/pic.height * (pic.height/0.7)
          }
          this.cxt.drawImage(pic,this.w*j-(pic.width-this.w)/2,this.h*i-(pic.height-this.h),pic.width,pic.height)
        }
      }
    },
    //下一关
    NextLevel(i){
      //iCurlevel当前的地图关数
      this.iCurlevel = this.iCurlevel + i;
      if (this.iCurlevel<0)
      {
        this.iCurlevel = 0;
        return;
      }
      var len = levels.length;
      if (this.iCurlevel > len-1)
      {
        this.iCurlevel = len-1;
      }
      localStorage.setItem('sokoban_level_now',this.iCurlevel)

      this.initLevel();//初始当前等级关卡
      this.moveTimes = 0;//游戏关卡移动步数清零
      this.showMoveInfo();//初始化当前关卡数据
    },
    //小人移动
    go(dir){
      var p1,p2;
      switch (dir)
      {
        case "up":
          this.curMan = this.up;
          //获取小人前面的两个坐标位置来进行判断小人是否能够移动
          p1 = new this.Point(this.perPosition.x-1,this.perPosition.y);
          p2 = new this.Point(this.perPosition.x-2,this.perPosition.y);
          break;
        case "down":
          this.curMan = this.down;
          p1 = new this.Point(this.perPosition.x+1,this.perPosition.y);
          p2 = new this.Point(this.perPosition.x+2,this.perPosition.y);
          break;
        case "left":
          this.curMan = this.left;
          p1 = new this.Point(this.perPosition.x,this.perPosition.y-1);
          p2 = new this.Point(this.perPosition.x,this.perPosition.y-2);
          break;
        case "right":
          this.curMan = this.right;
          p1 = new this.Point(this.perPosition.x,this.perPosition.y+1);
          p2 = new this.Point(this.perPosition.x,this.perPosition.y+2);
          break;
      }
      //若果小人能够移动的话，更新游戏数据，并重绘地图
      if (this.Trygo(p1,p2))
      {
        this.moveTimes ++;
        this.showMoveInfo();
      }
      //重绘地板
      this.InitMap();
      //重绘当前更新了数据的地图
      this.DrawMap(this.curMap);
      //若果移动完成了进入下一关
      if (this.checkFinish())
      {

        let passTheLevel = []
        if (localStorage.getItem('sokoban_pass_level_list')) {
          passTheLevel = JSON.parse(localStorage.getItem('sokoban_pass_level_list'))
        }
        if (!passTheLevel.includes(this.iCurlevel)) {
          passTheLevel.push(this.iCurlevel)
          this.showPassLeval = true
        }
        localStorage.setItem('sokoban_pass_level_list',JSON.stringify(passTheLevel))

        //定时器解决渲染地图后再弹窗
        setTimeout(()=>{
          this.popupModule = 2 // 1、按钮弹窗  2、消息提示
          this.popupMessage = '恭喜过关!!!'
          this.popupBtnNums = 0
           this.popupBtnList =  [
            {
              text: '进入下一关',
              ref: 'active',
              num: 1,
              fuc: this.NextLevel
            }
          ]
          this.dialogVisible = true

        },300)

      }
    },
    //判断是否推成功
    checkFinish(){
      for (var i=0;i<this.curMap.length ;i++ )
      {
        for (var j=0;j<this.curMap[i].length ;j++ )
        {
          //当前移动过的地图和初始地图进行比较，若果初始地图上的陷进参数在移动之后不是箱子的话就指代没推成功
          if (this.curLevel[i][j] == 2 && this.curMap[i][j] != 3 || this.curLevel[i][j] == 5 && this.curMap[i][j] != 3)
          {
            return false;
          }
        }
      }
      return true;
    },
    //判断小人是否能够移动
    Trygo(p1,p2){
      if(p1.x<0) return false;//若果超出地图的上边，不通过
      if(p1.y<0) return false;//若果超出地图的左边，不通过
      if(p1.x>this.curMap.length) return false;//若果超出地图的下边，不通过
      if(p1.y>this.curMap[0].length) return false;//若果超出地图的右边，不通过
      if(this.curMap[p1.x][p1.y]==1) return false;//若果前面是墙，不通过
      if (this.curMap[p1.x][p1.y]==3 || this.curMap[p1.x][p1.y]==5)
      {//若果小人前面是箱子那就还需要判断箱子前面有没有障碍物(箱子/墙)
        if (this.curMap[p2.x][p2.y]==1 || this.curMap[p2.x][p2.y]==3)
        {
          return false;
        }
        //若果判断不成功小人前面的箱子前进一步
        this.curMap[p2.x][p2.y] = 3;//更改地图对应坐标点的值
        //console.log(curMap[p2.x][p2.y]);
      }
      //若果都没判断成功小人前进一步
      this.curMap[p1.x][p1.y] = 4;//更改地图对应坐标点的值
      //若果小人前进了一步，小人原来的位置如何显示
      var v = this.curLevel[this.perPosition.x][this.perPosition.y];
      if (v!=2)//若果刚开始小人位置不是陷进的话
      {
        if (v==5)//可能是5 既有箱子又陷进
        {
          v=2;//若果小人本身就在陷进里面的话移开之后还是显示陷进
        }else{
          v=0;//小人移开之后之前小人的位置改为地板
        }
      }
      //重置小人位置的地图参数
      this.curMap[this.perPosition.x][this.perPosition.y] = v;
      //若果判断小人前进了一步，更新坐标值
      this.perPosition = p1;
      //若果小动了 返回true 指代能够移动小人
      return true;
    },

},
  destroyed() {

  },
  beforeDestory() {
  },
}
</script>
<style lang='less' scoped>
  .sokoban {
    height: 7rem;
    border: 1px solid transparent;
    .game{
      width: max-content;
      margin:0px auto;
      margin-top: -3.8vw;
      display: flex;
      #canvas {
        border: 0.1rem solid #C98322;
        border-radius: 0.2rem;
      }
      .leval_toops {
        position: absolute;
        right: 3.4rem;
        margin-top: 0.4rem;
        border: 0.04rem solid #5FC5DC;
        background: #5FC5DC;
        padding: 0.24rem 0.16rem;
        font-size: 0.26rem;
        font-weight: bold;
        border-radius: 0.16rem;
      }
      .toops {
        position: absolute;
        right: 1.2rem;
        margin-top: 0.4rem;
        border: 0.04rem solid #5FC5DC;
        padding: 0.24rem;
        border-radius: 0.16rem;
        font-size: 0.26rem;
        font-weight: bold;
        background: url('../../../assets/matrix_state.png') no-repeat;
        background-size: 100% 100%;
      }
      .message {
        position: absolute;
        height: 4rem;
        right: 1.18rem;
        bottom: 1.2rem;
        width: 2.4rem;
        padding: 0.3rem;
        border: 0.04rem solid #5FC5DC;
        background: url("../../../assets/connectFour_state.png") no-repeat;
        background-size: 100% 100%;


        border-radius: 0.16rem;
        h2 {
          color: #1EE8F8;
          margin-bottom: 0.2rem;
        }
        p {
          font-size: 0.236rem;
          display: flex;
          margin-bottom: 0.1rem;
          span {
            display: inline-block;
          }
          span:nth-child(2) {
            margin-top: -0.04rem;
            margin-left: 0.06rem;
          }
        }
      }
    }
  }
</style>
<style lang="less">
  .sokoban {
    .el-dialog {
      //width: fit-content;
      //margin-top: 0 !important;
      //top: 50%;
      //transform: translateY(-50%);
      //border-radius: 0.16rem;
      .el-dialog__header {
        .el-dialog__title {
          font-size: 0.5rem !important;
        }
      }
      .el-dialog__body {
        .btnList {
          padding: 0.2rem 0 ;

          .btnItem {
            width: 8.75rem;
            height: 1rem;
            line-height: 1.08rem;
            background: #5472B0;
            //color: #fff;
            border-radius: 0.2rem;
            margin-bottom: 0.3rem;
            text-align: center;
            font-weight: bold;
            font-size: 0.5rem;
            position: relative;
            //text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.3);
            letter-spacing: 0.05rem;
            text-indent: 0.05rem;
            img {
              width: 0.48rem;
              height: 0.48rem;
              position: absolute;
              top: 50%;
              left: 2.2rem;
              transform: translateY(-45%);
            }
            .box-btnItem {
              background: linear-gradient(to bottom, #FDFEFF 30%, #BED1FB 90%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              //text-shadow: 0rem 0.02rem 0.05rem rgba(0, 0, 0, 0.5);
            }
          }
          .btnItem:last-child {
            margin-bottom: 0;
          }
        }
        .popupMessage {
          padding: 0.2rem 0 ;
          .popupMessage {
            //line-height: 0.4rem;
            text-align: center;
            //font-size: 0.24rem;
            font-size: 0.45rem;
            margin-bottom: 0.3rem;
          }
          .popupMessageBtn {

            width: 8.75rem;
            height: 1rem;
            //width: 20vw;
            //height: 0.6rem;
            //line-height: 0.68rem;
            line-height: 1rem;
            background: #5472B0;
            font-weight: bold;
            text-align: center;
            //font-size: 0.24rem;
            font-size: 0.5rem;
            color: #fff;
            border-radius: 0.16rem;
            margin: 0 auto;
            margin-top: 0.2rem !important;
          }
          .popupMessageBtn:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

</style>
