<template>
  <div class="online_food_aihu_confirm">
    <div class="balance">我的余额:{{ money }}元</div>
    <div class="container">
      <div class="left">
        <div class="leftContent">
          <div class="diningRoomInfo">
            <template v-if="selectDiningRoom">
              <div class="infoItem">
                <p>服务单位:</p>
                <p>{{ selectDiningRoom.name }}</p>
              </div>
              <div class="infoItem">
                <p>订单金额:</p>
                <p>{{getItemTotalNum(rightList).price.toFixed(2)}}元</p>
              </div>
              <div class="infoItem">
                <p>取餐方式:</p>
                <p class="mode_btn" ref="mode_btn">{{orderTakeType ? '堂食' : '自提' }}</p>
              </div>
              <div class="infoItem">
                <p>餐品数量:</p>
                <p>{{getItemTotalNum(rightList).itemNums}}份</p>
              </div>
            </template>
          </div>

          <div class="sendOrderBtn" ref="sendOrderBtn">
            <div>
              确认预约
            </div>
          </div>

        </div>
      </div>

      <div class="right">
        <div class="rightContent scrollParent" ref="rightContent" v-if="this.rightList.length > 0">
          <div class="list" :style="{ top: '0rem' }">
            <div class="rightListItem"  v-for="(item, index) in rightList" :key="index">
              <div class="rightBtnList">
                <div class="delectNum"  :style="{opacity: item.itemNum > 0 ? 1 : 0.5}" :isParentNode="true" :ref="item.editCartBtn[0].ref" v-if="item.editCartBtn">
                  <svg t='1678087632198' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='5027' xmlns:xlink='http://www.w3.org/1999/xlink' width='70' height='70'><path d='M256 480m32 0l448 0q32 0 32 32l0 0q0 32-32 32l-448 0q-32 0-32-32l0 0q0-32 32-32Z' fill='#EFF0F5' p-id='5028'></path><path d='M512 64A448 448 0 1 0 960 512 448.5 448.5 0 0 0 512 64z m0 832a384 384 0 1 1 384-384 384.5 384.5 0 0 1-384 384z' fill='#EFF0F5' p-id='5029'></path></svg>
                </div>

                <div class="nums">
                  {{item.itemNum}}
                </div>
                <div class="addNum" :isParentNode="true"  :ref="item.editCartBtn[1].ref"  v-if="item.editCartBtn">
                  <svg t='1678087565924' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='3770' xmlns:xlink='http://www.w3.org/1999/xlink' width='70' height='70'><path d='M512 958.8C265.6 958.8 65.2 758.4 65.2 512S265.6 65.2 512 65.2 958.8 265.6 958.8 512 758.4 958.8 512 958.8z m0-832c-212.4 0-385.2 172.8-385.2 385.2S299.6 897.2 512 897.2 897.2 724.4 897.2 512 724.4 126.8 512 126.8z' fill='#EFF0F5' p-id='3771'></path><path d='M727.7 485H542.8V300.2h-61.6V485H296.3v61.7h184.9v184.8h61.6V546.7h184.9z' fill='#EFF0F5' p-id='3772'></path></svg>
                </div>
              </div>
              <div class="rightInfo"  :key="index"  :style="{opacity: item.itemNum > 0 ? 1 : 0.5}">
                <div class="img"  v-lazy-container="{ selector: 'img' }">
                  <img :data-src="item.pic1" :data-error="lazyError" :key="item.pic1" alt="">
                </div>
                <div class="foodDetails">
                  <div class="foodTop">
                    <div>
                      {{'星期' + new Date(item.dinnerDate).format('W')}}
                    </div>
                    <div>
                      {{ new Date(item.dinnerDate).format('MM月dd日')}}
                    </div>
                    <div>
                      {{item.periodName}}
                    </div>
                  </div>
                  <div class="foodCenter">
                    <div>
                      {{item.productName}}<span>{{item.itemNum}}</span>份
                    </div>
                    <div>
                      共<span>￥{{item.subTotal}}</span>
                    </div>
                  </div>
                  <div class="foodBottom">
                    <div>
                      单价
                    </div>
                    ￥{{ item.price.toFixed(2)}}
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>

      </div>

      <!--  二次确认弹窗-->
      <el-dialog
          :class="popupModule == 2 ? 'selectNum_info_box selectPeriod_info_box'  : popupModule == 4 ? 'popup_info_box' : 'popup_box'"
          :visible.sync="dialogVisible"
          :show-close="false"
          :close-on-click-modal="false"
          @opened="popupOpend"
          @close="popupClose"
          :title="popupModule == 2 && !orderInfo ? '切换取餐方式' : popupModule == 2 && orderInfo ? '选择支付方式' : '提示'"
          ref="popupBox"
      >
        <!--询问弹窗-->
        <div class="message_box selectNum_box" v-if="popupModule == 1">
          <div class="message" v-html="popupMessage"></div>

          <div class="popupBtnList">
            <div :ref="item.ref" v-for="(item,index) in popupBtnList" :key="index">
              {{item.text}}
            </div>
          </div>

        </div>

        <!--切换取餐方式-->
        <div class="selectPeriod" v-if="popupModule == 2" :style="{height: popupBtnList.length > 2 ? '2.6rem' : '2.3rem'}">
          <div class="periodList" style="left: 0rem" v-if="popupBtnList.length > 0">
            <div class="periodItem" :ref="item.ref" v-for="(item,index) in popupBtnList" :key="index" :data="popupBtnNums" :style="{opacity: item.show ? 0.5 : 1}">
              <p>{{item.text}}</p>
            </div>
          </div>

          <div class="pointer" v-if="popupBtnList.length > 2">
            <div v-for="(item,index) in popupBtnList.length - 1"  :key="index" :style="{width: popupBtnNums == index ? '0.2rem': '0.1rem' }"></div>
          </div>
        </div>

        <!--支付订单提示-->
        <div class="message_box info" v-if="popupModule == 4">
          <div class="message">
            <div class="title"><span v-html="popupMessage[0]"></span></div>
            <div class="tip" v-html="popupMessage[1]"></div>
          </div>
          <div class="popupBtn">
            <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index"> {{ item.text }}</div>

          </div>
        </div>

      </el-dialog>
    </div>
  </div>
</template>
      
<script>
import { QueryShopCart, CreateShopCart, CreatShopOrder, payH5, payEsim } from '@/api/index'

export default {
  name:'online_food_aihu_confirm',
  components: {},
  data() {
    return {
      money:0,
      selectDiningRoom: null,
      editBtnIndex: 0,
      orderTakeType: 0,

      lazyError: require('@/assets/default_pic_meal.png'),
      leftList: [],
      rightList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      order_detail: [],

      firstShow: true,
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      orderInfo: null
    }
  },
  created() {
    this.money = sessionStorage.getItem('accountMoney') ? sessionStorage.getItem('accountMoney') : 0
    this.selectDiningRoom =  JSON.parse(sessionStorage.getItem('selectDiningRoom'))
    //设置页面左上角标题
    this.$nextTick(() => {
      this.$store.dispatch('index/setMainTitle', "预约确认")
    })
  },
  computed: {
    getItemTotalNum() {
      return (list)=>{
        let priceTotal = {
          price: 0,
          itemNums: 0
        }
        if (list.length > 0) {
          list.map(item=>{
            priceTotal.itemNums += item.itemNum
            priceTotal.price += item.subTotal
          })
          return priceTotal
        } else {
          return priceTotal
        }
      }
    }
  },
  watch: {},
  mounted() {
    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.$store.getters.loadingState) {
          return
        }
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if ((Math.abs(styleTop) +scrollPageTop)  * fontSize > this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight) {
                this.$refs.popupRef.style.top = styleTop - (scrollTop/fontSize - Math.abs(styleTop)) + 'rem'
                scrollBar.style.top = barTop + (lastScrollBar * ((scrollTop/fontSize - Math.abs(styleTop)) * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight)) + 'px'
                return
              }
              if (scrollTop > 0) {
                  this.$refs.popupRef.style.top = styleTop - scrollPageTop + 'rem'
                  scrollBar.style.top = barTop + (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))   + 'px'
              }
            }
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightList[this.rightNums] && this.rightNums < this.rightList.length - 1) {
            if (this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref) {
              this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref = ""
              this.rightNums++
              this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref = "active"

              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          } else if(this.rightNums == this.rightList.length - 1){
            this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref = ""
            this.editBtnIndex = 0
            this.nextNums = -1
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.sendOrderBtn)
            })
          }
        }
        //左侧
        else {
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.sendOrderBtn)
          })
        }
      },
      up: () => {
        if (this.$store.getters.loadingState) {
          return
        }
        if (this.dialogVisible) {
          if (this.$refs.popupRef) {
            let scrollBar = this.$refs.popupRef.parentNode.querySelector('.scrollBar')
            let fontSize = Number(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0])
            if (scrollBar) {
              let scrollTop = this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight
              let scrollPageTop = 150/fontSize
              let styleTop = Number(this.$refs.popupRef.style.top.split('rem')[0])
              let barTop = Number(scrollBar.style.top.split('px')[0])
              let lastScrollBar =  (this.$refs.popupRef.parentNode.clientHeight - scrollBar.clientHeight)
              if (scrollTop > 0) {
                if ((styleTop + scrollPageTop) > 0) {
                  this.$refs.popupRef.style.top = '0rem'
                  scrollBar.style.top = '0px'
                  return
                } else {
                  this.$refs.popupRef.style.top = styleTop + scrollPageTop + 'rem'
                  scrollBar.style.top = barTop - (lastScrollBar * (scrollPageTop * fontSize) / (this.$refs.popupRef.clientHeight - this.$refs.popupRef.parentNode.clientHeight))  + 'px'
                }
              }
            }
          }
          return
        }

        // 右侧
        if (this.nextNums > -1) {
          if (this.rightList[this.rightNums] && this.rightNums > 0) {
            if (this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref) {
              this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref = ""
              this.rightNums --
              this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref = "active"

              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
          }
        }
        // 左侧
        else {
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.mode_btn)
            })
        }
      },
      left: () => {
        if (this.$store.getters.loadingState) {
          return
        }
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          if (this.rightList[this.rightNums]) {
            if (this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref) {
              if (this.editBtnIndex == 1) {
                this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref = ""
                this.editBtnIndex = 0
                this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref = "active"
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active)
                })
              } else
              if (this.editBtnIndex == 0) {
                this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref = ""
                this.editBtnIndex = 0
                this.nextNums = -1
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.sendOrderBtn)
                })
              }
            }
          }
        }
      },
      right: () => {
        if (this.$store.getters.loadingState) {
          return
        }
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左侧
        if (this.nextNums == -1) {
            if (this.rightList.length > 0) {
              this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref = "active"
              this.nextNums = this.rightNums
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            }
        }
        // 在右侧
        else {
          if (this.rightList[this.rightNums]) {
            if (this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref) {
              if (this.editBtnIndex == 0) {
                this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref = ""
                this.editBtnIndex = 1
                this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref = "active"
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active)
                })
              }
            }
          }
        }
      },
      enter: () => {
          if (this.$store.getters.loadingState) {
            return
          }
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnList[this.popupBtnNums].fuc(this.rightList[this.rightNums])
            this.dialogVisible = false
            return
          }
          // 右侧按钮
          if (this.nextNums > -1) {
            if (this.rightList[this.rightNums]) {
              if (this.rightList[this.rightNums].editCartBtn[this.editBtnIndex].ref) {
                // 增加
                if (this.editBtnIndex == 1) {
                  this.editCartShop(true)
                } else
                // 减少
                if (this.editBtnIndex == 0) {
                  this.editCartShop(false)
                }
              }
            }
          }
          // 左侧
          else {
            // 确认预约
            if (this.$store.getters.focus_dom == this.$refs.sendOrderBtn) {
              this.createOrder()
            } else
            // 自提or堂食
            if (this.$store.getters.focus_dom == this.$refs.mode_btn) {
              this.popupModule = 2
              this.orderInfo = null
              this.popupBtnList = [{
                text: '自提',
                value: 0,
                ref:'',
                fuc:()=>{
                  this.orderTakeType = 0
                  this.popupBtnList[this.popupBtnNums].ref = ''
                  this.dialogVisible = false
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.mode_btn)
                  })
                }
              },{
                text: '堂食',
                value: 1,
                ref:'',
                fuc:()=>{
                  this.orderTakeType = 1
                  this.popupBtnList[this.popupBtnNums].ref = ''
                  this.dialogVisible = false
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.mode_btn)
                  })
                }
              }]
              this.popupBtnNums = this.orderTakeType
              this.popupBtnList[this.popupBtnNums].ref = "active"
              this.dialogVisible = true
            }
          }
      },
      esc: () => {
        if (this.nextNums == -1) {
          if (this.dialogVisible) {
            if (this.popupModule == 2 && !this.orderInfo) {
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.dialogVisible = false
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.mode_btn)
              })
              return
            } else {
              this.$store.dispatch('index/setFocusDom', null)
              history.go(-1)
              return
            }
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'
            this.dialogVisible = false
            return
          }
        } else
        if (this.nextNums > -1) {
          if (this.dialogVisible) {
            if (this.popupModule == 2) {
              this.rightList[this.rightNums].ref = 'active'
            }
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.dialogVisible = false
            return
          }
        }
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      },
    })
  },
  methods: {
    createOrder() {
      let sendList = []
      this.rightList.map(item=>{
        if (item.itemNum > 0) {
          sendList.push(item.carttemId)
        }
      })
      this.$store.dispatch('app/setLoadingState',true)
      CreatShopOrder({
        cardId: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].card_id_md5,
        shopId: this.selectDiningRoom.shopId,
        orderTakeType: this.orderTakeType,
        carttemId: JSON.stringify(JSON.stringify(sendList))
      })
      .then(res=>{
        this.$store.dispatch('app/setLoadingState',false)
        if (res.code == 200) {
          this.orderInfo = res.data
          this.popupModule = 4
          this.popupMessage = ['您即将支付订单!<br>取消将重新提交预约!']
          let price = 0
          if (this.rightList.length > 0) {
            this.rightList.map(item => {
              price += item.subTotal
            })
          }
          this.popupMessage[1] = '总金额:'+price.toFixed(2)+'元'
          this.popupBtnList = [{
            text: '取消',
            ref:'active',
            fuc:()=>{
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', document.body)
              })
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.dialogVisible = false
              history.go(-1)
            }
          },{
            text: '确认',
            ref:'',
            fuc:()=>{
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.dialogVisible = false
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.sendOrderBtn)
              })
            }
          }]
          this.dialogVisible = true

        }
      })
      .catch(err=>{
        this.$store.dispatch('app/setLoadingState',false)
        this.orderInfo = null
        this.popupModule = 4
        this.popupMessage = ['提交购物车失败!<br><br>','请稍后再试!']
        this.popupBtnNums = 0
        this.popupBtnList = [{
          text: '确认',
          ref:'active',
          fuc:()=>{
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.dialogVisible = false
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', document.body)
            })
            history.go(-1)
          }
        }]
        this.dialogVisible = true
      })

    },
    payOrder(type) {
      let fuc = type ? payEsim :  payH5
      this.$store.dispatch('app/setLoadingState',true)
      fuc({
        cardId: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].card_id_md5,
        paymentId: this.orderInfo.paymentId
      })
      .then(res=>{
        this.$store.dispatch('app/setLoadingState',false)
        if (res.code == 200) {
          this.popupModule = 4
          this.popupMessage = ['下单成功!<br><br>','您可以在"餐厅列表页面"查看订单信息。']
          this.popupBtnNums = 0
          this.popupBtnList = [{
            text: '确认',
            ref:'active',
            fuc:()=>{
              sessionStorage.removeItem('foodSelectList')
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.dialogVisible = false
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', document.body)
              })
              history.go(-1)
            }
          }]
          this.dialogVisible = true

        }
      })
      .catch(err=>{
        this.$store.dispatch('app/setLoadingState',false)
        this.orderInfo = null
        this.popupModule = 4
        this.popupMessage = ['提交订单失败!<br><br>','请稍后再试!']
        this.popupBtnNums = 0
        this.popupBtnList = [{
          text: '确认',
          ref:'active',
          fuc:()=>{
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.dialogVisible = false
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', document.body)
            })
            history.go(-1)
          }
        }]
        this.dialogVisible = true
      })

      // payH5, payEsim
    },
    popupClose() {
      if (this.nextNums > -1) {
        this.popupBtnList[this.popupBtnNums].ref = ''
        if (this.rightList[this.rightNums] && this.popupModule == 2) {
          this.rightList[this.rightNums].ref = 'active'
        }
      } else {
        document.getElementById('focus_border').style.borderColor = '#fff'
        if (this.popupModule == 4 && this.orderInfo) {
          this.popupModule = 2
          this.popupBtnNums = 0
          this.popupBtnList = [{
            text: '余额支付',
            ref:'active',
            show: false,
            fuc:()=>{
              if (!this.popupBtnList[0].show) {
                this.popupBtnList[this.popupBtnNums].ref = ''
                this.dialogVisible = false
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.sendOrderBtn)
                })
                this.payOrder(0)
              }
            }
          },{
            text: '短信支付',
            show: true,
            ref:'',
            fuc:()=>{
              if (!this.popupBtnList[1].show) {
                  this.popupBtnList[this.popupBtnNums].ref = ''
                  this.dialogVisible = false
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.sendOrderBtn)
                  })
                  this.payOrder(1)
              }
            }
          }]
          let price = 0
          if (this.rightList.length > 0) {
            this.rightList.map(item => {
              price += item.subTotal
            })
          }
          if (Number(this.money) < price) {
            this.popupBtnList[0].show = true
            this.popupBtnList[0].text = '余额支付(余额不足)'
          }

          this.dialogVisible = true
          setTimeout(()=>{
            this.$nextTick(() => {
              document.getElementById('focus_border').style.borderColor = '#4b68a7'
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          },200)
          return
        } else {
          return
        }
        setTimeout(()=>{
          this.popupBtnNums = 0
          this.popupModule = 1
        },260)
      }
    },
    popupOpend() {

      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0] ? this.$refs.active[0] : this.$refs.active)
        document.getElementById('focus_border').style.borderColor = '#4b68a7'
      })
    },
    getData() {
      // 查询订单列表
      this.$store.dispatch('app/setLoadingState',true)
      QueryShopCart({
        cardId: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].card_id_md5,
        shopId: this.selectDiningRoom.shopId,
      }).then((res) => {
        this.$store.dispatch('app/setLoadingState',false)
        if (res.code == 200) {
          if (res.data.length == 0) {
            // sessionStorage.removeItem('foodSelectList')
            history.go(-1)
            return
          }

          res.data.map(item=>{
            item.ref = ""
            item.editCartBtn = [{
              ref:''
            },{
              ref:''
            }]
          })
          this.rightList = res.data

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.sendOrderBtn)
          })
        }
      }).catch(()=>{
        sessionStorage.removeItem('foodSelectList')
        history.go(-1)
      })
    },
    editCartShop(type) {
      let sendCart = JSON.parse(JSON.stringify(this.rightList[this.rightNums]))
      sendCart.itemNum = "1"
      sendCart.productSpecId = sendCart.productSpedId
      sendCart.dinnnerDate = sendCart.dinnerDate
      if (type) {
        sendCart.operationType = "1"
      } else {
        sendCart.operationType = "0"
      }

      if (this.rightList[this.rightNums].itemNum < 1 && sendCart.operationType == "0") {
        return
      }
      this.$store.dispatch('app/setLoadingState',true)
      CreateShopCart({
        cardId: this.$store.getters.getUserInfo.oldsters[this.$store.state.app.selectUserIndex].card_id_md5,
        cart: JSON.stringify([sendCart]),
      }).then(res=>{
        this.$store.dispatch('app/setLoadingState',false)
        if (res.code == 200) {
          if (type) {
            this.rightList[this.rightNums].itemNum++
            this.rightList[this.rightNums].subTotal += this.rightList[this.rightNums].price
          } else {
            this.rightList[this.rightNums].itemNum--
            if (this.rightList[this.rightNums].itemNum < 1) {
              this.rightList[this.rightNums].subTotal = 0
            } else {
              this.rightList[this.rightNums].subTotal -= this.rightList[this.rightNums].price
            }
          }
        }
      }).catch(err=>{
        this.$store.dispatch('app/setLoadingState',false)
      })
    }
  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = null
  },
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
  .online_food_aihu_confirm {
    width: 16.73rem;
    .balance {
      height: 2.24rem;
      line-height: 2.24rem;
      position: absolute;
      right: 1.2rem;
      top: 0;
      display: flex;
      text-align: right;
      font-size: 0.46rem;
      font-weight: bold;
      color: #f1f1f1;
      background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.65));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .container {
      height: 7rem;
      overflow: hidden;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      .left {
        width: 4rem;
        height: 7rem;
        margin-left: 0;
        .leftContent {
          width: 100%;
          height: 100%;
          position: relative;
          display: inline-block;
          .diningRoomInfo {
            width: 4.1rem;
            height: 5.8rem;
            background: #2A2B5B;
            border-radius: 0.25rem;
            font-size: 0.34rem;
            color: #8F96CC;
            position: relative;
            overflow: hidden;
            .infoItem {
              width: 3.7rem;
              border-bottom: 0.02rem solid #53567F;
              margin: 0 auto;
              padding: 0.15rem 0 0.1rem 0;
              display: flex;
              align-items: center;
              box-sizing: border-box;
              p:nth-child(1) {
                width: 1.5rem;
              }
              p:nth-child(2) {
                width: 2.1rem;
                font-weight: bold;
                color: #fff;
                margin-left: 0.16rem;

                overflow: hidden;
                display: -webkit-box;
                text-overflow:ellipsis;
                -webkit-line-clamp:5;
                -webkit-box-orient: vertical
              }
              .mode_btn {
                width: 1.18rem !important;
                height: 0.61rem;
                text-align: center;
                line-height: 0.61rem;
                display: inline-block;
                background: #5472B0;
                border-radius: 0.26rem;
                margin-left: 0.1rem;
              }
            }
            .infoItem:first-child {
              margin-top: 0.4rem;
            }
          }
          .sendOrderBtn {
            width: 4.1rem;
            height: 0.8rem;
            background: #5472B0;
            font-size: 0.4rem;
            font-weight: bold;
            border-radius: 0.25rem;
            margin-top: 0.4rem;
            color: #Fff;
            text-align: center;
            line-height: 0.8rem;
            position: relative;
          }
        }


      }
      .right {
        width: 12rem;
        height: 7rem;
        //position: relative;
        .rightContent {
          width: 100%;
          height: 7rem;
          position: relative !important;
          overflow: hidden;
          display: inline-block;
          .list {
            // margin-right: 0.22rem;
            position: absolute;
            transition: all 0.3s;
            .rightListItem {
              height: 2.2rem;
              padding: 0 0.35rem;
              padding-right: 0rem !important;
              padding-top: 0;
              margin-bottom: 0.2rem;
              border-radius: 0.24rem;
              font-size: 0.28rem;
              font-weight: bold;
              display: flex;
              align-items: center;
              flex-direction: row;
              .rightBtnList {
                width: 3.4rem;
                height: 100%;
                display: flex;
                align-items: center;
                flex-direction: row;
                .nums {
                  width: 0.8rem;
                  text-align: center;
                  border: 0.01rem dashed #333;
                  margin: 0 0.1rem;
                  font-weight: 500;
                }
                .delectNum,.addNum {
                  width: 1.2rem;
                  //height: 100%;
                  background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
                  font-size: 0.3rem;
                  border-radius: 0.26rem;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  transition: all 0.3s;
                  svg {
                    width: 0.6rem;
                    height: 0.6rem;
                    margin: 0.2rem 0;
                  }
                }
              }
              .rightInfo {
                width: 7.6rem;
                height: 100%;
                background-size: 100% 100% !important;
                position: relative;
                margin-left: 0.36rem;
                font-size: 0.36rem;
                overflow: hidden;
                border-radius: 0.24rem;
                background-image: linear-gradient(to bottom, #ECF1F8, #CED4E0);
                display: flex;
                align-items: center;
                transition: all 0.3s;
                img {
                  display: block;
                  width: 2.2rem;
                  height: 1.8rem;
                  border-radius: 0.26rem;
                  margin: 0.2rem 0.2rem;
                }
                .foodDetails {
                  width: calc(100% - 2.8rem);
                  height: 1.8rem;
                  color: #5D5D5F;
                  .foodTop {
                    display: flex;
                    font-size: 0.42rem;
                    font-weight: bold;
                    height: 0.62rem;
                    line-height: 0.5rem;
                    div {
                      margin-right: 0.24rem;
                    }
                  }
                  .foodCenter {
                    display: flex;
                    justify-content: space-between;
                    span {
                      color: #F54D23;
                    }
                  }
                  .foodBottom {
                    display: flex;
                    align-items: center;
                    margin-top: 0.14rem;
                    div {
                      width: 1rem;
                      text-align: center;
                      border-radius: 0.1rem;
                      color: #F4FFFE;
                      background: #5493DA;
                      font-size: 0.3rem;
                      letter-spacing: 0.03rem;
                      padding: 0.05rem 0;
                      margin-bottom: 0.05rem;
                      margin-right: 0.1rem;
                      font-weight: 500;
                    }
                  }
                }
              }
            }
          }
        }
  }
      .no_info {
        line-height: 7rem;
        text-align: center;
        font-size: 0.4rem;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        line-height: 6rem;
        color: #b5c0ff;
      }
    }
  }
</style>
<style lang="less">
  .online_food_aihu_confirm {
    .el-dialog {
      width: 9.4rem;
      height: 8rem;
      margin-top: 0 !important;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 0.26rem;
      // background: repeating-linear-gradient(to right, #c98693, #a95361);
      background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

      .el-dialog__header {
        height: 10%;
        display: flex;
        align-items: center;
        padding-top: 35px;
        .el-dialog__title {
          display: block;
          line-height: normal !important;
          height: 100%;
          font-size: 0.4rem;
          color: #fff;
          background-image: linear-gradient(
              to bottom,
              rgba(255, 255, 255, 1),
              rgba(255, 255, 255, 0.65)
          );
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          font-weight: bold;
          width: 100%;
          text-align: center;
        }
      }
      .el-dialog__body {
        width: 92%;
        height: calc(100% - 1.1rem);
        position: absolute;
        bottom: 4%;
        left: 4%;
        border-radius: 0.2rem;
        background: #fff;
        padding: 0 !important;
        .sendCall {
        }
        .cancel,.sendCall {
          width: calc(100% - 0.8rem);
          height: 4.26rem;
          //top: 0.4rem;
          //left: 0.4rem;
          padding: 0;
          margin-top: 0.4rem;
          margin-left: 0.4rem;
          //position: relative;
          overflow: hidden;
          position: relative;
          .message_content {
            width: 100%;
            position: absolute;
            transition: all 0.3s;
            .message_item {
              display: flex;
              flex-direction: row;
              font-size: 0.4rem !important;
              margin-bottom: 0.2rem;
              letter-spacing: 0.03rem;
              .type {
                width: 2.2rem;
                font-size: 0.4rem !important;
              }
            }
            .orderItem {
              letter-spacing: 0.03rem;
              font-size: 0.4rem !important;
              div {
                margin-bottom: 0.2rem;
              }
            }
          }
          .scroll {
            top: 1.8rem !important;
            left: auto !important;
            right: 0.8rem !important;
          }
        }
        .sendCall {
          .message_item {
            flex-direction: column !important;
          }
        }
        .result {
          display: flex;
          justify-content: center !important;
          align-items: center !important;
        }
        .info {
          display: flex;
          justify-content: center !important;
          align-items: center !important;
          margin-top: -0.4rem;
          // background: url(http://192.168.6.212/ptyanglaotv/assets/zc_tan_new.png) no-repeat !important;
          .title {
            display: flex;
            flex-direction: column;
            align-items: center !important;
            text-align: center !important;

            // margin-top: 0.9rem;
            span {
              font-size: 0.5rem;
              color: #464646 !important;
              letter-spacing: 0.14rem;
            }
          }
          .phone {
            width: 7rem;
            text-align: center;
            position: absolute;
            top: 2.3rem;
            font-size: 0.3rem;
            color: #464646;
          }
          .tip {
            text-align: center;
            color: #3288dc;
            font-size: 0.4rem;
            margin-top: 1.2rem;
          }
        }
        .popupBtnList {
          display: flex;
          flex-direction: row;
          justify-content: space-evenly;
          margin-top: 0.25rem;
          div {
            width: 3.84rem;
            height: 1rem;
            line-height: 1rem;
            text-align: center;
            border-radius: 0.3rem;
            color: #fff;
            font-size: 0.45rem;
            font-weight: bold;
            letter-spacing: 0.05rem;
            text-indent: 0.05rem;
            // background: repeating-linear-gradient(to right, #c98693, #a95361);
            background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
          }
        }
      }
    }
    .popup_info_box {
      .el-dialog {
        width: 8.6rem;
        height: 5.74rem;
        background: url("../../assets/zc_tan_new.png") no-repeat;
        background-size: 100% 100%;
        .el-dialog__header {
          display: none;
        }
        .el-dialog__body {
          background: transparent;
          .info {
            display: flex;
            align-items: center;
            margin-top: -0.4rem;
            flex-direction: column;
            .message {
              height: 3rem;
              margin-top: 0.6rem;
            }
            .title {
              span {
                color: #E7F1FA !important;
                font-weight: bold;
                text-indent: 0.14rem;
              }
            }
            .tip {
              margin-top: 0.8rem;
              font-size: 0.28rem;
              color: red;
              font-weight: bold;
              letter-spacing: 0.02rem;
              text-indent: 0.02rem;
            }
            .popupBtn {
              width: 100%;
              display: flex;
              display: flex;
              align-items: center;
              justify-content: space-around;
              div {
                width: 2.84rem;
                background: #5999D6;
                height: 0.9rem;
                border-radius: 0.4rem;
                line-height: 0.9rem;
                text-align: center;
                font-size: 0.45rem;
                color: #fff;
              }
            }
          }
        }
      }
    }
    .selectNum_info_box {
      .el-dialog {
        height: 5.5rem;
        .el-dialog__header {
          height: 1.1rem;
          padding: 0 !important;
          .el-dialog__title {
            font-size: 0.48rem;
            line-height: 1.1rem !important;
          }
        }

        .el-dialog__body {
          width: 100%;
          left: 0;
          bottom: 0;
          border-top-left-radius: 0;
          border-top-right-radius: 0;
          .selectNum_box {
            padding: 0.3rem;
            .tips {
              color: #F54D22;
              font-size: 0.34rem;
              margin: 0rem auto;
              width: 7.5rem;
              font-weight: bold;
            }
            .popupCenter {
              display: flex;
              justify-content: space-between;
              font-size: 0.45rem;
              width: 7.5rem;
              align-items: center;
              margin: 0 auto;
              margin-top: 0.6rem;
              .delectNum {
                width: 1.8rem;
                background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
                font-size: 0.3rem;
                height: 1rem;
                line-height: 1.5rem;
                float: left;
                text-align: center;
                position: relative;
                border-radius: 0.3rem;
              }
              .nums {
                width: 2.7rem;
                border: 0.01rem dashed #333;
                line-height: 1rem !important;
                color: #333;
                text-align: center;

              }
              .addNum {
                width: 1.8rem;
                background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
                font-size: 0.3rem;
                height: 1rem;
                float: left;
                text-align: center;
                line-height: 1.5rem;
                position: relative;
                border-radius: 0.3rem;

              }
            }
            .savePopupNum {
              width: 2.8rem;
              background: repeating-linear-gradient(to right, #6c88be, #4b68a7);
              height: 1rem;
              margin: 0.5rem auto;
              color: #fff;
              text-align: center;
              line-height: 1rem;
              border-radius: 0.3rem;
              font-size: 0.38rem;
            }
          }
        }
      }
    }
    .message_box_info {
      .message_box {
        align-items: center !important;
        .message {
          width: 7.3rem;
          min-height: 2.26rem !important;
          text-align: center;
          font-size: 0.44rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          color: #5472B0;

        }
        .popupBtnList {
          width: 100%;
        }
      }

    }
    .selectPeriod_info_box {
      .el-dialog {
        height: 4.3rem;
        .selectPeriod {
          width: 8.1rem;
          height: 2rem;
          overflow: hidden;
          position: relative;
          margin: 0.35rem auto;
          border-radius: 0.3rem;
          .popupTitle {
            height: 0.4rem;
            line-height: 0.4rem;
            font-size: 0.18rem;
            font-weight: bold;
            margin-bottom: 0.06rem;
          }
          .periodList {
            width: max-content;
            position: absolute;
            left: 0;
            transition: all 0.3s;
            .periodItem {
              float: left;
              display: flex;
              flex-direction: column;
              align-items: center;

              width: 3.9rem;
              height: 2.3rem;
              background: #5472B0;
              border-radius: 0.3rem;
              position: relative;
              margin-right: 0.3rem;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.3s;
              p {
                font-size: 0.35rem;
                width: 100%;
                color: #fff;
                text-align: center;
                font-weight: bold;
              }


            }
          }
          .pointer {
            width: 8.1rem;
            position: absolute;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            div {
              width: 0.1rem;
              height: 0.1rem;
              transition: all 0.3s;
              border-radius: 0.05rem;
              background: #888;
              display: inline-block;
              margin-right: 0.08rem;
            }
          }
        }
      }
    }
  }
</style>
      