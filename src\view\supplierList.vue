<template>
  <div class='supplierList'>

    <div class="supplierContent">
      <div class="list" :style="{left: '0rem',position: supplierList.length > 3 ? 'absolute' : 'relative'}" v-if="supplierList.length > 0">
        <div class="listItem" :ref="item.ref" v-for="(item,index) in supplierList" :key="index" :data="swiperIndex" :style="{background: 'url('+item.bg+')',
        marginRight: (supplierList.length < 4 && index == supplierList.length-1) ? '0rem' : '0.9rem' }">

          <div :class="item.name ? 'pic' : 'picItem'" v-lazy-container="{ selector: 'img' }">
            <img :data-src="item.icon_free"  :data-error="lazyError"  :key="index" alt="">

          </div>


          <div class="textContent">
            <span>{{ item.title }}</span>
            <span>{{ item.pinyin }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="pointer" v-if="supplierList.length > 3">
      <div v-for="(item,index) in supplierList.length - 2"  :key="index" :style="{width: pointerIndex == index ? '0.24rem': '0.14rem' }"></div>
    </div>


  </div>
</template>

<script>

import pinyin from '@/utils/pinYin'
export default {
  name:'supplier',
  components: {
  },
  data() {
    return {
      lazyError:  require('@/assets/fubao_logo.png'),
      supplierList:[],
      swiperIndex: 0,
      pointerIndex: 0,
      // iconList:[require('@/assets/icon_bg_1.png'),require('@/assets/icon_bg_2.png'),require('@/assets/icon_bg_3.png')]
    };
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle','供应商选择')
    sessionStorage.removeItem('supplierInfo')
    if (sessionStorage.getItem('supplierList')) {
      this.getData(JSON.parse(sessionStorage.getItem('supplierList')))
    }

  },
  computed: {},
  watch: {

  },
  mounted() {

    this.fuc.KeyboardEvents({
      down:()=>{

      },
      up:()=>{

      },
      left:()=>{
        if (this.swiperIndex > 0) {
          this.supplierList[this.swiperIndex].ref = ""
          this.swiperIndex --
          this.supplierList[this.swiperIndex].ref = "active"
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active);
            // 指示器
            const element = this.$refs.active[0]
            if (element) {
              const windowWidth = element.parentNode.parentNode.clientWidth  // 可视区域高度
              const elementWidth = Number(element.clientWidth)   // 当前元素高度
              const elementOffsetLeft = element.offsetLeft  // 当前元素距离顶端的高度
              const windowScrollLeft = element.parentNode.offsetLeft  // 页面下拉高度

              if(((elementOffsetLeft + elementWidth + windowScrollLeft < 0) || elementOffsetLeft + elementWidth + windowScrollLeft > windowWidth) &&
              element.parentNode.clientWidth > windowWidth) {
                this.pointerIndex--
              }
            }
          })
        }
      },
      right:()=>{
        if (this.swiperIndex < this.supplierList.length -1 ) {
          this.supplierList[this.swiperIndex].ref = ""
          this.swiperIndex ++
          this.supplierList[this.swiperIndex].ref = "active"

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active);
            // 指示器
            const element = this.$refs.active[0]
            if (element) {
              const windowWidth = element.parentNode.parentNode.clientWidth  // 可视区域高度
              const elementWidth = Number(element.clientWidth)   // 当前元素高度
              const elementOffsetLeft = element.offsetLeft  // 当前元素距离顶端的高度
              const windowScrollLeft = element.parentNode.offsetLeft  // 页面下拉高度

              if(((elementOffsetLeft + elementWidth + windowScrollLeft < 0) || elementOffsetLeft + elementWidth + windowScrollLeft > windowWidth) &&
              element.parentNode.clientWidth > windowWidth) {
                this.pointerIndex++
              }
            }
          })
        }

      },
      enter:()=>{
        if (sessionStorage.getItem('redirectInfo')) {
          sessionStorage.setItem('supplierInfo',JSON.stringify(this.supplierList[this.swiperIndex]))
          sessionStorage.setItem('supplierIndex',this.swiperIndex)
          let pageInfo = JSON.parse(sessionStorage.getItem('redirectInfo'))
          this.$nextTick(()=>{
            if (this.supplierList[this.swiperIndex].redirect) {
              let path = this.supplierList[this.swiperIndex].redirect
              if (path.indexOf('?type=') == -1) {
                path += '?type=' + this.supplierList[this.swiperIndex].type
              }
              this.$router.push({
                path: path
              })
            }
          })
        }

      },
      esc:()=>{
        this.$store.dispatch('index/setFocusDom', null);
        history.go(-1)
      }
    })
  },
  methods: {
    getData(list) {
      this.$store.dispatch('app/setLoadingState', true)
      if (sessionStorage.getItem('supplierIndex')) {
        this.swiperIndex = Number(sessionStorage.getItem('supplierIndex'))
        sessionStorage.removeItem('supplierIndex')
      }
      let bgNum = 0
      list.map((item,index) =>{
        bgNum ++
        if (bgNum > 3) {
          bgNum = 1
        }
        item.bg = require('@/assets/icon_bg_'+bgNum+'.png')
        item.ref = ""
        item.pinyin = pinyin.getPinYin(item.title,' ', true)
        if (item.icon_free) {
          item.icon_free = item.icon_free.indexOf('http') > -1 ? item.icon_free : process.env.VUE_APP_API + item.icon_free
          // item.icon_free = item.icon_free.indexOf('http') > -1 ? item.icon_free : 'http://*************:22003' + item.icon_free
        }
      })
      this.supplierList = list

      this.supplierList[this.swiperIndex].ref = "active"
      this.$nextTick(()=>{
        this.$store.dispatch('index/setFocusDom',this.$refs.active);
      })
      setTimeout(()=>{
        this.$store.dispatch('app/setLoadingState', false)
      },100)


    }

  },
  destroyed() {

  },
  beforeDestory() {
  },
}
</script>
<style lang='less' scoped>
.supplierList {
  height: 6.8rem;
  overflow: hidden;
  font-size: 0.2rem;
  color: #E7E7EF;
  display: flex;
  justify-content: center;
  position: relative;
  border-radius: 0.4rem;
  .supplierContent {
    position: relative;
    width: 100%;
    height: 6rem;
    border-radius: 0.4rem;
    display: flex;
    justify-content: center;
    overflow: hidden;
    .list {
      height: 100%;
      width: max-content;
      transition: all 0.3s;
      border-radius: 0.4rem;
      .listItem {
        width: 5rem;
        height: 100%;
        border-radius: 0.4rem;
        float: left;
        background-repeat: no-repeat !important;
        background-size: 100% 100% !important;
        position: relative;
        img {
          display: block;
          width: 2.3rem;
          height: 2.3rem;
          border-radius: 50%;
          position: absolute;
          top: 0.55rem;
          left: 50%;
          transform: translateX(-50%);

          border: 0.058rem solid #8093BF;
          box-sizing: border-box;
          padding: 0.08rem;
        }
        .textContent {
          position: absolute;
          width: 90%;
          text-align: center;
          top: 3.9rem;
          left: 50%;
          transform: translateX(-50%);
          span:first-child {
            width: 100%;
            display: inline-block;
            font-weight: bold;
            letter-spacing: 0.1rem;
            text-indent: 0.1rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 0.55rem;
            color: #5372AF;
            height: 1rem;
          }
          span:last-child {
            width: 100%;
            display: inline-block;
            font-size: 0.26rem;
            color: #9EAEC9;
            letter-spacing: 0.03rem;
          }
        }
      }
    }
  }



  .pointer {
    width: 4rem;
    position: absolute;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    div {
      width: 0.14rem;
      height: 0.14rem;
      transition: all 0.3s;
      border-radius: 0.14rem;
      background: #EEE;
      display: inline-block;
      margin-right: 0.1rem;
    }
  }

}

</style>
<style lang="less">

</style>
