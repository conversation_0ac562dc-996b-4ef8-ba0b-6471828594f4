<template>
  <div class="orderTime">
    <div class="container">
      <div class="left">
        <div class="messageCenterList scrollParent" v-if="leftList.length > 0">
          <div class="leftList" :style="{ top: '0rem' }">
            <div
              class="messageItem"
              :ref="item.ref"
              v-for="(item, index) in leftList"
              :key="index"
            >
              <div class="item_user">
                <span>{{ item.month_day }}</span>
              </div>
              <div class="status" v-if="item.state === 1">
                <span>已预约</span>
              </div>
            </div>
          </div>
        </div>
        <div class="no_content" v-else>暂无预约时间</div>
      </div>

      <div class="right scrollParent" ref="refRight">
        <div class="list" ref="refList" :style="{ top: '0rem' }">
          <div class="content">
            <div class="content_detail">
              <div class="pic" v-lazy-container="{ selector: 'img' }">
                <img
                  :data-src="apiUrl + list.img"
                  alt=""
                  :data-error="errorImg"
                />
                <div class="detail">详细介绍</div>
              </div>
              <div class="info">
                <div class="name">{{ list.name }}</div>
                <div class="title">{{ list.title }}</div>
                <div class="describe">{{ list.describe }}</div>
                <div class="max">最多可预约人数：{{ list.max_people }}人</div>
              </div>
            </div>
          </div>
          <div class="detail-info">
            <div class="content-info" v-html="list.content"></div>
          </div>
        </div>
      </div>

      <el-dialog
        class="popup_box"
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        title="专家坐诊"
      >
        <!--发起预约-->
        <div class="message_box sendCall" v-if="popupModule == 1">
          <div class="title">
            <p>请确认预约时间：</p>
            <span>{{
              leftList[leftNums] && leftList[leftNums].month_day
            }}</span>
          </div>
        </div>
        <div
          class="message_box result"
          style="
            text-align: center;
            font-size: 0.37rem;
            justify-content: center;
          "
          v-html="popupMessage"
          v-if="popupModule == 2"
        ></div>
        <div class="popupBtnList">
          <div
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
          >
            {{ item.text }}
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
        
  <script>
import { getTimeList, submitOrder } from '@/api/index'

export default {
  name: 'orderTime',
  components: {},
  data() {
    return {
      leftList: [],
      list: {},
      leftNums: 0,
      apiUrl: process.env.VUE_APP_API,
      errorImg: require('@/assets/avatar_icon.png'),
      sessionStorage_supplier_id: '',
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧
      rightNums: 0,
      content: '',
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      messageList: [],
    }
  },
  created() {
    //设置页面左上角标题
    this.$nextTick(() => {
      this.$store.dispatch('index/setMainTitle', '专家坐诊')
    })
  },
  computed: {},
  watch: {},
  mounted() {
    this.list = JSON.parse(this.$route.query.list)
    this.list.content = this.list.content.replace(
      new RegExp('<p', 'g'),
      '<p style="word-break:break-all;" '
    )
    this.sessionStorage_supplier_id = JSON.parse(
      sessionStorage.getItem('supplierInfo')
    ).fk_supplier_id
    if (this.content.indexOf('<img') > -1) {
      let reg = new RegExp('/public/storage/', 'g')
      this.content = this.content.replace(
        reg,
        process.env.VUE_APP_API + '/public/storage/'
      )
    }
    setTimeout(() => {
      this.fuc.setScroll()
    }, 100)

    this.getData()
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          return
        }
        // 在左侧
        if (this.nextNums == -1) {
          if (this.leftNums < this.leftList.length - 1) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            this.leftList[this.leftNums].ref = 'active'
          }
        }
        // 在右侧
        if (this.nextNums > -1) {
          const listEl = this.$refs.refList
          // const scrollBar = this.$refs.refScroll
          const scrollBar =
            this.$refs.refList.parentNode.querySelector('.scrollBar')

          // console.log('可视区域', listEl.parentNode.clientHeight)
          // console.log('元素距离顶部', listEl.offsetTop)
          // console.log('元素高度', listEl.clientHeight)
          // console.log('下拉高度', listEl.parentNode.offsetTop)

          if (listEl && scrollBar) {
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight

            const radio = listHeight / listVisHeight

            const potentialNewTop = currentTop - 150 // 预计的新top值

            const scrollNewTop = currentScrollTop + 150 / radio
            const maxScrollableHeight =
              listEl.clientHeight - listEl.parentNode.clientHeight // 最大可滚动高度
            const maxSrcollBarHeight =
              scrollBar.parentNode.clientHeight - scrollBar.clientHeight
            if (listVisHeight < listHeight) {
              // 将元素的top值与最大可滚动高度进行比较，以确定是否已经到达或超过了底部
              if (-potentialNewTop < maxScrollableHeight) {
                listEl.style.top = `${potentialNewTop}px`
              } else {
                // 已经到达或超过底部，调整top以确保内容底部和容器底部对齐
                listEl.style.top = `${-maxScrollableHeight - 50}px`
              }
            } else {
              return
            }

            if (scrollNewTop < maxSrcollBarHeight) {
              scrollBar.style.top = `${scrollNewTop}px`
            } else {
              scrollBar.style.top = `${maxSrcollBarHeight}px`
            }
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          return
        }
        // 在左侧
        if (this.nextNums == -1) {
          if (this.leftNums > 0 && this.leftList.length - 1 >= 0) {
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
          }
        }
        // 在右侧
        if (this.nextNums > -1) {
          const listEl = this.$refs.refList
          // const scrollBar = this.$refs.refScroll
          const scrollBar =
            this.$refs.refList.parentNode.querySelector('.scrollBar')

          if (listEl && scrollBar) {
            let currentTop = parseInt(listEl.style.top, 10)
            let currentScrollTop = parseInt(scrollBar.style.top, 10)
            let listVisHeight = listEl.parentNode.clientHeight
            let listHeight = listEl.clientHeight
            const radio = listHeight / listVisHeight
            const potentialNewTop = currentTop + 150 // 预计的新top值
            const scrollNewTop = currentScrollTop - 135 / radio

            // 检查是否已经到达最顶部
            if (potentialNewTop >= 0) {
              listEl.style.top = `0px` // 直接设置为0，确保不会超过顶部
              scrollBar.style.top = `0px`
            } else {
              listEl.style.top = `${potentialNewTop}px`
              scrollBar.style.top = `${scrollNewTop}px`
            }
          }
        }

        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          if (this.leftList.length > 0) {
            this.$refs.active = []
            this.leftList[this.leftNums].ref = 'active'
            this.nextNums = -1
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          } else {
            this.nextNums = 1
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.refRight)
            })
          }
        }
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在左
        if (this.nextNums == -1) {
          this.leftList[this.leftNums].ref = ''
          this.$store.dispatch('index/setFocusDom', this.$refs.refRight)
          this.nextNums = 0
        }
      },
      enter: () => {
        if (this.nextNums == -1) {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.leftList[this.leftNums].ref = 'active'

            this.dialogVisible = false
            // console.log(123,this.popupBtnList[this.popupBtnNums].fuc(this.leftList[this.leftNums]));
            this.popupBtnList[this.popupBtnNums].fuc(
              this.leftList[this.leftNums]
            )
            return
          }
          this.popupBtnList = [
            {
              text: '关闭',
              ref: '',
              fuc: () => {
                this.dialogVisible = false
              },
            },
            {
              text: '确认',
              ref: '',
              fuc: this.submitInfo,
            },
          ]
          this.dialogVisible = true
          this.popupModule = 1
          this.leftList[this.leftNums].ref = ''
          this.popupBtnList[this.popupBtnNums].ref = 'active'
        }
        return
      },
      esc: () => {
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.leftList[this.leftNums].ref = 'active'
          this.dialogVisible = false
          return
        }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        this.popupBtnNums = 0
        this.popupModule = 1
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#b36371'
      })
    },

    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      getTimeList({
        fk_zhuanjiazuozhen_zhuanjia_id: this.list.id,
        user_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].id,
        home_id: this.$store.getters.getUserInfo.home_id,
      }).then((res) => {
        if (res.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data))
          list.map((item) => {
            item.ref = ''
            if (item.month_day) {
              const date = new Date(item.month_day * 1000) // 假设时间戳是秒级的
              item.month_day =
                date.getFullYear() +
                '-' +
                String(date.getMonth() + 1).padStart(2, '0') +
                '-' +
                String(date.getDate()).padStart(2, '0')
            }
          })
          this.leftList = list
          if (this.leftList.length > 0) {
            this.nextNums = -1
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          } else {
            this.$refs.active = []
            this.$refs.active.push(this.$refs.refRight)
            this.nextNums = 0
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
        }
      })
    },
    submitInfo() {
      this.$store.dispatch('app/setLoadingState', true)
      submitOrder({
        fk_supplier_id: this.sessionStorage_supplier_id,
        fk_zhuanjiazuozhen_time_id: this.leftList[this.leftNums].id,
        user_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].id,
        home_id: this.$store.getters.getUserInfo.home_id,
      }).then(
        (res) => {
          if (res.code == 200) {
            this.popupModule = 2
            this.leftList[this.leftNums].ref = ''
            setTimeout(() => {
              this.getData()
            }, 300)
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                },
              },
            ]
            this.popupMessage = '订单发送成功！'
            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 2
          this.leftList[this.leftNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br>订单发送失败!</br>'
          if (
            error.response &&
            error.response.data &&
            error.response.data.msg
          ) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
  <style lang='less' scoped>
.orderTime {
  display: flex;
  position: relative;
  .container {
    height: 7rem;
    overflow: hidden;
    display: flex;
    .left {
      width: 3.5rem;
      height: 7rem;
      margin-left: 0;
      //padding-left: 0.1rem;
      //margin-left: 0.05rem;

      .messageCenterList {
        width: 100%;
        height: 100%;
        position: relative;
        .leftList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          // left: 0.8rem;
          transition: all 0.2s;
          .messageItem {
            width: 100%;
            height: 1.1rem;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0.5rem;
            margin-bottom: 0.35rem;
            position: relative;
            margin-right: 0.2rem !important;
            background-size: 100% 100% !important;
            // background: #ccc !important;
            background: #5472b0;
            border-radius: 0.3rem;
            overflow: hidden;
            .item_user {
              color: #fff;
              font-size: 0.26rem;
              font-weight: bold;
              text-align: center;
            }
            .status {
              left: inherit;
              span {
                position: absolute;
                bottom: 0rem;
                left: 0rem;
                width: 3.3rem !important;
                height: 0.4rem;
                background: #00b187;
                border-radius: 0.05rem;
                text-align: center;
                line-height: 0.35rem;
                font-weight: bold;
                color: #fff;
                letter-spacing: 0.03rem;
              }
            }
          }

          .messageItem:nth-child(3n + 3) {
            margin-right: 0;
          }
        }
      }
      .no_content {
        position: absolute;
        line-height: 7rem;
        left: 0.5rem;
        // width: 4rem;
        // height: 7rem;
        text-align: center;
        font-size: 0.4rem;
      }
    }
    .right {
      width: 11.5rem;
      height: 6.25rem;
      position: absolute;
      background: #25294f;
      font-size: 0.21rem;
      overflow: hidden;
      border-radius: 0.24rem;
      margin-left: 4rem;
      padding: 0.4rem;

      .list {
        width: 11.5rem;
        overflow-y: auto;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        .content {
          width: 100% !important;
          height: 100% !important;
          .content_detail {
            display: flex;
            width: 11rem;
            height: initial !important;
            .pic {
              img {
                width: 2rem;
                height: 2.5rem;
                overflow: hidden;
                border-radius: 0.15rem;
                background: #fff;
                border: 0.02rem solid #babdd3;
                object-fit: cover;
              }
              .detail {
                width: 1.8rem;
                height: 0.45rem;
                background: #4991ff;
                border-radius: 0.3rem;
                text-align: center;
                color: #fff;
                font-size: 0.26rem;
                font-weight: bold;
                line-height: 0.45rem;
                margin: 0.2rem 0 0.3rem 0.1rem;
              }
            }
            .info {
              // width: 100%;
              margin-top: 0.3rem;
              margin-left: 0.4rem;
              .name {
                font-weight: bold;
                color: #fff;
                font-size: 0.32rem;
                margin-bottom: 0.1rem;
              }
              .title {
                font-size: 0.26rem;
                color: #fff;
              }
              .describe,
              .max {
                width: 8.6rem;
                color: #b7c0f9;
                font-size: 0.24rem;
                margin-top: 0.1rem;
                // overflow: hidden;
                // text-overflow: ellipsis;
                // display: -webkit-box;
                // -webkit-line-clamp: 4;
                // -webkit-box-orient: vertical;
                letter-spacing: 0.01rem;
                word-wrap: break-word;
                white-space: normal;
              }
            }
          }
        }
        .detail-info {
          width: 11.5rem;
          height: 100%;
          color: #b7c0f9;
          font-size: 0.24rem;
          letter-spacing: 0.01rem;
          word-wrap: break-word;
          white-space: normal;
          .content-info {
            padding-left: 0.3rem;
          }
        }
      }
    }
  }
}
</style>
  <style lang="less">
.orderTime {
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    // background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

    background: repeating-linear-gradient(to right, #c98693, #a95361);
    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.4rem;
        letter-spacing: 0.03rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        justify-content: center !important;
        align-items: center !important;
        .title {
          font-size: 0.48rem;
          font-weight: bold;
          // padding-bottom: 0.12rem !important;
          p {
            margin-bottom: 0.22rem;
          }
        }
      }
      .result {
        font-size: 0.48rem !important;
        font-weight: bold !important;
        letter-spacing: 0.03rem !important;
      }
      .cancel {
        flex-direction: column;
        div {
          text-align: center;
        }
        div:nth-child(2) {
          margin-top: 0.1rem;
          color: red;
          line-height: 0.48rem;
        }
        //align-items: normal !important;
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          // background: repeating-linear-gradient(to right, #6c88be, #4b68a7);

          background: repeating-linear-gradient(to right, #c98693, #a95361);
        }
      }
    }
  }
  .container {
    .left {
      .scroll {
        top: 2.28rem !important;
        left: 4.8rem !important;
      }
    }
  }
}
</style>
        