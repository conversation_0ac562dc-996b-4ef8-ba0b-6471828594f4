<template>
  <div class="tetri_index">
    <div class="leftState">
      <div class="points">
        <span>当前得分</span>
        <span>{{points}}</span>
      </div>
      <div class="max">
        <span>最高得分</span>
        <span>{{max}}</span>
      </div>

      <div class="level">
        <span>等级</span>
        <span>{{ speedRun }}</span>
      </div>
    </div>
    <div class="rect" :class="drop?'drop':''">
      <div class="screen">
        <div class="panel">
          <Matrix :propMatrix="matrix" :cur="cur" :reset="reset" />
          <Logo :cur="!!cur" :reset="reset" />
<!--          <div class="state">-->
<!--            <Point :cur="!!cur" :max="max" :point="points" />-->
<!--&lt;!&ndash;            起始行&ndash;&gt;-->
<!--            <p>{{pContent}}</p>-->
<!--            <Number :number='cur ? clearLines : startLines' />-->
<!--&lt;!&ndash;            级别&ndash;&gt;-->
<!--            <p>{{level}}</p>-->
<!--            <Number :number='cur?speedRun:speedStart' :length="1" />-->
<!--            <p>{{nextText}}</p>-->
<!--            <Next :data="next" />-->
<!--            <div class="bottom">-->
<!--              <Music :data="music" />-->
<!--              <Pause :data="pause" />-->
<!--&lt;!&ndash;              <Number :propTime="true" />&ndash;&gt;-->
<!--            </div>-->
<!--          </div>-->
        </div>
      </div>
    </div>

    <div class="rightState">
      <div class="nextMatrix">
        <span>下一个</span>
        <Next :data="next" />
      </div>

    </div>

    <div class="message">
      <h2>操作说明:</h2>
      <p>
        <span>1.</span>
        <span>左、右健平移方块。</span>
      </p>
      <p>
        <span>2.</span>
        <span>上键切换方块形态。</span>
      </p>
      <p>
        <span>3.</span>
        <span>下键加速下落。</span>
      </p>
      <p>
        <span>4.</span>
        <span>确认键暂停/继续。</span>
      </p>
      <p>
        <span>5.</span>
        <span>返回键退出游戏。</span>
      </p>
    </div>

<!--    <Keyboard :filling='filling' />-->
  </div>
</template>

<style lang="less">
@import './index.less';
@import './loader.less';
</style>

<script src="./index.js"></script>

