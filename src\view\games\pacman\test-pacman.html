<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pacman Game Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #000;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .game-grid {
            display: grid;
            grid-template-columns: repeat(20, 20px);
            grid-gap: 1px;
            background: #000;
            border: 2px solid #0000ff;
            margin: 20px 0;
        }
        .cell {
            width: 20px;
            height: 20px;
            background: #000;
        }
        .pacman {
            background: #ffff00;
            border-radius: 50%;
        }
        .dot {
            background: #ffb897;
            border-radius: 50%;
        }
        .wall {
            background: #0000ff;
        }
        .power-pellet {
            background: #ffb897;
            border-radius: 50%;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .controls {
            margin: 20px 0;
        }
        .score {
            font-size: 24px;
            margin: 10px 0;
        }
        .instructions {
            margin: 20px 0;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Pacman Game Test</h1>
        <div class="score">Score: <span id="score">0</span></div>
        <div class="score">Status: <span id="status">Ready</span></div>
        
        <div id="gameGrid" class="game-grid"></div>
        
        <div class="controls">
            <h3>Controls:</h3>
            <p>Use arrow keys to move Pacman</p>
            <p>Eat all dots to win!</p>
            <p>Avoid walls!</p>
        </div>
        
        <div class="instructions">
            <h3>Game Elements:</h3>
            <p>🟡 Yellow circle = Pacman</p>
            <p>🟤 Small brown circles = Dots (10 points each)</p>
            <p>🟤 Large brown circles = Power pellets (50 points each)</p>
            <p>🔵 Blue squares = Walls</p>
        </div>
        
        <button onclick="startGame()">Start Game</button>
        <button onclick="resetGame()">Reset Game</button>
    </div>

    <script>
        // Simple Pacman game logic for testing
        let gameState = {
            pacmanPosition: 21,
            direction: 'RIGHT',
            score: 0,
            gameOver: false,
            dots: [],
            walls: [],
            powerPellets: [42, 59, 342, 359],
            speed: 300,
            gameInterval: null
        };

        function initializeGame() {
            // Create walls (border + some internal walls)
            gameState.walls = [];
            
            // Top and bottom borders
            for (let i = 1; i <= 20; i++) {
                gameState.walls.push(i);
                gameState.walls.push(i + 380);
            }
            
            // Left and right borders
            for (let i = 1; i <= 20; i++) {
                gameState.walls.push(i * 20);
                gameState.walls.push((i - 1) * 20 + 1);
            }
            
            // Some internal walls
            const innerWalls = [
                43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58,
                63, 83, 103, 123, 143, 163, 183, 203, 223, 243, 263, 283, 303, 323, 343,
                125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138,
                145, 158, 165, 178, 185, 198,
                225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238,
                245, 258, 265, 278, 285, 298,
            ];
            gameState.walls.push(...innerWalls);
            
            // Create dots in all empty spaces
            gameState.dots = [];
            for (let i = 1; i <= 400; i++) {
                if (!gameState.walls.includes(i) && 
                    !gameState.powerPellets.includes(i) && 
                    i !== gameState.pacmanPosition) {
                    gameState.dots.push(i);
                }
            }
            
            renderGame();
        }

        function renderGame() {
            const grid = document.getElementById('gameGrid');
            grid.innerHTML = '';
            
            for (let i = 1; i <= 400; i++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                
                if (gameState.pacmanPosition === i) {
                    cell.classList.add('pacman');
                } else if (gameState.walls.includes(i)) {
                    cell.classList.add('wall');
                } else if (gameState.dots.includes(i)) {
                    cell.classList.add('dot');
                } else if (gameState.powerPellets.includes(i)) {
                    cell.classList.add('power-pellet');
                }
                
                grid.appendChild(cell);
            }
            
            document.getElementById('score').textContent = gameState.score;
        }

        function getNextPosition(position, direction) {
            switch (direction) {
                case 'LEFT':
                    return position % 20 === 1 ? position + 19 : position - 1;
                case 'UP':
                    return position <= 20 ? position + 380 : position - 20;
                case 'RIGHT':
                    return position % 20 === 0 ? position - 19 : position + 1;
                case 'DOWN':
                    return position > 380 ? position - 380 : position + 20;
                default:
                    return position;
            }
        }

        function movePacman() {
            const nextPosition = getNextPosition(gameState.pacmanPosition, gameState.direction);
            
            // Check wall collision
            if (gameState.walls.includes(nextPosition)) {
                gameState.gameOver = true;
                document.getElementById('status').textContent = 'Game Over - Hit Wall!';
                clearInterval(gameState.gameInterval);
                return;
            }
            
            // Move pacman
            gameState.pacmanPosition = nextPosition;
            
            // Check dot collision
            if (gameState.dots.includes(nextPosition)) {
                const index = gameState.dots.indexOf(nextPosition);
                gameState.dots.splice(index, 1);
                gameState.score += 10;
            }
            
            // Check power pellet collision
            if (gameState.powerPellets.includes(nextPosition)) {
                const index = gameState.powerPellets.indexOf(nextPosition);
                gameState.powerPellets.splice(index, 1);
                gameState.score += 50;
            }
            
            // Check win condition
            if (gameState.dots.length === 0 && gameState.powerPellets.length === 0) {
                gameState.gameOver = true;
                document.getElementById('status').textContent = 'You Win!';
                clearInterval(gameState.gameInterval);
                return;
            }
            
            renderGame();
        }

        function startGame() {
            if (gameState.gameInterval) {
                clearInterval(gameState.gameInterval);
            }
            
            gameState.gameOver = false;
            document.getElementById('status').textContent = 'Playing...';
            
            gameState.gameInterval = setInterval(movePacman, gameState.speed);
        }

        function resetGame() {
            if (gameState.gameInterval) {
                clearInterval(gameState.gameInterval);
            }
            
            gameState.pacmanPosition = 21;
            gameState.direction = 'RIGHT';
            gameState.score = 0;
            gameState.gameOver = false;
            gameState.powerPellets = [42, 59, 342, 359];
            
            document.getElementById('status').textContent = 'Ready';
            
            initializeGame();
        }

        // Keyboard controls
        document.addEventListener('keydown', (event) => {
            if (gameState.gameOver) return;
            
            switch (event.keyCode) {
                case 37: // Left
                    gameState.direction = 'LEFT';
                    break;
                case 38: // Up
                    gameState.direction = 'UP';
                    break;
                case 39: // Right
                    gameState.direction = 'RIGHT';
                    break;
                case 40: // Down
                    gameState.direction = 'DOWN';
                    break;
            }
        });

        // Initialize the game when page loads
        initializeGame();
    </script>
</body>
</html>
