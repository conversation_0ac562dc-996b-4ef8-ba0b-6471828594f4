<template>
  <div class="expert">
    <div class="container">
      <!-- <div class="left">
        <div class="leftList"></div>
      </div> -->
      <div class="right">
        <div
          class="messageCenterList scrollParent"
          v-if="this.rightList.length > 0"
        >
          <div class="rightList" :style="{ top: '0rem' }">
            <div
              class="messageItem"
              :ref="item.ref"
              v-for="(item, index) in rightList"
              :key="index"
            >
              <div class="item_user">
                <div class="content">
                  <div class="pic" v-lazy-container="{ selector: 'img' }">
                    <img
                      :data-src="apiUrl + item.img"
                      alt=""
                      :data-error="errorImg"
                    />
                    <div class="status" v-if="item.is_appointment">
                      <span>已预约</span>
                    </div>
                  </div>

                  <div class="info">
                    <div class="name">{{ item.name }}</div>
                    <div class="title">{{ item.title }}</div>
                    <div class="describe">{{ item.describe }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="no_info" v-else>暂无数据</div>
      </div>
      <div
        class="tab_list scrollParent"
        :key="unfinishList.length"
        v-if="unfinishList.length > 0"
      >
        <div class="list" :style="{ top: '0rem' }">
          <div
            class="unfinishList"
            :ref="item.ref"
            v-for="(item, index) in unfinishList"
            :key="index"
          >
            <div class="listContent">
              <div
                class="status"
                :style="{ color: statusList[item.status].color }"
              >
                {{ statusList[item.status].text }}
              </div>

              <div class="apply">
                <div class="title">申请时间</div>
                <div class="time">{{ item.apply_time }}</div>
              </div>
              <div class="appoint">
                <div class="title">预约时间</div>
                <div class="time">
                  {{ item.month_day }}
                </div>
              </div>
              <div class="doc">
                <div class="title">坐诊医生：</div>
                <div class="name">{{ item.zhuanjia_name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="no_content" v-else>暂无记录</div>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      :show-close="false"
      :close-on-click-modal="false"
      @opened="popupOpend"
      @close="popupClose"
      title="订单详情"
    >
      <!--发起预约-->
      <div class="message_box sendCall" v-if="popupModule == 1">
        <div class="message_content">
          <div class="status item">
            当前状态：{{ detailList.status == 2 ? '预约成功' : '预约取消' }}
          </div>
          <div class="apply item">申请时间：{{ detailList.apply_time }}</div>
          <div class="time item">预约时间：{{ detailList.month_day }}</div>
          <div class="doctor item">
            坐诊医生：{{ detailList.zhuanjia_name }}
          </div>
          <div class="level item">医生职称：{{ detailList.title }}</div>
          <div class="unit item">
            服务单位：{{ detailList.service_org_title }}
          </div>
          <div class="addr item">服务地址：{{ detailList.address }}</div>
        </div>
      </div>

      <!--取消预约-->
      <div class="message_box cancel" v-if="popupModule == 2">
        <div>是否要取消预约?</div>
      </div>

      <div
        class="message_box"
        style="text-align: center; font-size: 0.37rem; justify-content: center"
        v-html="popupMessage"
        v-if="popupModule == 3"
      ></div>

      <div class="popupBtnList">
        <div :ref="item.ref" v-for="(item, index) in popupBtnList" :key="index">
          {{ item.text }}
        </div>
      </div>
    </el-dialog>
    <div class="address">
      <div class="name">{{ supplierInfo && supplierInfo.title }}</div>
    </div>
    <div class="done"><div class="item">服务记录</div></div>
    <div class="notice" v-if="supplierInfo && supplierInfo.telephone">
      <img :src="require('../../assets/phone.png')" alt="" />
      客服热线: {{ supplierInfo.telephone }}
    </div>
  </div>
</template>
      
      <script>
import {
  getExpertList,
  getOrderList,
  cancelOrder,
  getOrderDetail,
} from '@/api/index'

export default {
  name: 'expert',
  components: {},
  data() {
    return {
      unfinishList: [],
      leftList: [],
      rightList: [],
      statusList: [],
      detailList: {},
      supplierInfo: null,
      apiUrl: process.env.VUE_APP_API,
      errorImg: require('@/assets/avatar_icon.png'),
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧  -2在最右边
      rightNums: 0,
      unfinishNums: 0,
      firstShow: true,
      dialogVisible: false,
      popupModule: 1,
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [],
      messageList: [],
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '专家坐诊')
  },
  computed: {},
  watch: {},
  mounted() {
    this.statusList = JSON.parse(localStorage.getItem('statusList'))
    this.supplierInfo = JSON.parse(sessionStorage.getItem('supplierInfo'))
    this.getData()
    this.getOrderList()
    if (sessionStorage.getItem('indexFocusDoc')) {
      this.rightNums = Number(sessionStorage.getItem('indexFocusDoc'))
      sessionStorage.removeItem('indexFocusDoc')
    }
    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums < this.rightList.length - 1) {
            if (this.rightList[this.rightNums + 2]) {
              this.rightList[this.rightNums].ref = ''
              this.rightNums += 2
              this.rightList[this.rightNums].ref = 'active'
            } else {
              if (
                this.rightNums <
                  this.rightList.length - (this.rightList.length % 2) &&
                this.rightList.length % 2 != 0
              ) {
                this.rightList[this.rightNums].ref = ''
                this.rightNums = this.rightList.length - 1
                this.rightList[this.rightNums].ref = 'active'
              }
            }
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 在最右侧
        else if (this.nextNums == -2) {
          if (this.unfinishNums < this.unfinishList.length - 1) {
            this.unfinishList[this.unfinishNums].ref = ''
            this.unfinishNums++
            this.unfinishList[this.unfinishNums].ref = 'active'
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 左侧
        else {
          if (this.leftNums < this.leftList.length - 1) {
            this.firstShow = false
            this.rightNums = 0
            this.rightList = []
            this.input = ''
            this.$store.dispatch('app/setViewAreaOffsetTop', 0)
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            this.rightList.push(
              ...this.messageList[this.leftNums].children.map((item) => {
                if (!item.img.includes('http')) {
                  item.img = `${
                    process.env.VUE_APP_API + item.doctor_menzhen_avatar
                  }`
                }

                return item
              })
            )
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$refs.active[0].classList.add('select')
            })
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          if (this.rightNums > 0 && this.rightNums - 2 >= 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums -= 2
            this.rightList[this.rightNums].ref = 'active'
          }
        }
        // 在最右侧
        else if (this.nextNums == -2) {
          if (this.unfinishNums > 0 && this.unfinishNums - 1 >= 0) {
            this.unfinishList[this.unfinishNums].ref = ''
            this.unfinishNums--
            this.unfinishList[this.unfinishNums].ref = 'active'
          }

          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 左侧
        else {
          if (this.leftNums > 0) {
            this.rightNums = 0
            this.firstShow = false
            this.$store.dispatch('app/setViewAreaOffsetTop', 0)
            this.rightList = []
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.rightList.push(
              ...this.messageList[this.leftNums].children.map((item) => {
                if (!item.img.includes('http')) {
                  item.img = `${
                    process.env.VUE_APP_API + item.doctor_menzhen_avatar
                  }`
                }
                return item
              })
            )
            this.leftList[this.leftNums].ref = 'active'
            this.$nextTick(() => {
              this.$refs.active[0].classList.add('select')
            })
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 左侧
        if (this.nextNums > -1) {
          if (this.rightNums % 2 != 0) {
            this.rightList[this.rightNums].ref = ''
            this.rightNums--
            this.rightList[this.rightNums].ref = 'active'
          }
        }
        // 右侧
        else if (this.nextNums == -2) {
          this.unfinishList[this.unfinishNums].ref = ''
          this.rightList[this.rightNums].ref = 'active'
          this.nextNums = this.rightNums
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          this.rightList[this.rightNums].ref = ''
          if (this.rightNums !== this.rightList.length - 1 && this.rightNums % 2 !== 1) {
            this.rightNums++
            this.rightList[this.rightNums].ref = 'active'
          } else if (this.unfinishList.length > 0) {
            this.unfinishList[this.unfinishNums].ref = 'active'
            this.nextNums = -2
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        if (this.nextNums > -1 && this.rightList.length > 0) {
          if (this.rightList[this.rightNums]) {
            sessionStorage.setItem('indexFocusDoc', this.rightNums)
            this.$nextTick(() => {
              this.$router.push({
                path: '/orderTime',
                query: {
                  list: JSON.stringify(this.rightList[this.rightNums]),
                },
              })
            })
          }
        } else if (this.nextNums == -2 && this.unfinishList.length > 0) {
          if (this.dialogVisible) {
            // 弹窗
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.unfinishList[this.unfinishNums].ref = 'active'
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].fuc(
              this.unfinishList[this.unfinishNums]
            )
            return
          }
          if (this.unfinishList[this.unfinishNums]) {
            this.getOrderDetail()
            if (this.unfinishList[this.unfinishNums].status == 2) {
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                },
                {
                  text: '取消订单',
                  ref: '',
                  fuc: this.cancelOrder,
                },
              ]
            } else {
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: '',
                  fuc: () => {
                    this.dialogVisible = false
                  },
                },
              ]
            }
            this.dialogVisible = true
            this.popupModule = 1
            this.unfinishList[this.unfinishNums].ref = ''
            this.popupBtnList[this.popupBtnNums].ref = 'active'
          }
        }
      },
      esc: () => {
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ''
          this.unfinishList[this.unfinishNums].ref = 'active'
          this.dialogVisible = false
          return
        }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })
  },
  methods: {
    popupClose() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        this.popupBtnNums = 0
        this.popupModule = 1
      })
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#b36371'
      })
    },
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      this.$store.dispatch('app/setLoadingState', true)
      getExpertList({
        fk_zhuanjiazuozhen_service_org_id: this.supplierInfo.fk_supplier_id,
        user_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].id,
        home_id: this.$store.getters.getUserInfo.home_id,
      })
        .then((res) => {
          this.$store.dispatch('app/setLoadingState', false)
          if (res.code == 200) {
            if (res.data) {
              let rightListClone = JSON.parse(JSON.stringify(res.data))
              rightListClone.map((item) => {
                item.ref = ''
              })
              this.rightList = rightListClone
            }
            if (this.rightList.length > 0) {
              this.nextNums = this.leftNums
              this.rightList[this.rightNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active)
              })
            } else {
              this.nextNums = -1
            }
          }
        })
        .catch(() => {
          this.$store.dispatch('app/setLoadingState', false)
        })
    },
    cancelOrder() {
      this.$store.dispatch('app/setLoadingState', true)
      cancelOrder({
        order_id: this.unfinishList[this.unfinishNums].id,
        fk_supplier_id: this.supplierInfo.fk_supplier_id,
        home_id: this.$store.getters.getUserInfo.home_id,
      }).then(
        (res) => {
          if (res.code == 200) {
            this.popupModule = 3
            this.unfinishList[this.unfinishNums].ref = ''
            this.popupBtnList = [
              {
                text: '确认',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  let unfinishListClone = JSON.parse(JSON.stringify(res.data))
                  unfinishListClone.map((item) => {
                    item.ref = ''
                    if (item.month_day) {
                      item.month_day = this.formatDate(item.month_day)
                    }
                    if (item.apply_time) {
                      item.apply_time = this.formatDate(item.apply_time)
                    }
                  })
                  this.unfinishList = unfinishListClone
                  this.unfinishNums = 0
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active)
                    this.getData()
                  })
                },
              },
            ]
            this.popupMessage = '取消成功'
            this.popupBtnNums = 0
            this.dialogVisible = true
          }
          this.$store.dispatch('app/setLoadingState', false)
        },
        (error) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupModule = 3
          this.unfinishList[this.unfinishNums].ref = ''
          this.popupBtnList = [
            {
              text: '确认',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
              },
            },
          ]
          this.popupMessage = '<br/>取消失败!<br>'
          if (
            error.response &&
            error.response.data &&
            error.response.data.msg
          ) {
            this.popupMessage += error.response.data.msg
          }
          this.popupBtnNums = 0
          this.dialogVisible = true
        }
      )
    },

    // 时间格式化
    formatDate(timestamp) {
      const date = new Date(timestamp * 1000)
      return `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(
        2,
        '0'
      )}月${String(date.getDate()).padStart(2, '0')}日`
    },
    // 右侧数据
    getOrderList() {
      getOrderList({
        user_id:
          this.$store.getters.getUserInfo.oldsters[
            this.$store.state.app.selectUserIndex
          ].id,
        fk_supplier_id: this.supplierInfo.fk_supplier_id,
      }).then((res) => {
        if (res.code == 200) {
          let unfinishListClone = JSON.parse(JSON.stringify(res.data))
          unfinishListClone.map((item) => {
            item.ref = ''
            if (item.month_day) {
              item.month_day = this.formatDate(item.month_day)
            }
            if (item.apply_time) {
              item.apply_time = this.formatDate(item.apply_time)
            }
          })
          this.unfinishList = unfinishListClone
        }
      })
    },
    // 订单详情
    getOrderDetail() {
      getOrderDetail({
        order_id: this.unfinishList[this.unfinishNums].id,
      }).then((res) => {
        if (res.code == 200) {
          this.detailList = res.data
          if (this.detailList.month_day) {
            this.detailList.month_day = this.formatDate(
              this.detailList.month_day
            )
          }
          if (this.detailList.apply_time) {
            this.detailList.apply_time = this.formatDate(
              this.detailList.apply_time
            )
          }
        }
      })
    },
  },
  destroyed() {},
  beforeDestory() {},
}
</script>
      <style lang='less' scoped>
.expert {
  .address {
    display: flex;
    position: absolute;
    top: 1.6rem;
    left: 1.75rem;
    .name {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .name::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .done {
    position: absolute;
    top: 1.6rem;
    right: 3.65rem;
    .item {
      font-size: 0.36rem;
      font-weight: bold;
      letter-spacing: 0.03rem;
      color: #b5c0ff;
    }
    .item::before {
      content: '';
      width: 0.1rem;
      height: 0.1rem;
      position: absolute;
      background: #b5c0ff;
      border-radius: 50%;
      top: 50%;
      left: -0.25rem;
    }
  }
  .notice {
    position: absolute;
    font-size: 0.28rem;
    right: 1.2rem;
    bottom: 0.6rem;
    color: #b2baef;
    display: flex;
    flex-direction: row;
    align-items: center;
    img {
      position: relative;
      top: 0.02rem;
      left: -0.05rem;
      width: 0.32rem;
      height: 0.32rem;
    }
  }
  .container {
    height: 7rem;
    overflow: hidden;
    display: flex;
    // grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
    // gap: 0.16rem;
    font-size: 0.2rem;
    color: #e7e7ef;
    .noData {
      div {
        font-size: 0.5rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -200%);
        letter-spacing: 0.05rem;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
      }
    }
    .aside {
      width: 2rem;
      height: 0.7rem;
      text-align: center;
      line-height: 0.7rem;
      margin-bottom: 0.2rem;
      background: #343d74;
      border-radius: 0.2rem;
      position: relative;
      font-weight: bold;
      transition: all 0.3s;
      letter-spacing: 0.05rem;
      text-indent: 0.05rem;
      font-size: 0.34rem;
      color: #e7e7ef;
      text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
    }
    .left {
      width: 2.4rem;
      border-radius: 0.2rem;
      height: 7rem;
      position: relative;
      .leftList {
        width: 94%;
        text-align: center;
        position: absolute;
        transition: all 0.3s;
        .leftItem {
          height: 0.7rem;
          line-height: 0.78rem;
          margin-bottom: 0.2rem;
          background: #343d74;
          border-radius: 0.2rem;
          position: relative;
          font-weight: bold;
          transition: all 0.3s;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          font-size: 0.34rem;
          color: #e7e7ef;
          text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
          .badge {
            width: 0.15rem;
            height: 0.15rem;
            border-radius: 50%;
            background: #ff5f5f;
            position: absolute;
            top: 50%;
            right: 0.2rem;
            transform: translateY(-50%);
          }
        }
        .select {
          background: #89a7ff;
          transition: all 0.3s;
          text-shadow: 0.03rem 0.03rem 0.01rem rgba(102, 129, 218, 0.6);
        }
      }
      .myQR {
        padding: 0rem 0.6rem;
        .qr_img {
          width: 2.05rem;
          height: 2.05rem;
          background: #413f55;
          margin: 0 auto;
          padding: 0.06rem;
          margin-bottom: 0rem;
          border-radius: 0.18rem;
        }
        img {
          border-radius: 0.14rem;
          display: block;
          width: 100%;
          height: 100%;
        }
        div {
          width: 2.2rem;
          font-size: 0.31rem;
          font-weight: bold;
          line-height: 0.45rem;
          text-align: center;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          margin-top: 0.02rem;
          text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
        }
        div:nth-child(3) {
          margin-top: 0.05rem;
          letter-spacing: 0;
          font-weight: 500;
          font-size: 0.33rem;
        }
      }
    }
    .right {
      // width: 12.8rem;
      height: 6rem;
      margin-left: 0rem;
      //padding-left: 0.1rem;
      //margin-left: 0.05rem;

      .messageCenterList {
        width: 12rem;
        height: 100%;
        position: relative;
        display: inline-block;
        overflow: hidden;
        .rightList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          border-radius: 0.3rem;
          position: absolute;
          left: 0.4rem;
          transition: all 0.2s;
          .messageItem {
            width: 5.5rem;
            height: 2.85rem;
            // padding: 0.5rem;
            margin-bottom: 0.28rem;
            background-size: 100% 100% !important;
            background: #262954;
            border-radius: 0.24rem;
            overflow: hidden;
            .item_user {
              display: flex;
              // flex-direction: column;
              justify-content: center;
              align-items: center;
              height: 100%;
              padding: 0 0.35rem;
              .content {
                display: flex;
                height: initial !important;
                .pic {
                  position: relative;
                  overflow: hidden;
                  img {
                    width: 1.7rem;
                    height: 2.2rem;
                    overflow: hidden;
                    border-radius: 0.06rem;
                    background: #fff;
                    border: 0.02rem solid #babdd3;
                    object-fit: cover;
                  }
                  .status {
                    span {
                      position: absolute;
                      top: 0rem;
                      left: 0rem;
                      width: 0.9rem !important;
                      height: 0.35rem;
                      background: #fd8b19;
                      border-radius: 0.05rem;
                      text-align: center;
                      line-height: 0.35rem;
                      font-weight: bold;
                      color: #fff;
                      letter-spacing: 0.03rem;
                    }
                  }
                }

                .info {
                  margin-left: 0.2rem;
                  .name {
                    font-weight: bold;
                    color: #fff;
                    font-size: 0.32rem;
                    margin-bottom: 0.1rem;
                  }
                  .title {
                    font-size: 0.26rem;
                    color: #fff;
                  }
                  .describe {
                    width: 2.8rem;
                    color: #b7c0f9;
                    font-size: 0.24rem;
                    margin-top: 0.1rem;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 4;
                    -webkit-box-orient: vertical;
                    letter-spacing: 0.01rem;
                    word-wrap: break-word;
                    white-space: normal;
                  }
                }
              }
            }
          }
          .messageItem:nth-child(2n + 1) {
            margin-right: 0.28rem;
          }
        }
      }
      .no_info {
        position: absolute;
        left: 4rem;
        line-height: 7rem;
        width: 4rem;
        height: 7rem;
        text-align: center;
        font-size: 0.4rem;
      }
    }
    .tab_list {
      width: 4.15rem;
      height: 6rem;
      position: relative;
      left: 0.55rem;
      overflow: hidden;
      .list {
        margin-right: 0.24rem;
        position: absolute;
        transition: all 0.3s;
        .unfinishList {
          width: 3.85rem;
          height: 2.85rem;
          margin-bottom: 0.28rem;
          background: #262954;
          border-radius: 0.24rem;
          font-size: 0.26rem;
          font-weight: bold;
          .listContent {
            padding: 0.18rem 0.25rem;

            .status {
              color: #3ce1ae;
              margin-bottom: 0.06rem;
              letter-spacing: 0.03rem;
            }
            .apply,
            .appoint,
            .doc {
              margin-bottom: 0.06rem;
              .title {
                color: #b6c0fd;
                margin-right: 0.06rem;
              }
            }
            .depart,
            .doc {
              display: flex;
            }
          }
        }
      }
    }
    .no_content {
      position: absolute;
      right: 1.5rem;
      line-height: 7rem;
      width: 4rem;
      height: 7rem;
      text-align: center;
      font-size: 0.4rem;
    }
  }
}
</style>
  <style lang="less">
.expert {
  .el-dialog {
    width: 9.4rem;
    height: 8rem;
    margin-top: 0 !important;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0.26rem;
    background: repeating-linear-gradient(to right, #c98693, #a95361);
    .el-dialog__header {
      height: 10%;
      display: flex;
      align-items: center;
      padding-top: 0.4rem;
      .el-dialog__title {
        display: block;
        line-height: normal !important;
        height: 100%;
        font-size: 0.4rem;
        color: #fff;
        background-image: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.65)
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: bold;
        width: 100%;
        text-align: center;
      }
    }
    .el-dialog__body {
      width: 92%;
      height: 80%;
      position: absolute;
      bottom: 4%;
      left: 4%;
      border-radius: 0.2rem;
      background: #fff;
      padding: 0 !important;
      .message_box {
        padding: 0.5rem;
        color: #464646;
        font-size: 0.32rem;
        font-weight: bold;
        height: 60%;
        display: flex;
        align-items: center;
        .message_content {
          margin-top: 0.3rem;
          .item {
            font-size: 0.35rem;
            font-weight: bold;
            margin-bottom: 0.1rem !important;
          }
        }
      }

      .cancel {
        flex-direction: column;
        div {
          text-align: center;
        }
        div:nth-child(2) {
          margin-top: 0.1rem;
          color: red;
          line-height: 0.48rem;
        }
        //align-items: normal !important;
      }
      .popupBtnList {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 0.25rem;
        div {
          width: 3.84rem;
          height: 1rem;
          line-height: 1.08rem;
          text-align: center;
          border-radius: 0.3rem;
          color: #fff;
          font-size: 0.45rem;
          font-weight: bold;
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          background: repeating-linear-gradient(to right, #c98693, #a95361);
        }
      }
    }
  }
}
</style>