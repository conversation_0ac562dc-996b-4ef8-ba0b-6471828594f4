<template>
  <div class="zegoCallIndex">
    <div class="left_zego">
      <div class="leftList">
        <div class="leftItem" :ref="item.ref" v-for="(item, index) in leftList" :key="index">
          <div>
            {{ item.title }}
          </div>
          <div class="badge" v-if="item.badge"></div>
        </div>
      </div>
      <div class="myQR">
        <div class="qr_img">
          <img v-if="qrInfo.url" :src="qrInfo.url" alt="" />
        </div>
        <div class="qrName">
          {{ qrInfo.name }}
        </div>
        <div class="qrId">
          {{ qrInfo.id }}
        </div>
      </div>
    </div>

    <div class="right_zego">
      <!--我的好友 -->
      <div class="item_list scrollParent" v-if="leftNums == 1">
        <div class="friendsList" :style="{ top: '0rem' }" v-if="friendsList.length > 0">
          <div
            class="friendsItem"
            :ref="item.ref"
            v-for="(item, index) in friendsList"
            :key="index"
            :style="{ background: 'url(' + item.bg + ')' }"
          >
            <img
              :class="'typeIcon' + (item.device_type == 2 ? ' typeIcon_phone' : '')"
              :src="
                item.device_type == 2
                  ? require('@/assets/phone_type.png')
                  : require('@/assets/tv.png')
              "
              alt=""
            />
            <div class="item_user" :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }">
              <div>
                <span>{{ item.title ? item.title : item.phone }}</span>
                <!--                <span  v-if="item.device_type == 2" style="letter-spacing: 0;font-size: 0.38rem" >{{item.title ? item.title : item.title}}</span>-->
                <!--                <span  v-else style="letter-spacing: 0;font-size: 0.38rem" >{{item.title}}</span>-->

                <!--                {{item.title ? item.title :item.phone}}-->
              </div>
              <div>ID:{{ item.fk_friend_id }}</div>
            </div>
            <div  v-lazy-container="{ selector: 'img' }">
              <img class="avatar" v-if="item.avatar" :data-src="item.avatar" :data-error="lazyError" alt="" />
            </div>
            <div class="iconList">
              <div class="topIcon" v-if="item.top > 0">
                <img :src="require('@/assets/top_icon.png')" alt="" />
              </div>
              <div class="callenter" v-if="item.hands_free > 0">
                <img :src="require('@/assets/callenter.png')" alt="" />
              </div>
            </div>
          </div>
        </div>

        <div class="noData" v-else>
          <div v-if="!this.$store.getters.loadingState">暂无好友</div>
        </div>
      </div>

      <!--添加好友-->
      <div class="addFriend" v-if="leftNums == 2">
        <div class="addItem" v-for="(item, index) in addFriendBtn" :key="index">
          <div
            class="input_content"
            v-if="index == 0"
            :ref="item.ref"
            :style="{ opacity: item.show ? 1 : 0.6 }"
          >
            <!--            <el-input v-model="input" placeholder="请输入手机号或ID号"></el-input>-->
            <div class="input" v-if="input.length > 0">
              {{ input }}
            </div>
            <div class="input" v-else>请输入好友ID号</div>
          </div>
          <div
            class="btn_content"
            :ref="item.ref"
            v-else
            :style="{
              background: item.ref == 'active' ? '#89A7FF' : '#333D72',
              opacity: item.show ? 1 : 0.6,
            }"
          >
            {{ item.title }}
          </div>
        </div>

        <div class="searchFriend">
          <template v-if="searchFriend.length != 0">
            <div
              class="friendsItem"
              v-for="(item, index) in searchFriend"
              :key="index"
              :style="{ background: 'url(' + item.bg + ')' }"
            >
              <img
                :class="'typeIcon' + (item.device_type == 2 ? ' typeIcon_phone' : '')"
                :src="
                  item.device_type ? require('@/assets/phone_type.png') : require('@/assets/tv.png')
                "
                alt=""
              />
              <div
                class="item_user"
                :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }"
              >
                <div>
                  <span v-if="item.title">{{ item.title }}</span>
                  <span v-else style="letter-spacing: 0; font-size: 0.38rem">{{ item.phone }}</span>
                  <!--                    {{item.title}}-->
                </div>
                <div>ID:{{ item.id }}</div>
              </div>
              <div v-lazy-container="{ selector: 'img' }">
                <img class="avatar" v-if="item.avatar" :data-src="item.avatar" :data-error="lazyError" alt="" />
              </div>
            </div>
          </template>

          <template v-if="searchLoading">
            <div class="searchLoading">搜索中...</div>
          </template>

          <template v-if="noUser">
            <div class="searchLoading">暂无该用户</div>
          </template>

          <template v-if="dontAddMySelf">
            <div class="searchLoading">不能搜索自己</div>
          </template>
        </div>

        <el-drawer
          title="我是标题"
          :visible.sync="drawer"
          direction="btt"
          :with-header="false"
          @opened="drawerOpened"
          @close="drawerClosed"
        >
          <div class="numList">
            <div class="num_item" v-for="(item, index) in drawerBtn" :ref="item.ref" :key="index">
              {{
                item.num > -1 ? item.num : item.num == -1 ? '删除' : item.num == -2 ? '搜索' : ''
              }}
            </div>
          </div>
        </el-drawer>
      </div>

      <!--通话记录-->
      <div class="applyFriendList scrollParent" :key="2" v-if="leftNums == 0">
        <div class="applyList" :style="{ top: '0rem' }" v-if="applyList.length > 0">
          <div
            class="applyItem"
            :ref="item.ref"
            v-for="(item, index) in applyList"
            :key="index"
            :style="{ background: 'url(' + item.bg + ')' }"
          >
            <div class="item_user" :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }">
              <div class="userContent" v-html="item.content"></div>
            </div>
            <div class="time">
              {{ item.created_at }}
            </div>
            <div v-lazy-container="{ selector: 'img' }">
              <img class="avatar" v-if="item.avatar" :data-src="item.avatar" :data-error="lazyError" alt="" />
            </div>
            <div
              class="badge"
              v-if="!item.is_read"
              :style="{
                borderColor: item.badgeColor,
                top: item.avatar ? '0.4rem' : '0.45rem',
                left: item.avatar ? '1.1rem' : '1rem',
              }"
            ></div>
          </div>
        </div>

        <div class="noData" v-else>
          <div v-if="!this.$store.getters.loadingState">暂无近期记录</div>
        </div>
      </div>

      <!--消息中心-->
      <div class="messageCenterList scrollParent" :key="3" v-if="leftNums == 3">
        <div class="messageList" :style="{ top: '0rem' }">
          <div
            class="messageItem"
            :ref="item.ref"
            v-for="(item, index) in messageList"
            :key="index"
            :style="{ background: 'url(' + item.bg + ')' }"
          >
            <img
              :class="'typeIcon' + (item.type ? ' typeIcon_phone' : '')"
              :src="item.type ? require('@/assets/phone_type.png') : require('@/assets/tv.png')"
              alt=""
            />
            <div class="item_user" :style="{ textShadow: '0.05rem 0.05rem 0.04rem ' + item.color }">
              <div>
                {{ item.name }}
              </div>
              <div>ID:{{ item.id }}</div>
            </div>
          </div>
        </div>
      </div>

      <el-dialog
        :visible.sync="dialogVisible"
        :show-close="false"
        :close-on-click-modal="false"
        @opened="popupOpend"
        @close="popupClose"
        :title="popupModule == 1 ? '操作' : '提示'"
        custom-class="operatePopup"
      >
        <!--呼叫弹窗-->
        <div class="callMyFriend" v-if="popupModule == 1">
            <div
                class="myFriendsBtn"
                :ref="item.ref"
                v-for="(item, index) in popupBtnList"
                :key="index"

            >
              <div class="box-myFriendsBtn"  v-html="item.text"></div>

            </div>
          </div>
        <!--消息弹窗-->
        <div class="popupMessage" v-if="popupModule == 2">
          <div class="popupMessage" v-html="popupMessage"></div>
          <div
            class="popupMessageBtn"
            :ref="item.ref"
            v-for="(item, index) in popupBtnList"
            :key="index"
          >
            {{ item.text }}
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  GetUserQR,
  GetFriendIsCall,
  GetZegoToken,
  GetCallLogList,
  SetLogRead,
  GetMyfriendsList,
  SetFriendTop,
  CancelFriendTop,
  SetFreeHands,
  CancelFreeHands,
  DestroyFriend,
  SearchUser,
  AddFriend,
  youAreMyFriend,
  GetCallTime
} from '@/api/index'
import websocketData from '@/utils/websocketData'
import Axios from 'axios'
import store from "@/store";

export default {
  name:'zegoCall',
  components: {},
  inject: ['reload'],
  data() {
    return {
      lazyError: require('@/assets/transparent.png'),
      zegoToken: null,
      timer: null,
      leftList: [],
      leftNums: 0,
      nextNums: -1, // -1  说明焦点在左侧   > -1  在右侧

      friendsList: [],
      rightNums: 0,

      qrInfo: {
        url: '',
        name: '李某某',
        id: 'ID:12345678',
      },
      firstShow: true,

      dialogVisible: false,
      popupModule: 1, // 1、好友弹窗  2、消息提示
      popupMessage: '',
      popupBtnNums: 0,
      popupBtnList: [
        {
          text: '呼叫好友',
          ref: '',
          fuc: this.callUser,
        },
        {
          text: '删除好友',
          ref: '',
          fuc: this.delectMyfriendPopup,
        },
      ],
      bgList: [
        {
          // color: 'rgba(89,167,216,1)',
          color: 'rgba(141,133,218,1)',
          bg: require('@/assets/4_bg_pic.png'),
        },
        {
          // color: 'rgba(210,126,126,1)',
          color: 'rgba(141,133,218,1)',
          bg: require('@/assets/4_bg_pic.png'),
        },
        {
          // color: 'rgba(90,184,148,1)',
          color: 'rgba(141,133,218,1)',
          bg: require('@/assets/4_bg_pic.png'),
        },
        {
          // color: 'rgba(141,133,218,1)',
          color: 'rgba(141,133,218,1)',
          bg: require('@/assets/4_bg_pic.png'),
        },
        {
          // color: 'rgba(108,151,206,1)',
          color: 'rgba(141,133,218,1)',
          bg: require('@/assets/4_bg_pic.png'),
        },
        {
          // color: 'rgba(198,164,100,1)',
          color: 'rgba(141,133,218,1)',
          bg: require('@/assets/4_bg_pic.png'),
        },
      ],
      applyBgList: [
        {
          // color: 'rgba(89,167,216,1)',
          // badgeColor: 'rgba(131,205,242,1)',
          // bg: require('@/assets/1_apply_bg_pic.png'),
          color: 'rgba(141,133,218,1)',
          badgeColor: 'rgba(175,169,244,1)',
          bg: require('@/assets/3_apply_bg_pic.png'),
        },
        {
          // color: 'rgba(198,164,100,1)',
          // badgeColor: 'rgba(223,196,133,1)',
          // bg: require('@/assets/2_apply_bg_pic.png'),
          color: 'rgba(141,133,218,1)',
          badgeColor: 'rgba(175,169,244,1)',
          bg: require('@/assets/3_apply_bg_pic.png'),
        },
        {
          color: 'rgba(141,133,218,1)',
          badgeColor: 'rgba(175,169,244,1)',
          bg: require('@/assets/3_apply_bg_pic.png'),
        },
      ],
      input: '',
      addFriendBtn: [],
      drawer: false,
      drawerBtn: [
        {
          num: 1,
          ref: '',
        },
        {
          num: 2,
          ref: '',
        },
        {
          num: 3,
          ref: '',
        },
        {
          num: 0,
          ref: '',
        },
        {
          num: 4,
          ref: '',
        },
        {
          num: 5,
          ref: '',
        },
        {
          num: 6,
          ref: '',
        },
        {
          num: -1,
          ref: '',
        },
        {
          num: 7,
          ref: '',
        },
        {
          num: 8,
          ref: '',
        },
        {
          num: 9,
          ref: '',
        },
        {
          num: -2,
          ref: '',
        },
      ],
      searchFriend: [],
      searchLoading: false,
      noUser: false,
      dontAddMySelf: false,

      applyList: [],

      messageList: [],
    }
  },
  created() {
    //设置页面左上角标题
    this.$store.dispatch('index/setMainTitle', '视频通话')
    this.getUserRr()
    if (window.location.href.indexOf('type') > -1) {
      this.leftNums = Number(window.location.href.split('type=')[1].split('&')[0])
    }
  },
  computed: {},
  watch: {
    input() {
      if (this.input.length > 0) {
        this.addFriendBtn[1].show = true
      } else {
        this.addFriendBtn[1].show = false
      }
      this.searchFriend = []
      this.noUser = false
      this.dontAddMySelf = false
    },
    searchFriend: {
      handler(newVal, oldVal) {
        if (this.searchFriend.length > 0) {
          this.addFriendBtn[2].show = true
          this.addFriendBtn[3].show = true
          this.searchLoading = false
        } else {
          if (this.addFriendBtn.length > 0) {
            this.addFriendBtn[2].show = false
            this.addFriendBtn[3].show = false
          }
        }
      },
      deep: true, // 开启深度监听
    },
    "$store.state.app.webscoketMessage":{
      handler(res, oldVal) {
        if (res.code >= 200 && res.code < 300) {
          switch (res.code) {
              // 好友不在线
            case 201:
              this.popupMessage = res.msg + '<br/>' + '请稍后再拨'
              this.popupModule = 2
              // this.$store.dispatch('index/setFocusDom', null);
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: 'active',
                  fuc: () => {
                    this.dialogVisible = false
                    this.popupBtnList[this.popupBtnNums].ref = ''
                    if (this.leftNums == 0) {
                      this.applyList[this.rightNums].ref = 'active'
                    } else if (this.leftNums == 1) {
                      this.friendsList[this.rightNums].ref = 'active'
                    } else if (this.leftNums == 2) {
                      this.addFriendBtn[this.rightNums].ref = 'active'
                    }
                    this.$nextTick(() => {
                      this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                    })
                  },
                },
              ]

              if (this.leftNums == 0) {
                this.applyList[this.rightNums].ref = ''
              } else if (this.leftNums == 1) {
                this.friendsList[this.rightNums].ref = ''
              } else if (this.leftNums == 2) {
                this.addFriendBtn[this.rightNums].ref = ''
              }

              setTimeout(() => {
                this.dialogVisible = true
              }, 300)

              break
              // 好友在线
            case 202:
              this.sendZegoCall(res.data.room_id,res.data.user_id,this.zegoToken)
              break
            // 对方挂断
            case 203:
              if (this.leftNums == 0) {
                this.$nextTick(()=>{
                  this.reload();
                })
                // this.rightNums = 0
                // this.applyForList()
              }
              break;
              // 小程序一次性订阅通知失败
            case 204:
              this.popupMessage = `该用户未授权来电通知,<br/>暂时无法通知您的来电信息!`
              this.popupModule = 2
              // this.$store.dispatch('index/setFocusDom', null);
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: 'active',
                  fuc: () => {
                    this.dialogVisible = false
                    this.popupBtnList[this.popupBtnNums].ref = ''
                    if (this.leftNums == 0) {
                      this.applyList[this.rightNums].ref = 'active'
                    } else if (this.leftNums == 1) {
                      this.friendsList[this.rightNums].ref = 'active'
                    } else if (this.leftNums == 2) {
                      this.addFriendBtn[this.rightNums].ref = 'active'
                    }
                    this.$nextTick(() => {
                      this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                    })
                  },
                },
              ]

              if (this.leftNums == 0) {
                this.applyList[this.rightNums].ref = ''
              } else if (this.leftNums == 1) {
                this.friendsList[this.rightNums].ref = ''
              } else if (this.leftNums == 2) {
                this.addFriendBtn[this.rightNums].ref = ''
              }

              setTimeout(() => {
                this.dialogVisible = true
              }, 300)
              break;
            // 扫码添加好友后
            case 205:
              if (this.leftNums == 1) {
                this.$nextTick(()=>{
                  this.reload();
                })
              }
              break;
          }
        }
      },
      deep: true, // 开启深度监听
    }

  },
  mounted() {
    this.qrInfo.name = this.$store.getters.getUserInfo.title + '家'
    this.qrInfo.id = 'ID:' + this.$store.getters.getUserInfo.home_id

    this.getData()

    this.fuc.KeyboardEvents({
      down: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums < this.popupBtnList.length - 1) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums++
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          switch (this.leftNums) {
            case 1: // 我的好友
              if (this.rightNums < this.friendsList.length - 1) {
                if (this.friendsList[this.rightNums + 3]) {
                  this.friendsList[this.rightNums].ref = ''
                  this.rightNums += 3
                  this.friendsList[this.rightNums].ref = 'active'
                } else {
                  if (
                    this.rightNums < this.friendsList.length - (this.friendsList.length % 3) &&
                    this.friendsList.length % 3 != 0
                  ) {
                    this.friendsList[this.rightNums].ref = ''
                    this.rightNums = this.friendsList.length - 1
                    this.friendsList[this.rightNums].ref = 'active'
                  }
                }
              }
              break
            case 2: // 添加好友
              if (this.drawer) {
                if (this.drawerBtn[this.rightNums + 4]) {
                  this.drawerBtn[this.rightNums].ref = ''
                  this.rightNums += 4
                  this.drawerBtn[this.rightNums].ref = 'active'
                }
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active)
                })
                return
              }
              if ([0].includes(this.rightNums)) {
                this.addFriendBtn[this.rightNums].ref = ''
                this.rightNums = 1
                this.addFriendBtn[this.rightNums].ref = 'active'
              }
              break
            case 0: // 通话记录
              if (this.rightNums < this.applyList.length - 1) {
                this.applyList[this.rightNums].ref = ''
                // this.setIsRead(this.applyList[this.rightNums])
                this.rightNums++
                this.applyList[this.rightNums].ref = 'active'
              }
              break
            case 3: // 信息通知
              if (this.rightNums < this.messageList.length - 1) {
                this.messageList[this.rightNums].ref = ''
                this.rightNums++
                this.messageList[this.rightNums].ref = 'active'
              }
              break
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 左侧
        else {
          if (this.leftNums < this.leftList.length - 1) {
            if (this.leftNums == 0) {
              this.leftList[0].badge = false
            }
            this.firstShow = false
            this.rightNums = 0
            this.friendsList = []
            this.applyList = []
            this.searchFriend = []
            this.messageList = []
            this.input = ''
            this.$store.dispatch('app/setViewAreaOffsetTop', 0)
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ''
            this.leftNums++
            this.leftList[this.leftNums].ref = 'active'
            sessionStorage.setItem('zegoListIndex',this.leftNums)
            this.$nextTick(() => {
              this.$refs.active[0].classList.add('select')
              if (this.leftList[this.leftNums].fuc) {
                this.leftList[this.leftNums].fuc()
              }
            })
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      up: () => {
        if (this.dialogVisible) {
          if (this.popupBtnNums > 0) {
            this.popupBtnList[this.popupBtnNums].ref = ''
            this.popupBtnNums--
            this.popupBtnList[this.popupBtnNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          switch (this.leftNums) {
            case 1: // 我的好友
              if (this.friendsList[this.rightNums - 3]) {
                this.friendsList[this.rightNums].ref = ''
                this.rightNums -= 3
                this.friendsList[this.rightNums].ref = 'active'
              }
              break
            case 2: // 添加好友
              if (this.drawer) {
                if (this.drawerBtn[this.rightNums - 4]) {
                  this.drawerBtn[this.rightNums].ref = ''
                  this.rightNums -= 4
                  this.drawerBtn[this.rightNums].ref = 'active'
                }
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active)
                })
                return
              }
              if ([1, 2, 3].includes(this.rightNums)) {
                this.addFriendBtn[this.rightNums].ref = ''
                this.rightNums = 0
                this.addFriendBtn[this.rightNums].ref = 'active'
              }
              break
            case 0: // 通话记录
              if (this.rightNums > 0) {
                this.applyList[this.rightNums].ref = ''
                // this.setIsRead(this.applyList[this.rightNums])
                this.rightNums--
                this.applyList[this.rightNums].ref = 'active'
              }
              break
            case 3: // 好友申请
              if (this.rightNums > 0) {
                this.messageList[this.rightNums].ref = ''
                this.rightNums--
                this.messageList[this.rightNums].ref = 'active'
              }
              break
          }
        }
        // 左侧
        else {
          if (this.leftNums > 0) {
            this.rightNums = 0
            this.firstShow = false
            this.$store.dispatch('app/setViewAreaOffsetTop', 0)
            this.friendsList = []
            this.applyList = []
            this.searchFriend = []
            this.messageList = []
            this.input = ''
            this.$refs.active[0].classList.remove('select')
            this.leftList[this.leftNums].ref = ''
            this.leftNums--
            this.leftList[this.leftNums].ref = 'active'
            sessionStorage.setItem('zegoListIndex',this.leftNums)
            this.$nextTick(() => {
              this.$refs.active[0].classList.add('select')
              if (this.leftList[this.leftNums].fuc) {
                this.leftList[this.leftNums].fuc()
              }
            })
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      left: () => {
        if (this.dialogVisible) {
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          switch (this.leftNums) {
            case 1: // 我的好友
              if (this.rightNums % 3 == 0) {
                this.friendsList[this.rightNums].ref = ''
                this.leftList[this.nextNums].ref = 'active'
                this.nextNums = -1
              } else {
                this.friendsList[this.rightNums].ref = ''
                this.rightNums--
                this.friendsList[this.rightNums].ref = 'active'
              }
              break
            case 2: // 添加好友
              if (this.drawer) {
                if (this.rightNums % 4 != 0) {
                  this.drawerBtn[this.rightNums].ref = ''
                  this.rightNums--
                  this.drawerBtn[this.rightNums].ref = 'active'
                }
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active)
                })
                return
              }
              if ([0, 1].includes(this.rightNums)) {
                this.addFriendBtn[this.rightNums].ref = ''
                this.leftNums = this.nextNums
                this.leftList[this.leftNums].ref = 'active'
                this.nextNums = -1
              } else if ([2, 3].includes(this.rightNums)) {
                this.addFriendBtn[this.rightNums].ref = ''
                this.rightNums--
                this.addFriendBtn[this.rightNums].ref = 'active'
              }
              break
            case 0: // 通话记录
              this.applyList[this.rightNums].ref = ''
              this.leftList[this.nextNums].ref = 'active'
              this.nextNums = -1
              break
            case 3: // 信息通知
              this.messageList[this.rightNums].ref = ''
              this.leftList[this.nextNums].ref = 'active'
              this.nextNums = -1
              break
          }
        }
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      },
      right: () => {
        if (this.dialogVisible) {
          return
        }
        // 在右侧
        if (this.nextNums > -1) {
          switch (this.leftNums) {
            case 1: // 我的好友
              if (this.rightNums < this.friendsList.length - 1) {
                if (this.rightNums % 3 != 2) {
                  this.friendsList[this.rightNums].ref = ''
                  this.rightNums++
                  this.friendsList[this.rightNums].ref = 'active'
                }
              }
              break
            case 2: // 添加好友
              if (this.drawer) {
                if (this.rightNums % 4 != 3) {
                  this.drawerBtn[this.rightNums].ref = ''
                  this.rightNums++
                  this.drawerBtn[this.rightNums].ref = 'active'
                }
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active)
                })
                return
              }
              if ([1, 2].includes(this.rightNums)) {
                this.addFriendBtn[this.rightNums].ref = ''
                this.rightNums++
                this.addFriendBtn[this.rightNums].ref = 'active'
              }

              break
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
        // 在左侧
        else if (this.nextNums == -1) {
          switch (this.leftNums) {
            case 1: // 我的好友
              if (this.friendsList.length > 0) {
                this.leftList[this.leftNums].ref = ''
                this.nextNums = this.leftNums
                this.friendsList[this.rightNums].ref = 'active'
              }
              break
            case 2: // 添加好友
              this.leftList[this.leftNums].ref = ''
              this.nextNums = this.leftNums
              this.addFriendBtn[this.rightNums].ref = 'active'
              this.drawer = true
              break
            case 0: // 通话记录
              if (this.applyList.length > 0) {
                this.leftList[this.leftNums].ref = ''
                this.nextNums = this.leftNums
                this.applyList[this.rightNums].ref = 'active'
              }
              break
            case 3: // 信息通知
              if (this.messageList.length > 0) {
                this.leftList[this.leftNums].ref = ''
                this.nextNums = this.leftNums
                this.messageList[this.rightNums].ref = 'active'
              }
              break
          }
          this.$nextTick(() => {
            this.$store.dispatch('index/setFocusDom', this.$refs.active)
          })
        }
      },
      enter: () => {
        if (this.dialogVisible) {
          // 弹窗
          switch (this.leftNums) {
            case 1: // 我的好友
              if (this.popupBtnList[this.popupBtnNums].fuc) {
                this.popupBtnList[this.popupBtnNums].fuc(this.friendsList[this.rightNums])
              }
              break
            case 2: // 我的好友
              if (this.popupBtnList[this.popupBtnNums].fuc) {
                this.popupBtnList[this.popupBtnNums].fuc()
              }
              break
            case 0: // 好友申请
              if (this.popupBtnList[this.popupBtnNums].fuc) {
                this.popupBtnList[this.popupBtnNums].fuc(this.applyList[this.rightNums])
              }
              break
          }
          return
        }
        // 右侧
        if (this.nextNums > -1) {
          switch (this.leftNums) {
            case 1: // 我的好友
              if (this.friendsList.length > 0) {
                this.popupBtnList = [
                  {
                    text: '呼叫',
                    ref: '',
                    // fuc: this.callUser({data:this.friendsList[this.rightNums]}),
                    fuc: this.selectThisUser,
                  },
                  {
                    text: `<div class="btnText">${this.friendsList[this.rightNums].top > 0 ? '取消置顶' : '置顶'}</div><img src="${require('@/assets/top_icon.png')}" />`,
                    ref: '',
                    fuc: this.setTopMyFriend,
                  },
                  {
                    text: `<div class="btnText">${this.friendsList[this.rightNums].hands_free > 0 ? '取消自动接听' : '自动接听'}</div><img src="${require('@/assets/callenter.png')}" />`,
                    ref: '',
                    fuc: this.setHandsFree,
                  },
                  {
                    text: '删除',
                    ref: '',
                    fuc: this.delectMyfriendPopup,
                  },
                ]
                if (this.friendsList[this.rightNums].customer) {
                  this.popupBtnList = [
                    {
                      text: '呼叫',
                      ref: '',
                      // fuc: this.callUser({data:this.friendsList[this.rightNums]}),
                      fuc: this.selectThisUser,
                    },
                  ]
                }
                this.friendsList[this.rightNums].ref = ''
                this.popupBtnList[this.popupBtnNums].ref = 'active'
                this.dialogVisible = true
              }
              break
            case 2: // 添加好友
              if (this.drawer) {
                if (this.drawerBtn[this.rightNums].num > -1) {
                  if (this.input.length < 11) {
                    this.input += this.drawerBtn[this.rightNums].num
                  }
                } else if (this.drawerBtn[this.rightNums].num == -1) {
                  if (this.input.length > 0) {
                    this.input = this.input.slice(0, -1)
                  }
                } else if (this.drawerBtn[this.rightNums].num == -2) {
                  this.drawer = false
                  if (this.input.length > 0) {
                    this.searchUser()
                  }
                }
                return
              }
              if ([0].includes(this.rightNums)) {
                // 唤起软键盘
                this.drawer = true
              } else if ([1].includes(this.rightNums)) {
                // 搜索
                if (this.input.length > 0) {
                  this.searchUser()
                }
              } else if ([2].includes(this.rightNums)) {
                // 添加
                if (this.searchFriend.length > 0) {
                  this.sendAddFriend()
                }
              } else if ([3].includes(this.rightNums)) {
                // 呼叫
                if (this.searchFriend.length == 0) {
                  return
                }

                this.$store.dispatch('app/setLoadingState', true)

                setTimeout(() => {
                  this.$store.dispatch('app/setLoadingState', false)
                  if (!this.$store.state.app.onLineList[0].show) {
                    this.no_camerOnline()
                    return
                  }
                  this.callUser({data: this.searchFriend[0]})
                  // websocketData.value().socket.send({
                  //   type: 1, // 视频通话
                  //   user_id: this.searchFriend[0].id,
                  //
                  //   user_id_caller_avatar:
                  //     process.env.VUE_APP_API +
                  //     '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png',
                  //   user_id_caller_title: this.$store.getters.getUserInfo.oldsters[0].name,
                  // })
                }, 300)
              }
              break
            case 0: // 通话记录
              if (this.applyList.length > 0) {
                this.popupBtnList = [
                  {
                    text: '立即呼叫',
                    ref: '',
                    fuc: this.applyThisFriend,
                  },
                  {
                    text: '加为好友',
                    ref: '',
                    fuc: this.applyThisFriend,
                  },
                  {
                    text: '所有记录',
                    ref: '',
                    fuc: this.allLogList,
                  },
                ]
                if (this.applyList[this.rightNums].customer) {
                  this.popupBtnList = [
                    {
                      text: '立即呼叫',
                      ref: '',
                      fuc: this.applyThisFriend,
                    },
                    {
                      text: '所有记录',
                      ref: '',
                      fuc: this.allLogList,
                    },
                  ]
                }
                // this.setIsRead(this.applyList[this.rightNums])
                this.applyList[this.rightNums].ref = ''
                this.popupBtnList[this.popupBtnNums].ref = 'active'
                this.dialogVisible = true
              }
              break
          }
        }
      },
      esc: () => {
        if (this.dialogVisible) {
          this.popupBtnList[this.popupBtnNums].ref = ''
          let isList = this.friendsList[this.rightNums]
            ? this.friendsList[this.rightNums]
            : this.addFriendBtn[this.rightNums]
            ? this.addFriendBtn[this.rightNums]
            : this.applyList[this.rightNums]
            ? this.applyList[this.rightNums]
            : []
          isList.ref = 'active'
          this.dialogVisible = false
          return
        }
        if (this.drawer) {
          // if (this.input.length > 0) {
          //   this.input = this.input.slice(0, -1)
          // } else {
          this.drawer = false
          // }
          return
        }
        this.$store.dispatch('index/setFocusDom', null)
        history.go(-1)
      },
    })

    // this.$nextTick(() => {
    //   // 接收消息
    //   websocketData.message(async (res) => {
    //
    //   })
    // })
  },
  methods: {
    // setIsRead(item) {
    //   if (item.is_read > 0) {
    //     return
    //   }
    //   let index = this.rightNums
    //
    //   this.applyList[index].is_read = 1
    //
    //   let badgeShow = false
    //   this.applyList.map((item, index) => {
    //     badgeShow = false
    //     if (item.is_read == 0) {
    //       badgeShow = true
    //       return
    //     }
    //   })
    //   this.leftList[0].badge = badgeShow
    //
    //   SetLogRead({
    //     id: item.id,
    //   }).then((res) => {})
    // },
    allLogList() {
      this.$store.dispatch('index/setFocusDom', null);
      sessionStorage.setItem('zegoFocus',this.rightNums)
      this.$router.push({
        path: './zegoLogAll_list',
        query:{
          fk_friend_id: this.applyList[this.rightNums].fk_friend_id
        }
      })
    },
    callUser(res) {
      // 获取剩余通话时长
      GetCallTime({
        fk_home_id: this.$store.getters.getUserInfo.home_id
      })
      .then(data=>{
        if (data.code == 200) {
          // true:超时  false:未超时
          if (data.data.timed_out == false) {
            GetFriendIsCall({
              fk_friend_id: res.data.fk_friend_id ? res.data.fk_friend_id : res.data.user_id,
            })
            .then((isCallData) => {
                  if (isCallData.code == 200) {
                    let isList = this.friendsList[this.rightNums]
                        ? this.friendsList[this.rightNums]
                        : this.addFriendBtn[this.rightNums]
                            ? this.addFriendBtn[this.rightNums]
                            : this.applyList[this.rightNums]
                                ? this.applyList[this.rightNums]
                                : []
                    // 对方闲置状态
                    if (!isCallData.data.is_call) {

                      Axios.all([
                        GetZegoToken({ user_id: this.$store.getters.getUserInfo.home_id }),
                      ]).then(
                          Axios.spread((tokenData) => {
                            if (tokenData.code == 200) {
                              this.zegoToken = tokenData.data.token
                              this.callMyfriend(res)
                            }
                          })
                      )
                    }
                    // 对方正在通话中
                    else {
                      this.popupMessage = '对方正在通话中,请稍后再拨!'
                      this.popupModule = 2
                      this.popupBtnList = [
                        {
                          text: '关闭',
                          ref: 'active',
                          fuc: () => {
                            this.dialogVisible = false
                            this.popupBtnList[this.popupBtnNums].ref = ''
                            isList.ref = 'active'
                            this.$nextTick(() => {
                              this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                            })
                          },
                        },
                      ]
                      isList.ref = ''
                      this.dialogVisible = true
                    }
                  }
                })
          } else {
            this.no_CallTime()
          }

        }
      })
      .catch(err=>{
        this.no_CallTime()
      })

      return



    },

    callMyfriend(item) {
      this.$store.dispatch('app/setLoadingState', true)
      this.dialogVisible = false
      this.popupBtnList[this.popupBtnNums].ref = ''
      let isList = this.friendsList[this.rightNums]
          ? this.friendsList[this.rightNums]
          : this.addFriendBtn[this.rightNums]
              ? this.addFriendBtn[this.rightNums]
              : this.applyList[this.rightNums]
                  ? this.applyList[this.rightNums]
                  : []

      isList.ref = 'active'

      setTimeout(() => {
        this.$store.dispatch('app/setLoadingState', false)
        if (!this.$store.state.app.onLineList[0].show) {
          this.no_camerOnline()
          return
        }

        websocketData.value().socket.send({
          type: 1, // 视频通话
          user_id: item.data.fk_friend_id ? item.data.fk_friend_id : item.data.user_id,

          user_id_caller_avatar: process.env.VUE_APP_API + '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png',
          user_id_caller_title: this.$store.getters.getUserInfo.oldsters[0].name,
        })
      }, 300)
    },

    sendZegoCall(roomID, userReceiver, token) {
      let isList = this.friendsList[this.rightNums]
          ? this.friendsList[this.rightNums]
          : this.addFriendBtn[this.rightNums]
              ? this.addFriendBtn[this.rightNums]
              : this.applyList[this.rightNums]
                  ? this.applyList[this.rightNums]
                  : []

      let receiverObj = {
        title: '',
        avatar: '',
      }
      if (this.leftNums == 0) {
        receiverObj.title =
            this.applyList[this.rightNums].called_name != ''
                ? this.applyList[this.rightNums].called_name
                : this.applyList[this.rightNums].called_phone
        receiverObj.avatar =
            this.applyList[this.rightNums].avatar != ''
                ? process.env.VUE_APP_API + this.applyList[this.rightNums].avatar
                : process.env.VUE_APP_API +
                '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png'
      } else if (this.leftNums == 1) {
        receiverObj.title =
            this.friendsList[this.rightNums].title != ''
                ? this.friendsList[this.rightNums].title
                : this.friendsList[this.rightNums].phone
        receiverObj.avatar =
            this.friendsList[this.rightNums].avatar != ''
                ? process.env.VUE_APP_API + this.friendsList[this.rightNums].avatar
                : process.env.VUE_APP_API +
                '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png'
      } else if (this.leftNums == 2) {
        receiverObj.title =
            this.searchFriend[0].title != '' ? this.searchFriend[0].title : this.searchFriend[0].phone
        receiverObj.avatar =
            this.searchFriend[0].avatar != ''
                ? process.env.VUE_APP_API + this.searchFriend[0].avatar
                : process.env.VUE_APP_API +
                '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png'
      }

      let be_friend = 0

      youAreMyFriend({
        fk_user_id: this.$store.getters.getUserInfo.home_id,
        fk_friend_id: isList.fk_friend_id
      })
      .then(res=>{
        if (res.code == 200) {
          be_friend = res.data.be_friend
        }
      })
      .finally(() => {
        let obj = {
          user_id_caller: this.$store.getters.getUserInfo.home_id,
          // user_id_caller_avatar: '',
          // user_id_caller_title: '',
          user_id_token: token,
          user_id_receiver: userReceiver,
          user_id_receiver_avatar: receiverObj.avatar,
          user_id_receiver_title: receiverObj.title,
          img_rul_receiver: process.env.VUE_APP_API +  '/public/storage/uploaded/2024_02/3815bd3afd5c70d8dd09ebcdb95a64fc.png',
          room_id: roomID,
          be_friend: be_friend
        }

        // 积分埋点  存在当前页面 且 存在任务  且  未完成任务 且为功能获取
        this.$store.state.app.pointList.map(item=>{
          if (item.redirect && item.redirect.indexOf(this.$route.path) > -1 && !item.harvest && item.redirect.indexOf('pointType=1') > -1) {
            this.fuc.insertPointNum(item.id)
          }
        })

        this.$bridge.callhandler('enterVideoChat', obj, () => {})
        this.dialogVisible = false
        this.popupBtnList[this.popupBtnNums].ref = ''
        isList.ref = 'active'
        this.$nextTick(() => {
          this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        })
      });




    },
    no_CallTime() {
      this.popupMessage = '剩余通话时长不足'
      this.popupModule = 2
      // this.$store.dispatch('index/setFocusDom', null);
      this.popupBtnList = [
        {
          text: '关闭',
          ref: 'active',
          fuc: () => {
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].ref = ''
            if (this.leftNums == 0) {
              this.applyList[this.rightNums].ref = 'active'
            } else if (this.leftNums == 1) {
              this.friendsList[this.rightNums].ref = 'active'
            } else if (this.leftNums == 2) {
              this.addFriendBtn[this.rightNums].ref = 'active'
            }
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
            })
          },
        },
      ]

      if (this.leftNums == 0) {
        this.applyList[this.rightNums].ref = ''
      } else if (this.leftNums == 1) {
        this.friendsList[this.rightNums].ref = ''
      } else if (this.leftNums == 2) {
        this.addFriendBtn[this.rightNums].ref = ''
      }

      setTimeout(() => {
        this.dialogVisible = true
      }, 300)
    },
    no_camerOnline() {
      this.popupMessage = '请先连接摄像头'
      this.popupModule = 2
      // this.$store.dispatch('index/setFocusDom', null);
      this.popupBtnList = [
        {
          text: '关闭',
          ref: 'active',
          fuc: () => {
            this.dialogVisible = false
            this.popupBtnList[this.popupBtnNums].ref = ''
            if (this.leftNums == 0) {
              this.applyList[this.rightNums].ref = 'active'
            } else if (this.leftNums == 1) {
              this.friendsList[this.rightNums].ref = 'active'
            } else if (this.leftNums == 2) {
              this.addFriendBtn[this.rightNums].ref = 'active'
            }
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
            })
          },
        },
      ]

      if (this.leftNums == 0) {
        this.applyList[this.rightNums].ref = ''
      } else if (this.leftNums == 1) {
        this.friendsList[this.rightNums].ref = ''
      } else if (this.leftNums == 2) {
        this.addFriendBtn[this.rightNums].ref = ''
      }

      setTimeout(() => {
        this.dialogVisible = true
      }, 300)
    },

    setTopMyFriend(item) {
      this.$store.dispatch('app/setLoadingState', true)

      this.dialogVisible = false
      this.popupBtnList[this.popupBtnNums].ref = ''
      this.friendsList[this.rightNums].ref = 'active'

      let fuc = item.top > 0 ? CancelFriendTop : SetFriendTop

      fuc({
        user_id: item.user_id,
        fk_friend_id: item.fk_friend_id,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$store.dispatch('app/setLoadingState', false)
            this.getMyFriends(() => {
              this.rightNums = 0
              // item.top > 0 ? this.rightNums = this.friendsList.length - 1 : this.rightNums = 0
            })

            setTimeout(() => {
              this.popupMessage = item.top > 0 ? '取消置顶成功' : '置顶成功'
              this.popupModule = 2
              this.popupBtnList = [
                {
                  text: '关闭',
                  ref: 'active',
                  fuc: () => {
                    this.dialogVisible = false
                    this.popupBtnList[this.popupBtnNums].ref = ''
                    this.friendsList[this.rightNums].ref = 'active'
                    this.$nextTick(() => {
                      this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                    })
                  },
                },
              ]
              this.friendsList[this.rightNums].ref = ''
              this.dialogVisible = true
            }, 300)
          }
        })
        .catch((err) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupMessage = item.top > 0 ? '取消置顶失败，请稍后再试!' : '置顶失败，请稍后再试!'

          if (err.response && err.response.data && err.response.data.msg) {
            this.popupMessage = err.response.data.msg
          }
          this.popupModule = 2
          this.popupBtnList = [
            {
              text: '关闭',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
                this.popupBtnList[this.popupBtnNums].ref = ''
                this.friendsList[this.rightNums].ref = 'active'
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                })
              },
            },
          ]
          setTimeout(() => {
            this.friendsList[this.rightNums].ref = ''
            this.dialogVisible = true
          }, 300)
        })
      // SetFriendTop, CancelFriendTop
    },
    setHandsFree(item) {
      this.$store.dispatch('app/setLoadingState', true)

      this.dialogVisible = false
      this.popupBtnList[this.popupBtnNums].ref = ''
      this.friendsList[this.rightNums].ref = 'active'

      let fuc = item.hands_free > 0 ? CancelFreeHands : SetFreeHands

      fuc({
        user_id: item.user_id,
        fk_friend_id: item.fk_friend_id,
      })
      .then((res) => {
        if (res.code == 200) {
          this.$store.dispatch('app/setLoadingState', false)
          // this.getMyFriends(() => {
          //   this.rightNums = 0
          //   // item.top > 0 ? this.rightNums = this.friendsList.length - 1 : this.rightNums = 0
          // })
          this.friendsList[this.rightNums].hands_free = this.friendsList[this.rightNums].hands_free ? 0 : 1
          setTimeout(() => {
            this.popupMessage = item.hands_free > 0 ? '设置自动接听成功' : '取消自动接听成功'
            this.popupModule = 2
            this.popupBtnList = [
              {
                text: '关闭',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  this.popupBtnList[this.popupBtnNums].ref = ''
                  this.friendsList[this.rightNums].ref = 'active'
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                  })
                },
              },
            ]
            this.friendsList[this.rightNums].ref = ''
            this.dialogVisible = true
          }, 300)
        }
      })
      .catch((err) => {
        this.$store.dispatch('app/setLoadingState', false)
        this.popupMessage = item.hands_free > 0 ? '取消自动接听失败，请稍后再试!' : '设置自动接听失败，请稍后再试!'

        if (err.response && err.response.data && err.response.data.msg) {
          this.popupMessage = err.response.data.msg
        }
        this.popupModule = 2
        this.popupBtnList = [
          {
            text: '关闭',
            ref: 'active',
            fuc: () => {
              this.dialogVisible = false
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.friendsList[this.rightNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
              })
            },
          },
        ]
        setTimeout(() => {
          this.friendsList[this.rightNums].ref = ''
          this.dialogVisible = true
        }, 300)
      })
    },
    delectMyfriendPopup(item) {
      this.dialogVisible = false
      this.popupBtnList[this.popupBtnNums].ref = ''
      this.friendsList[this.rightNums].ref = 'active'
      setTimeout(() => {
        this.popupModule = 2

        // item.nick_name ? item.nick_name :
        this.popupMessage = '是否要删除好友' + item.title + '?'
        this.popupBtnNums = 0
        this.popupBtnList = [
          {
            text: '关闭',
            ref: 'active',
            fuc: () => {
              this.dialogVisible = false
              this.popupBtnList[this.popupBtnNums].ref = ''
              this.friendsList[this.rightNums].ref = 'active'
              this.$nextTick(() => {
                this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
              })
            },
          },
          {
            text: '删除',
            ref: '',
            fuc: this.delectMyfriend,
          },
        ]

        this.friendsList[this.rightNums].ref = ''
        this.dialogVisible = true
      }, 300)
    },
    delectMyfriend(item) {
      this.$store.dispatch('app/setLoadingState', true)
      this.dialogVisible = false
      this.popupBtnList[this.popupBtnNums].ref = ''
      if (this.friendsList.length > 0) {
        this.friendsList[0].ref = 'active'
      } else {
        this.leftList[this.nextNums].ref = 'active'
      }
      DestroyFriend({
        user_id: item.user_id,
        fk_friend_id: item.fk_friend_id,
      })
        .then((res) => {
          this.$store.dispatch('app/setLoadingState', false)
          if (res.code == 200) {
            this.getMyFriends(() => {
              this.rightNums = 0
              // item.top > 0 ? this.rightNums = this.friendsList.length - 1 : this.rightNums = 0
            })

            this.popupMessage = '删除好友成功'
            this.popupModule = 2
            this.popupBtnList = [
              {
                text: '关闭',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  this.popupBtnList[this.popupBtnNums].ref = ''
                  if (this.friendsList.length == 0) {
                    this.leftList[this.nextNums].ref = 'active'
                    this.nextNums = -1
                  } else {
                    this.friendsList[this.rightNums].ref = 'active'
                  }
                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                  })
                },
              },
            ]

            setTimeout(() => {
              if (this.friendsList.length == 0) {
                this.leftList[this.nextNums].ref = 'active'
                this.nextNums = -1
              } else {
                this.friendsList[this.rightNums].ref = ''
              }
              this.dialogVisible = true
            }, 300)
          }
        })
        .catch((err) => {
          this.$store.dispatch('app/setLoadingState', false)
          this.popupMessage = '删除好友失败，请稍后再试!'

          if (err.response && err.response.data && err.response.data.msg) {
            this.popupMessage = err.response.data.msg
          }
          this.popupModule = 2
          this.popupBtnList = [
            {
              text: '关闭',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
                this.popupBtnList[this.popupBtnNums].ref = ''
                this.friendsList[this.rightNums].ref = 'active'
                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                })
              },
            },
          ]
          setTimeout(() => {
            this.friendsList[this.rightNums].ref = ''
            this.dialogVisible = true
          }, 300)
        })
    },
    popupClose() {
      // if (this.leftNums == 0) {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#fff'
        setTimeout(()=>{
          this.popupBtnNums = 0
          this.popupModule = 1
        },0)

      })
      // }
    },
    popupOpend() {
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
        document.getElementById('focus_border').style.borderColor = '#5472B0'
      })
    },

    getUserRr() {
      clearInterval(this.timer)
      this.timer = null
      GetUserQR({
        home_id: this.$store.getters.getUserInfo.home_id,
      }).then((res) => {
        if (res.code == 200) {
          this.qrInfo.url = process.env.VUE_APP_API + res.data.path
          this.timer = setInterval(() => {
            this.getUserRr()
          }, res.data.times * 1000 * 60)
        }
      })
    },
    getData() {
      this.$store.dispatch('index/setFocusDom', this.$refs.active)
      let leftICon = [
        {
          id: 3,
          title: '通话记录',
          badge: false,
          ref: '',
          fuc: this.applyForList,
        },
        {
          id: 1,
          title: '我的好友',
          badge: false,
          ref: '',
          fuc: this.getMyFriends,
        },
        {
          id: 2,
          title: '搜索用户',
          badge: false,
          ref: '',
          fuc: this.addFriend,
        },

        // {
        //   id: 4,
        //   title: '信息通知',
        //   badge: true,
        //   ref:'',
        //   fuc: this.getMessageList
        // }
      ]

      if (sessionStorage.getItem('zegoListIndex')) {
        this.leftNums = Number(sessionStorage.getItem('zegoListIndex'))
      }

      leftICon[this.leftNums].ref = 'active'
      this.leftList = leftICon

      this.$nextTick(() => {
        if (window.location.href.indexOf('type') > -1 || sessionStorage.getItem('zegoListIndex')) {
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
          // sessionStorage.removeItem('zegoListIndex')
        }
        this.$refs.active[0].classList.add('select')
        if (this.leftList[this.leftNums].fuc) {
          this.leftList[this.leftNums].fuc()
        }
      })
    },
    // 我的好友
    getMyFriends(callback) {
      this.$store.dispatch('app/setLoadingState', true);
      this.friendsList = []
      GetMyfriendsList({
        user_id: this.$store.getters.getUserInfo.home_id,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$store.dispatch('app/setLoadingState', false);
            let friendsList = JSON.parse(JSON.stringify(res.data.data))
            let itemIndex = 0
            friendsList.map((item, index) => {
              if (index % 6 == 0) {
                itemIndex = 0
              }
              item.ref = ''
              item.bg = this.bgList[itemIndex].bg
              item.color = this.bgList[itemIndex].color
              if (item.avatar != '') {
                item.avatar = process.env.VUE_APP_API + item.avatar
              }
              if (item.id != this.$store.getters.getUserInfo.home_id) {
                this.friendsList.push(item)
              }
              itemIndex++
            })
            this.$nextTick(() => {
              this.fuc.setScroll()
              if (callback) {
                callback()
              }
            })
          }
        })
        .catch((err) => {
          this.$store.dispatch('app/setLoadingState', false);
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
    },
    // 选择好友
    selectThisUser(item) {
      this.$store.dispatch('app/setLoadingState', true)

      this.dialogVisible = false
      this.popupBtnList[this.popupBtnNums].ref = ''
      this.friendsList[this.rightNums].ref = 'active'

      setTimeout(() => {
        this.$store.dispatch('app/setLoadingState', false)
        if (!this.$store.state.app.onLineList[0].show) {
          this.no_camerOnline()
          return
        }
        this.callUser({data: item})
      }, 300)
    },

    // 添加好友
    addFriend() {
      this.addFriendBtn = [
        // 搜索框
        {
          ref: '',
          title: '',
          show: true,
        },
        {
          ref: '',
          title: '搜索',
          show: false,
        },
        {
          ref: '',
          title: '添加',
          show: false,
        },
        {
          ref: '',
          title: '呼叫',
          show: false,
        },
      ]
    },
    drawerOpened() {
      this.addFriendBtn[this.rightNums].ref = ''
      this.rightNums = 0
      this.drawerBtn[this.rightNums].ref = 'active'
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active)

        document.getElementById('focus_border').style.borderColor = '#5472B0'
      })
    },
    drawerClosed() {
      this.drawerBtn[this.rightNums].ref = ''
      this.rightNums = 0
      this.addFriendBtn[this.rightNums].ref = 'active'
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active)
        document.getElementById('focus_border').style.borderColor = '#fff'
      })
    },
    // 搜索用户
    searchUser() {
      this.searchFriend = []
      this.searchLoading = true
      this.noUser = false
      this.dontAddMySelf = false
      // 不能添加自己
      if (Number(this.input) == this.$store.getters.getUserInfo.home_id) {
        this.searchLoading = false
        this.noUser = false
        this.searchFriend = []
        this.dontAddMySelf = true
        return
      }

      SearchUser({
        user_id: this.$store.getters.getUserInfo.home_id,
        fk_friend_id: this.input.length < 11 ? Number(this.input) : null,
        phone: this.input.length > 10 ? Number(this.input) : null,
      })
      .then((res) => {
        this.searchLoading = false
        if (res.code == 200) {
          if (res.data.avatar != '') {
            res.data.avatar = process.env.VUE_APP_API + res.data.avatar
            res.data.fk_friend_id = res.data.id
          }
          res.data.bg = this.bgList[Math.ceil(Math.random() * 5)].bg
          this.searchFriend.push(res.data)
        }
      })
      .catch((err) => {
        this.searchLoading = false
        this.noUser = true
      })
    },
    // 添加用户
    sendAddFriend() {
      // this.searchFriend[0]
      AddFriend({
        user_id: this.$store.getters.getUserInfo.home_id,
        fk_friend_id: this.searchFriend[0].id,
      })
        .then((res) => {
          if (res.code == 200) {
            this.popupMessage = '好友添加成功!'
            this.popupModule = 2
            this.popupBtnList = [
              {
                text: '关闭',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  this.popupBtnList[this.popupBtnNums].ref = ''
                  this.addFriendBtn[this.rightNums].ref = 'active'

                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                  })
                },
              },
            ]

            this.addFriendBtn[this.rightNums].ref = ''
            setTimeout(() => {
              this.dialogVisible = true
            }, 300)
          }
        })
        .catch((err) => {
          this.popupMessage = '好友添加失败，请稍后再试!'
          if (err.response && err.response.data && err.response.data.msg) {
            this.popupMessage = err.response.data.msg
          }
          this.popupModule = 2
          this.popupBtnList = [
            {
              text: '关闭',
              ref: 'active',
              fuc: () => {
                this.dialogVisible = false
                this.popupBtnList[this.popupBtnNums].ref = ''
                this.addFriendBtn[this.rightNums].ref = 'active'

                this.$nextTick(() => {
                  this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                })
              },
            },
          ]

          this.addFriendBtn[this.rightNums].ref = ''
          setTimeout(() => {
            this.dialogVisible = true
          }, 300)
        })
    },
    //通话记录
    applyForList() {
      this.applyList = []
      this.$store.dispatch('app/setLoadingState', true);
      GetCallLogList({
        user_id: this.$store.getters.getUserInfo.home_id,
      }).then((res) => {
        this.$store.dispatch('app/setLoadingState', false);
        if (res.code == 200 && res.data.data) {
          if (sessionStorage.getItem('zegoFocus')) {
            this.rightNums = Number(sessionStorage.getItem('zegoFocus'))
            sessionStorage.removeItem('zegoFocus')
          }
          let applyList = JSON.parse(JSON.stringify(res.data.data))
          let itemIndex = 0
          let badgeShow = false
          applyList.map((item, index) => {
            item.ref = ''
            if (index % 3 == 0) {
              itemIndex = 0
            }
            if (item.avatar != '') {
              item.avatar = process.env.VUE_APP_API + item.avatar
            }
            item.bg = this.applyBgList[itemIndex].bg
            item.color = this.applyBgList[itemIndex].color
            item.badgeColor = this.applyBgList[itemIndex].badgeColor
            this.applyList.push(item)
            itemIndex++
            if (item.is_read == 0) {
              badgeShow = true
            }
          })
          this.leftList[0].badge = badgeShow

          this.$nextTick(() => {
            this.fuc.setScroll()
          })

          if (this.firstShow && this.applyList.length > 0) {
            this.nextNums = this.leftNums
            this.leftList[this.leftNums].ref = ''
            this.applyList[this.rightNums].ref = 'active'
            this.$nextTick(() => {
              this.$store.dispatch('index/setFocusDom', this.$refs.active)
            })
            this.firstShow = false
          } else {
            this.nextNums = -1
          }
        }
      }).catch(err=>{
        this.$store.dispatch('app/setLoadingState', false);
        this.$nextTick(()=>{
          this.$store.dispatch('index/setFocusDom', this.$refs.active)
        })
      })
    },
    // 通话记录添加好友&&呼叫好友
    applyThisFriend(item) {
      // 呼叫好友
      if (!this.popupBtnNums) {
        this.$store.dispatch('app/setLoadingState', true)

        this.dialogVisible = false
        this.popupBtnList[this.popupBtnNums].ref = ''
        this.applyList[this.rightNums].ref = 'active'

        setTimeout(() => {
          this.$store.dispatch('app/setLoadingState', false)
          if (!this.$store.state.app.onLineList[0].show) {
            this.no_camerOnline()
            return
          }
          this.callUser({data: item})
        }, 300)
        return
      }
      // 添加好友
      this.dialogVisible = false
      this.popupBtnList[this.popupBtnNums].ref = ''
      this.applyList[this.rightNums].ref = 'active'
      this.$nextTick(() => {
        this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
      })

      AddFriend({
        user_id: this.$store.getters.getUserInfo.home_id,
        fk_friend_id: item.fk_friend_id,
      })
        .then((res) => {
          if (res.code == 200) {
            this.popupMessage = '好友添加成功!'
            this.popupModule = 2
            this.popupBtnList = [
              {
                text: '关闭',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  this.popupBtnList[this.popupBtnNums].ref = ''
                  this.applyList[this.rightNums].ref = 'active'

                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                  })
                },
              },
            ]

            this.applyList[this.rightNums].ref = ''
            setTimeout(() => {
              this.dialogVisible = true
            }, 300)
          }
        })
        .catch((err) => {
          this.applyList[this.rightNums].ref = ''
          setTimeout(() => {
            this.popupMessage = '好友添加失败，请稍后再试!'
            if (err.response && err.response.data && err.response.data.msg) {
              this.popupMessage = err.response.data.msg
            }
            this.popupModule = 2
            this.popupBtnList = [
              {
                text: '关闭',
                ref: 'active',
                fuc: () => {
                  this.dialogVisible = false
                  this.popupBtnList[this.popupBtnNums].ref = ''
                  this.applyList[this.rightNums].ref = 'active'

                  this.$nextTick(() => {
                    this.$store.dispatch('index/setFocusDom', this.$refs.active[0])
                  })
                },
              },
            ]
            this.dialogVisible = true
          }, 300)
        })
    },

    //信息通知
    getMessageList() {
      let messageList = [
        {
          id: 19,
          name: '张磊',
          type: 1, // 0 TV   1 手机
          ref: '',
        },
        {
          id: 20,
          name: '陈维浩',
          type: 1, // 0 TV   1 手机
          ref: '',
        },
        {
          id: 25,
          name: '机顶盒13A6',
          type: 0, // 0 TV   1 手机
          ref: '',
        },
        {
          id: 22,
          name: '杨洁亮',
          type: 1, // 0 TV   1 手机
          ref: '',
        },
        {
          id: 23,
          name: '裴玉',
          type: 1, // 0 TV   1 手机
          ref: '',
        },
        {
          id: 26,
          name: '机顶盒BBCC',
          type: 0, // 0 TV   1 手机
          ref: '',
        },
        {
          id: 24,
          name: '周威',
          type: 1, // 0 TV   1 手机
          ref: '',
        },
        {
          id: 12345678,
          name: '郭某人',
          type: 1, // 0 TV   1 手机
          ref: '',
        },
      ]

      let itemIndex = 0

      setTimeout(() => {
        messageList.map((item, index) => {
          if (index % 6 == 0) {
            itemIndex = 0
          }

          item.bg = this.bgList[itemIndex].bg
          item.color = this.bgList[itemIndex].color
          if (item.id != this.$store.getters.getUserInfo.home_id) {
            this.messageList.push(item)
          }
          itemIndex++
        })
        this.$nextTick(() => {
          this.fuc.setScroll()
        })
      }, 13)
    },
  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = null
  },
  beforeDestory() {},
}
</script>
<style lang='less' scoped>
.zegoCallIndex {
  height: 7rem;
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 每行显示两个元素 */
  gap: 0.16rem;
  font-size: 0.2rem;
  color: #e7e7ef;
  .noData {
    div {
      font-size: 0.5rem;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -200%);
      letter-spacing: 0.05rem;
      text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
    }
  }
  .left_zego {
    width: 3.54rem;
    border-radius: 0.15rem;
    .leftList {
      height: 3.6rem;
      padding-top: 0.1rem;
      padding: 0.28rem 0.52rem;
      padding-top: 0;
      text-align: center;
      .leftItem {
        height: 0.7rem;
        line-height: 0.78rem;
        margin-bottom: 0.2rem;
        background: #343d74;
        border-radius: 0.2rem;
        position: relative;
        font-weight: bold;
        transition: all 0.3s;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        font-size: 0.34rem;
        color: #e7e7ef;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
        .badge {
          width: 0.15rem;
          height: 0.15rem;
          border-radius: 50%;
          background: #ff5f5f;
          position: absolute;
          top: 50%;
          right: 0.2rem;
          transform: translateY(-50%);
        }
      }
      .select {
        background: #89a7ff;
        transition: all 0.3s;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(102, 129, 218, 0.6);
      }
    }
    .myQR {
      padding: 0rem 0.6rem;
      margin-top: -0.05rem;
      .qr_img {
        width: 2.05rem;
        height: 2.05rem;
        background: #413f55;
        margin: 0 auto;
        padding: 0.06rem;
        margin-bottom: 0rem;
        border-radius: 0.18rem;
      }
      img {
        border-radius: 0.14rem;
        display: block;
        width: 100%;
        height: 100%;
      }
      div {
        width: 2.2rem;
        font-size: 0.31rem;
        font-weight: bold;
        line-height: 0.45rem;
        text-align: center;
        letter-spacing: 0.05rem;
        text-indent: 0.05rem;
        margin-top: 0.18rem;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
      }
      .qrName {
        overflow: hidden;
        /*文本不会换行*/
        white-space: nowrap;
        /*当文本溢出包含元素时，以省略号表示超出的文本*/
        text-overflow: ellipsis;
      }
      div:nth-child(3) {
        margin-top: 0.05rem;
        letter-spacing: 0;
        font-weight: 500;
        font-size: 0.33rem;
      }
    }
  }
  .right_zego {
    width: 12.95rem;
    height: 7rem;
    margin-left: 0;
    //padding-left: 0.1rem;
    //margin-left: 0.05rem;
    .item_title {
      height: 0.6rem;
      line-height: 0.6rem;
      background: #ccc;
      font-size: 0.28rem;
      font-weight: bold;
      color: #000;
      padding-left: 0.2rem;
    }
    .item_list {
      //height: calc(100% - 0.76rem);
      height: 100%;
      //background: #ccc;
      border-radius: 0.26rem;

      overflow: hidden;

      position: relative;
      .friendsList {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(3, 1fr); /* 每行显示两个元素 */
        //gap: 0.16rem;
        gap: 0;
        position: absolute;
        top: 0;
        transition: all 0.2s;
        //.friendsItem:nth-child(3n + 3) {
        //  margin-right: 0;
        //}
      }
    }
    .friendsItem {
      border-radius: 0.26rem;
      height: 2.1rem;
      overflow: hidden;
      color: #000;
      font-weight: bold;
      position: relative;
      margin-bottom: 0.35rem;
      margin-right: 0.35rem;
      background-size: 100% 100% !important;
      .typeIcon {
        display: block;
        width: 0.56rem;
        height: 0.45rem;
        position: absolute;
        left: 0.5rem;
        top: 0.3rem;
      }
      .typeIcon_phone {
        top: 0.28rem;
        width: 0.32rem;
        height: 0.52rem;
      }
      .item_user {
        text-align: left;
        position: absolute;
        left: 0.5rem;
        top: 0.85rem;
        div {
          //line-height: 0.6rem;
          color: #e7e7ef;
          width: 2.6rem;
          overflow: hidden;
          /*文本不会换行*/
          white-space: nowrap;
          /*当文本溢出包含元素时，以省略号表示超出的文本*/
          text-overflow: ellipsis;
        }
        div:nth-child(1) {
          font-size: 0.38rem;
          letter-spacing: 0.02rem;
          //text-indent: -0.05rem;
        }
        div:nth-child(2) {
          font-size: 0.3rem;
          font-weight: 600;
        }
      }

      .avatar {
        width: 0.9rem;
        height: 0.9rem;
        border-radius: 50%;
        position: absolute;
        right: 0.185rem;
        top: 0.276rem;
      }
      .iconList {
        width: 1.12rem;
        display: flex;
        flex-direction: row-reverse;
        justify-content: space-between;
        align-items: center;
        align-content: center;
        position: absolute;
        bottom: 0.16rem;
        right: 0.2rem;
        .topIcon,.callenter {
          width: 0.48rem;
          height: 0.48rem;
          img {
            display: block;
            width: 100%;
            height: 100%;
          }
        }
      }

    }
    .addFriend {
      width: 56.4%;
      height: 100%;
      margin: 0 auto;
      margin-left: 2rem;
      position: relative;
      .addItem,
      .input_content,
      .btn_content {
        border-radius: 0.3rem;
        transition: all 0.3s;
      }
      .addItem:nth-child(2),
      .addItem:nth-child(3),
      .addItem:nth-child(4) {
        width: 3.46rem;
        height: 1.08rem;
        text-align: center;
        background: #333d72;
        border-radius: 0.3rem;

        font-weight: bold;
        letter-spacing: 0.2rem;
        text-indent: 0.2rem;
        font-size: 0.48rem;
        color: #e7e7ef;
        text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.6);
        overflow: hidden;
        .btn_content {
          line-height: 1.16rem;
        }
      }
      .addItem:nth-child(2) {
        position: absolute;
        bottom: 0.05rem;
        left: -1.5rem;
      }
      .addItem:nth-child(3) {
        position: absolute;
        bottom: 0.05rem;
        left: 2.2rem;
      }
      .addItem:nth-child(4) {
        position: absolute;
        bottom: 0.05rem;
        left: 5.9rem;
      }
      .searchFriend {
        width: 4rem;
        margin: 0 auto;
        margin-top: 1.26rem;
        .searchLoading {
          font-size: 0.45rem;
          font-weight: bold;
          text-align: center;
          letter-spacing: 0.05rem;
          height: 1.6rem;
          line-height: 1.6rem;
          color: #fff;
          background-image: linear-gradient(to bottom, #ffffff, rgba(255, 255, 255, 0.65));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .friendsItem {
          width: 3.97rem !important;
        }
        .avatar {
          width: 0.95rem;
          height: 0.95rem;
          border-radius: 50%;
          position: absolute;
          top: 0.24rem !important;
          right: 0.16rem
        }
      }
    }

    .applyFriendList {
      height: 100%;
      position: relative;
      .applyList {
        width: 83.6%;
        border-radius: 0.3rem;
        position: absolute;
        left: 0.8rem;
        transition: all 0.2s;
        .applyItem {
          width: 100%;
          height: 1.8602rem;
          margin-bottom: 0.35rem;
          background-size: 100% 100% !important;
          border-radius: 0.3rem;
          overflow: hidden;
          position: relative;
          .item_user {
            text-align: left;
            position: absolute;
            left: 1.8rem;
            top: 50%;
            transform: translateY(-80%);
            .userContent {
              //line-height: 0.6rem;
              color: #e7e7ef;
              width: 8.8rem;
              overflow: hidden;
              /*文本不会换行*/
              white-space: nowrap;
              /*当文本溢出包含元素时，以省略号表示超出的文本*/
              text-overflow: ellipsis;
              font-weight: bold;
            }
            div:nth-child(1) {
              font-size: 0.45rem;
              letter-spacing: 0.02rem;
              //text-indent: -0.05rem;
            }
            div:nth-child(2) {
              font-size: 0.3rem;
              font-weight: 600;
            }
          }
          .time {
            position: absolute;
            right: 0.4rem;
            bottom: 0.25rem;
            font-size: 0.26rem;
            font-weight: bold;
          }
          .avatar {
            width: 0.95rem;
            height: 0.95rem;
            border-radius: 50%;
            position: absolute;
            left: 4.3%;
            top: 52%;
            transform: translateY(-50%);
          }
          .badge {
            width: 0.15rem;
            height: 0.15rem;
            border-radius: 50%;
            background: #ff5f5f;
            position: absolute;
            top: 0.45rem;
            left: 1rem;
            border: 0.03rem solid transparent;
          }
        }
      }
    }
    .messageCenterList {
      height: 100%;

      position: relative;
      .messageList {
        width: 83.6%;
        border-radius: 0.3rem;
        position: absolute;
        left: 0.8rem;
        transition: all 0.2s;
        .messageItem {
          width: 100%;
          height: 1.86rem;
          margin-bottom: 0.36rem;
          background-size: 100% 100% !important;
          background: #ccc !important;
          border-radius: 0.3rem;
          overflow: hidden;
        }
      }
    }
  }
}
</style>
<style lang="less">
.zegoCallIndex {
  .called_name {
    padding: 0 0.1rem;
    font-weight: bold;
    //color: rgba(210,126,126,1);
  }
  .el-dialog {
    //width: fit-content;
    //margin-top: 0 !important;
    //top: 50%;
    //transform: translateY(-50%);
    //border-radius: 0.16rem;
    .el-dialog__header {
      .el-dialog__title {
        font-size: 0.5rem !important;
      }
    }
    .el-dialog__body {
      .callMyFriend {
        padding: 0.2rem 0 ;

        .myFriendsBtn {
          width: 8.75rem;
          height: 1rem;
          line-height: 1.08rem;
          background: #5472B0;
          //color: #fff;
          border-radius: 0.2rem;
          margin-bottom: 0.3rem;
          text-align: center;
          font-weight: bold;
          font-size: 0.5rem;
          position: relative;
          //text-shadow: 0.03rem 0.03rem 0.01rem rgba(0, 0, 0, 0.3);
          letter-spacing: 0.05rem;
          text-indent: 0.05rem;
          img {
            width: 0.48rem;
            height: 0.48rem;
            position: absolute;
            top: 50%;
            left: 2.2rem;
            transform: translateY(-45%);
          }
          .box-myFriendsBtn {
            background: linear-gradient(to bottom, #FDFEFF 30%, #BED1FB 90%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            //text-shadow: 0rem 0.02rem 0.05rem rgba(0, 0, 0, 0.5);
          }
        }
        .myFriendsBtn:last-child {
          margin-bottom: 0;
        }
      }
      .popupMessage {
        padding: 0.2rem 0 ;
        .popupMessage {
          //line-height: 0.4rem;
          text-align: center;
          //font-size: 0.24rem;
          font-size: 0.45rem;
          margin-bottom: 0.3rem;
        }
        .popupMessageBtn {

          width: 8.75rem;
          height: 1rem;
          //width: 20vw;
          //height: 0.6rem;
          //line-height: 0.68rem;
          line-height: 1.18rem;
          background: #5472B0;
          font-weight: bold;
          text-align: center;
          //font-size: 0.24rem;
          font-size: 0.5rem;
          color: #fff;
          border-radius: 0.16rem;
          margin: 0 auto;
          margin-top: 0.2rem !important;
        }
        .popupMessageBtn:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  .addFriend {
    .input_content {
      background: #fff;
      height: 1.1rem;
      line-height: 1.2rem;
      padding: 0 0.2rem;
      text-align: center;
      letter-spacing: 0.04rem;
      .input {
        font-size: 0.48rem;
        color: #72797f;
        font-weight: bold;
        input {
          height: 1.1rem;
          line-height: 1.1rem;
          border-radius: 0.3rem;
          text-align: center;
          letter-spacing: 0.04rem;
          text-indent: 0.04rem;
          color: #72797f;
          font-weight: bold;
          font-size: 0.48rem;
        }
      }
    }
    .el-drawer__wrapper {
      width: 5.56rem;
      left: 50%;
      transform: translateX(-28%);
    }
    .el-drawer.btt {
      height: 3.24rem !important;
      top: 4.14rem;
      //bottom: 3.4rem;
      border-radius: 0.3rem;
      padding: 0.2rem;
      color: #fff;
      .numList {
        display: grid;
        grid-template-columns: repeat(4, 1fr); /* 每行显示两个元素 */
        gap: 0.16rem;
        overflow: hidden;
        .num_item {
          width: 0.8rem;
          height: 0.84rem;
          line-height: 0.92rem;
          text-align: center;
          border-radius: 0.1rem;
          background: #89a7ff;
          font-size: 0.4rem;
          font-weight: bold;
        }

        .num_item:nth-child(4n + 4) {
          width: 2.2rem;
        }
      }
    }
  }
}
</style>
