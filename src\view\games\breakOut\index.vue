<template>
  <div class="breakOut">
    <canvas id="canvas" ref="canvas"></canvas>
  </div>
</template>

<script>
import Krakout  from './game'
export default {
  name:'breakOut',
  components: {
  },
  inject: ['reload'],
  data() {
    return {
      game: null,
      gameLeave: 1
    };
  },
  created() {
    this.$store.dispatch('index/setMainTitle',JSON.parse(sessionStorage.getItem('redirectInfo')).title ? JSON.parse(sessionStorage.getItem('redirectInfo')).title : '打砖块')
    this.$store.dispatch('index/setFocusDom', null)
  },
  computed: {},
  watch: {},
  mounted() {
    this.fuc.KeyboardEvents({
      down:()=>{

      },
      up:()=>{

      },
      left:()=>{

      },
      right:()=>{

      },
      enter:()=>{
        if (this.game.stage == 0) {
          this.game.initGame()
          this.game.start()
          return
        }
        if (this.game.stage == 3 || this.game.stage == 4) {
          this.game.init()
          return
        }
      },
      esc:()=>{
        history.go(-1)

      }
    },null,true)

    this.gameLeave = Math.floor(Math.random() * 2)

    const canvas = this.$refs.canvas
    this.game = new Krakout(canvas, {
      playGame: this.gameLeave,
      tip: ['左右键控制棒子，确认键发射小球', '按确认键继续'],
      failAttrs: {
        tip: ['按确认键重置游戏']
      },
      successAttrs: {
        tip: ['按确认键重置游戏']
      }
    })

    this.$nextTick(()=>{
      this.game.init()
      this.game.bind('keydown', function({keyCode}) {
        if ((keyCode == 37 || keyCode == 39) && (this.stage == 1 || this.stage == 2)) {
          this.moveBar(keyCode == 37 ? 'left' : 'right')
        }
      })
      .bind('keyup', function({keyCode}) {
        if (keyCode == 37 || keyCode == 39) {
          if (this.stage == 1 || this.stage == 2) {
            this.stopMoveBar()
          }
        }
      })
    })


  },
  methods:{

},

  destroyed() {


  },
  beforeDestory() {

  },
}
</script>
<style lang='less' scoped>
  .breakOut {
    height: 7rem;
    border: 1px solid transparent;
    canvas {
      display: block;
      background-color: transparent;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      border: 0.01rem solid #fff;
      box-sizing: border-box;
      height: 80%;
    }
    canvas:focus {
      outline: none;
    }
  }

</style>
<style lang="less">
  .breakOut {

  }
</style>
